<!-- 筛选页面 -->
<!-- 添加WXS模块用于数字格式化 -->
<wxs module="utils">
  function formatNumber(price, wornCount) {
    if (price && wornCount && wornCount > 0) {
      var value = price / wornCount;
      return value.toFixed(2);
    }
    return "0.00";
  }

  function formatPrice(price) {
    if (price) {
      return parseFloat(price).toFixed(2);
    }
    return "0.00";
  }

  module.exports = {
    formatNumber: formatNumber,
    formatPrice: formatPrice
  };
</wxs>

<view class="filter-container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav">
    <view class="nav-left" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title">
      <view class="title-text">衣物筛选</view>
      <view class="subtitle-text">分类、筛选与管理</view>
    </view>
    <!-- 添加右侧操作按钮 -->
    <view class="nav-right">
      <view class="nav-action">
        <text class="iconfont icon-more-setting"></text>
      </view>
    </view>
  </view>

  <!-- 主体内容区 -->
  <view class="filter-content">
    <!-- 左侧类别列表 -->
    <view class="category-list">

      <view class="category-item {{selectedCategory === 'all' ? 'active' : ''}}"
            bindtap="selectCategory" data-category="all">
        <view class="category-name">所有衣服</view>
        <view class="category-count">{{totalCount}}</view>
      </view>

      <!-- 衣服类别列表 -->
      <block wx:for="{{categories}}" wx:key="category">
        <view class="category-item {{selectedCategory === item.category ? 'active' : ''}}"
              bindtap="selectCategory" data-category="{{item.category}}">
          <view class="category-name">{{item.name}}</view>
          <view class="category-count">{{item.count}}</view>
        </view>
      </block>
    </view>

    <!-- 右侧内容区 -->
    <view class="content-area">
      <!-- 搜索框 -->
      <view class="search-container">
        <view class="search-box">
          <icon type="search" size="18" color="#999"></icon>
          <input class="search-input"
                 placeholder="搜索衣物名称、颜色、风格等"
                 value="{{searchKeyword}}"
                 bindchange="onSearchInput"
                 confirm-type="search"
                 bindtap="handleIOSFocus"
                 data-field="searchKeyword"
                 focus="{{searchKeywordFocus}}" />
          <icon wx:if="{{isSearchActive}}"
                type="clear"
                size="18"
                color="#999"
                class="clear-icon"
                catchtap="clearSearch"></icon>
        </view>
      </view>

      <!-- 筛选按钮栏 -->
      <view class="filter-tabs">
        <!-- 细分类筛选按钮 - 仅在选择特定类别时显示 -->
        <view class="filter-tab {{activeFilterPanel === 'subcategory' ? 'active' : ''}}"
              bindtap="toggleFilterPanel" data-type="subcategory"
              wx:if="{{selectedCategory !== 'latest' && selectedCategory !== 'all' && typeDetails.length > 0}}">
          <text class="filter-tab-text">细分类{{selectedTypeDetail ? ('：' + selectedTypeDetail) : ''}}</text>
          <text class="filter-tab-icon">{{activeFilterPanel === 'subcategory' ? '↑' : '↓'}}</text>
        </view>

        <!-- 季节筛选按钮 -->
        <view class="filter-tab {{activeFilterPanel === 'season' ? 'active' : ''}} {{selectedSeasons.length > 0 ? 'has-selected' : ''}}"
              bindtap="toggleFilterPanel" data-type="season">
          <text class="filter-tab-text">季节{{displaySeasons ? ('：' + displaySeasons) : ''}}</text>
          <text class="filter-tab-icon">{{activeFilterPanel === 'season' ? '↑' : '↓'}}</text>
        </view>

        <!-- 风格筛选按钮 -->
        <view class="filter-tab {{activeFilterPanel === 'style' ? 'active' : ''}} {{selectedStyles.length > 0 ? 'has-selected' : ''}}"
              bindtap="toggleFilterPanel" data-type="style">
          <text class="filter-tab-text">风格{{displayStyles ? ('：' + displayStyles) : ''}}</text>
          <text class="filter-tab-icon">{{activeFilterPanel === 'style' ? '↑' : '↓'}}</text>
        </view>

        <!-- 颜色筛选按钮 -->
        <view class="filter-tab {{activeFilterPanel === 'color' ? 'active' : ''}} {{selectedColors.length > 0 ? 'has-selected' : ''}}"
              bindtap="toggleFilterPanel" data-type="color">
          <text class="filter-tab-text">颜色{{displayColors ? ('：' + displayColors) : ''}}</text>
          <text class="filter-tab-icon">{{activeFilterPanel === 'color' ? '↑' : '↓'}}</text>
        </view>

        <!-- 存储位置筛选按钮 -->
        <view class="filter-tab {{activeFilterPanel === 'storageLocation' ? 'active' : ''}} {{selectedStorageLocations.length > 0 ? 'has-selected' : ''}}"
              bindtap="toggleFilterPanel" data-type="storageLocation">
          <text class="filter-tab-text">位置{{displayStorageLocations ? ('：' + displayStorageLocations) : ''}}</text>
          <text class="filter-tab-icon">{{activeFilterPanel === 'storageLocation' ? '↑' : '↓'}}</text>
        </view>

        <!-- 衣柜筛选按钮 -->
        <view class="filter-tab {{activeFilterPanel === 'wardrobe' ? 'active' : ''}} {{selectedWardrobe ? 'has-selected' : ''}}"
              bindtap="toggleFilterPanel" data-type="wardrobe">
          <text class="filter-tab-text">衣柜{{displayWardrobe ? ('：' + displayWardrobe) : ''}}</text>
          <text class="filter-tab-icon">{{activeFilterPanel === 'wardrobe' ? '↑' : '↓'}}</text>
        </view>

        <!-- 清除所有筛选按钮 -->
        <view class="clear-all-tab" bindtap="clearAllFilters" wx:if="{{hasActiveFilters}}">
          <text class="clear-all-icon">×</text>
          <text>清除</text>
        </view>
      </view>

      <!-- 筛选面板区域 -->
      <view class="filter-panels" wx:if="{{activeFilterPanel !== ''}}">
        <!-- 细分类筛选面板 -->
        <view class="filter-panel" wx:if="{{activeFilterPanel === 'subcategory'}}">
          <view class="filter-panel-content">
            <view class="filter-item {{selectedTypeDetail === '' ? 'active' : ''}}"
                  bindtap="selectTypeDetail" data-type="">
              全部
            </view>
            <block wx:for="{{typeDetails}}" wx:key="*this">
              <view class="filter-item {{selectedTypeDetail === item ? 'active' : ''}}"
                    bindtap="selectTypeDetail" data-type="{{item}}">
                {{item}}
              </view>
            </block>
          </view>
        </view>

        <!-- 季节筛选面板 -->
        <view class="filter-panel" wx:if="{{activeFilterPanel === 'season'}}">
          <view class="filter-panel-content">
            <view class="filter-item {{selectedSeasons.length === 0 ? 'active' : ''}}"
                  bindtap="clearSeasonFilter">
              全部
            </view>
            <block wx:for="{{seasonOptions}}" wx:key="*this">
              <view class="filter-item {{filterSeasonIsSelected[item] ? 'active' : ''}}"
                    bindtap="toggleSeasonFilter" data-season="{{item}}">
                <icon type="{{filterSeasonIsSelected[item] ? 'success' : 'circle'}}" size="16" color="{{filterSeasonIsSelected[item] ? '#07c160' : '#999'}}"></icon>
                <text style="margin-left: 5px;">{{item}}</text>
              </view>
            </block>
          </view>
        </view>

        <!-- 风格筛选面板 -->
        <view class="filter-panel" wx:if="{{activeFilterPanel === 'style'}}">
          <view class="filter-panel-content">
            <view class="filter-item {{selectedStyles.length === 0 ? 'active' : ''}}"
                  bindtap="clearStyleFilter">
              全部
            </view>
            <block wx:for="{{styleOptions}}" wx:key="*this">
              <view class="filter-item {{filterStyleIsSelected[item] ? 'active' : ''}}"
                    bindtap="toggleStyleFilter" data-style="{{item}}">
                <icon type="{{filterStyleIsSelected[item] ? 'success' : 'circle'}}" size="16" color="{{filterStyleIsSelected[item] ? '#07c160' : '#999'}}"></icon>
                <text style="margin-left: 5px;">{{item}}</text>
              </view>
            </block>
          </view>
        </view>

        <!-- 颜色筛选面板 -->
        <view class="filter-panel" wx:if="{{activeFilterPanel === 'color'}}">
          <view class="filter-panel-content">
            <view class="filter-item {{selectedColors.length === 0 ? 'active' : ''}}"
                  bindtap="clearColorFilter">
              全部
            </view>
            <block wx:for="{{colorOptions}}" wx:key="*this">
              <view class="filter-item {{filterColorIsSelected[item] ? 'active' : ''}}"
                    bindtap="toggleColorFilter" data-color="{{item}}">
                <icon type="{{filterColorIsSelected[item] ? 'success' : 'circle'}}" size="16" color="{{filterColorIsSelected[item] ? '#07c160' : '#999'}}"></icon>
                <text style="margin-left: 5px;">{{item}}</text>
              </view>
            </block>
          </view>
        </view>

        <!-- 存储位置筛选面板 -->
        <view class="filter-panel" wx:if="{{activeFilterPanel === 'storageLocation'}}">
          <view class="filter-panel-content">
            <view class="filter-item {{selectedStorageLocations.length === 0 ? 'active' : ''}}"
                  bindtap="clearStorageLocationFilter">
              全部
            </view>
            <block wx:for="{{storageLocationOptions}}" wx:key="*this">
              <view class="filter-item {{filterStorageLocationIsSelected[item] ? 'active' : ''}}"
                    bindtap="toggleStorageLocationFilter" data-location="{{item}}">
                <icon type="{{filterStorageLocationIsSelected[item] ? 'success' : 'circle'}}" size="16" color="{{filterStorageLocationIsSelected[item] ? '#07c160' : '#999'}}"></icon>
                <text style="margin-left: 5px;">{{item}}</text>
              </view>
            </block>
          </view>
        </view>

        <!-- 衣柜筛选面板 -->
        <view class="filter-panel" wx:if="{{activeFilterPanel === 'wardrobe'}}">
          <view class="filter-panel-content">
            <view class="filter-item {{selectedWardrobe === '' ? 'active' : ''}}"
                  bindtap="clearWardrobeFilter">
              全部
            </view>
            <view class="filter-item {{selectedWardrobe === 'unclassified' ? 'active' : ''}}"
                  bindtap="selectUnclassifiedWardrobe">
              <icon type="{{selectedWardrobe === 'unclassified' ? 'success' : 'circle'}}" size="16" color="{{selectedWardrobe === 'unclassified' ? '#07c160' : '#999'}}"></icon>
              <text style="margin-left: 5px;">未分类</text>
            </view>
            <block wx:for="{{wardrobeOptions}}" wx:key="index">
              <view class="filter-item {{filterWardrobeIsSelected[item._id] ? 'active' : ''}}"
                    bindtap="selectWardrobeFilter" data-wardrobeid="{{item._id}}">
                <icon type="{{filterWardrobeIsSelected[item._id] ? 'success' : 'circle'}}" size="16" color="{{filterWardrobeIsSelected[item._id] ? '#07c160' : '#999'}}"></icon>
                <text style="margin-left: 5px;">{{item.name}}</text>
              </view>
            </block>
          </view>
        </view>
      </view>

      <!-- 衣物列表 -->
      <view class="clothes-list-container">
        <view class="clothes-list-title">
          <text>{{listTitle}}</text>
          <text class="clothes-count">{{filteredClothes.length}}件</text>
        </view>

        <scroll-view scroll-y="true" class="clothes-list" bindscrolltolower="loadMoreClothes">
          <view class="clothes-grid">
            <block wx:if="{{filteredClothes.length > 0}}">
              <block wx:for="{{displayClothes}}" wx:key="_id">
                <view class="clothes-item {{isSelectionMode && clothesIsSelected[item._id] ? 'selected' : ''}}"
                      bindtap="viewClothesDetail"
                      bindlongpress="onClothesLongPress"
                      data-id="{{item._id}}"
                      data-name="{{item.name || ((item.color || '') + ' ' + (item.style || ''))}}">
                  <view class="clothes-image-container">
                    <!-- 选择模式下显示复选框 -->
                    <view class="selection-checkbox" wx:if="{{isSelectionMode}}">
                      <icon type="{{clothesIsSelected[item._id] ? 'success' : 'circle'}}"
                            size="22"
                            color="{{clothesIsSelected[item._id] ? '#ffffff' : '#999'}}"></icon>
                    </view>
                    <image class="clothes-image" src="{{item.tempImageUrl || tempPlaceholderUrl}}" mode="aspectFit" lazy-load="true"></image>
                    <view class="price-tag" wx:if="{{item.price}}">¥{{utils.formatPrice(item.price)}}</view>
                    <view class="cost-effectiveness-tag" wx:if="{{item.price && item.wornCount && item.wornCount > 0}}">
                      {{utils.formatNumber(item.price, item.wornCount)}}/次
                    </view>
                  </view>
                  <view class="clothes-info">
                    <text class="clothes-name">{{item.name || ((item.color || '') + ' ' + (item.style || ''))}}</text>
                    <view class="clothes-meta">
                      <text class="clothes-category">{{item.category}}</text>
                      <text class="clothes-type" wx:if="{{item.type_detail}}">{{item.type_detail}}</text>
                    </view>
                  </view>
                </view>
              </block>
            </block>
            <block wx:else>
              <view class="no-data-tip">没有符合条件的衣物</view>
            </block>
          </view>

          <!-- 加载更多指示器 -->
          <view class="loading-more" wx:if="{{isLoadingMore}}">
            <view class="loading-spinner"></view>
            <text>加载更多...</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>

  <!-- 批量编辑工具栏 -->
  <view class="selection-toolbar" wx:if="{{isSelectionMode}}">
    <view class="selection-btn" bindtap="toggleSelectAll">
      <view class="toolbar-checkbox {{selectAll ? 'selected' : ''}}">
        <icon type="{{selectAll ? 'success' : 'circle'}}" size="22" color="{{selectAll ? '#ffffff' : '#999'}}"></icon>
      </view>
      <text>{{selectAll ? '取消全选' : '全选'}}</text>
    </view>
    <view class="selection-btn edit" bindtap="showBatchEditModal">
      <text>批量编辑</text>
    </view>
    <view class="selection-btn delete" bindtap="showBatchDeleteConfirm">
      <text>批量删除</text>
    </view>
    <view class="selection-btn cancel" bindtap="exitSelectionMode">
      <text>取消</text>
    </view>
  </view>
</view>

<!-- 批量编辑弹窗 -->
<view class="batch-edit-modal-overlay" wx:if="{{showBatchEditModal}}">
  <view class="batch-edit-modal-content" catchtap="preventBubble">
    <view class="batch-edit-modal-header">
      <text class="batch-edit-modal-title">批量编辑 ({{selectedClothes.length}}件衣物)</text>
      <view class="close-btn" bindtap="hideBatchEditModal">×</view>
    </view>
    <view class="batch-edit-modal-body">
      <view class="batch-edit-tip">注意：只有填写的字段会被更新，空白字段不会修改原有值</view>

      <!-- 类别选择 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">类别</view>
        <view class="batch-edit-value" bindtap="showBatchCategoryPicker">
          {{batchEditData.category || '不修改'}}
          <text class="arrow-right">▶</text>
        </view>
      </view>

      <!-- 细分类输入 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">细分类</view>
        <input class="batch-edit-input" type="text" value="{{batchEditData.type_detail}}"
               bindinput="onBatchEditChange" data-field="type_detail"
               placeholder="不修改" />
      </view>

      <!-- 颜色输入 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">颜色</view>
        <input class="batch-edit-input" type="text" value="{{batchEditData.color}}"
               bindinput="onBatchEditChange" data-field="color"
               placeholder="不修改" />
      </view>

      <!-- 风格输入 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">风格</view>
        <input class="batch-edit-input" type="text" value="{{batchEditData.style}}"
               bindinput="onBatchEditChange" data-field="style"
               placeholder="不修改" />
      </view>

      <!-- 季节选择 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">季节</view>
        <view class="batch-edit-value" bindtap="showBatchSeasonPicker">
          {{batchEditData.season || '不修改'}}
          <text class="arrow-right">▶</text>
        </view>
      </view>

      <!-- 衣柜选择 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">衣柜</view>
        <view class="batch-edit-value" bindtap="showBatchWardrobePicker">
          {{batchEditData.wardrobeName || '不修改'}}
          <text class="arrow-right">▶</text>
        </view>
      </view>

      <!-- 品牌输入 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">品牌</view>
        <input class="batch-edit-input" type="text" value="{{batchEditData.brand}}"
               bindinput="onBatchEditChange" data-field="brand"
               placeholder="不修改" />
      </view>

      <!-- 材质输入 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">材质</view>
        <input class="batch-edit-input" type="text" value="{{batchEditData.material}}"
               bindinput="onBatchEditChange" data-field="material"
               placeholder="不修改" />
      </view>

      <!-- 尺码输入 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">尺码</view>
        <input class="batch-edit-input" type="text" value="{{batchEditData.size}}"
               bindinput="onBatchEditChange" data-field="size"
               placeholder="不修改" />
      </view>

      <!-- 存放位置输入 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">存放位置</view>
        <input class="batch-edit-input" type="text" value="{{batchEditData.storageLocation}}"
               bindinput="onBatchEditChange" data-field="storageLocation"
               placeholder="不修改" />
      </view>

      <!-- 购买渠道输入 -->
      <view class="batch-edit-row">
        <view class="batch-edit-label">购买渠道</view>
        <input class="batch-edit-input" type="text" value="{{batchEditData.purchaseChannel}}"
               bindinput="onBatchEditChange" data-field="purchaseChannel"
               placeholder="不修改" />
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="batch-edit-actions">
      <button class="action-btn cancel" bindtap="hideBatchEditModal">取消</button>
      <button class="action-btn save" bindtap="saveBatchEdit">保存</button>
    </view>
  </view>
</view>

<!-- 衣物详情弹窗 -->
<view class="card-detail-modal" wx:if="{{showDetail}}" bindtap="closeClothesDetail">
  <view class="modal-content" catchtap="preventBubble">
    <view class="modal-header">
      <text class="modal-title">{{detailClothes.name || ((detailClothes.color || '') + ' ' + (detailClothes.style || ''))}}</text>
      <view class="close-btn" bindtap="closeClothesDetail">×</view>
    </view>
    <view class="modal-body">
      <!-- 图片和价格信息卡片 -->
      <view class="detail-card image-card">
        <view class="item-image-container">
          <image class="item-image" src="{{detailClothes.tempImageUrl}}" mode="aspectFit" lazy-load="true"></image>
          <view class="price-tag" wx:if="{{detailClothes.price}}">¥{{utils.formatPrice(detailClothes.price)}}</view>
          <view class="cost-effectiveness-tag" wx:if="{{detailClothes.price && detailClothes.wornCount && detailClothes.wornCount > 0}}">¥{{utils.formatNumber(detailClothes.price, detailClothes.wornCount)}}/次</view>
          <view class="discard-mark" wx:if="{{detailClothes.wantToDiscard}}">断舍离</view>
        </view>
      </view>

      <!-- 基本信息卡片 -->
      <view class="detail-card">
        <view class="card-title">基本信息</view>
        <view class="card-content">
          <view class="detail-row">
            <text class="detail-label">类别</text>
            <text class="detail-value">{{detailClothes.category || '未分类'}}</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.type_detail}}">
            <text class="detail-label">细分类</text>
            <text class="detail-value">{{detailClothes.type_detail}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">颜色</text>
            <text class="detail-value">{{detailClothes.color || '未知'}}</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.size}}">
            <text class="detail-label">尺码</text>
            <text class="detail-value">{{detailClothes.size}}</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.material}}">
            <text class="detail-label">材质</text>
            <text class="detail-value">{{detailClothes.material}}</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.style}}">
            <text class="detail-label">风格</text>
            <text class="detail-value">{{detailClothes.style}}</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.wardrobeName}}">
            <text class="detail-label">所属衣柜</text>
            <text class="detail-value">{{detailClothes.wardrobeName}}</text>
          </view>
        </view>
      </view>

      <!-- 购买信息卡片 -->
      <view class="detail-card" wx:if="{{detailClothes.brand || detailClothes.price || detailClothes.purchaseDate || detailClothes.purchaseChannel}}">
        <view class="card-title">购买信息</view>
        <view class="card-content">
          <view class="detail-row" wx:if="{{detailClothes.brand}}">
            <text class="detail-label">品牌</text>
            <text class="detail-value">{{detailClothes.brand}}</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.purchaseChannel}}">
            <text class="detail-label">购买渠道</text>
            <text class="detail-value">{{detailClothes.purchaseChannel}}</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.price}}">
            <text class="detail-label">价格</text>
            <text class="detail-value">¥{{utils.formatPrice(detailClothes.price)}}</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.purchaseDate}}">
            <text class="detail-label">购买时间</text>
            <text class="detail-value">{{detailClothes.purchaseDate}}</text>
          </view>
        </view>
      </view>

      <!-- 使用信息卡片 -->
      <view class="detail-card" wx:if="{{detailClothes.wornCount || detailClothes.season || detailClothes.storageLocation}}">
        <view class="card-title">使用信息</view>
        <view class="card-content">
          <view class="detail-row" wx:if="{{detailClothes.wornCount}}">
            <text class="detail-label">穿着次数</text>
            <text class="detail-value">{{detailClothes.wornCount}}次</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.price && detailClothes.wornCount && detailClothes.wornCount > 0}}">
            <text class="detail-label">每次成本</text>
            <text class="detail-value">¥{{utils.formatNumber(detailClothes.price, detailClothes.wornCount)}}/次</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.storageLocation}}">
            <text class="detail-label">存储位置</text>
            <text class="detail-value">{{detailClothes.storageLocation}}</text>
          </view>
          <view class="detail-row" wx:if="{{detailClothes.season}}">
            <text class="detail-label">适用季节</text>
            <text class="detail-value">{{detailClothes.season}}</text>
          </view>
        </view>
      </view>

      <!-- 备注信息卡片 -->
      <view class="detail-card" wx:if="{{detailClothes.remark}}">
        <view class="card-title">备注</view>
        <view class="card-content">
          <view class="detail-row">
            <text class="detail-value remark-value">{{detailClothes.remark}}</text>
          </view>
        </view>
      </view>

      <view class="modal-actions">
        <button class="action-btn delete-btn" bindtap="deleteClothesFromDetail" data-id="{{detailClothes._id}}">删除</button>
        <button class="action-btn edit" bindtap="editClothes" data-id="{{detailClothes._id}}">编辑</button>
      </view>
    </view>
  </view>
</view>

<!-- 编辑衣物弹窗 -->
<view class="edit-modal-overlay" wx:if="{{showEditModal}}">
  <view class="edit-modal-content" catchtap="preventBubble">
    <view class="edit-modal-header">
      <text class="edit-modal-title">编辑衣物</text>
      <view class="close-btn" bindtap="hideEditClothingModal">×</view>
    </view>
    <view class="edit-modal-body">
      <!-- 基本信息 -->
      <view class="edit-section">
        <view class="edit-row">
          <view class="edit-label">名称</view>
          <input class="edit-input" type="text" value="{{editingClothing.name}}"
                 bindchange="onNameChange"
                 placeholder="请输入衣物名称"
                 bindtap="handleIOSFocus"
                 data-field="editingClothing.name"
                 focus="{{editingClothing.nameFocus}}"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">类别</view>
          <view class="edit-value" bindtap="showCategoryPicker">
            {{editingClothing.category || '请选择类别'}}
            <text class="arrow-right">▶</text>
          </view>
        </view>
        <view class="edit-row">
          <view class="edit-label">细分类</view>

            <input class="edit-input" type="text" value="{{editingClothing.type_detail}}"
                   bindchange="onTypeDetailChange"
                   bindfocus="showSuggestions"
                   data-field="editingClothing.type_detail"
                   placeholder="请输入细分类，如：长袖、短袖等"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.type_detailFocus}}"/>
            <!-- 历史值气泡 -->
            <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.type_detail.length > 0 && currentSuggestionField === 'type_detail'}}">
              <scroll-view scroll-x="true" class="bubbles-scroll">
                <view class="bubble-container">
                  <view class="suggestion-bubble" wx:for="{{suggestions.type_detail}}" wx:key="*this" bindtap="selectSuggestion" data-field="type_detail" data-value="{{item}}">
                    {{item}}
                  </view>
                </view>
              </scroll-view>
            </view>

        </view>
        <view class="edit-row">
          <view class="edit-label">颜色</view>

            <input class="edit-input" type="text" value="{{editingClothing.color}}"
                   bindchange="onColorChange"
                   bindfocus="showSuggestions"
                   data-field="editingClothing.color"
                   placeholder="请输入颜色"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.colorFocus}}"/>
            <!-- 历史值气泡 -->
            <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.color.length > 0 && currentSuggestionField === 'color'}}">
              <scroll-view scroll-x="true" class="bubbles-scroll">
                <view class="bubble-container">
                  <view class="suggestion-bubble" wx:for="{{suggestions.color}}" wx:key="*this" bindtap="selectSuggestion" data-field="color" data-value="{{item}}">
                    {{item}}
                  </view>
                </view>
              </scroll-view>
            </view>

        </view>
        <view class="edit-row">
          <view class="edit-label">风格</view>

            <input class="edit-input" type="text" value="{{editingClothing.style}}"
                   bindchange="onStyleChange"
                   bindfocus="showSuggestions"
                   data-field="editingClothing.style"
                   placeholder="请输入风格"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.styleFocus}}"/>
            <!-- 历史值气泡 -->
            <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.style.length > 0 && currentSuggestionField === 'style'}}">
              <scroll-view scroll-x="true" class="bubbles-scroll">
                <view class="bubble-container">
                  <view class="suggestion-bubble" wx:for="{{suggestions.style}}" wx:key="*this" bindtap="selectSuggestion" data-field="style" data-value="{{item}}">
                    {{item}}
                  </view>
                </view>
              </scroll-view>
            </view>

        </view>
        <view class="edit-row">
          <view class="edit-label">尺码</view>

            <input class="edit-input" type="text" value="{{editingClothing.size}}"
                   bindchange="onSizeChange"
                   bindfocus="showSuggestions"
                   data-field="editingClothing.size"
                   placeholder="请输入尺码，如：S、M、L、XL等"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.sizeFocus}}"/>
            <!-- 历史值气泡 -->
            <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.size.length > 0 && currentSuggestionField === 'size'}}">
              <scroll-view scroll-x="true" class="bubbles-scroll">
                <view class="bubble-container">
                  <view class="suggestion-bubble" wx:for="{{suggestions.size}}" wx:key="*this" bindtap="selectSuggestion" data-field="size" data-value="{{item}}">
                    {{item}}
                  </view>
                </view>
              </scroll-view>
            </view>

        </view>
        <view class="edit-row">
          <view class="edit-label">品牌</view>

            <input class="edit-input" type="text" value="{{editingClothing.brand}}"
                   bindchange="onBrandChange"
                   bindfocus="showSuggestions"
                   data-field="editingClothing.brand"
                   placeholder="请输入品牌名称"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.brandFocus}}"/>
            <!-- 历史值气泡 -->
            <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.brand.length > 0 && currentSuggestionField === 'brand'}}">
              <scroll-view scroll-x="true" class="bubbles-scroll">
                <view class="bubble-container">
                  <view class="suggestion-bubble" wx:for="{{suggestions.brand}}" wx:key="*this" bindtap="selectSuggestion" data-field="brand" data-value="{{item}}">
                    {{item}}
                  </view>
                </view>
              </scroll-view>
            </view>

        </view>
        <view class="edit-row">
          <view class="edit-label">购买渠道</view>

            <input class="edit-input" type="text" value="{{editingClothing.purchaseChannel}}"
                   bindchange="onPurchaseChannelChange"
                   bindfocus="showSuggestions"
                   data-field="editingClothing.purchaseChannel"
                   placeholder="请输入购买渠道，如：淘宝、线下店等"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.purchaseChannelFocus}}"/>
            <!-- 历史值气泡 -->
            <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.purchaseChannel.length > 0 && currentSuggestionField === 'purchaseChannel'}}">
              <scroll-view scroll-x="true" class="bubbles-scroll">
                <view class="bubble-container">
                  <view class="suggestion-bubble" wx:for="{{suggestions.purchaseChannel}}" wx:key="*this" bindtap="selectSuggestion" data-field="purchaseChannel" data-value="{{item}}">
                    {{item}}
                  </view>
                </view>
              </scroll-view>
            </view>

        </view>
        <view class="edit-row">
          <view class="edit-label">存储位置</view>

            <input class="edit-input" type="text" value="{{editingClothing.storageLocation}}"
                   bindchange="onStorageLocationChange"
                   bindfocus="showSuggestions"
                   data-field="editingClothing.storageLocation"
                   placeholder="请输入存储位置，如：卧室衣柜等"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.storageLocationFocus}}"/>
            <!-- 历史值气泡 -->
            <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.storageLocation.length > 0 && currentSuggestionField === 'storageLocation'}}">
              <scroll-view scroll-x="true" class="bubbles-scroll">
                <view class="bubble-container">
                  <view class="suggestion-bubble" wx:for="{{suggestions.storageLocation}}" wx:key="*this" bindtap="selectSuggestion" data-field="storageLocation" data-value="{{item}}">
                    {{item}}
                  </view>
                </view>
              </scroll-view>
            </view>

        </view>
        <view class="edit-row">
          <view class="edit-label">材质</view>

            <input class="edit-input" type="text" value="{{editingClothing.material}}"
                   bindchange="onMaterialChange"
                   bindfocus="showSuggestions"
                   data-field="editingClothing.material"
                   placeholder="请输入材质，如：棉、麻、丝等"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.materialFocus}}"/>
            <!-- 历史值气泡 -->
            <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.material.length > 0 && currentSuggestionField === 'material'}}">
              <scroll-view scroll-x="true" class="bubbles-scroll">
                <view class="bubble-container">
                  <view class="suggestion-bubble" wx:for="{{suggestions.material}}" wx:key="*this" bindtap="selectSuggestion" data-field="material" data-value="{{item}}">
                    {{item}}
                  </view>
                </view>
              </scroll-view>
            </view>

        </view>
        <view class="edit-row">
          <view class="edit-label">价格</view>
          <input class="edit-input" type="digit" value="{{editingClothing.price}}"
                 bindchange="onPriceChange"
                 placeholder="请输入价格"
                 bindtap="handleIOSFocus"
                 data-field="editingClothing.price"
                 focus="{{editingClothing.priceFocus}}"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">穿着次数</view>
          <input class="edit-input" type="number" value="{{editingClothing.wornCount}}"
                 bindchange="onWornCountChange"
                 placeholder="请输入穿着次数"
                 bindtap="handleIOSFocus"
                 data-field="editingClothing.wornCount"
                 focus="{{editingClothing.wornCountFocus}}"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">上次穿着</view>
          <view class="edit-value" bindtap="showLastWornDatePicker">
            {{editingClothing.lastWornDate || '未记录'}}
            <text class="arrow-right">▶</text>
          </view>
        </view>
        <view class="edit-row">
          <view class="edit-label">购买时间</view>
          <view class="edit-value" bindtap="showPurchaseDatePicker">
            {{editingClothing.purchaseDate || '未记录'}}
            <text class="arrow-right">▶</text>
          </view>
        </view>
        <view class="edit-row">
          <view class="edit-label">季节</view>
          <view class="edit-value" bindtap="showSeasonPicker">
            {{editingClothing.season || '请选择季节'}}
            <text class="arrow-right">▶</text>
          </view>
        </view>
        <!-- 添加所属衣柜选择 -->
        <view class="edit-row">
          <view class="edit-label">所属衣柜</view>
          <view class="edit-value" bindtap="showWardrobePicker">
            {{editingClothing.wardrobeName || '请选择衣柜'}}
            <text class="arrow-right">▶</text>
          </view>
        </view>
        <view class="edit-row">
          <view class="edit-label">断舍离标记</view>
          <switch checked="{{editingClothing.wantToDiscard}}" bindchange="onWantToDiscardChange"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">备注</view>
          <input class="edit-textarea" value="{{editingClothing.remark}}"
                 bindchange="onRemarkChange"
                 placeholder="请输入备注信息，如洗涤注意事项、搭配建议等"
                 maxlength="200"
                 auto-height="true"
                 bindtap="handleIOSFocus"
                 data-field="editingClothing.remark"
                 focus="{{editingClothing.remarkFocus}}" />
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="edit-actions">
        <button class="action-btn delete" bindtap="deleteCurrentClothing">删除</button>
        <button class="action-btn save" bindtap="saveClothingEdit">保存</button>
      </view>
    </view>
  </view>
</view>

<!-- 类别选择器弹窗 -->
<view class="category-picker-overlay" wx:if="{{showCategoryPicker}}">
  <view class="category-picker-content" catchtap="preventBubble">
    <view class="category-picker-header">
      <text class="category-picker-title">选择类别</text>
      <view class="close-btn" bindtap="hideCategoryPicker">×</view>
    </view>
    <view class="category-options">
      <!-- 动态显示所有用户类别 -->
      <block wx:for="{{userCategories}}" wx:key="*this">
        <view class="category-option {{tempSelectedCategory === item ? 'selected' : ''}}"
              bindtap="selectEditCategoryForIOS" data-category="{{item}}">
          <text>{{item}}</text>
        </view>
      </block>
      <view class="category-option {{tempSelectedCategory === '自定义' ? 'selected' : ''}}"
            bindtap="selectEditCategoryForIOS" data-category="自定义">
          <text>自定义</text>
        </view>
    </view>

    <!-- 自定义类别输入框 -->
    <view class="custom-category-input" wx:if="{{tempSelectedCategory === '自定义'}}">
      <input class="edit-input" type="text" value="{{customCategoryValue}}"
             bindchange="onCustomCategoryInput"
             placeholder="请输入自定义类别"
             bindtap="handleIOSFocus"
             data-field="customCategory"
             focus="{{customCategoryFocus}}"/>
    </view>

    <view class="category-picker-actions">
      <button class="action-btn cancel" bindtap="hideCategoryPicker">取消</button>
      <button class="action-btn confirm" bindtap="confirmCategoryPicker">确认</button>
    </view>
  </view>
</view>

<!-- 日期选择器弹窗 -->
<view class="date-picker-overlay" wx:if="{{showDatePicker}}">
  <view class="date-picker-content" catchtap="preventBubble">
    <view class="date-picker-header">
      <text class="date-picker-title">{{datePickerTitle}}</text>
      <view class="close-btn" bindtap="hideDatePicker">×</view>
    </view>
    <view class="date-picker-body">
      <picker mode="date" value="{{datePickerValue}}" bindchange="onDatePickerChange">
        <view class="date-picker-current">{{datePickerValue || '请选择日期'}}</view>
      </picker>
    </view>
    <view class="date-picker-actions">
      <button class="action-btn cancel" bindtap="hideDatePicker">取消</button>
      <button class="action-btn confirm" bindtap="confirmDatePicker">确认</button>
    </view>
  </view>
</view>

<!-- 季节选择器弹窗 -->
<view class="season-picker-overlay" wx:if="{{showSeasonPicker}}">
  <view class="season-picker-content" catchtap="preventBubble">
    <view class="season-picker-header">
      <text class="season-picker-title">选择季节</text>
      <view class="close-btn" bindtap="hideSeasonPicker">×</view>
    </view>
    <view class="season-options">
      <view class="season-option {{seasonIsSelected['春季'] ? 'selected' : ''}}"
            bindtap="selectSeason" data-season="春季">
        <icon type="{{seasonIsSelected['春季'] ? 'success' : 'circle'}}" size="20" color="{{seasonIsSelected['春季'] ? '#07c160' : '#999'}}"></icon>
        <text>春季</text>
      </view>
      <view class="season-option {{seasonIsSelected['夏季'] ? 'selected' : ''}}"
            bindtap="selectSeason" data-season="夏季">
        <icon type="{{seasonIsSelected['夏季'] ? 'success' : 'circle'}}" size="20" color="{{seasonIsSelected['夏季'] ? '#07c160' : '#999'}}"></icon>
        <text>夏季</text>
      </view>
      <view class="season-option {{seasonIsSelected['秋季'] ? 'selected' : ''}}"
            bindtap="selectSeason" data-season="秋季">
        <icon type="{{seasonIsSelected['秋季'] ? 'success' : 'circle'}}" size="20" color="{{seasonIsSelected['秋季'] ? '#07c160' : '#999'}}"></icon>
        <text>秋季</text>
      </view>
      <view class="season-option {{seasonIsSelected['冬季'] ? 'selected' : ''}}"
            bindtap="selectSeason" data-season="冬季">
        <icon type="{{seasonIsSelected['冬季'] ? 'success' : 'circle'}}" size="20" color="{{seasonIsSelected['冬季'] ? '#07c160' : '#999'}}"></icon>
        <text>冬季</text>
      </view>
    </view>
    <view class="season-picker-actions">
      <button class="action-btn cancel" bindtap="hideSeasonPicker">取消</button>
      <button class="action-btn confirm" bindtap="confirmSeasonPicker">确认</button>
    </view>
  </view>
</view>

<!-- 衣柜选择器弹窗 -->
<view class="season-picker-overlay" wx:if="{{showWardrobePicker}}">
  <view class="season-picker-content" catchtap="preventBubble">
    <view class="season-picker-header">
      <text class="season-picker-title">选择衣柜</text>
      <view class="close-btn" bindtap="hideWardrobePicker">×</view>
    </view>
    <scroll-view scroll-y="true" class="wardrobe-options-scroll" catchtouchmove="preventBubble">
      <view class="season-options">
        <block wx:for="{{wardrobes}}" wx:key="_id">
          <view class="season-option {{wardrobeIsSelected[item._id] ? 'selected' : ''}}"
                bindtap="selectWardrobe" data-wardrobeid="{{item._id}}">
            <icon type="{{wardrobeIsSelected[item._id] ? 'success' : 'circle'}}" size="20" color="{{wardrobeIsSelected[item._id] ? '#07c160' : '#999'}}"></icon>
            <text>{{item.name}}</text>
          </view>
        </block>
      </view>
    </scroll-view>
    <view class="season-picker-actions">
      <button class="action-btn cancel" bindtap="hideWardrobePicker">取消</button>
      <button class="action-btn confirm" bindtap="confirmWardrobePicker">确认</button>
    </view>
  </view>
</view>