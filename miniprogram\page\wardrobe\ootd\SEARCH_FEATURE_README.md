# OOTD页面搜索功能说明

## 功能概述

在OOTD页面的选择穿搭功能中新增了搜索功能，支持搜索搭配和单品的全部信息。

## 功能特点

### 1. 智能显示
- 搜索框仅在选择"穿搭"或"单件衣物"时显示
- 选择"上传图片"时隐藏搜索框

### 2. 实时搜索
- 支持实时搜索，输入关键词即时显示结果
- 不区分大小写的模糊匹配

### 3. 全面搜索范围

#### 穿搭搜索支持：
- 穿搭名称
- 穿搭类型（日常、职业、派对、运动、季节）
- 穿搭分类
- 穿搭描述
- 穿搭标签
- 穿搭中包含的衣物信息（名称、分类、品牌、颜色等）

#### 单品搜索支持：
- 衣物名称
- 衣物分类
- 衣物品牌
- 衣物颜色
- 衣物材质
- 衣物尺寸
- 衣物季节
- 衣物风格
- 衣物描述
- 衣物标签
- 衣物购买地点
- 衣物价格

### 4. 智能过滤
- 搜索结果会结合当前选择的类型/分类进行二次过滤
- 切换类型/分类时会自动重新应用搜索条件

### 5. 用户体验
- 清晰的搜索提示文字
- 一键清空搜索功能
- 搜索框聚焦时的视觉反馈
- 符合应用主题的样式设计

## 技术实现

### 数据结构
- `searchKeyword`: 当前搜索关键词
- `showSearchBox`: 是否显示搜索框
- `originalOutfits`: 原始穿搭数据（用于搜索）
- `originalClothes`: 原始衣物数据（用于搜索）

### 核心方法
- `onSearchInput()`: 处理搜索输入
- `clearSearch()`: 清空搜索
- `performSearch()`: 执行搜索
- `searchOutfits()`: 搜索穿搭
- `searchClothes()`: 搜索单品

### 样式特点
- 圆角搜索框设计
- 搜索图标和清除按钮
- 聚焦时的动画效果
- 适配秋季和粉蓝主题

## 使用方法

1. 在OOTD页面选择"穿搭"或"单件衣物"
2. 在搜索框中输入关键词
3. 系统会实时显示匹配的结果
4. 点击"×"按钮可清空搜索
5. 搜索结果会结合当前选择的类型/分类显示

## 注意事项

- 搜索功能会保持原有的类型/分类过滤逻辑
- 切换主类型时会自动清空搜索关键词
- 搜索结果为空时会显示相应的空状态提示
