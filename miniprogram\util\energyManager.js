/**
 * 能量管理模块
 * 提供用于管理用户体力值的工具函数
 */

// 体力值上限
const MAX_ENERGY = 500;
// 体力值下限
const MIN_ENERGY = 0;
// 默认体力值
const DEFAULT_ENERGY = 50;

// 各功能消耗的体力值
const ENERGY_COST = {
  IMAGE_EXTRACTION: 2,  // AI抠图一件衣服
  OUTFIT_CREATION: 5,   // AI创建搭配
  WARDROBE_ANALYSIS: 10,  // AI分析衣柜
  OUTFIT_SCORING: 2  // AI穿搭评分
};

/**
 * 减少用户体力值
 * @param {Number} amount - 要减少的体力值数量
 * @param {String} [userOpenId] - 用户OpenID，如果不提供则从本地存储获取
 * @returns {Promise<Object>} 包含更新后体力值的Promise
 */
function decreaseEnergy(amount, userOpenId) {
  // 验证参数
  if (amount === undefined || amount <= 0) {
    return Promise.reject(new Error('减少的体力值必须大于0'));
  }

  // 获取用户OpenID
  const openId = userOpenId || wx.getStorageSync('userOpenId');
  if (!openId) {
    return Promise.reject(new Error('获取用户openid失败'));
  }

  // 获取当前体力值
  return getCurrentEnergy(openId)
    .then(currentEnergy => {
      // 计算新的体力值，确保不小于0
      const newEnergy = Math.max(MIN_ENERGY, currentEnergy - amount);

      // 更新体力值
      return updateEnergy(newEnergy, openId);
    });
}

/**
 * 增加用户体力值
 * @param {Number} amount - 要增加的体力值数量
 * @param {String} [userOpenId] - 用户OpenID，如果不提供则从本地存储获取
 * @returns {Promise<Object>} 包含更新后体力值的Promise
 */
function increaseEnergy(amount, userOpenId) {
  // 验证参数
  if (amount === undefined || amount <= 0) {
    return Promise.reject(new Error('增加的体力值必须大于0'));
  }

  // 获取用户OpenID
  const openId = userOpenId || wx.getStorageSync('userOpenId');
  if (!openId) {
    return Promise.reject(new Error('获取用户openid失败'));
  }

  // 获取当前体力值
  return getCurrentEnergy(openId)
    .then(currentEnergy => {
      // 计算新的体力值，确保不超过最大值
      const newEnergy = Math.min(MAX_ENERGY, currentEnergy + amount);

      // 更新体力值
      return updateEnergy(newEnergy, openId);
    });
}

/**
 * 获取当前用户体力值
 * @param {String} userOpenId - 用户OpenID
 * @returns {Promise<Number>} 包含当前体力值的Promise
 */
function getCurrentEnergy(userOpenId) {
  return new Promise((resolve, reject) => {
    // 尝试从本地缓存获取
    const cacheKey = 'userInfoCache';
    const cachedData = wx.getStorageSync(cacheKey) || {};

    if (cachedData.catEnergy !== undefined) {
      resolve(cachedData.catEnergy);
      return;
    }

    // 如果缓存中没有，则从数据库获取
    const db = wx.cloud.database();
    db.collection('users')
      .where({ _openid: userOpenId })
      .field({ catEnergy: true })
      .get()
      .then(res => {
        if (res.data && res.data.length > 0) {
          const energy = res.data[0].catEnergy !== undefined ? res.data[0].catEnergy : DEFAULT_ENERGY;
          resolve(energy);
        } else {
          // 如果没有找到用户记录，返回默认值
          resolve(DEFAULT_ENERGY);
        }
      })
      .catch(err => {
        console.error('获取用户体力值失败:', err);
        // 出错时返回默认值
        resolve(DEFAULT_ENERGY);
      });
  });
}

/**
 * 更新用户体力值
 * @param {Number} energy - 新的体力值
 * @param {String} userOpenId - 用户OpenID
 * @returns {Promise<Object>} 包含更新结果的Promise
 */
function updateEnergy(energy, userOpenId) {
  return new Promise((resolve, reject) => {
    // 确保体力值在有效范围内
    const validEnergy = Math.max(MIN_ENERGY, Math.min(MAX_ENERGY, energy));

    // 调用云函数更新体力值
    wx.cloud.callFunction({
      name: 'updateUserEnergy',
      data: {
        openid: userOpenId,
        catEnergy: validEnergy
      }
    })
    .then(res => {
      console.log('体力值更新成功:', res);

      // 更新本地缓存
      const cacheKey = 'userInfoCache';
      const cachedData = wx.getStorageSync(cacheKey) || {};
      cachedData.catEnergy = validEnergy;
      wx.setStorageSync(cacheKey, cachedData);

      // 返回更新后的体力值和结果
      resolve({
        success: true,
        catEnergy: validEnergy,
        result: res.result
      });

      // 触发页面刷新（如果在当前页面）
      const currentPages = getCurrentPages();
      const currentPage = currentPages[currentPages.length - 1];
      if (currentPage && currentPage.route.includes('settings')) {
        currentPage.setData({
          'userInfo.catEnergy': validEnergy
        });
      }
    })
    .catch(err => {
      console.error('更新体力值失败:', err);
      reject(err);
    });
  });
}

/**
 * 检查用户是否有足够的体力值
 * @param {String} actionType - 动作类型，可以是 'IMAGE_EXTRACTION', 'OUTFIT_CREATION', 或 'WARDROBE_ANALYSIS'
 * @param {String} [userOpenId] - 用户OpenID，如果不提供则从本地存储获取
 * @returns {Promise<Boolean>} 包含检查结果的Promise
 */
function hasEnoughEnergy(actionType, userOpenId) {
  // 获取动作需要的体力值
  const requiredEnergy = ENERGY_COST[actionType];
  if (requiredEnergy === undefined) {
    return Promise.reject(new Error('未知的动作类型'));
  }

  // 获取用户OpenID
  const openId = userOpenId || wx.getStorageSync('userOpenId');
  if (!openId) {
    return Promise.reject(new Error('获取用户openid失败'));
  }

  // 获取当前体力值并检查
  return getCurrentEnergy(openId)
    .then(currentEnergy => {
      return currentEnergy >= requiredEnergy;
    });
}

/**
 * 消耗体力值执行操作
 * @param {String} actionType - 动作类型，可以是 'IMAGE_EXTRACTION', 'OUTFIT_CREATION', 或 'WARDROBE_ANALYSIS'
 * @param {String} [userOpenId] - 用户OpenID，如果不提供则从本地存储获取
 * @returns {Promise<Object>} 包含操作结果的Promise
 */
function consumeEnergyForAction(actionType, userOpenId) {
  // 获取动作需要的体力值
  const requiredEnergy = ENERGY_COST[actionType];
  if (requiredEnergy === undefined) {
    return Promise.reject(new Error('未知的动作类型'));
  }

  // 获取用户OpenID
  const openId = userOpenId || wx.getStorageSync('userOpenId');
  if (!openId) {
    return Promise.reject(new Error('获取用户openid失败'));
  }

  // 检查并消耗体力值
  return hasEnoughEnergy(actionType, openId)
    .then(hasEnough => {
      if (!hasEnough) {
        return {
          success: false,
          message: '体力值不足',
          requiredEnergy: requiredEnergy
        };
      }

      // 消耗体力值
      return decreaseEnergy(requiredEnergy, openId)
        .then(result => {
          return {
            success: true,
            message: '操作成功',
            consumedEnergy: requiredEnergy,
            remainingEnergy: result.catEnergy
          };
        });
    });
}

// 导出模块
module.exports = {
  decreaseEnergy,
  increaseEnergy,
  getCurrentEnergy,
  hasEnoughEnergy,
  consumeEnergyForAction,
  ENERGY_COST,
  MAX_ENERGY,
  MIN_ENERGY,
  DEFAULT_ENERGY
};
