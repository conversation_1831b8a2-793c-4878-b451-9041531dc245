/* 主容器 */
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 加载状态 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
  background-color: rgba(255, 255, 255, 0.9);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid transparent;
  border-radius: 50%;
  border-top-color: #442D1C;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 30rpx;
  color: #442D1C;
}

/* 返回按钮 */
.back-button {
  position: fixed;
  top: 70rpx;
  left: 30rpx;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 70rpx;
  padding: 0 30rpx;
  border-radius: 35rpx;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-top: 4rpx solid #442D1C;
  border-left: 4rpx solid #442D1C;
  transform: rotate(-45deg);
  margin-right: 12rpx;
}

.back-button text {
  font-size: 28rpx;
}

/* 图标样式共用 */
.contents-icon-view {
  width: 32rpx;
  height: 32rpx;
  position: relative;
}

.contents-icon-view:before, 
.contents-icon-view:after,
.contents-icon-view::before, 
.contents-icon-view::after {
  content: '';
  position: absolute;
  left: 0;
  width: 100%;
  height: 3rpx;
  background-color: #442D1C;
  border-radius: 3rpx;
}

.contents-icon-view:before, .contents-icon-view::before {
  top: 6rpx;
}

.contents-icon-view:after, .contents-icon-view::after {
  top: 16rpx;
}

.contents-icon-view::after {
  top: auto;
  bottom: 6rpx;
}

.theme-pinkBlue .contents-icon-view:before,
.theme-pinkBlue .contents-icon-view:after,
.theme-pinkBlue .contents-icon-view::before,
.theme-pinkBlue .contents-icon-view::after {
  background-color: #D47C99;
}

.theme-blackWhite .contents-icon-view:before,
.theme-blackWhite .contents-icon-view:after,
.theme-blackWhite .contents-icon-view::before,
.theme-blackWhite .contents-icon-view::after {
  background-color: #000000;
}

/* 杂志内容区域 */
.magazine-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  padding: 110rpx 0 90rpx 0;
  box-sizing: border-box;
  overflow: hidden;
}

/* 杂志页面共通样式 */
.magazine-page {
  width: 94%;
  height: 94%;
  background-color: #ffffff;
  border-radius: 10rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

/* 书脊效果 */
.magazine-spine {
  position: absolute;
  left: -5rpx;
  top: 0;
  width: 20rpx;
  height: 100%;
  background: linear-gradient(to right, 
    rgba(0, 0, 0, 0.4), 
    rgba(0, 0, 0, 0.2) 40%, 
    rgba(0, 0, 0, 0.1) 60%, 
    transparent);
  border-radius: 5rpx 0 0 5rpx;
  z-index: 10;
}

/* 页码样式 */
.page-number {
  position: absolute;
  bottom: 15rpx;
  right: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #888;
  z-index: 10;
}

/* 封面页样式 */
.cover-page {
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
}

.magazine-cover {
  padding: 40rpx;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.magazine-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.magazine-name {
  font-size: 36rpx;
  font-weight: bold;
  letter-spacing: 2rpx;
  color: #333;
  text-transform: uppercase;
}

.magazine-issue {
  font-size: 24rpx;
  color: #888;
  margin-top: 10rpx;
}

.cover-image-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  border-radius: 8rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 40rpx;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, 
    rgba(0, 0, 0, 0) 30%, 
    rgba(0, 0, 0, 0.6) 80%);
}

.cover-title {
  position: absolute;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  display: flex;
  flex-direction: column;
  color: #ffffff;
}

.cover-subtitle {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.cover-heading {
  font-size: 60rpx;
  font-weight: bold;
  line-height: 1.2;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.cover-description {
  font-size: 32rpx;
  opacity: 0.9;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.magazine-info {
  display: flex;
  flex-direction: column;
}

.magazine-tagline {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  text-align: center;
}

.magazine-highlights {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.highlight-item {
  margin: 10rpx;
  padding: 10rpx 20rpx;
  background-color: #f3f3f3;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #555;
}

.page-nav-hint {
  position: absolute;
  bottom: 30rpx;
  right: 30rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #888;
  animation: pulse 2s infinite;
}

.swipe-icon {
  width: 20rpx;
  height: 20rpx;
  border-top: 2rpx solid #888;
  border-right: 2rpx solid #888;
  transform: rotate(45deg);
  margin-left: 10rpx;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* 目录页样式 */
.contents-page {
  padding: 60rpx 40rpx;
  box-sizing: border-box;
}

.contents-header {
  margin-bottom: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.contents-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.contents-issue {
  font-size: 28rpx;
  color: #888;
}

.contents-body {
  flex: 1;
  overflow: auto;
}

.contents-section {
  margin-bottom: 60rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-bottom: 15rpx;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100rpx;
  height: 6rpx;
  background-color: #442D1C;
  border-radius: 3rpx;
}

.theme-pinkBlue .section-title::after {
  background-color: #D47C99;
}

.theme-blackWhite .section-title::after {
  background-color: #000000;
}

.contents-items {
  display: flex;
  flex-direction: column;
}

.contents-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.item-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #442D1C;
  margin-right: 20rpx;
}

.theme-pinkBlue .item-number {
  color: #D47C99;
}

.theme-blackWhite .item-number {
  color: #000000;
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.item-description {
  font-size: 26rpx;
  color: #999;
  flex: 1;
  text-align: right;
}

.contents-footer {
  margin-top: 40rpx;
  text-align: center;
}

.footer-text {
  font-size: 24rpx;
  color: #999;
}

/* 类别页共通样式 */
.category-page {
  padding: 40rpx 25rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.category-header {
  margin-bottom: 20rpx;
}

.category-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.category-subtitle {
  font-size: 24rpx;
  color: #888;
  letter-spacing: 2rpx;
}

.category-content {
  flex: 1;
  overflow: auto;
}

.category-intro {
  margin-bottom: 15rpx;
}

.intro-text {
  font-size: 26rpx;
  line-height: 1.4;
  color: #666;
}

.featured-outfit {
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 85%;
}

.featured-image {
  width: 100%;
  height: 880rpx;  /* 大屏设备的基础高度 */
  object-fit: contain;
  background-color: #f8f8f8;
  padding: 10rpx 0;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  border-bottom: 1rpx solid #f0f0f0;  /* 添加一条浅色底边，提高图片与内容区分度 */
}

.featured-info {
  padding: 15rpx 20rpx;
  background-color: #fff;
}

.featured-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.featured-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.view-more-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #442D1C;
  color: #fff;
  font-size: 24rpx;
  border-radius: 8rpx;
  margin: 15rpx auto 5rpx auto;
  width: 55%;
  height: 50rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
}

.theme-pinkBlue .view-more-button {
  background-color: #D47C99;
}

.theme-blackWhite .view-more-button {
  background-color: #000000;
}

.view-more-button text {
  color: #fff;
  font-weight: 500;
}

/* 特别企划页样式 */
.features-page {
  padding: 60rpx 40rpx;
  box-sizing: border-box;
}

.features-header {
  margin-bottom: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.features-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.features-subtitle {
  font-size: 28rpx;
  color: #888;
  letter-spacing: 2rpx;
}

.features-content {
  flex: 1;
  overflow: auto;
}

.features-intro {
  margin-bottom: 40rpx;
}

.editor-picks {
  margin-bottom: 60rpx;
}

.editor-pick-item {
  display: flex;
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.pick-image {
  width: 240rpx;
  height: 240rpx;
  object-fit: cover;
}

.pick-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #fff;
}

.pick-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.pick-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.recommendations {
  margin-top: 40rpx;
}

.recommendations-header {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-bottom: 15rpx;
}

.recommendations-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100rpx;
  height: 6rpx;
  background-color: #442D1C;
  border-radius: 3rpx;
}

.theme-pinkBlue .recommendations-header::after {
  background-color: #D47C99;
}

.theme-blackWhite .recommendations-header::after {
  background-color: #000000;
}

.recommendations-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.recommendation-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.recommendation-image {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}

.recommendation-title {
  padding: 15rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
  background-color: #fff;
}

/* 空状态展示 */
.empty-state {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.create-button {
  padding: 15rpx 40rpx;
  background-color: #442D1C;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.theme-pinkBlue .create-button {
  background-color: #D47C99;
}

.theme-blackWhite .create-button {
  background-color: #000000;
}

.create-button text {
  color: #fff;
}

/* 侧边类别索引 */
.category-index {
  position: fixed;
  top: 0;
  right: -80%;
  width: 80%;
  height: 100vh;
  background-color: #fff;
  box-shadow: -10rpx 0 20rpx rgba(0, 0, 0, 0.1);
  z-index: 200;
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
}

.category-index.show {
  right: 0;
}

.index-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60rpx 40rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.index-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.index-list {
  flex: 1;
  overflow: auto;
  padding: 20rpx 0;
}

.index-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: center;
}

.close-button {
  width: 100%;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #fff;
  background-color: #442D1C;
  border-radius: 40rpx;
}

.theme-pinkBlue .close-button {
  background-color: #D47C99;
}

.theme-blackWhite .close-button {
  background-color: #000000;
}

.index-item {
  padding: 30rpx 40rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
}

.index-item.active {
  background-color: #f9f9f9;
  border-left: 8rpx solid #442D1C;
}

.theme-pinkBlue .index-item.active {
  border-left-color: #D47C99;
}

.theme-blackWhite .index-item.active {
  border-left-color: #000000;
}

.item-text {
  font-size: 32rpx;
  color: #333;
}

/* 添加底部目录按钮样式 */
.contents-button-bottom {
  position: fixed;
  bottom: 40rpx;  /* 将按钮位置上移 */
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  height: 65rpx;  /* 略微增加高度提高可点击区域 */
  width: 165rpx;  /* 略微增加宽度 */
  padding: 0 15rpx;
  border-radius: 32rpx;
  background-color: rgba(255, 255, 255, 0.95);  /* 增加不透明度 */
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.25);  /* 增强阴影效果 */
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
  border: 1rpx solid rgba(200, 200, 200, 0.5);  /* 添加细边框增强视觉边界 */
}

.contents-button-bottom:active {
  transform: translateX(-50%) scale(0.95);
  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.2);
}

.contents-button-bottom .contents-icon-view {
  width: 22rpx;  /* 略微增加图标大小 */
  height: 22rpx;
  margin-right: 8rpx;
}

.contents-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-left: 4rpx;
}

/* 根据主题设置颜色 */
.theme-autumn .contents-button-bottom {
  background-color: rgba(232, 209, 167, 0.98);
  border: 1rpx solid rgba(68, 45, 28, 0.4);
}

.theme-autumn .contents-text {
  color: #442D1C;
}

.theme-pinkBlue .contents-button-bottom {
  background-color: rgba(249, 201, 214, 0.98);
  border: 1rpx solid rgba(212, 124, 153, 0.4);
}

.theme-pinkBlue .contents-text {
  color: #D47C99;
}

.theme-blackWhite .contents-button-bottom {
  background-color: rgba(255, 255, 255, 0.98);
  border: 1rpx solid rgba(0, 0, 0, 0.4);
}

.theme-blackWhite .contents-text {
  color: #000000;
} 