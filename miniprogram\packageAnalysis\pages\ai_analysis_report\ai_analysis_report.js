const app = getApp();

Page({
  data: {
    statusBarHeight: 20, // 默认值，会在onLoad中获取真实值
    aiAnalysisResult: null
  },
  
  onLoad: function (options) {
    // 设置状态栏高度
    this.setData({
      statusBarHeight: app.globalData.statusBarHeight
    });
    
    // 如果有分析结果，从上一页面获取
    if (options.from === 'clothesAnalysis') {
      const eventChannel = this.getOpenerEventChannel();
      eventChannel.on('passAnalysisData', (data) => {
        this.setData({
          aiAnalysisResult: data.aiAnalysisResult
        });
      });
    }
  },
  
  // 返回上一页
  navigateBack: function () {
    wx.navigateBack({
      delta: 1
    });
  }
}); 