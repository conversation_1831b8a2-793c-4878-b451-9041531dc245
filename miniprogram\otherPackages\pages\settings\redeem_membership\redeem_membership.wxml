<view class="container">
  <view class="header">
    <view class="title">会员兑换</view>
    <view class="subtitle">输入会员兑换码获取VIP会员权益</view>
  </view>
  
  <view class="redeem-card">
    <view class="card-title">会员兑换码</view>
    
    <view class="input-container">
      <input class="redeem-input" placeholder="请输入会员兑换码" value="{{redeemCode}}" bindinput="onInputChange" />
      <view class="redeem-btn {{redeemCode ? 'active' : ''}}" bindtap="redeemCode">
        {{isRedeeming ? '兑换中...' : '兑换'}}
      </view>
    </view>
    
    <view class="tips">
      <view class="tip-item">• 兑换码区分大小写，请准确输入</view>
      <view class="tip-item">• 会员兑换码可兑换月度、季度或年度会员</view>
      <view class="tip-item">• 如果您已是VIP会员，兑换后会在现有会员期限上增加相应天数</view>
    </view>
  </view>
  
  <!-- 兑换结果弹窗 -->
  <view class="result-modal" wx:if="{{showResult}}">
    <view class="result-mask" bindtap="hideResult"></view>
    <view class="result-content">
      <view class="result-icon {{redeemResult.success ? 'success' : 'fail'}}">
        <text>{{redeemResult.success ? '✓' : '✗'}}</text>
      </view>
      
      <view class="result-title">{{redeemResult.success ? '兑换成功' : '兑换失败'}}</view>
      <view class="result-message">{{redeemResult.message}}</view>
      
      <block wx:if="{{redeemResult.success}}">
        <view class="result-detail">
          <view class="detail-item">
            <view class="detail-label">会员类型:</view>
            <view class="detail-value">
              {{redeemResult.membershipType === 'monthly' ? '月度会员' : 
                (redeemResult.membershipType === 'quarterly' ? '季度会员' : '年度会员')}}
            </view>
          </view>
          
          <view class="detail-item">
            <view class="detail-label">会员天数:</view>
            <view class="detail-value">{{redeemResult.membershipDays}}天</view>
          </view>
          
          <view class="detail-item">
            <view class="detail-label">到期时间:</view>
            <view class="detail-value">{{formatDate(redeemResult.expireDate)}}</view>
          </view>
        </view>
      </block>
      
      <view class="result-btn" bindtap="hideResult">确定</view>
    </view>
  </view>
  
  <!-- 会员权益展示 -->
  <view class="benefits-section">
    <view class="section-title">会员特权</view>
    
    <view class="benefit-list">
      <view class="benefit-item">
        <view class="benefit-icon">💾</view>
        <view class="benefit-content">
          <view class="benefit-title">批量上传</view>
          <view class="benefit-desc">一键批量上传衣物照片</view>
        </view>
      </view>
      
      <view class="benefit-item">
        <view class="benefit-icon">⚡</view>
        <view class="benefit-content">
          <view class="benefit-title">每月赠送500体力值</view>
          <view class="benefit-desc">使用AI功能无需担心体力不足</view>
        </view>
      </view>
      
      <view class="benefit-item">
        <view class="benefit-icon">💰</view>
        <view class="benefit-content">
          <view class="benefit-title">特殊衣物箱</view>
          <view class="benefit-desc">换季箱、断舍离箱、代办物品箱</view>
        </view>
      </view>
      
      <view class="benefit-item">
        <view class="benefit-icon">👗</view>
        <view class="benefit-content">
          <view class="benefit-title">无限搭配</view>
          <view class="benefit-desc">创建无限数量的搭配组合</view>
        </view>
      </view>
      
      <view class="benefit-item">
        <view class="benefit-icon">👟</view>
        <view class="benefit-content">
          <view class="benefit-title">无限衣柜</view>
          <view class="benefit-desc">可添加无限数量衣柜</view>
        </view>
      </view>
    </view>
  </view>
</view>
