const colors = require('../../../../util/colors');

// 导入模块
const userManager = require('./modules/userManager');
const clothesManager = require('./modules/clothesManager');
const imageManager = require('./modules/imageManager');
const canvasManager = require('./modules/canvasManager');
const outfitManager = require('./modules/outfitManager');
// 导入本地图片缓存模块
const localImageCache = require('../../outfit/modules/localImageCache');

Page({
  data: {
    // 风格切换设置
    themeStyle: 'autumn', // 默认为秋季风格，可选值：'autumn'或'pinkBlue'

    // 使用全局颜色配置
    colors: {
      darkBrown: "#442D1C",     // 深棕色 Cowhide Cocoa
      spicedWine: "#74301C",    // 红棕色 Spiced Wine
      toastedCaramel: "#84592B", // 焦糖色 Toasted Caramel
      oliveHarvest: "#9D9167",   // 橄榄色 Olive Harvest
      goldenBatter: "#E8D1A7",   // 金黄色 Golden Batter
    },

    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',      // 深粉色
      pinkMedium: '#EEA0B2',    // 中粉色
      pinkLight: '#F9C9D6',     // 浅粉色
      blueLight: '#CBE0F9',     // 浅蓝色
      blueMedium: '#97C8E5',    // 中蓝色
      blueDark: '#5EA0D0',      // 深蓝色
    },

    // 衣物数据
    clothes: [],                // 所有衣物
    filteredClothes: [],        // 按类别筛选后的衣物
    currentCategory: null,      // 当前选中的类别
    userOpenId: '',             // 用户openid

    // 画布相关
    canvasItems: [],            // 画布上的衣物项
    canvasWidth: 600,           // 画布宽度
    canvasHeight: 800,          // 画布高度
    activeItemId: null,         // 当前选中的画布项目ID
    activeItemLayer: 0,         // 当前选中项目的图层

    // 触摸状态
    touchStartX: 0,             // 触摸开始的X坐标
    touchStartY: 0,             // 触摸开始的Y坐标
    itemStartX: 0,              // 项目触摸开始时的X位置
    itemStartY: 0,              // 项目触摸开始时的Y位置
    isMoving: false,            // 是否正在移动

    // 手势相关参数
    fingerDistance: 0,         // 两指之间的距离
    initialFingerDistance: 0,  // 初始两指之间的距离
    initialItemWidth: 0,       // 初始项目宽度
    initialItemHeight: 0,      // 初始项目高度
    initialRotation: 0,        // 初始旋转角度
    previousAngle: 0,          // 前一次触摸的角度
    isScaling: false,          // 是否正在缩放
    isRotating: false,         // 是否正在旋转
    isSingleTouch: true,       // 是否单指触摸

    nextId: 1,                  // 下一个画布项的ID

    // 页面状态
    isLoading: true,            // 是否正在加载
    isSaving: false,            // 是否正在保存
    outfitId: '',               // 搭配ID
    outfitName: "",             // 搭配名称
    hasCustomName: true,        // 用户是否自定义了名称
    statusBarHeight: 20,        // 状态栏高度，默认值

    // 穿搭类型选择
    outfitCategory: 'daily',    // 默认为日常穿搭
    outfitCategoryOptions: [    // 穿搭类型选项
      { value: 'daily', name: '日常穿搭', icon: '👕' },
      { value: 'work', name: '职业穿搭', icon: '👔' },
      { value: 'party', name: '派对穿搭', icon: '👗' },
      { value: 'sport', name: '运动穿搭', icon: '🏃' },
      { value: 'seasonal', name: '季节穿搭', icon: '🍂' }
    ],
    showCategoryPicker: false,  // 是否显示类型选择器
    currentCategoryIcon: '👕',   // 当前选中的类型图标
    currentCategoryName: '日常穿搭', // 当前选中的类型名称

    // 定义衣物类别 - 初始只有全部类别，其他类别将动态加载
    categories: [
      { id: 0, name: '全部', icon: '全', count: 0 }
    ],

    // 左侧面板相关
    closetPanelWidth: 0,         // 左侧面板宽度
    clothesScrollHeight: 300,    // 衣物滚动区域高度
  },

  onLoad: function(options) {
    console.log('outfit_edit 页面 onLoad 开始', options);

    // 获取状态栏高度并设置到页面样式变量
    wx.getSystemInfo({
      success: (res) => {
        const statusBarHeight = res.statusBarHeight;

        // 将状态栏高度保存到全局数据
        const app = getApp();
        if (app.globalData) {
          app.globalData.statusBarHeight = statusBarHeight;
        }

        // 设置到页面数据中
        this.setData({
          statusBarHeight: statusBarHeight
        });
      }
    });

    // 初始化云环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-3gi97kso9ab01185',
        traceUser: true,
      });
    }

    // 初始化本地图片缓存
    localImageCache.ensureCacheDir();

    // 清除过期的缓存
    localImageCache.clearExpiredCache();

    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });
      // 应用主题样式
      this.applyThemeStyle(savedTheme);
    }

    // 获取穿搭ID
    if (options && options.id) {
      this.setData({
        outfitId: options.id
      });

      // 获取用户OpenID并加载穿搭详情
      this.getUserOpenIdAndLoadOutfitDetail();
    } else {
      console.error('编辑页面缺少ID参数');
      wx.showToast({
        title: '缺少搭配ID',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }

    // 获取屏幕尺寸来设置画布大小
    const systemInfo = wx.getSystemInfoSync();

    // 计算屏幕可用高度（排除状态栏和导航栏）
    const statusBarHeight = systemInfo.statusBarHeight || 20;
    const customNavHeight = 90; // 自定义导航栏高度（rpx）
    const navHeight = statusBarHeight + customNavHeight / 2; // 换算为px

    // 计算其他元素的高度
    const closetHeight = 260 / 2; // 衣物选择区域高度（rpx转换为px）
    const toolbarHeight = 100 / 2; // 工具栏高度（rpx转换为px）
    const marginHeight = 40 / 2; // 上下边距总和（rpx转换为px）

    // 计算画布可用高度
    const availableHeight = systemInfo.windowHeight - navHeight - closetHeight - toolbarHeight - marginHeight;

    // 计算画布宽度和高度，保持纵横比例为4:3
    const canvasWidth = systemInfo.windowWidth * 0.9; // 90%屏幕宽度
    let canvasHeight = availableHeight;

    // 确保画布高度不超过可用高度
    if (canvasHeight > availableHeight) {
      canvasHeight = availableHeight;
    }

    // 确保画布高度不小于最小值
    if (canvasHeight < 300) {
      canvasHeight = 300;
    }

    // 计算左侧面板宽度，用于优化分类布局
    const closetPanelWidth = systemInfo.windowWidth * 0.4; // 40%屏幕宽度

    console.log('计算画布尺寸:', {
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight,
      statusBarHeight,
      navHeight,
      closetHeight,
      toolbarHeight,
      marginHeight,
      availableHeight,
      canvasWidth,
      canvasHeight
    });

    this.setData({
      canvasWidth,
      canvasHeight,
      closetPanelWidth,
      // 默认不选择任何类别，显示全部衣物
      currentCategory: null
    });
  },

  // 获取用户OpenID并加载穿搭详情
  getUserOpenIdAndLoadOutfitDetail: function() {
    // 获取用户OpenID
    userManager.getUserOpenId()
      .then(openid => {
        this.setData({
          userOpenId: openid,
          isLoading: true
        });

        // 加载穿搭详情
        return this.loadOutfitDetail();
      })
      .catch(err => {
        console.error('获取用户OpenID失败', err);
        wx.showToast({
          title: '加载用户信息失败',
          icon: 'none'
        });
      });
  },

  // 加载穿搭详情
  loadOutfitDetail: function() {
    wx.showLoading({
      title: '加载搭配中...'
    });

    // 使用outfitManager获取搭配详情
    outfitManager.getOutfitDetail(this.data.outfitId)
      .then(outfitData => {
        console.log('获取到穿搭数据:', outfitData);

        // 处理搭配项目，确保图层信息正确加载
        const processedItems = outfitData.items && outfitData.items.length > 0 ?
          outfitData.items.map((item, index) => {
            const processedItem = {
              ...item,
              // 确保所有必要的属性都存在
              x: (item.x !== undefined && item.x !== null) ? item.x : 0,
              y: (item.y !== undefined && item.y !== null) ? item.y : 0,
              width: (item.width !== undefined && item.width !== null) ? item.width : 100,
              height: (item.height !== undefined && item.height !== null) ? item.height : 100,
              rotation: item.rotation || 0,
              // 确保图层信息正确加载，如果没有图层信息则使用索引作为默认值
              layer: item.layer !== undefined && item.layer !== null ? item.layer : index
            };

            console.log(`加载项目 ${processedItem.id}: 图层 ${processedItem.layer}, 位置 (${processedItem.x}, ${processedItem.y})`);
            return processedItem;
          }) : [];

        // 按图层排序项目，确保显示顺序正确
        const sortedItems = [...processedItems].sort((a, b) => a.layer - b.layer);
        console.log('加载后按图层排序的项目:', sortedItems.map(item => ({
          id: item.id,
          layer: item.layer
        })));

        // 设置穿搭基本信息
        this.setData({
          outfitName: outfitData.name || '未命名搭配',
          outfitCategory: outfitData.category || 'daily',
          canvasItems: sortedItems
        });

        // 计算下一个ID
        const nextId = this.calculateNextId(outfitData.items || []);
        this.setData({ nextId });

        // 更新当前类别信息
        this.updateCurrentCategoryInfo();

        // 加载用户的所有衣物
        return this.loadUserClothes();
      })
      .then(clothes => {
        // 刷新画布上的衣物图片URL
        if (this.data.canvasItems && this.data.canvasItems.length > 0) {
          const updatedCanvasItems = this.data.canvasItems.map(item => {
            // 在衣物列表中查找对应的衣物
            const matchedCloth = this.data.clothes.find(cloth => cloth._id === item.clothId);
            if (matchedCloth) {
              // 更新图片URL和抠图相关信息
              const updatedItem = { ...item };

              // 更新图片URL
              if (matchedCloth.imageUrl) {
                updatedItem.imageUrl = matchedCloth.imageUrl;
              }

              // 更新抠图标记
              updatedItem.isProcessed = matchedCloth.isProcessed;

              // 更新抠图相关字段
              if (matchedCloth.processedImageFileID) {
                updatedItem.processedImageFileID = matchedCloth.processedImageFileID;
              }
              if (matchedCloth.processedImageUrl) {
                updatedItem.processedImageUrl = matchedCloth.processedImageUrl;
              }
              if (matchedCloth.bgRemovedFileID) {
                updatedItem.bgRemovedFileID = matchedCloth.bgRemovedFileID;
              }

              console.log(`更新画布项 ${item.id} 的图片信息:`, {
                clothId: item.clothId,
                isProcessed: updatedItem.isProcessed,
                hasProcessedImage: !!updatedItem.processedImageFileID ||
                                  !!updatedItem.processedImageUrl ||
                                  !!updatedItem.bgRemovedFileID
              });

              return updatedItem;
            }
            return item;
          });

          this.setData({ canvasItems: updatedCanvasItems });
        }
      })
      .catch(err => {
        console.error('加载穿搭详情失败:', err);
        wx.showToast({
          title: '加载搭配详情失败',
          icon: 'none'
        });

        // 加载用户的所有衣物
        return this.loadUserClothes();
      });
  },

  // 计算下一个ID
  calculateNextId: function(items) {
    if (!items || items.length === 0) return 1;
    const maxId = Math.max(...items.map(item => item.id || 0));
    return maxId + 1;
  },

  // 加载用户所有衣物
  loadUserClothes: function() {
    console.log('[outfit_edit] 开始加载用户衣物');

    if (!this.data.userOpenId) {
      console.error('[outfit_edit] 用户OpenID为空，无法加载衣物');
      return Promise.reject(new Error('用户未登录'));
    }

    wx.showLoading({
      title: '加载衣物...',
      mask: true
    });

    // 先检查是否有wardrobe模块的缓存数据
    const userCacheKey = `user_clothes_cache_${this.data.userOpenId}`;
    const userCachedData = wx.getStorageSync(userCacheKey);

    // 如果存在wardrobe模块的缓存数据，优先使用它来更新当前模块的缓存
    if (userCachedData && userCachedData.clothes) {
      console.log('[outfit_edit] 发现wardrobe模块缓存数据，更新本模块缓存');

      // 从clothes数据生成categoryData（如果userCachedData中没有）
      let categoryData = userCachedData.categoryData;
      if (!categoryData) {
        console.log('[outfit_edit] wardrobe缓存中没有categoryData，从clothes数据生成');
        // 生成categoryData - 每个衣物的_id和category作为单独记录
        categoryData = userCachedData.clothes.map(item => {
          return {
            _id: item._id,
            category: item.category
          };
        });

        console.log('[outfit_edit] 已从clothes数据生成categoryData:',
          '记录数量:', categoryData.length);
      }

      // 检查缓存是否过期
      const cacheExpiry = userCachedData.expiry || 0;
      const now = Date.now();

      if (cacheExpiry > now) {
        console.log('[outfit_edit] 使用缓存数据，缓存有效期还剩:',
          Math.round((cacheExpiry - now) / 1000 / 60), '分钟');

        // 使用缓存的衣物数据
        const clothes = userCachedData.clothes || [];

        // 从衣物数据中提取所有类别
        const categoriesSet = new Set();
        clothes.forEach(item => {
          if (item.category) {
            categoriesSet.add(item.category);
          }
        });

        // 将Set转换为数组
        const categoriesArray = Array.from(categoriesSet);
        console.log('[outfit_edit] 从缓存数据中提取到的类别:', categoriesArray);

        // 构建动态类别数组，保留原有的"全部"类别
        let dynamicCategories = [this.data.categories[0]];

        // 为每个类别创建对应的对象
        let idCounter = 1;
        categoriesArray.forEach(category => {
          let iconText = category.substring(0, 1); // 取类别的第一个字作为图标
          dynamicCategories.push({
            id: idCounter++,
            name: category,
            icon: iconText,
            count: 0,
            category: category
          });
        });

        console.log('[outfit_edit] 从缓存生成的动态类别数组:', dynamicCategories);

        // 使用缓存的类别数据更新UI
        const updatedCategories = clothesManager.updateCategoryCounts(
          clothes,
          dynamicCategories
        );

        this.setData({
          clothes,
          filteredClothes: clothes,
          categories: updatedCategories,
          isLoading: false
        });

        // 即使使用缓存，也要确保图片URL是最新的，但不从数据库重新加载数据
        this.refreshAllClothesImages(clothes)
          .then(updatedClothes => {
            console.log('[outfit_edit] 成功刷新所有衣物图片URL, 数量:', updatedClothes.length);

            // 确保currentCategory不是undefined
            const safeCategory = this.data.currentCategory !== undefined ? this.data.currentCategory : null;

            const filteredClothes = clothesManager.filterByCategory(
              updatedClothes,
              safeCategory
            );

            this.setData({
              clothes: updatedClothes,
              filteredClothes: filteredClothes
            });

            wx.hideLoading();
          })
          .catch(err => {
            console.warn('[outfit_edit] 刷新缓存衣物图片URLs失败:', err);
            // 即使刷新失败，也使用缓存数据
            wx.hideLoading();
          });

        return Promise.resolve(clothes);
      } else {
        console.log('[outfit_edit] 发现缓存数据但已过期，将重新加载');
      }
    }

    // 如果没有缓存或缓存已过期，从数据库加载
    return clothesManager.getUserClothes(this.data.userOpenId)
      .then(clothes => {
        console.log('[outfit_edit] 获取到用户衣物，数量:', clothes.length);

        // 从衣物数据中提取所有类别
        const categoriesSet = new Set();
        clothes.forEach(item => {
          const category = item.category || item.categoryId || item.category_id;
          if (category) {
            categoriesSet.add(String(category)); // 转换为字符串确保唯一性
          }
        });

        // 将Set转换为数组
        const categoriesArray = Array.from(categoriesSet);
        console.log('[outfit_edit] 从数据库提取到的类别:', categoriesArray);

        // 打印前10件衣物的类别信息，便于调试
        console.log('[outfit_edit] 衣物类别信息示例:');
        clothes.slice(0, 10).forEach((item, index) => {
          console.log(`衣物${index}: id=${item._id}, 类别=${item.category || item.categoryId || item.category_id}`);
        });

        // 构建动态类别数组，保留原有的"全部"类别
        let dynamicCategories = [this.data.categories[0]];

        // 为每个类别创建对应的对象
        let idCounter = 1;
        categoriesArray.forEach(category => {
          let iconText = category.substring(0, 1); // 取类别的第一个字作为图标
          dynamicCategories.push({
            id: idCounter++,
            name: category,
            icon: iconText,
            count: 0,
            category: category
          });
        });

        console.log('[outfit_edit] 生成的动态类别数组:', dynamicCategories);

        // 更新类别数量
        const updatedCategories = clothesManager.updateCategoryCounts(
          clothes,
          dynamicCategories
        );

        console.log('更新后的类别数组:', updatedCategories);

        // 打印更新后的类别详细信息
        updatedCategories.forEach(cat => {
          console.log(`类别详情: id=${cat.id}, name=${cat.name}, category=${cat.category}, count=${cat.count}`);
        });

        // 按类别过滤衣物
        const filteredClothes = clothesManager.filterByCategory(
          clothes,
          this.data.currentCategory
        );

        this.setData({
          clothes,
          filteredClothes,
          categories: updatedCategories,
          isLoading: false
        });

        wx.hideLoading();

        return clothes;
      })
      .catch(err => {
        console.error('[outfit_edit] 加载用户衣物失败:', err);

        this.setData({
          isLoading: false
        });

        wx.hideLoading();

        wx.showToast({
          title: '加载衣物失败',
          icon: 'none'
        });

        // 使用默认类别
        const defaultCategories = [
          { id: 0, name: '全部', icon: '全', count: 0 },
          { id: 1, name: '上衣', icon: '👕', count: 0 },
          { id: 2, name: '裤子', icon: '👖', count: 0 },
          { id: 3, name: '外套', icon: '🧥', count: 0 },
          { id: 4, name: '鞋子', icon: '👟', count: 0 },
          { id: 5, name: '配饰', icon: '👔', count: 0 }
        ];

        this.setData({
          categories: defaultCategories
        });

        return [];
      });
  },

  // 刷新所有衣物图片
  refreshAllClothesImages: function(clothes) {
    if (!clothes || clothes.length === 0) {
      return Promise.resolve([]);
    }

    console.log(`开始刷新 ${clothes.length} 件衣物的图片，优先使用抠图后的图片`);

    // 统计抠图相关字段的数量
    const stats = {
      processedImageFileID: 0,
      processedImageUrl: 0,
      bgRemovedFileID: 0
    };

    clothes.forEach(item => {
      if (item.processedImageFileID) stats.processedImageFileID++;
      if (item.processedImageUrl) stats.processedImageUrl++;
      if (item.bgRemovedFileID) stats.bgRemovedFileID++;
    });

    console.log('衣物抠图字段统计:', stats);

    const refreshPromises = clothes.map(item => {
      return new Promise(resolve => {
        // 记录原始状态
        const originalIsProcessed = item.isProcessed;

        imageManager.processClothImage(item)
          .then(updatedItem => {
            // 如果抠图状态发生变化，记录日志
            if (originalIsProcessed !== updatedItem.isProcessed) {
              console.log(`衣物 ${updatedItem._id || 'unknown'} 抠图状态变化: ${originalIsProcessed} -> ${updatedItem.isProcessed}`);
            }
            resolve(updatedItem);
          })
          .catch((err) => {
            console.error(`处理衣物 ${item._id || 'unknown'} 图片失败:`, err);
            // 出错时返回原始项
            resolve(item);
          });
      });
    });

    return Promise.all(refreshPromises)
      .then(updatedClothes => {
        // 统计使用抠图的衣物数量
        const processedCount = updatedClothes.filter(item => item.isProcessed).length;
        console.log(`刷新完成，共有 ${processedCount}/${updatedClothes.length} 件衣物使用了抠图图片`);
        return updatedClothes;
      });
  },

  // 更新当前类别信息
  updateCurrentCategoryInfo: function() {
    console.log('updateCurrentCategoryInfo 被调用, 当前类别:', this.data.outfitCategory);

    const categoryOption = this.data.outfitCategoryOptions.find(option =>
      option.value === this.data.outfitCategory
    );

    console.log('找到的类别选项:', categoryOption);

    if (categoryOption) {
      this.setData({
        currentCategoryIcon: categoryOption.icon,
        currentCategoryName: categoryOption.name
      });

      console.log('更新后的类别信息:', {
        currentCategoryIcon: this.data.currentCategoryIcon,
        currentCategoryName: this.data.currentCategoryName
      });
    } else {
      console.error('未找到匹配的类别选项');
    }
  },

  // 应用主题样式
  applyThemeStyle: function(style) {
    console.log('应用主题样式:', style);
    // 这里可以根据主题样式做一些额外的处理
  },

  // 选择衣物类别
  selectCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id;
    console.log('选择衣物类别:', categoryId);

    // 获取选中类别的信息
    const selectedCategory = this.data.categories.find(cat => cat.id === categoryId);
    console.log('选中的类别信息:', selectedCategory);

    // 如果选中的是全部类别，则使用null
    const filterCategory = categoryId === 0 ? null : (selectedCategory ? selectedCategory.category : null);

    console.log('使用的过滤类别:', filterCategory);
    console.log('当前衣物数量:', this.data.clothes ? this.data.clothes.length : 0);

    // 打印所有类别信息，便于调试
    console.log('所有类别信息:');
    this.data.categories.forEach(cat => {
      console.log(`类别: id=${cat.id}, name=${cat.name}, category=${cat.category}, count=${cat.count}`);
    });

    // 过滤衣物
    const filteredClothes = clothesManager.filterByCategory(
      this.data.clothes,
      filterCategory
    );

    console.log('过滤后的衣物数量:', filteredClothes.length);

    // 打印过滤后的衣物信息，便于调试
    if (filteredClothes.length > 0) {
      console.log('第一件过滤后的衣物:', filteredClothes[0]);
    } else {
      console.log('过滤后没有衣物');
    }

    this.setData({
      currentCategory: categoryId === 0 ? null : categoryId,
      filteredClothes: filteredClothes
    });

    // 振动反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  // 添加衣物到画布
  addClothToCanvas: function(e) {
    const index = e.currentTarget.dataset.index;
    const cloth = this.data.filteredClothes[index];

    console.log('添加衣物到画布:', cloth);

    if (!cloth) {
      console.error('衣物数据为空');
      return;
    }

    // 确保衣物有图片URL
    if (!cloth.imageUrl) {
      // 如果没有imageUrl，尝试处理图片
      imageManager.processClothImage(cloth)
        .then(processedCloth => {
          this.addClothToCanvasInternal(processedCloth);
        })
        .catch(err => {
          console.error('处理衣物图片失败:', err);
          // 即使处理失败，也尝试添加
          this.addClothToCanvasInternal(cloth);
        });
    } else {
      // 直接添加到画布
      this.addClothToCanvasInternal(cloth);
    }
  },

  // 内部方法，实际添加衣物到画布
  addClothToCanvasInternal: function(cloth) {
    console.log('添加衣物到画布，衣物信息:', {
      id: cloth._id,
      isProcessed: cloth.isProcessed,
      hasProcessedImage: !!cloth.processedImageFileID || !!cloth.processedImageUrl || !!cloth.bgRemovedFileID,
      imageUrl: cloth.imageUrl ? cloth.imageUrl.substring(0, 50) + '...' : 'none'
    });

    // 使用canvasManager添加衣物
    const result = canvasManager.addClothToCanvas(
      this.data.canvasItems,
      cloth,
      this.data.canvasWidth,
      this.data.canvasHeight,
      this.data.nextId
    );

    // 确保添加的项目保留isProcessed标记
    const addedItemIndex = result.items.findIndex(item => item.id === result.nextId - 1);
    if (addedItemIndex !== -1) {
      // 保留原始的isProcessed标记和其他抠图相关信息
      result.items[addedItemIndex].isProcessed = cloth.isProcessed;
      result.items[addedItemIndex].processedImageFileID = cloth.processedImageFileID;
      result.items[addedItemIndex].processedImageUrl = cloth.processedImageUrl;
      result.items[addedItemIndex].bgRemovedFileID = cloth.bgRemovedFileID;

      console.log('已保留抠图标记，添加的项目:', {
        id: result.items[addedItemIndex].id,
        isProcessed: result.items[addedItemIndex].isProcessed,
        imageUrl: result.items[addedItemIndex].imageUrl ?
                 result.items[addedItemIndex].imageUrl.substring(0, 50) + '...' : 'none'
      });
    }

    // 按图层排序画布项目，使图层值较小的在前面显示
    result.items.sort((a, b) => (a.layer || 0) - (b.layer || 0));

    // 获取新添加项目的实际图层值
    const newItemLayer = result.items.find(item => item.id === result.nextId - 1)?.layer || 0;

    console.log('添加衣物后排序:', result.items.map(item => ({
      id: item.id,
      layer: item.layer || 0,
      isProcessed: item.isProcessed
    })));

    console.log(`新添加的项目 ${result.nextId - 1} 的图层值: ${newItemLayer}`);

    this.setData({
      canvasItems: result.items,
      nextId: result.nextId,
      activeItemId: result.nextId - 1, // 选中新添加的项
      activeItemLayer: newItemLayer // 使用实际的图层值
    });

    // 振动反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  // 选中画布项
  selectItem: function(e) {
    const itemId = parseInt(e.currentTarget.dataset.id);
    console.log('选中画布项:', itemId);

    if (itemId !== undefined) {
      // 找到选中的项目
      const selectedItem = this.data.canvasItems.find(item => item.id === itemId);
      const itemLayer = selectedItem ? (selectedItem.layer || 0) : 0;

      this.setData({
        activeItemId: itemId,
        activeItemLayer: itemLayer,
        // 选中项目但不改变其图层
        canvasItems: canvasManager.selectItemWithoutLayerChange(this.data.canvasItems, itemId)
      });

      // 振动反馈
      wx.vibrateShort({
        type: 'light'
      });
    }

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
  },

  // 处理画布触摸开始事件
  handleCanvasTouchStart: function(e) {
    console.log('触摸开始事件:', e);
    const touches = e.touches;
    const itemId = parseInt(e.currentTarget.dataset.id);

    // 如果是画布项的触摸事件，先选中该项
    if (itemId !== undefined) {
      // 找到选中的项目
      const selectedItem = this.data.canvasItems.find(item => item.id === itemId);
      const itemLayer = selectedItem ? (selectedItem.layer || 0) : 0;

      this.setData({
        activeItemId: itemId,
        activeItemLayer: itemLayer,
        // 选中项目但不改变其图层
        canvasItems: canvasManager.selectItemWithoutLayerChange(this.data.canvasItems, itemId)
      });
    }

    // 记录触摸信息
    if (touches && touches.length === 1) {
      // 单指触摸
      const touch = touches[0];

      // 获取触摸坐标，兼容不同的属性名
      const touchX = touch.x || touch.clientX || touch.pageX || 0;
      const touchY = touch.y || touch.clientY || touch.pageY || 0;

      console.log('触摸坐标:', touchX, touchY);

      this.setData({
        touchStartX: touchX,
        touchStartY: touchY,
        isSingleTouch: true,
        isScaling: false,
        isRotating: false
      });

      // 如果是画布的触摸事件，查找触摸位置下的画布项
      if (itemId === undefined) {
        const position = { x: touchX, y: touchY };
        const item = canvasManager.findTopItemAtPosition(this.data.canvasItems, position);

        if (item) {
          // 选中触摸到的项
          this.setData({
            activeItemId: item.id,
            activeItemLayer: item.layer || 0,
            itemStartX: item.x,
            itemStartY: item.y,
            // 选中项目但不改变其图层
            canvasItems: canvasManager.selectItemWithoutLayerChange(this.data.canvasItems, item.id)
          });
        } else {
          // 取消选中
          this.setData({
            activeItemId: null
          });
        }
      }
    } else if (touches && touches.length === 2) {
      // 双指触摸，准备缩放或旋转
      const item = this.data.canvasItems.find(item => item.id === this.data.activeItemId);

      if (item) {
        // 计算两指间距离和角度
        const touch1 = {
          x: touches[0].x || touches[0].clientX || touches[0].pageX || 0,
          y: touches[0].y || touches[0].clientY || touches[0].pageY || 0
        };
        const touch2 = {
          x: touches[1].x || touches[1].clientX || touches[1].pageX || 0,
          y: touches[1].y || touches[1].clientY || touches[1].pageY || 0
        };

        console.log('双指触摸:', touch1, touch2);

        const distance = canvasManager.getDistance(touch1, touch2);
        const angle = canvasManager.getAngle(touch1, touch2);

        this.setData({
          isSingleTouch: false,
          initialFingerDistance: distance,
          initialItemWidth: item.width,
          initialItemHeight: item.height,
          previousAngle: angle,
          initialRotation: item.rotation || 0
        });
      }
    }
  },

  // 处理画布触摸移动事件
  handleCanvasTouchMove: function(e) {
    const touches = e.touches;
    const itemId = parseInt(e.currentTarget.dataset.id);

    if (this.data.activeItemId === null) {
      return;
    }

    // 如果是画布项的触摸事件，确保使用正确的itemId
    const activeId = itemId !== undefined ? itemId : this.data.activeItemId;

    if (touches && touches.length === 1 && this.data.isSingleTouch) {
      // 单指移动
      const touch = touches[0];

      // 获取触摸坐标，兼容不同的属性名
      const touchX = touch.x || touch.clientX || touch.pageX || 0;
      const touchY = touch.y || touch.clientY || touch.pageY || 0;

      const dx = touchX - this.data.touchStartX;
      const dy = touchY - this.data.touchStartY;

      console.log('触摸移动:', touchX, touchY, 'dx:', dx, 'dy:', dy);

      if (Math.abs(dx) > 2 || Math.abs(dy) > 2) {
        // 移动项
        this.setData({
          isMoving: true,
          canvasItems: canvasManager.moveItem(
            this.data.canvasItems,
            activeId,
            dx,
            dy
          ),
          touchStartX: touchX,
          touchStartY: touchY
        });
      }
    } else if (touches && touches.length === 2) {
      // 双指手势 - 缩放和旋转
      const touch1 = {
        x: touches[0].x || touches[0].clientX || touches[0].pageX || 0,
        y: touches[0].y || touches[0].clientY || touches[0].pageY || 0
      };
      const touch2 = {
        x: touches[1].x || touches[1].clientX || touches[1].pageX || 0,
        y: touches[1].y || touches[1].clientY || touches[1].pageY || 0
      };

      console.log('双指移动手势:', touch1, touch2);

      const distance = canvasManager.getDistance(touch1, touch2);
      const angle = canvasManager.getAngle(touch1, touch2);

      // 计算缩放比例
      const scale = distance / this.data.initialFingerDistance;

      // 计算角度差
      let angleDiff = angle - this.data.previousAngle;

      // 修正大角度变化（当从接近0度到接近360度或相反时）
      if (angleDiff > 180) angleDiff -= 360;
      if (angleDiff < -180) angleDiff += 360;

      // 缩放
      if (Math.abs(scale - 1) > 0.01) {
        // 缩放中心点
        const center = {
          x: (touch1.x + touch2.x) / 2,
          y: (touch1.y + touch2.y) / 2
        };

        console.log('缩放中心点:', center, '缩放比例:', scale);

        this.setData({
          isScaling: true,
          canvasItems: canvasManager.scaleItem(
            this.data.canvasItems,
            this.data.activeItemId,
            scale,
            center
          ),
          initialFingerDistance: distance
        });
      }

      // 旋转
      if (Math.abs(angleDiff) > 1) {
        this.setData({
          isRotating: true,
          canvasItems: canvasManager.rotateItem(
            this.data.canvasItems,
            this.data.activeItemId,
            angleDiff
          ),
          previousAngle: angle
        });
      }
    }
  },

  // 处理画布触摸结束事件
  handleCanvasTouchEnd: function() {
    this.setData({
      isMoving: false,
      isScaling: false,
      isRotating: false
    });
  },

  // 删除选中的画布项
  deleteSelectedItem: function(e) {
    const itemId = e ? parseInt(e.currentTarget.dataset.id) : this.data.activeItemId;

    if (itemId === null) {
      wx.showToast({
        title: '请先选择项目',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '确定要删除选中的衣物吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            canvasItems: canvasManager.removeItem(
              this.data.canvasItems,
              itemId
            ),
            activeItemId: null
          });

          // 振动反馈
          wx.vibrateShort();
        }
      }
    });

    // 阻止事件冒泡
    if (e) {
      e.stopPropagation && e.stopPropagation();
    }
  },

  // 旋转项目
  rotateItem: function(e) {
    const itemId = parseInt(e.currentTarget.dataset.id);
    const direction = e.currentTarget.dataset.direction;

    if (itemId === null) return;

    // 旋转角度，顺时针为正，逆时针为负
    const angle = direction === 'cw' ? 15 : -15;

    this.setData({
      canvasItems: canvasManager.rotateItem(
        this.data.canvasItems,
        itemId,
        angle
      )
    });

    // 振动反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
  },

  // 调整图层
  adjustLayer: function(e) {
    const itemId = parseInt(e.currentTarget.dataset.id);
    const direction = e.currentTarget.dataset.direction;

    if (itemId === null) return;

    let updatedItems = [...this.data.canvasItems];
    const itemIndex = updatedItems.findIndex(item => item.id === itemId);

    if (itemIndex === -1) return;

    const currentLayer = updatedItems[itemIndex].layer || 0;
    let newLayer = currentLayer;

    if (direction === 'up') {
      // 上移图层 - 直接增加1，不限制最大值
      newLayer = currentLayer + 1;
      console.log(`图层上移: 项目 ${itemId} 从 ${currentLayer} 到 ${newLayer}`);
    } else if (direction === 'down') {
      // 下移图层 - 不能小于0
      newLayer = currentLayer > 0 ? currentLayer - 1 : 0;
      console.log(`图层下移: 项目 ${itemId} 从 ${currentLayer} 到 ${newLayer}`);
    }

    // 更新项目的图层值
    updatedItems[itemIndex] = {
      ...updatedItems[itemIndex],
      layer: newLayer
    };

    // 按图层排序画布项目，使图层值较小的在前面显示
    updatedItems.sort((a, b) => (a.layer || 0) - (b.layer || 0));

    // 验证图层调整结果
    console.log('图层调整后的所有项目:', updatedItems.map(item => ({
      id: item.id,
      layer: item.layer || 0
    })));

    this.setData({
      canvasItems: updatedItems,
      activeItemLayer: newLayer
    });

    // 振动反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
  },

  // 调整大小
  resizeItem: function(e) {
    const itemId = parseInt(e.currentTarget.dataset.id);
    const action = e.currentTarget.dataset.action;

    if (itemId === null) return;

    let updatedItems = [...this.data.canvasItems];
    const itemIndex = updatedItems.findIndex(item => item.id === itemId);

    if (itemIndex === -1) return;

    const item = updatedItems[itemIndex];
    const scaleFactor = action === 'increase' ? 1.1 : 0.9;

    // 获取原始宽高比，如果存在
    const aspectRatio = item.aspectRatio || (item.width / item.height);

    // 计算新的宽高，保持宽高比
    const newWidth = item.width * scaleFactor;
    // 根据宽高比计算新高度，确保比例一致
    const newHeight = newWidth / aspectRatio;

    // 计算新的位置，保持中心点不变
    const centerX = item.x + item.width / 2;
    const centerY = item.y + item.height / 2;
    const newX = centerX - newWidth / 2;
    const newY = centerY - newHeight / 2;

    console.log(`调整项目 ${item.id} 大小: 宽高比 ${aspectRatio.toFixed(2)}, 新尺寸 ${newWidth.toFixed(0)}x${newHeight.toFixed(0)}`);

    // 更新项目
    updatedItems[itemIndex] = {
      ...item,
      width: newWidth,
      height: newHeight,
      x: newX,
      y: newY,
      aspectRatio: aspectRatio // 保存宽高比，确保一致性
    };

    this.setData({
      canvasItems: updatedItems
    });

    // 振动反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
  },

  // 修改搭配名称
  changeOutfitName: function(e) {
    const name = e.detail.value;
    this.setData({
      outfitName: name,
      hasCustomName: true
    });
  },

  // 显示/隐藏类型选择器
  toggleCategoryPicker: function() {
    this.setData({
      showCategoryPicker: !this.data.showCategoryPicker
    });
  },

  // 选择搭配类型
  selectOutfitCategory: function(e) {
    const category = e.currentTarget.dataset.value;
    console.log('选择搭配类型:', category);

    // 找到对应的类型选项
    const categoryOption = this.data.outfitCategoryOptions.find(option =>
      option.value === category
    );

    if (!categoryOption) {
      console.error('无效的类型选项:', category);
      return;
    }

    // 保持当前的搭配名称
    this.setData({
      showCategoryPicker: false,
      outfitCategory: category
    });

    // 更新当前类型信息
    this.updateCurrentCategoryInfo();

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
    return false;
  },

  // 清空画布
  clearCanvas: function() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空当前画布吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            canvasItems: [],
            activeItemId: null
          });

          // 振动反馈
          wx.vibrateShort();
        }
      }
    });
  },

  // 处理画布点击事件
  canvasTap: function(e) {
    console.log('画布点击事件:', e);

    // 如果点击的是画布背景，取消选中状态
    if (e.target.id === 'canvas-container' || e.target.dataset.id === undefined) {
      this.setData({
        activeItemId: null
      });
    }
  },

  // 保存搭配
  saveOutfit: function() {
    if (this.data.canvasItems.length === 0) {
      wx.showToast({
        title: '画布为空，无法保存',
        icon: 'none'
      });
      return;
    }

    if (this.data.isSaving) {
      return;
    }

    // 验证图层信息完整性
    console.log('保存前验证图层信息:');
    this.data.canvasItems.forEach(item => {
      console.log(`项目 ${item.id}: 图层 ${item.layer}, 位置 (${item.x}, ${item.y}), 尺寸 ${item.width}x${item.height}`);
    });

    // 确保所有项目都有有效的图层值
    const validatedItems = this.data.canvasItems.map(item => ({
      ...item,
      layer: item.layer !== undefined && item.layer !== null ? item.layer : 0
    }));

    // 按图层排序验证
    const sortedItems = [...validatedItems].sort((a, b) => a.layer - b.layer);
    console.log('按图层排序后的项目:', sortedItems.map(item => ({
      id: item.id,
      layer: item.layer
    })));

    this.setData({
      isSaving: true,
      canvasItems: validatedItems // 使用验证后的项目数据
    });

    wx.showLoading({
      title: '保存中...',
    });

    // 将画布转为图片 - 使用验证后的项目数据
    outfitManager.generateOutfitImage(
      'outfitCanvas',
      validatedItems, // 使用验证后的数据，确保图层信息正确
      this.data.canvasWidth,
      this.data.canvasHeight
    )
      .then(imageFileID => {
        console.log('搭配图片已保存:', imageFileID);

        // 更新搭配数据到数据库 - 使用验证后的项目数据
        return outfitManager.updateOutfitInDatabase(
          this.data.outfitId,
          this.data.outfitName,
          imageFileID,
          validatedItems, // 使用验证后的数据，确保图层信息正确保存
          this.data.outfitCategory
        );
      })
      .then(res => {
        console.log('搭配数据已更新:', res);

        this.setData({
          isSaving: false
        });

        wx.hideLoading();

        // 设置标记，通知其他页面刷新数据
        wx.setStorageSync('needRefreshOutfits', true);

        // 显示成功提示
        wx.showToast({
          title: '更新成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟返回
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(err => {
        console.error('更新搭配失败:', err);

        this.setData({
          isSaving: false
        });

        wx.hideLoading();

        wx.showToast({
          title: err.message || '更新失败',
          icon: 'none'
        });
      });
  },

  // 返回上一页
  goBack: function() {
    // 如果有未保存的修改，提示用户
    wx.showModal({
      title: '提示',
      content: '有未保存的修改，确定要返回吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 页面显示时执行
  onShow: function() {
    console.log('outfit_edit 页面 onShow');

    // 如果已经加载了衣物数据，则刷新图片URL
    if (this.data.clothes && this.data.clothes.length > 0 && !this.data.isLoading) {
      wx.showLoading({
        title: '刷新衣物图片...',
      });

      console.log('开始刷新衣物图片，优先使用抠图后的图片');

      // 刷新所有衣物的图片URL
      this.refreshAllClothesImages(this.data.clothes)
        .then(updatedClothes => {
          console.log('页面显示时刷新图片URL完成');

          // 统计抠图衣物数量
          const processedCount = updatedClothes.filter(item => item.isProcessed).length;
          console.log(`共有 ${processedCount}/${updatedClothes.length} 件衣物使用了抠图`);

          this.setData({
            clothes: updatedClothes,
            filteredClothes: clothesManager.filterByCategory(
              updatedClothes,
              this.data.currentCategory
            )
          });

          wx.hideLoading();
        })
        .catch(err => {
          console.error('刷新衣物图片失败:', err);
          wx.hideLoading();
        });
    }
  },

  // 处理图片加载错误
  handleImageError: function(e) {
    console.log('图片加载错误:', e);

    const index = e.currentTarget.dataset.index;
    const type = e.currentTarget.dataset.type || 'cloth';

    if (type === 'cloth' && index !== undefined) {
      // 衣物列表中的图片加载错误
      const defaultImageUrl = imageManager.getDefaultImageUrl();

      // 更新filteredClothes中的图片
      const filteredClothes = [...this.data.filteredClothes];
      if (filteredClothes[index]) {
        filteredClothes[index].imageUrl = defaultImageUrl;

        this.setData({
          filteredClothes
        });
      }
    }
  },

  // 屏幕尺寸变化时重新计算高度
  onResize: function() {
    console.log('屏幕尺寸变化，重新计算高度');

    // 获取屏幕尺寸
    const systemInfo = wx.getSystemInfoSync();

    // 计算屏幕可用高度（排除状态栏和导航栏）
    const statusBarHeight = systemInfo.statusBarHeight || 20;
    const customNavHeight = 90; // 自定义导航栏高度（rpx）
    const navHeight = statusBarHeight + customNavHeight / 2; // 换算为px

    // 计算其他元素的高度
    const closetHeight = 260 / 2; // 衣物选择区域高度（rpx转换为px）
    const toolbarHeight = 100 / 2; // 工具栏高度（rpx转换为px）
    const marginHeight = 40 / 2; // 上下边距总和（rpx转换为px）

    // 计算画布可用高度
    const availableHeight = systemInfo.windowHeight - navHeight - closetHeight - toolbarHeight - marginHeight;

    // 计算画布宽度和高度
    const canvasWidth = systemInfo.windowWidth * 0.9; // 90%屏幕宽度
    let canvasHeight = availableHeight;

    // 确保画布高度不超过可用高度
    if (canvasHeight > availableHeight) {
      canvasHeight = availableHeight;
    }

    // 确保画布高度不小于最小值
    if (canvasHeight < 300) {
      canvasHeight = 300;
    }

    console.log('重新计算画布尺寸:', {
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight,
      statusBarHeight,
      navHeight,
      closetHeight,
      toolbarHeight,
      marginHeight,
      availableHeight,
      canvasWidth,
      canvasHeight
    });

    this.setData({
      canvasWidth,
      canvasHeight
    });
  }
});