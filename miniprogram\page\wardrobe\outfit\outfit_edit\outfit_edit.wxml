<view class="container {{themeStyle}}" style="background-color: {{themeStyle === 'autumn' ? colors.goldenBatter : pinkBlueColors.pinkLight}};">
  <!-- 加载提示 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view class="main-content" wx:if="{{!isLoading}}">
    <!-- 自定义导航栏 -->
    <view class="custom-nav" style="background-color: {{themeStyle === 'autumn' ? colors.toastedCaramel : pinkBlueColors.pinkMedium}}; padding-top: {{statusBarHeight}}px;">
      <view class="nav-content">
        <view class="back-button" bindtap="goBack">
          <text class="back-icon">←</text>
          <text>返回</text>
        </view>
        <view class="page-title">编辑搭配</view>
      </view>
    </view>

    <!-- 搭配信息编辑 -->
    <view class="outfit-info-editor" style="background-color: {{themeStyle === 'autumn' ? colors.oliveHarvest : pinkBlueColors.blueMedium}};">
      <view class="outfit-name-input">
        <input
          value="{{outfitName}}"
          placeholder="搭配名称"
          bindinput="changeOutfitName"
          style="color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};"
        />
      </view>

      <view class="outfit-category-selector" bindtap="toggleCategoryPicker">
        <text class="category-icon">{{currentCategoryIcon}}</text>
        <text class="category-name" style="color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};">{{currentCategoryName}}</text>
        <text class="dropdown-icon">▼</text>

        <!-- 类型选择器下拉菜单 -->
        <view class="category-dropdown" wx:if="{{showCategoryPicker}}">
          <view
            class="category-option"
            wx:for="{{outfitCategoryOptions}}"
            wx:key="value"
            data-value="{{item.value}}"
            bindtap="selectOutfitCategory"
          >
            <text class="option-icon">{{item.icon}}</text>
            <text class="option-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 衣物选择区域 -->
    <view class="closet-container">
      <!-- 类别选择和保存按钮 -->
      <view class="category-list" style="background-color: {{themeStyle === 'autumn' ? colors.spicedWine : pinkBlueColors.blueDark}};">
        <view class="category-list-container">
          <scroll-view scroll-x="true" class="categories-scroll">
            <view
              wx:for="{{categories}}"
              wx:key="id"
              class="category-item {{currentCategory === item.id || (currentCategory === null && item.id === 0) ? 'active' : ''}}"
              data-id="{{item.id}}"
              bindtap="selectCategory"
            >
              <text class="category-icon">{{item.icon}}</text>
              <text class="category-name">{{item.name}}</text>
              <text class="category-count">({{item.count}})</text>
            </view>
          </scroll-view>
          <view class="save-button-container">
            <view class="save-button" bindtap="saveOutfit" style="opacity: {{isSaving ? 0.5 : 1}}">
              <text>保存</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 衣物列表 -->
      <view class="clothes-list">
        <scroll-view
          scroll-x="true"
          class="clothes-scroll"
          enhanced="true"
          show-scrollbar="false"
          bounces="true"
        >
          <view class="clothes-grid">
            <view
              wx:for="{{filteredClothes}}"
              wx:key="_id"
              class="cloth-item"
              data-index="{{index}}"
              bindtap="addClothToCanvas"
              hover-class="cloth-item-hover"
            >
              <image
                src="{{item.imageUrl}}"
                mode="aspectFit"
                class="cloth-image"
                data-index="{{index}}"
                data-type="cloth"
                binderror="handleImageError"
              ></image>
              <view class="cloth-name">{{item.name || '未命名'}}</view>
            </view>

            <!-- 无数据提示 -->
            <view class="no-clothes-tip" wx:if="{{filteredClothes.length === 0}}">
              <text>没有找到衣物</text>
              <view class="tip-detail">请尝试选择其他类别</view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 画布区域 -->
    <view class="canvas-panel" bindtap="canvasTap">
      <view class="canvas-container" style="width: {{canvasWidth}}px; height: {{canvasHeight}}px; background-color: white;">
        <!-- 提示信息 -->
        <view class="canvas-hint" wx:if="{{canvasItems.length === 0}}">
          <text>从上方选择衣物添加到画布</text>
          <text>单指拖动移动位置</text>
          <text>双指捕合缩放大小</text>
          <text>双指旋转调整角度</text>
          <text>选中衣物后可使用控制按钮</text>
        </view>

        <!-- 画布项目 - 按图层排序显示 -->
        <block wx:for="{{canvasItems}}" wx:key="id" wx:for-item="item" wx:for-index="index">
          <!-- 画布项目本体 -->
          <view
            class="canvas-item {{activeItemId === item.id ? 'active' : ''}}"
            style="left: {{item.x}}px; top: {{item.y}}px; width: {{item.width}}px; height: {{item.height}}px; transform: rotate({{item.rotation || 0}}deg); z-index: {{item.layer || 0}};"
            data-id="{{item.id}}"
            bindtouchstart="handleCanvasTouchStart"
            bindtouchmove="handleCanvasTouchMove"
            bindtouchend="handleCanvasTouchEnd"
            bindtouchcancel="handleCanvasTouchEnd"
          >
            <image
              src="{{item.imageUrl}}"
              mode="aspectFit"
              style="width: 100%; height: 100%;"
              catchtap="selectItem"
              data-id="{{item.id}}"
            ></image>

            <!-- 图层信息，仅在项目被选中时显示 -->
            <view class="layer-info" wx:if="{{activeItemId === item.id}}">
              <text>{{item.layer || 0}}</text>
            </view>
          </view>

          <!-- 控制按钮，单独放置，不作为画布项目的子元素 -->
          <block wx:if="{{activeItemId === item.id}}">
            <!-- 删除按钮 -->
            <view
              class="control-btn delete-btn"
              style="left: {{item.x + item.width - 20}}px; top: {{item.y - 40}}px; width: 40px; height: 40px; line-height: 40px; font-size: 28px;"
              data-id="{{item.id}}"
              catchtap="deleteSelectedItem"
            >×</view>

            <!-- 顺时针旋转按钮 -->
            <view
              class="control-btn rotate-cw-btn"
              style="left: {{item.x - 40}}px; top: {{item.y - 40}}px; width: 40px; height: 40px; line-height: 40px; font-size: 28px;"
              data-id="{{item.id}}"
              data-direction="cw"
              catchtap="rotateItem"
            >↻</view>

            <!-- 逆时针旋转按钮 -->
            <view
              class="control-btn rotate-ccw-btn"
              style="left: {{item.x + 40}}px; top: {{item.y - 40}}px; width: 40px; height: 40px; line-height: 40px; font-size: 28px;"
              data-id="{{item.id}}"
              data-direction="ccw"
              catchtap="rotateItem"
            >↺</view>

            <!-- 图层上移按钮 -->
            <view
              class="control-btn layer-up-btn"
              style="left: {{item.x - 40}}px; top: {{item.y + item.height - 20}}px; width: 40px; height: 40px; line-height: 40px;"
              data-id="{{item.id}}"
              data-direction="up"
              catchtap="adjustLayer"
            >↑</view>

            <!-- 图层下移按钮 -->
            <view
              class="control-btn layer-down-btn"
              style="left: {{item.x + 40}}px; top: {{item.y + item.height - 20}}px; width: 40px; height: 40px; line-height: 40px;"
              data-id="{{item.id}}"
              data-direction="down"
              catchtap="adjustLayer"
            >↓</view>

            <!-- 放大按钮 -->
            <view
              class="control-btn size-increase-btn"
              style="left: {{item.x + item.width - 20}}px; top: {{item.y + item.height / 2 - 20}}px; width: 40px; height: 40px; line-height: 40px;"
              data-id="{{item.id}}"
              data-action="increase"
              catchtap="resizeItem"
            >+</view>

            <!-- 缩小按钮 -->
            <view
              class="control-btn size-decrease-btn"
              style="left: {{item.x + item.width - 20}}px; top: {{item.y + item.height - 20}}px; width: 40px; height: 40px; line-height: 40px;"
              data-id="{{item.id}}"
              data-action="decrease"
              catchtap="resizeItem"
            >-</view>
          </block>
        </block>
      </view>

      <!-- 画布工具栏 -->
      <view class="canvas-toolbar" style="background-color: {{themeStyle === 'autumn' ? colors.darkBrown : pinkBlueColors.pinkDark}};">
        <view class="toolbar-button" bindtap="deleteSelectedItem">
          <text class="button-icon">🗑️</text>
          <text class="button-text">删除</text>
        </view>
        <view class="toolbar-button" bindtap="clearCanvas">
          <text class="button-icon">🧹</text>
          <text class="button-text">清空</text>
        </view>
        <view class="toolbar-button" wx:if="{{activeItemId !== null}}">
          <text class="button-text">图层: {{activeItemLayer}}</text>
        </view>
      </view>
    </view>

    <!-- 用于生成图片的画布，不可见 -->
    <canvas type="2d" id="outfitCanvas" style="width: {{canvasWidth}}px; height: {{canvasHeight}}px; position: absolute; left: -9999px;"></canvas>
  </view>
</view>
