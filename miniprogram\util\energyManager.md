# 体力值管理模块使用说明

## 简介

`energyManager.js` 是一个用于管理用户体力值的工具模块，提供了增加、减少、检查和消耗体力值的功能。

## 使用方法

### 1. 引入模块

```javascript
const energyManager = require('../../util/energyManager');
```

### 2. 减少用户体力值

```javascript
// 减少指定数量的体力值
energyManager.decreaseEnergy(5)
  .then(result => {
    console.log('体力值减少成功，当前体力值:', result.catEnergy);
  })
  .catch(err => {
    console.error('体力值减少失败:', err);
  });
```

### 3. 增加用户体力值

```javascript
// 增加指定数量的体力值
energyManager.increaseEnergy(10)
  .then(result => {
    console.log('体力值增加成功，当前体力值:', result.catEnergy);
  })
  .catch(err => {
    console.error('体力值增加失败:', err);
  });
```

### 4. 检查用户是否有足够的体力值

```javascript
// 检查用户是否有足够的体力值执行指定操作
energyManager.hasEnoughEnergy('IMAGE_EXTRACTION')
  .then(hasEnough => {
    if (hasEnough) {
      console.log('用户有足够的体力值执行AI抠图操作');
      // 执行操作...
    } else {
      console.log('用户体力值不足，无法执行AI抠图操作');
      // 提示用户...
    }
  });
```

### 5. 消耗体力值执行操作

```javascript
// 消耗体力值执行指定操作
energyManager.consumeEnergyForAction('OUTFIT_CREATION')
  .then(result => {
    if (result.success) {
      console.log('操作成功，消耗了', result.consumedEnergy, '点体力值，剩余', result.remainingEnergy);
      // 执行后续操作...
    } else {
      console.log(result.message);
      // 提示用户体力值不足...
    }
  });
```

### 6. 获取当前用户体力值

```javascript
// 获取当前用户体力值
energyManager.getCurrentEnergy()
  .then(energy => {
    console.log('当前用户体力值:', energy);
  });
```

## 常量

模块提供了以下常量：

- `ENERGY_COST.IMAGE_EXTRACTION`: AI抠图一件衣服消耗的体力值（2点）
- `ENERGY_COST.OUTFIT_CREATION`: AI创建搭配消耗的体力值（1点）
- `ENERGY_COST.WARDROBE_ANALYSIS`: AI分析衣柜消耗的体力值（2点）
- `MAX_ENERGY`: 体力值上限（500点）
- `MIN_ENERGY`: 体力值下限（0点）
- `DEFAULT_ENERGY`: 默认体力值（50点）

## 示例：在衣物上传时消耗体力值

```javascript
// 在衣物上传前检查并消耗体力值
function uploadClothing() {
  // 先检查用户是否有足够的体力值
  energyManager.consumeEnergyForAction('IMAGE_EXTRACTION')
    .then(result => {
      if (result.success) {
        // 体力值足够，已经扣除，继续上传衣物
        console.log('已消耗', result.consumedEnergy, '点体力值，开始上传衣物');
        // 执行上传逻辑...
      } else {
        // 体力值不足，提示用户
        wx.showModal({
          title: '体力值不足',
          content: '您的体力值不足，无法执行AI抠图操作。请通过观看广告或完成每日任务获取更多体力值。',
          showCancel: false
        });
      }
    })
    .catch(err => {
      console.error('检查体力值失败:', err);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    });
}
```

## 示例：在AI分析衣柜时消耗体力值

```javascript
// 在分析衣柜前检查并消耗体力值
function analyzeWardrobe() {
  // 先检查用户是否有足够的体力值
  energyManager.consumeEnergyForAction('WARDROBE_ANALYSIS')
    .then(result => {
      if (result.success) {
        // 体力值足够，已经扣除，继续分析衣柜
        console.log('已消耗', result.consumedEnergy, '点体力值，开始分析衣柜');
        // 执行分析逻辑...
      } else {
        // 体力值不足，提示用户
        wx.showModal({
          title: '体力值不足',
          content: '您的体力值不足，无法执行AI分析衣柜操作。请通过观看广告或完成每日任务获取更多体力值。',
          showCancel: false
        });
      }
    })
    .catch(err => {
      console.error('检查体力值失败:', err);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    });
}
```
