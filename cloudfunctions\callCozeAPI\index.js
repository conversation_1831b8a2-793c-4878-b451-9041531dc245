// 云函数入口文件
const cloud = require('wx-server-sdk')
const request = require('request-promise')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// Coze API令牌
const COZE_API_TOKEN = 'pat_HWB8Lin7wPFlv2PMLdOya7nqKBSCOlPikPmVr6mOdIU75VvGKaDlcZ0eRRjA9Q7k';

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, imageUrl, workflowId } = event;
  
  // 参数校验
  if (!action) {
    return {
      success: false,
      message: '缺少action参数'
    };
  }
  
  try {
    switch (action) {
      case 'processImage':
        return await processImage(imageUrl, workflowId);
      default:
        return {
          success: false,
          message: `不支持的action: ${action}`
        };
    }
  } catch (error) {
    console.error('云函数执行失败:', error);
    return {
      success: false,
      message: error.message || '云函数执行出错',
      error: error
    };
  }
};

/**
 * 调用Coze API处理图片
 * @param {String} imageUrl - 图片URL
 * @param {String} workflowId - 工作流ID
 * @return {Object} 处理结果
 */
async function processImage(imageUrl, workflowId) {
  if (!imageUrl) {
    return {
      success: false,
      message: '缺少imageUrl参数'
    };
  }
  
  if (!workflowId) {
    return {
      success: false,
      message: '缺少workflowId参数'
    };
  }
  
  try {
    console.log('准备调用Coze API进行图片处理:', imageUrl);
    
    // 构建Coze API请求
    const options = {
      method: 'POST',
      url: 'https://api.coze.cn/v1/workflow/run',
      headers: {
        'Authorization': `Bearer ${COZE_API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: {
        workflow_id: workflowId,
        parameters: {
          input: imageUrl
        }
      },
      json: true // 自动转换为JSON
    };
    
    // 发送请求
    const response = await request(options);
    console.log('Coze API响应:', JSON.stringify(response, null, 2));
    
    // 检查结果
    if (response.code === 0 && response.data) {
      // 检查响应数据格式
      console.log('Coze API返回数据类型:', typeof response.data);
      console.log('Coze API返回数据内容:', response.data);
      
      // 尝试解析数据，以便在日志中更清晰地看到数据结构
      try {
        // 如果是字符串但格式是JSON，则解析出来便于检查
        if (typeof response.data === 'string') {
          const parsedData = JSON.parse(response.data);
          console.log('解析后的Coze API数据:', parsedData);
        }
      } catch (parseError) {
        console.log('响应数据不是有效的JSON格式:', parseError.message);
      }
      
      return {
        success: true,
        data: response.data,
        debug_url: response.debug_url,
        rawResponse: response
      };
    } else {
      return {
        success: false,
        message: response.msg || '调用Coze API失败',
        response: response
      };
    }
  } catch (error) {
    console.error('调用Coze API出错:', error);
    return {
      success: false,
      message: error.message || '调用Coze API出错',
      error: error
    };
  }
} 