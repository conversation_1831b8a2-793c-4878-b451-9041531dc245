# 衣柜助手 - 智能服装管理微信小程序

衣柜助手是一款基于微信小程序的智能服装管理应用，帮助用户轻松管理个人衣物、搭配穿着方案，并通过AI技术提供智能化服务。

## 🌟 核心功能

### 服装管理
- **智能分类**: 自动识别并分类上衣、裤子、裙子、外套、鞋子、配饰等不同类型的服装
- **衣物统计**: 自动计算并展示各类别衣物数量，帮助用户了解衣柜构成
- **时装详情**: 记录每件衣物的详细信息，包括类型、颜色、风格、保暖度等
- **分页浏览**: 支持按类别分页查看所有衣物，操作简单直观
- **衣柜管理**: 支持创建多个衣柜，将衣物分类存放在不同衣柜中
- **筛选功能**: 支持按季节、风格、颜色、存储位置等多维度筛选衣物

### 智能处理
- **背景抠图**: 自动去除衣物照片背景，生成透明背景的衣物图像
- **AI分析**: 利用人工智能分析衣物特征，自动提取关键属性
- **云端存储**: 安全存储用户衣物信息和图片到云端，随时随地访问
- **本地缓存**: 合理利用本地缓存，减少重复数据请求，提升用户体验

### 穿搭管理
- **穿搭库**: 创建和管理不同场合的穿搭组合，包括日常、职业、派对、运动和季节性穿搭
- **OOTD选择**: 通过"今日穿搭"功能，用户可以为当天选择合适的穿搭
- **穿搭记录**: 系统会保存用户的穿搭历史，方便回顾和重复使用成功的搭配
- **穿搭推荐**: 基于用户衣物特征和场合需求，智能推荐合适的穿搭方案

### 农历风水穿搭
- **五行分析**: 根据农历日期、节气和八卦信息分析当前五行状态
- **风水推演**: 基于五行相生相克原理，推荐最适合当日的穿搭方案
- **运势增益**: 为每件推荐衣物标注五行属性和可能带来的运势增益
- **文化传承**: 融合中国传统文化元素，提供有文化内涵的穿搭建议

### 兑换码系统
- **容量扩展**: 通过兑换码增加衣物和穿搭容量上限
- **灵活奖励**: 支持仅增加衣物容量、仅增加穿搭容量或同时增加两种容量
- **兑换历史**: 记录用户的兑换历史，便于追踪容量变化
- **后台管理**: 管理员可以创建、管理兑换码，查看使用情况

### 主题任务系统
- **任务进度跟踪**: 在设置页面中展示用户的主题任务完成进度
- **多主题支持**: 支持多个主题任务，每个主题有多个进度阶段
- **可视化展示**: 图形化展示当前任务状态，显示完成步骤和未完成步骤
- **奖励机制**: 完成任务可获得额外衣物或穿搭容量奖励
- **云端同步**: 任务进度保存在云端，支持多设备同步

### 交互体验
- **卡片滑动**: 流畅的卡片滑动交互，轻松切换不同衣物类别
- **防误触机制**: 智能区分滑动和点击操作，提升用户体验
- **加载优化**: 智能控制加载UI显示，提供流畅的用户体验
- **多视图模式**: 支持网格视图和详细列表视图，满足不同浏览需求

## 🧩 项目结构

```
miniprogram/
├── page/wardrobe/          // 主要功能模块
│   ├── closet/             // 衣柜管理功能
│   │   ├── modules/        // 功能模块
│   │   │   ├── cardManager.js     // 卡片UI管理
│   │   │   ├── dataManager.js     // 数据加载和管理
│   │   │   ├── imageProcessor.js  // 图像处理
│   │   │   ├── userManager.js     // 用户信息管理
│   │   │   └── closetUtils.js     // 通用工具函数
│   ├── filter/             // 筛选功能
│   ├── ootd/               // 今日穿搭功能
│   ├── profile/            // 用户信息
│   ├── calendar/           // 日历搭配
│   └── outfit/             // 穿搭推荐
├── page/settings/          // 设置页面
│   ├── redeem/             // 兑换码功能
│   └── admin/              // 管理员功能
│       └── redeem_codes/   // 兑换码管理
│
cloudfunctions/               // 云函数
├── analyzeClothing/         // 衣物AI分析
├── processClothing/         // 衣物图像处理
├── getOutfitRecommendation/ // 穿搭推荐（支持风水推演）
├── redeemCode/              // 兑换码验证与奖励发放
├── login/                   // 用户登录
├── getOpenId/               // 获取用户OpenID
├── getWeather/              // 获取天气信息
├── getUserTasks/            // 获取用户任务进度
├── updateUserTasks/         // 更新用户任务进度
├── callDeepSeek/            // 通用DeepSeek大模型调用
└── getTempFileURL/          // 获取临时文件URL
```

## 🔧 技术亮点

### 1. 智能衣物分析
- 使用视觉大模型分析衣物图片
- 自动识别衣物类型、颜色、风格和适合场合
- 提取关键特征用于后续智能推荐

### 2. 背景抠图技术
- 使用云端API自动处理衣物照片
- 去除背景，生成专业透明背景图片
- 支持模板参数自定义，优化抠图效果

### 3. 敏感内容过滤机制
- 集成敏感词过滤模块，支持对用户输入的内容进行检查
- 包含政治、暴力、色情等多种类别的敏感词库
- 在保存衣物信息前自动检查，确保内容合规
- 提供敏感词替换功能，支持内容自动过滤

### 4. 农历风水穿搭推演
- 基于大语言模型进行风水穿搭推演
- 结合农历节气、八卦和五行相生相克原理
- 为每件衣物标注五行属性和运势增益效果
- 提供风水学原理解释，增强文化内涵

### 5. 性能优化
- **延迟加载**：仅在需要时加载数据，减少不必要的网络请求
- **条件渲染**：智能控制UI元素渲染，提高页面响应速度
- **缓存策略**：合理利用本地缓存，减少重复数据请求
- **图像优化**：优化图像加载和显示策略，提升用户体验

### 6. 多衣柜管理
- 支持创建和管理多个衣柜
- 可将衣物分配到不同衣柜中
- 支持按衣柜筛选衣物
- 衣柜数据云端同步，支持多设备访问

## 💡 后续开发计划

- [x] 添加智能穿搭推荐功能
- [x] 添加农历风水穿搭推演功能
- [x] 添加敏感内容过滤功能
- [x] 添加兑换码功能，支持扩容衣物和穿搭数量
- [x] 添加主题任务系统，跟踪用户学习进度
- [x] 支持服装季节性标签管理
- [ ] 加入社区功能，支持穿搭分享
- [ ] 扩展服装管理属性，增加更多服装维度
- [ ] 优化小程序性能，提升加载速度
- [ ] 添加更详细的五行分析报告
- [ ] 支持用户手动选择想要增强的运势类型
- [ ] 增加更多传统文化元素，如二十四节气专属穿搭建议

## 📝 技术说明

### 真机与模拟器兼容性
- **本地图片处理**: 支持本地文件系统路径(wxfile://)和远程图片URL(http://, https://)，确保在真机环境和模拟器中均能正常显示图片
- **跨平台缓存**: 图片缓存系统支持各种来源的图片，提高加载速度和离线使用体验
- **多环境适配**: 代码经过优化，可同时在开发者工具模拟器和真机环境中流畅运行

### 兑换码使用指南
1. 在设置页面中点击"兑换码"按钮
2. 输入或粘贴获得的兑换码
3. 点击"兑换"按钮验证兑换码
4. 兑换成功后，衣物容量和/或搭配容量会立即增加
5. 兑换历史会记录所有成功兑换的兑换码信息

### 详细视图模式

在类别页面中，提供了详细视图模式，用户可以在网格视图和详细视图之间切换：

- **网格视图**：以网格形式展示衣物，每个格子显示衣物图片、名称和颜色
- **详细视图**：以列表形式展示衣物，一行一个衣物，显示更多详细信息：
  - 衣物图片
  - 名称
  - 细分类
  - 颜色
  - 价格
  - 购买时间
  - 每次成本（价格 ÷ 穿着次数）

## 🤝 贡献

欢迎通过Issue和Pull Request的方式贡献代码，帮助我们改进项目。
