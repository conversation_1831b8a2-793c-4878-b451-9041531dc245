.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  background-color: #f0f4f3;
  padding: 30rpx;
  padding-top: 0;
  box-sizing: border-box;
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  background-color: #f0f4f3;
  z-index: 1000;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.nav-content {
  display: flex;
  align-items: center;
  height: 90rpx;
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.back-icon {
  font-size: 36rpx;
  color: #2a8c70;
  font-weight: bold;
}

.nav-title {
  flex: 1;
  display: flex;
  justify-content: center;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2a8c70;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.section-subtitle {
  font-size: 30rpx;
  color: #5c7d8d;
  font-weight: 400;
  margin-top: 6rpx;
  letter-spacing: 1rpx;
}

.nav-placeholder {
  width: 60rpx;
}

/* 页面内容区域 */
.page-content {
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 顶部安全区域间隔 */
.status-bar {
  width: 100%;
}

/* 卡片通用阴影 */
.card-shadow {
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
  border-radius: 24rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 上部分卡片容器 */
.top-section {
  background-color: #ffffff;
  margin-bottom: 30rpx;
  position: relative;
  border-top: 8rpx solid #89c3b0;
  margin-top: calc(90rpx + env(safe-area-inset-top, 0px));
}

/* 图表类型选择器样式 */
.chart-selector-container {
  width: 100%;
  white-space: nowrap;
  padding: 20rpx 0;
  background-color: #ffffff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.chart-selector {
  display: inline-flex;
  padding: 0 20rpx;
}

.selector-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  margin: 10rpx 10rpx;
  border-radius: 16rpx;
  background-color: #f0f4f3;
  transition: all 0.3s ease;
  position: relative;
  border: 2rpx solid transparent;
}

.selector-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.selector-text {
  font-size: 24rpx;
  color: #556b68;
  font-weight: 500;
}

.selector-item.active {
  background-color: #e7f2ee;
  color: #2a8c70;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(42, 140, 112, 0.15);
  transform: translateY(-2rpx);
  border: 2rpx solid #89c3b0;
}

.selector-item.active .selector-text {
  color: #2a8c70;
}

/* 图表信息区域 */
.chart-info {
  padding: 30rpx 30rpx 10rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

/* 细分类标题栏 */
.subcategory-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15rpx;
}

.back-button {
  align-self: flex-start;
  display: flex;
  align-items: center;
  background-color: #e7f2ee;
  color: #2a8c70;
  font-size: 26rpx;
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
  margin-bottom: 10rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.button-hover {
  background-color: #d0e8df;
  transform: scale(0.98);
}

.back-icon {
  font-size: 28rpx;
  margin-right: 6rpx;
}

.subcategory-title {
  font-size: 30rpx;
  color: #2a8c70;
  margin-bottom: 10rpx;
}

.chart-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
}

.chart-subtitle {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.count-badge {
  background-color: #e7f2ee;
  color: #2a8c70;
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  display: inline-block;
}

/* 图例区域 */
.chart-legend {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 20rpx;
}

.legend-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  width: 48%;
  padding: 12rpx 16rpx;
  border-radius: 12rpx;
  background-color: #f8fbfa;
  transition: all 0.25s ease;
}

.legend-hover {
  background-color: #e7f2ee;
  transform: translateX(4rpx);
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
}

.legend-text {
  font-size: 26rpx;
  color: #333;
  margin-right: 10rpx;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.legend-count {
  font-size: 26rpx;
  color: #2a8c70;
  font-weight: bold;
  margin-right: 5rpx;
}

.legend-percent {
  font-size: 24rpx;
  color: #999;
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  height: 500rpx;
  padding: 10rpx 30rpx 40rpx;
  box-sizing: border-box;
  position: relative;
  z-index: 10; /* 增加z-index确保图表在上层 */
  touch-action: auto; /* 增强触摸响应 */
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateY(0);
  max-height: 500rpx;
  overflow: hidden;
}

/* 隐藏图表时的样式 */
.chart-container.hide {
  opacity: 0;
  transform: translateY(20rpx);
  max-height: 0;
  padding: 0 30rpx;
  margin: 0;
}

/* 显示图表时的样式 */
.chart-container.show {
  opacity: 1;
  transform: translateY(0);
  max-height: 500rpx;
}

.analysis-chart {
  width: 100%;
  height: 100%;
  display: block; /* 确保是块级元素 */
  touch-action: auto; /* 增强触摸响应 */
}

/* 统计摘要容器样式 */
.summary-container {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  border-top: 8rpx solid #769bbb;
}

.summary-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.summary-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  text-align: center;
  position: relative;
}

.summary-subtitle {
  font-size: 26rpx;
  color: #769bbb;
  text-align: center;
}

.summary-title::after {
  content: '';
  position: absolute;
  bottom: -16rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background-color: #769bbb;
  border-radius: 2rpx;
}

/* 新的弹性布局样式 */
.summary-flex-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 24rpx;
}

.summary-item {
  width: 48%;
  background-color: #f0f6fb;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(118, 155, 187, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background-color: #769bbb;
  opacity: 0.6;
}

.summary-item-hover {
  transform: translateY(-3rpx);
  box-shadow: 0 8rpx 20rpx rgba(118, 155, 187, 0.2);
}

.summary-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.summary-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #3a6b99;
  margin-bottom: 15rpx;
  text-align: center;
  display: flex;
  align-items: center;
}

.unit {
  font-size: 24rpx;
  margin-left: 4rpx;
  font-weight: normal;
}

.summary-label {
  font-size: 24rpx;
  color: #769bbb;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .summary-item {
    width: 48%;
    margin-bottom: 20rpx;
    padding: 20rpx 10rpx;
  }

  .summary-value {
    font-size: 32rpx;
  }

  .summary-label {
    font-size: 22rpx;
  }

  .legend-item {
    width: 100%;
  }
}

/* AI分析按钮 */
.ai-analysis-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 200rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #2a8c70, #89c3b0);
  border-radius: 40rpx;
  box-shadow: 0 6rpx 20rpx rgba(42, 140, 112, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 900;
  transition: all 0.3s;
  animation: pulse 2s infinite;
  color: #ffffff;
}

.ai-analysis-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(42, 140, 112, 0.2);
}

.ai-icon {
  font-size: 40rpx;
  margin-right: 10rpx;
}

.ai-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 加载状态 */
.ai-analysis-btn.loading {
  background: linear-gradient(135deg, #5c7d8d, #89a3b0);
  animation: none;
}

.ai-loading-icon {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #ffffff;
  border-radius: 50%;
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* AI分析结果面板 */
.ai-analysis-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.ai-analysis-panel.show {
  transform: translateX(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);
  background-color: #2a8c70;
  color: #ffffff;
  padding-top: calc(env(safe-area-inset-top) + 30rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.panel-header::after {
  content: '';
  position: absolute;
  top: -40rpx;
  right: -40rpx;
  width: 200rpx;
  height: 200rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.panel-title {
  padding-top: 50rpx;
  font-size: 36rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.panel-title::before {
  margin-right: 12rpx;
  font-size: 40rpx;
}

.panel-close {
  font-size: 48rpx;
  line-height: 1;
  padding: 10rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.panel-close:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.panel-content {
  flex: 1;
  padding: 30rpx 20rpx;
  background-color: #f5f8f7;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: calc(env(safe-area-inset-bottom) + 60rpx);
  box-sizing: border-box;
  width: 100%;
}

/* 分析部分样式 */
.analysis-section {
  margin-bottom: 50rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.12);
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  transform: translateZ(0);
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
  width: 100%;
  box-sizing: border-box;
  margin-left: 0;
  margin-right: 0;
}

/* 卡片右上角标签 */
.analysis-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.03);
  clip-path: polygon(0 0, 100% 0, 100% 100%);
}

/* 不同模块的卡片顶部标识 */
.overview-section {
  border-top: 8rpx solid #2a8c70;
  box-shadow: 0 12rpx 30rpx rgba(42, 140, 112, 0.10);
  position: relative;
}

/* 添加总体概览的堆叠效果 */
.overview-section::after {
  content: '';
  position: absolute;
  top: -8rpx;
  left: 0;
  right: 0;
  height: 8rpx;
  background-color: rgba(42, 140, 112, 0.7);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  z-index: -1;
  transform: translateY(-100%);
  box-shadow: 0 -2rpx 5rpx rgba(42, 140, 112, 0.2);
}

.analysis-section:nth-child(3) {
  border-top: 8rpx solid #4285F4; /* 模块1：衣橱基础概览 */
  box-shadow: 0 12rpx 30rpx rgba(66, 133, 244, 0.10);
  position: relative;
}

/* 添加衣橱基础概览的堆叠效果 */
.analysis-section:nth-child(3)::after {
  content: '';
  position: absolute;
  top: -8rpx;
  left: 0;
  right: 0;
  height: 8rpx;
  background-color: rgba(66, 133, 244, 0.7);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  z-index: -1;
  transform: translateY(-100%);
  box-shadow: 0 -2rpx 5rpx rgba(66, 133, 244, 0.2);
}

.analysis-section:nth-child(4) {
  border-top: 8rpx solid #EA4335; /* 模块2：使用效率分析 */
  box-shadow: 0 12rpx 30rpx rgba(234, 67, 53, 0.10);
  position: relative;
}

/* 添加明确的使用效率分析卡片堆叠效果 */
.analysis-section:nth-child(4)::after {
  content: '';
  position: absolute;
  top: -8rpx;
  left: 0;
  right: 0;
  height: 8rpx;
  background-color: rgba(234, 67, 53, 0.7);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  z-index: -1;
  transform: translateY(-100%);
  box-shadow: 0 -2rpx 5rpx rgba(234, 67, 53, 0.2);
}

.analysis-section:nth-child(5) {
  border-top: 8rpx solid #FBBC05; /* 模块3：消费行为洞察 */
  box-shadow: 0 12rpx 30rpx rgba(251, 188, 5, 0.10);
}

.analysis-section:nth-child(6) {
  border-top: 8rpx solid #34A853; /* 模块4：搭配优化建议 */
  box-shadow: 0 12rpx 30rpx rgba(52, 168, 83, 0.10);
}

.analysis-section:nth-child(7) {
  border-top: 8rpx solid #8D6E63; /* 模块5：可持续诊断 */
  box-shadow: 0 12rpx 30rpx rgba(141, 110, 99, 0.10);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f4f3;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60rpx;
  height: 4rpx;
  background-color: #2a8c70;
  border-radius: 2rpx;
}

.section-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  background-color: #f8fbfa;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.analysis-item {
  margin-bottom: 24rpx;
  padding: 16rpx;
  background-color: #fafcfb;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
}

.analysis-item:hover, .analysis-item:active {
  background-color: #f0f7f4;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);
}

.item-label {
  font-size: 28rpx;
  color: #5c7d8d;
  margin-bottom: 10rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  padding-left: 10rpx;
  border-left: 4rpx solid #e7f2ee;
}

/* 警告样式 */
.item-label.warning, .item-value.warning {
  color: #e75e76;
  font-weight: 600;
}

.analysis-item .item-label.warning + .item-value.warning {
  background-color: #fff5f6;
  padding: 12rpx 16rpx;
  border-left: 4rpx solid #e75e76;
}

/* 子区域样式 */
.analysis-subsection {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  background-color: #f8fbfa;
  border-radius: 16rpx;
  padding: 20rpx 10rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.subsection-title {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  font-weight: 600;
  color: #3a6b99;
  margin-bottom: 16rpx;
  text-align: center;
  padding-bottom: 10rpx;
  border-bottom: 1rpx dashed rgba(128, 128, 128, 0.2);
}

/* 数据表格样式 */
.data-table {
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  font-size: 20rpx;
  border: 1rpx solid #e7f2ee;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.06);
  margin-bottom: 16rpx;
  table-layout: fixed;
  box-sizing: border-box;
  margin-left: 0;
  margin-right: 0;
}

.table-header {
  background-color: #e7f2ee;
  color: #2a8c70;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
  font-size: 20rpx;
  min-height: 48rpx;
  line-height: 1;
}

.table-row {
  border-bottom: 1rpx solid #f0f4f3;
  transition: background-color 0.2s ease;
  min-height: 52rpx;
  line-height: 1;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover, .table-row:active {
  background-color: #f9f9f9;
}

.th, .td {
  flex: 1;
  padding: 10rpx 2rpx;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  box-sizing: border-box;
  font-size: 20rpx;
  line-height: 1.1;
}

/* 表格列宽调整 - 优化以确保内容完全显示 */
.table-header .th:nth-child(1),
.table-row .td:nth-child(1) {
  flex: 0.8;
  padding-left: 6rpx;
}

.table-header .th:nth-child(2),
.table-row .td:nth-child(2) {
  flex: 0.7;
}

.table-header .th:nth-child(3),
.table-row .td:nth-child(3) {
  flex: 0.6;
}

/* 确保最后一列(单次成本)有足够空间 */
.table-header .th:last-child,
.table-row .td:last-child {
  flex: 1.2;
  padding-right: 6rpx;
  text-align: right;
}

/* 为高亮列提供更多空间 */
.table-header .th.highlight-column,
.table-row .td.highlight-column {
  flex: 1.2;
  font-size: 20rpx;
  overflow: visible;
  padding-right: 6rpx;
}

/* 小屏幕适配 */
@media screen and (max-width: 375px) {
  .th, .td {
    font-size: 18rpx;
    padding: 10rpx 2rpx;
  }

  .table-header .th.highlight-column,
  .table-row .td.highlight-column {
    font-size: 18rpx;
  }
}

/* 单次成本高亮列样式 */
.highlight-column {
  background-color: #f8eee3;
  color: #e26432;
  font-weight: bold;
}

.th.highlight-column {
  background-color: #ffd2b3;
  color: #c24e1b;
}

/* 成本效益表格自定义样式 */
.cost-table .table-row:nth-child(1) .highlight-column {
  background-color: #e7f2ee;
  color: #2a8c70;
}

.cost-table .table-row:nth-child(2) .highlight-column {
  background-color: #ebf2e7;
  color: #4f9a6c;
}

/* 浪费表格自定义样式 */
.waste-table .table-row:nth-child(1) .highlight-column {
  background-color: #fce5e5;
  color: #d84545;
}

.waste-table .table-row:nth-child(2) .highlight-column {
  background-color: #fceaea;
  color: #d06868;
}

/* 成本解释说明样式 */
.cost-explanation, .waste-explanation, .value-explanation, .elimination-summary {
  font-size: 24rpx;
  color: #777;
  text-align: center;
  margin-top: 16rpx;
  padding: 12rpx 16rpx;
  background-color: #f8fbfa;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  position: relative;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.cost-explanation {
  border-left: 4rpx solid #89c3b0;
}

.waste-explanation {
  border-left: 4rpx solid #e78a8a;
}

.value-explanation {
  border-left: 4rpx solid #9e9e9e;
}

/* 激活卡片样式 */
.activation-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 16rpx;
}

.activation-card {
  background-color: #f8fbfa;
  border-radius: 16rpx;
  padding: 20rpx;
  border-left: 6rpx solid #89c3b0;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.activation-card:hover, .activation-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 28rpx rgba(0, 0, 0, 0.12);
}

.activation-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: #89c3b0;
  opacity: 0.1;
  border-bottom-left-radius: 50%;
}

.activation-item {
  font-size: 30rpx;
  font-weight: 600;
  color: #2a8c70;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.activation-item::before {
  content: '👕';
  margin-right: 8rpx;
  font-size: 32rpx;
}

.activation-formula {
  font-size: 26rpx;
  color: #333;
  padding: 10rpx 16rpx;
  background-color: #e7f2ee;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
  display: inline-block;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}

.activation-desc {
  font-size: 26rpx;
  color: #5c7d8d;
  line-height: 1.5;
  padding: 8rpx 0;
}

.analysis-list {
  margin-top: 16rpx;
}

.list-item {
  display: flex;
  margin-bottom: 16rpx;
}

.list-icon {
  font-size: 28rpx;
  color: #2a8c70;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.list-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  flex: 1;
}

/* 穿搭建议卡片 */
.outfit-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.outfit-card {
  background-color: #f0f4f3;
  border-radius: 16rpx;
  padding: 20rpx;
  border-left: 8rpx solid #89c3b0;
}

.outfit-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #2a8c70;
  margin-bottom: 8rpx;
}

.outfit-occasion {
  font-size: 26rpx;
  color: #5c7d8d;
  margin-bottom: 16rpx;
}

.outfit-items {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.outfit-item {
  background-color: #e7f2ee;
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #2a8c70;
}

.outfit-desc {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.5;
}

/* 淘汰建议清单样式 */
.elimination-table {
  border: 1rpx solid #fcd6d6;
}

.elimination-table .table-header {
  background-color: #fce5e5;
  color: #e26464;
}

.elimination-reason {
  flex: 1.1 !important;
  text-align: left;
  padding-left: 6rpx;
  max-width: 35%;
}

.elimination-summary {
  font-size: 24rpx;
  text-align: center;
  margin-top: 16rpx;
  padding: 12rpx;
  background-color: #fef8f8;
  border-radius: 8rpx;
  color: #c25151;
  border-left: 4rpx solid #e78a8a;
}

.title-icon {
  margin-right: 8rpx;
  display: inline-block;
  vertical-align: middle;
}

/* 价值系数表格样式 */
.value-table {
  border: 1rpx solid #e0e0e0;
}

.value-column {
  font-weight: bold;
  flex: 0.9 !important;
}

.high-value .value-column {
  color: #4CAF50;
}

.high-value .table-header .value-column {
  background-color: #e8f5e9;
}

.low-value .value-column {
  color: #FF9800;
}

.low-value .table-header .value-column {
  background-color: #fff3e0;
}

.value-explanation {
  font-size: 24rpx;
  text-align: center;
  margin-top: 16rpx;
  padding: 12rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  color: #555;
  border-left: 4rpx solid #9e9e9e;
}

/* 为表格添加悬停效果 */
.table-row:hover, .table-row:active {
  background-color: #f9f9f9;
}

/* 表格内数据的文本对齐 */
.td:nth-child(1) {
  text-align: left;
  padding-left: 16rpx;
}

.td:nth-child(3),
.td:nth-child(4),
.td:nth-child(5) {
  text-align: right;
  padding-right: 16rpx;
}

/* 表格列宽调整 */
.table-header .th:nth-child(1),
.table-row .td:nth-child(1) {
  flex: 0.9;
}

.table-header .th:nth-child(2),
.table-row .td:nth-child(2) {
  flex: 0.9;
}

.table-header .th:nth-child(3),
.table-row .td:nth-child(3) {
  flex: 0.8;
}

.table-header .th:nth-child(4),
.table-row .td:nth-child(4) {
  flex: 0.8;
}

.table-header .th:nth-child(5),
.table-row .td:nth-child(5) {
  flex: 1;
}

/* 模块图标样式 */
.module-icon-overview {
  background-color: #e8f5e9;
  color: #2a8c70;
}

.module-icon-1 {
  background-color: #e8f4fc;
  color: #4285F4;
}

.module-icon-2 {
  background-color: #fce9e8;
  color: #EA4335;
}

.module-icon-3 {
  background-color: #fff8e6;
  color: #FBBC05;
}

.module-icon-4 {
  background-color: #e8f5e9;
  color: #34A853;
}

.module-icon-5 {
  background-color: #f0ebe7;
  color: #8D6E63;
}

/* 模块样式增强 */
.module-1 {
  transform: translateZ(0);
}

.module-2 {
  transform: translateZ(0);
}

.module-3 {
  transform: translateZ(0);
}

.module-4 {
  transform: translateZ(0);
}

.module-5 {
  transform: translateZ(0);
}

/* 卡片悬停效果 */
.analysis-section:hover {
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.16);
  transform: translateY(-4rpx);
}

/* 子标题图标通用样式 */
.title-icon {
  margin-right: 8rpx;
  display: inline-block;
  vertical-align: middle;
  font-size: 28rpx;
}

/* 卡片模块通用样式 */
.card-module {
  position: relative;
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-module::after {
  content: '';
  position: absolute;
  bottom: -4rpx;
  left: 0;
  right: 0;
  height: 10rpx;
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
  z-index: -1;
  background: inherit;
  opacity: 0.2;
  filter: blur(4rpx);
}

/* 卡片标题下方分隔线 */
.card-module .section-header {
  position: relative;
  margin-bottom: 25rpx;
}

.card-module .section-header::after {
  height: 5rpx;
  width: 80rpx;
  border-radius: 3rpx;
}

.overview-section .section-header::after { background-color: #2a8c70; }
.module-1 .section-header::after { background-color: #4285F4; }
.module-2 .section-header::after { background-color: #EA4335; }
.module-3 .section-header::after { background-color: #FBBC05; }
.module-4 .section-header::after { background-color: #34A853; }
.module-5 .section-header::after { background-color: #8D6E63; }

/* 模块图标样式增强 */
.section-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  background-color: #f8fbfa;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 适配性更强的表格布局 */
.table-row, .table-header {
  display: flex;
  width: 100%;
  min-width: 100%;
  box-sizing: border-box;
  flex-wrap: nowrap;
}

/* 确保单元格内容始终可见 */
.table-header .th.highlight-column,
.table-row .td.highlight-column {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 特别处理最后一列（通常是操作列或值列） */
.table-header .th:last-child,
.table-row .td:last-child {
  padding-right: 6rpx;
}

/* 小屏幕适配 */
@media screen and (max-width: 375px) {
  .th, .td {
    font-size: 20rpx;
    padding: 12rpx 4rpx;
  }

  .section-title {
    font-size: 32rpx;
  }

  .analysis-section {
    padding: 25rpx 20rpx;
  }

  .analysis-subsection {
    padding: 16rpx 12rpx;
  }
}

/* 筛选按钮 */
.filter-btn {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  width: 90rpx;
  height: 90rpx;
  background: linear-gradient(135deg, #2a8c70, #89c3b0);
  border-radius: 50%;
  box-shadow: 0 6rpx 20rpx rgba(42, 140, 112, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 900;
  transition: all 0.3s;
  animation: pulse 2s infinite;
  color: #ffffff;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 6rpx 20rpx rgba(42, 140, 112, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 8rpx 25rpx rgba(42, 140, 112, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 6rpx 20rpx rgba(42, 140, 112, 0.3);
  }
}

.filter-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(42, 140, 112, 0.2);
  animation: none;
}

.filter-btn-icon {
  font-size: 40rpx;
}

/* 筛选面板背景遮罩 */
.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 898;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* 筛选面板 */
.filter-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 650rpx;
  max-height: 0;
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
  z-index: 899;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.9);
  visibility: hidden;
}

.filter-panel.show {
  max-height: 800rpx;
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  visibility: visible;
}

.filter-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg, #2a8c70, #89c3b0);
  color: #ffffff;
  position: relative;
}

.filter-panel-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.3), transparent);
}

.filter-panel-header text {
  font-size: 34rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.filter-panel-close {
  font-size: 40rpx;
  line-height: 1;
  padding: 6rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-panel-close:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.filter-panel-content {
  padding: 30rpx;
  max-height: 700rpx;
  overflow-y: auto;
  background-color: #f8fbfa;
}

/* 筛选器容器样式 */
.filter-container {
  width: 690rpx;
  margin: 20rpx auto;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 50rpx;
}

/* 下拉选择器样式 - 美化版 */
.wardrobe-picker-container {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 28rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(42, 140, 112, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-left: 6rpx solid #89c3b0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  margin-bottom: 24rpx;
}

.wardrobe-picker-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(137, 195, 176, 0.05), transparent);
  pointer-events: none;
}

.wardrobe-picker-container:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(42, 140, 112, 0.05);
  background-color: #f9fffc;
}

.wardrobe-picker-label {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #2a8c70;
  font-weight: 600;
}

.wardrobe-icon {
  margin-right: 12rpx;
  font-size: 36rpx;
  background-color: #e7f2ee;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.wardrobe-picker-selected {
  display: flex;
  align-items: center;
  background-color: #f0f6f3;
  padding: 16rpx 30rpx;
  border-radius: 12rpx;
  min-width: 260rpx;
  justify-content: space-between;
  border: 1rpx solid rgba(42, 140, 112, 0.1);
  transition: all 0.3s ease;
}

.wardrobe-picker-selected:active {
  background-color: #e7f2ee;
}

.picker-arrow {
  font-size: 24rpx;
  color: #2a8c70;
  margin-left: 20rpx;
  transition: transform 0.3s ease;
}

/* 日期筛选器样式 */
.date-filter-container {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 28rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(42, 140, 112, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-left: 6rpx solid #769bbb; /* 不同的边框颜色区分不同筛选器 */
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  margin-bottom: 24rpx;
}

.date-filter-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(118, 155, 187, 0.05), transparent);
  pointer-events: none;
}

.date-filter-container:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(118, 155, 187, 0.05);
  background-color: #f9faff;
}

.date-filter-label {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #3a6b99;
  font-weight: 600;
}

.date-filter-icon {
  margin-right: 12rpx;
  font-size: 36rpx;
  background-color: #f0f6fb;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.date-filter-selected {
  display: flex;
  align-items: center;
  background-color: #f0f6fb;
  padding: 16rpx 30rpx;
  border-radius: 12rpx;
  min-width: 260rpx;
  justify-content: space-between;
  border: 1rpx solid rgba(58, 107, 153, 0.1);
  transition: all 0.3s ease;
}

.date-filter-selected:active,
.date-filter-selected.filter-open {
  background-color: #e7f0f9;
}

.filter-arrow {
  font-size: 24rpx;
  color: #3a6b99;
  margin-left: 20rpx;
  transition: transform 0.3s ease;
}

.filter-open .filter-arrow {
  transform: rotate(180deg);
}

/* 日期筛选面板样式 */
.date-filter-panel {
  width: 100%;
  margin: 0 auto 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: slideDown 0.3s ease;
  z-index: 10;
  position: relative;
  border: 1rpx solid rgba(118, 155, 187, 0.1);
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.date-filter-options {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  gap: 24rpx;
}

.date-filter-option {
  flex: 1 0 calc(33.33% - 24rpx);
  min-width: 180rpx;
  padding: 20rpx 24rpx;
  background-color: #f8fbfa;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.date-filter-option.active {
  background-color: #e7f0f9;
  color: #3a6b99;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(58, 107, 153, 0.1);
  border-color: rgba(58, 107, 153, 0.2);
}

.date-filter-actions {
  padding: 20rpx;
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #f0f4f3;
}

.date-filter-clear {
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  color: #999;
  background-color: #f8f8f8;
  border-radius: 30rpx;
  transition: all 0.2s ease;
}

.date-filter-clear:active {
  background-color: #f0f0f0;
  color: #666;
}

/* 日期选择器模态框样式 */
.date-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.date-picker-content {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 36rpx rgba(0, 0, 0, 0.15);
  animation: scaleIn 0.3s ease;
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.date-picker-header {
  padding: 30rpx;
  background-color: #3a6b99;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-picker-close {
  font-size: 40rpx;
  line-height: 1;
  padding: 10rpx;
}

.date-range-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  gap: 20rpx;
}

.date-range-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.date-range-label {
  font-size: 28rpx;
  color: #3a6b99;
  font-weight: 500;
  padding-left: 10rpx;
}

.date-picker-field {
  padding: 20rpx 30rpx;
  font-size: 30rpx;
  text-align: center;
  border: 1rpx solid #f0f4f3;
  border-radius: 10rpx;
  background-color: #f8f8f8;
}

.date-picker-actions {
  display: flex;
  border-top: 1rpx solid #f0f4f3;
}

.date-picker-action {
  flex: 1;
  padding: 24rpx 0;
  text-align: center;
  font-size: 30rpx;
  color: #666;
  transition: all 0.2s ease;
}

.date-picker-action.primary {
  color: #3a6b99;
  font-weight: 500;
  background-color: #f0f6fb;
}

.date-picker-action:active {
  background-color: #f8f8f8;
}

.date-picker-action.primary:active {
  background-color: #e7f0f9;
}

/* 保留徽章和副标题样式 - 美化版 */
.wardrobe-badge {
  margin-left: 10rpx;
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  background-color: #89c3b0;
  color: #fff;
  border-radius: 20rpx;
  vertical-align: middle;
  box-shadow: 0 2rpx 6rpx rgba(42, 140, 112, 0.2);
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  max-width: 180rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}

.wardrobe-subtitle {
  display: block;
  font-size: 24rpx;
  color: #2a8c70;
  margin-top: 8rpx;
  font-weight: 500;
  letter-spacing: 1rpx;
  white-space: nowrap;
  text-align: center;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 选择器打开时的样式效果 */
.picker-open .picker-arrow {
  transform: rotate(180deg);
}

.picker-open {
  background-color: #e7f2ee;
  border-color: rgba(42, 140, 112, 0.2);
}

/* 原有样式保留 */
page {
  background-color: #f6f6f6;
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f6f6f6;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}

/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.nav-content {
  display: flex;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: #333;
}

.nav-title {
  flex: 1;
  text-align: center;
}

.title-container {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

.nav-placeholder {
  width: 60rpx;
}

/* 正文内容 */
.page-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 20rpx 180rpx;
  box-sizing: border-box;
}

/* 卡片统一阴影效果 */
.card-shadow {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border-radius: 16rpx;
}

/* 顶部图表区域 */
.top-section {
  width: 710rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-top: 20rpx;
  overflow: hidden;
  padding-bottom: 30rpx;
}

/* 图表选择器样式 */
.chart-selector-container {
  width: 100%;
  padding: 20rpx 0;
  background-color: #f8f8f8;
  overflow: hidden;
}

.chart-selector {
  display: flex;
  padding: 0 20rpx;
  white-space: nowrap;
}

.selector-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 30rpx;
  margin: 0 10rpx;
  border-radius: 40rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.selector-item.active {
  background-color: #6fb1bd;
}

.selector-icon {
  font-size: 36rpx;
  margin-bottom: 6rpx;
}

.selector-text {
  font-size: 24rpx;
  color: #666;
}

.selector-item.active .selector-text {
  color: #ffffff;
  font-weight: 500;
}

/* 图表信息区域 */
.chart-info {
  padding: 20rpx 30rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.chart-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.count-badge {
  background-color: #f0f0f0;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

/* 细分类相关样式 */
.subcategory-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.back-button {
  display: flex;
  align-items: center;
  padding: 8rpx 20rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
}

.back-button .back-icon {
  font-size: 24rpx;
  margin-right: 4rpx;
}

.button-hover {
  opacity: 0.8;
}

.subcategory-title {
  font-size: 28rpx;
  color: #6fb1bd;
  font-weight: bold;
}

/* 图例样式 */
.chart-legend {
  margin-top: 20rpx;
}

.legend-row {
  display: flex;
  margin-bottom: 20rpx;
}

.legend-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 10rpx 16rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.legend-item:last-child {
  margin-right: 0;
}

.legend-hover {
  background-color: #f0f0f0;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #333;
  max-width: 120rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10rpx;
}

.legend-count {
  font-size: 22rpx;
  color: #666;
  margin-right: 6rpx;
}

.legend-percent {
  font-size: 22rpx;
  color: #999;
}

/* 图表容器 */
.chart-container {
  width: 100%;
  height: 500rpx;
  box-sizing: border-box;
  margin-top: 20rpx;
}

.analysis-chart {
  width: 100%;
  height: 100%;
}

/* 总览区域样式 */
.summary-container {
  width: 710rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-top: 30rpx;
  padding: 30rpx;
  box-sizing: border-box;
}

.summary-header {
  margin-bottom: 30rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.summary-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.summary-flex-container {
  display: flex;
  flex-direction: column;
}

.summary-row {
  display: flex;
  margin-bottom: 30rpx;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-item {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s;
}

.summary-item:last-child {
  margin-right: 0;
}

.summary-item-hover {
  background-color: #f0f0f0;
}

.summary-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.summary-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
}

.unit {
  font-size: 24rpx;
  font-weight: normal;
  margin-left: 4rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #999;
}

/* 样式类图标 */
.clothes-icon, .brand-icon {
  color: #6fb1bd;
}

.price-icon, .avg-icon {
  color: #f3a54a;
}

.color-icon, .date-icon {
  color: #9b95c9;
}

.most-icon, .least-icon {
  color: #e75e76;
}

/* AI分析按钮 */
.ai-analysis-btn {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 320rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #6fb1bd, #9b95c9);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(111, 177, 189, 0.3);
  z-index: 100;
}

.ai-text {
  color: #ffffff;
  font-size: 30rpx;
  font-weight: 500;
}

/* AI分析加载状态 */
.ai-analysis-btn.loading {
  background: linear-gradient(135deg, #6fb1bd99, #9b95c999);
}

.ai-loading-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}