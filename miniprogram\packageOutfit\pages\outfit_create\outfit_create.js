// page/wardrobe/outfit/outfit_create/outfit_create.js
const colors = require('../../../../util/colors');

// 导入模块
const userManager = require('./modules/userManager');
const clothesManager = require('./modules/clothesManager');
const imageManager = require('./modules/imageManager');
const canvasManager = require('./modules/canvasManager');
const outfitManager = require('./modules/outfitManager');
// 导入本地图片缓存模块
const localImageCache = require('../../outfit/modules/localImageCache');

Page({
  data: {
    // 风格切换设置
    themeStyle: 'autumn', // 默认为秋季风格，可选值：'autumn'或'pinkBlue'

    // 使用全局颜色配置
    colors: {
      darkBrown: "#442D1C",     // 深棕色 Cowhide Cocoa
      spicedWine: "#74301C",    // 红棕色 Spiced Wine
      toastedCaramel: "#84592B", // 焦糖色 Toasted Caramel
      oliveHarvest: "#9D9167",   // 橄榄色 Olive Harvest
      goldenBatter: "#E8D1A7",   // 金黄色 Golden Batter
    },

    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',      // 深粉色
      pinkMedium: '#EEA0B2',    // 中粉色
      pinkLight: '#F9C9D6',     // 浅粉色
      blueLight: '#CBE0F9',     // 浅蓝色
      blueMedium: '#97C8E5',    // 中蓝色
      blueDark: '#5EA0D0',      // 深蓝色
    },

    // 衣物数据
    clothes: [],                // 所有衣物
    filteredClothes: [],        // 按类别筛选后的衣物
    currentCategory: null,      // 当前选中的类别
    userOpenId: '',             // 用户openid

    // 画布相关
    canvasItems: [],            // 画布上的衣物项
    canvasWidth: 600,           // 画布宽度
    canvasHeight: 800,          // 画布高度
    activeItemId: null,         // 当前选中的画布项目ID

    // 触摸状态
    touchStartX: 0,             // 触摸开始的X坐标
    touchStartY: 0,             // 触摸开始的Y坐标
    itemStartX: 0,              // 项目触摸开始时的X位置
    itemStartY: 0,              // 项目触摸开始时的Y位置
    isMoving: false,            // 是否正在移动

    // 手势相关参数
    fingerDistance: 0,         // 两指之间的距离
    initialFingerDistance: 0,  // 初始两指之间的距离
    initialItemWidth: 0,       // 初始项目宽度
    initialItemHeight: 0,      // 初始项目高度
    initialRotation: 0,        // 初始旋转角度
    previousAngle: 0,          // 前一次触摸的角度
    isScaling: false,          // 是否正在缩放
    isRotating: false,         // 是否正在旋转
    isSingleTouch: true,       // 是否单指触摸

    nextId: 1,                  // 下一个画布项的ID

    // 页面状态
    isLoading: true,            // 是否正在加载
    isSaving: false,            // 是否正在保存
    outfitName: "我的搭配",      // 搭配名称
    hasCustomName: false,  // 添加跟踪属性：用户是否自定义了名称

    // 穿搭类型选择
    outfitCategory: 'daily',    // 默认为日常穿搭
    outfitCategoryOptions: [    // 穿搭类型选项
      { value: 'daily', name: '日常穿搭', icon: '👕' },
      { value: 'work', name: '职业穿搭', icon: '👔' },
      { value: 'party', name: '派对穿搭', icon: '👗' },
      { value: 'sport', name: '运动穿搭', icon: '🏃' },
      { value: 'seasonal', name: '季节穿搭', icon: '🍂' }
    ],
    customCategoryOptions: [],  // 用户自定义的穿搭类型选项
    newCustomCategoryName: '',  // 新自定义类型名称
    showCategoryPicker: false,  // 是否显示类型选择器
    currentCategoryIcon: '👕',   // 当前选中的类型图标
    currentCategoryName: '日常穿搭', // 当前选中的类型名称

    // 定义衣物类别 - 初始只有全部类别，其他类别将动态加载
    categories: [
      { id: 0, name: '全部', icon: '全', count: 0 }
    ],

    // 左侧面板相关
    closetPanelWidth: 0,         // 左侧面板宽度
  },

  onLoad: function(options) {
    console.log('outfit_create 页面 onLoad 开始', options);

    // 初始化云环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-3gi97kso9ab01185',
        traceUser: true,
      });
    }

    // 初始化本地图片缓存
    localImageCache.ensureCacheDir();

    // 清除过期的缓存
    localImageCache.clearExpiredCache();

    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });
      // 应用主题样式
      this.applyThemeStyle(savedTheme);
    }

    // 加载用户自定义的穿搭类型
    this.loadCustomCategories();

    // 检查是否有传入的AI推荐数据
    if (options && options.fromAI === 'true') {
      // 记录这是一个AI推荐来源的创建
      this.setData({
        isFromAI: true
      });

      // 如果有传入的搭配名称
      if (options.name) {
        this.setData({
          outfitName: decodeURIComponent(options.name),
          hasCustomName: true
        });
      }

      // 如果有传入的搭配类型
      if (options.category) {
        // 检查传入的类型是否有效
        const validCategory = this.data.outfitCategoryOptions.some(option => option.value === options.category);
        if (validCategory) {
          this.setData({
            outfitCategory: options.category
          });
        }
      }

      // 如果有传入的搭配项目
      if (options.items) {
        try {
          const items = JSON.parse(decodeURIComponent(options.items));
          console.log('接收到AI推荐的搭配项目:', items);

          // 预处理这些项目，稍后加载到画布上
          this.aiRecommendedItems = items;
        } catch (e) {
          console.error('解析AI推荐项目失败:', e);
        }
      }
    } else {
      // 检查是否有传入的穿搭类型
      if (options && options.category) {
        // 检查传入的类型是否有效
        const validCategory = this.data.outfitCategoryOptions.some(option => option.value === options.category);
        if (validCategory) {
          this.setData({
            outfitCategory: options.category
          });

          // 根据类型设置默认搭配名称
          const categoryOption = this.data.outfitCategoryOptions.find(option => option.value === options.category);
          if (categoryOption) {
            this.setData({
              outfitName: `我的${categoryOption.name}`
            });
          }
        }
      }
    }

    // 获取屏幕尺寸来设置画布大小
    const systemInfo = wx.getSystemInfoSync();
    const canvasWidth = systemInfo.windowWidth * 0.9; // 90%屏幕宽度
    const canvasHeight = systemInfo.windowHeight * 0.6; // 60%屏幕高度

    // 计算左侧面板宽度，用于优化分类布局
    const closetPanelWidth = systemInfo.windowWidth * 0.4; // 40%屏幕宽度

    this.setData({
      canvasWidth,
      canvasHeight,
      closetPanelWidth,
      // 默认不选择任何类别，显示全部衣物
      currentCategory: null
    });

    console.log('页面初始化完成，屏幕宽度:', systemInfo.windowWidth, '左侧面板宽度:', closetPanelWidth);

    // 获取用户OpenID并加载衣物
    this.getUserOpenIdAndLoadClothes();

    // 更新当前类型信息
    this.updateCurrentCategoryInfo();

    console.log('outfit_create 页面 onLoad 完成');
  },

  // 获取用户OpenID并加载衣物数据
  getUserOpenIdAndLoadClothes: function() {
    wx.showLoading({
      title: '加载中...',
    });

    // 先尝试从本地缓存获取OpenID
    const cachedOpenId = wx.getStorageSync('openid');
    if (cachedOpenId) {
      console.log('从本地缓存获取OpenID:', cachedOpenId);
      this.setData({
        userOpenId: cachedOpenId
      });

      // 加载衣物数据
      this.loadUserClothes(cachedOpenId);
      return;
    }

    userManager.getUserOpenId()
      .then(openid => {
        console.log('成功获取用户OpenID:', openid);
        this.setData({
          userOpenId: openid
        });

        // 加载衣物数据
        return this.loadUserClothes(openid);
      })
      .catch(err => {
        console.error('获取OpenID失败:', err);
        this.useTestData();
        wx.hideLoading();
      });
  },

  // 加载用户衣物数据
  loadUserClothes: function(openid) {
    console.log('[outfit_create] 开始加载用户衣物数据, 用户ID:', openid);

    wx.showLoading({
      title: '加载衣物...',
      mask: true
    });

    // 先检查是否有wardrobe模块的缓存数据
    const userCacheKey = `user_clothes_cache_${openid}`;
    const userCachedData = wx.getStorageSync(userCacheKey);

    // 如果存在wardrobe模块的缓存数据，优先使用它来更新当前模块的缓存
    if (userCachedData && userCachedData.clothes) {
      console.log('[outfit_create] 发现wardrobe模块缓存数据，更新本模块缓存');

      // 从clothes数据生成categoryData（如果userCachedData中没有）
      let categoryData = userCachedData.categoryData;
      if (!categoryData) {
        console.log('[outfit_create] wardrobe缓存中没有categoryData，从clothes数据生成');
        // 生成categoryData - 每个衣物的_id和category作为单独记录
        categoryData = userCachedData.clothes.map(item => {
          return {
            _id: item._id,
            category: item.category
          };
        });

        console.log('[outfit_create] 已从clothes数据生成categoryData:',
          '记录数量:', categoryData.length);
      }

      // 更新当前模块的缓存
      const outfitCacheKey = `outfit_create_clothes_${openid}`;
      wx.setStorageSync(outfitCacheKey, {
        clothes: userCachedData.clothes,
        categoryData: categoryData,
        timestamp: Date.now()
      });

      console.log('[outfit_create] 已使用wardrobe模块缓存数据更新本模块缓存');
    }

    // 再检查是否有最近的本地缓存数据
    const cacheKey = `outfit_create_clothes_${openid}`;
    const cachedData = wx.getStorageSync(cacheKey);

    if (cachedData && cachedData.clothes && cachedData.timestamp) {
      // 检查缓存是否还在有效期内（5分钟）
      const now = Date.now();
      if (now - cachedData.timestamp < 5 * 60 * 1000) {
        console.log('[outfit_create] 使用本地缓存的衣物数据, 数量:', cachedData.clothes.length);

        const { clothes, categoryData } = cachedData;

        // 从衣物数据中提取所有不同的类别
        console.log('[outfit_create] 从缓存的衣物数据中提取类别');
        const categoriesSet = new Set();
        categoryData.forEach(item => {
          if (item && item.category && item.category.trim()) {
            categoriesSet.add(item.category.trim());
          }
        });

        // 将Set转换为数组
        const categoriesArray = Array.from(categoriesSet);
        console.log('[outfit_create] 从缓存数据中提取到的类别:', categoriesArray);

        // 构建动态类别数组，保留原有的"全部"类别
        let dynamicCategories = [this.data.categories[0]];

        // 为每个类别创建对应的对象
        let idCounter = 1;
        categoriesArray.forEach(category => {
          let iconText = category.substring(0, 1); // 取类别的第一个字作为图标
          dynamicCategories.push({
            id: idCounter++,
            name: category,
            icon: iconText,
            count: 0,
            category: category
          });
        });

        console.log('[outfit_create] 从缓存生成的动态类别数组:', dynamicCategories);

        // 使用缓存的类别数据更新UI
        const updatedCategories = clothesManager.updateCategoryCounts(
          categoryData,
          dynamicCategories
        );

        this.setData({
          clothes,
          filteredClothes: clothes,
          categories: updatedCategories,
          isLoading: false
        });

        // 即使使用缓存，也要确保图片URL是最新的，但不从数据库重新加载数据
        this.refreshAllClothesImages(clothes)
          .then(updatedClothes => {
            console.log('[outfit_create] 成功刷新所有衣物图片URL, 数量:', updatedClothes.length);

            // 确保currentCategory不是undefined
            const safeCategory = this.data.currentCategory !== undefined ? this.data.currentCategory : null;

            const filteredClothes = clothesManager.filterByCategory(
              updatedClothes,
              safeCategory
            );

            this.setData({
              clothes: updatedClothes,
              filteredClothes: filteredClothes
            });

            wx.hideLoading();
          })
          .catch(err => {
            console.warn('[outfit_create] 刷新缓存衣物图片URLs失败:', err);
            // 即使刷新失败，也使用缓存数据
            wx.hideLoading();
          });

        return;
      } else {
        console.log('[outfit_create] 发现缓存数据但已过期，将重新加载');
      }
    }

    clothesManager.loadClothes(openid)
      .then(result => {
        console.log('[outfit_create] 成功获取衣物数据, 概览:',
          '衣物数量:', result.clothes ? result.clothes.length : 0,
          '类别数据数量:', result.categoryData ? result.categoryData.length : 0);

        const { clothes, categoryData } = result;

        // 验证数据完整性
        if (!clothes || !Array.isArray(clothes)) {
          console.error('[outfit_create] 衣物数据无效:', clothes);
          throw new Error('衣物数据无效');
        }

        if (!categoryData || !Array.isArray(categoryData)) {
          console.warn('[outfit_create] 类别数据无效，可能影响统计准确性');
        } else {
          console.log(`[outfit_create] 成功获取到全部 ${categoryData.length} 条记录用于类别统计`);
        }

        // 从衣物数据中提取所有不同的类别
        const categoriesSet = new Set();
        categoryData.forEach(item => {
          if (item && item.category && item.category.trim()) {
            categoriesSet.add(item.category.trim());
          }
        });

        // 将Set转换为数组
        const categoriesArray = Array.from(categoriesSet);
        console.log('[outfit_create] 从衣物数据中提取到的类别:', categoriesArray);

        // 构建动态类别数组，保留原有的"全部"类别
        let dynamicCategories = [this.data.categories[0]];

        // 为每个类别创建对应的对象
        let idCounter = 1;
        categoriesArray.forEach(category => {
          let iconText = category.substring(0, 1); // 取类别的第一个字作为图标
          dynamicCategories.push({
            id: idCounter++,
            name: category,
            icon: iconText,
            count: 0,
            category: category
          });
        });

        console.log('[outfit_create] 生成的动态类别数组:', dynamicCategories);

        // 更新类别数量
        console.log('[outfit_create] 开始更新类别数量');
        const updatedCategories = clothesManager.updateCategoryCounts(
          categoryData,
          dynamicCategories
        );

        // 验证类别统计结果
        const totalCount = updatedCategories[0]?.count || 0;
        const sumOfCategories = updatedCategories.slice(1).reduce((sum, cat) => sum + (cat.count || 0), 0);

        if (totalCount !== sumOfCategories) {
          console.warn('[outfit_create] 类别数量总和与总数不匹配:',
            '类别总和:', sumOfCategories,
            '总数:', totalCount,
            '差异:', totalCount - sumOfCategories);
        } else {
          console.log('[outfit_create] 类别数量总和与总数匹配:', totalCount);
        }

        // 输出更新后的类别数量，便于调试
        console.log('[outfit_create] 更新后的类别统计:');
        updatedCategories.forEach(cat => {
          console.log(`[outfit_create] 类别 ${cat.name} (${cat.category || '全部'}) 数量: ${cat.count}`);
        });

        // 将数据保存到本地缓存
        wx.setStorageSync(cacheKey, {
          clothes,
          categoryData,
          timestamp: Date.now()
        });

        // 设置数据
        console.log('[outfit_create] 更新页面数据: 衣物数量:', clothes.length);
        this.setData({
          clothes,
          filteredClothes: clothes,
          categories: updatedCategories,
          isLoading: false
        });

        // 刷新所有衣物图片URL，优先使用缓存
        console.log('[outfit_create] 开始刷新所有衣物图片URL');
        return this.refreshAllClothesImages(clothes);
      })
      .then(updatedClothes => {
        console.log('[outfit_create] 成功刷新所有衣物图片URL, 数量:', updatedClothes.length);

        // 确保currentCategory不是undefined
        const safeCategory = this.data.currentCategory !== undefined ? this.data.currentCategory : null;

        console.log('[outfit_create] 过滤衣物，当前类别:', safeCategory);
        const filteredClothes = clothesManager.filterByCategory(
          updatedClothes,
          safeCategory
        );

        console.log('[outfit_create] 过滤后衣物数量:', filteredClothes.length);
        this.setData({
          clothes: updatedClothes,
          filteredClothes: filteredClothes
        });

        wx.hideLoading();
      })
      .catch(err => {
        console.error('[outfit_create] 加载衣物失败:', err);
        this.useTestData();
        wx.hideLoading();
      });
  },

  // 强制刷新所有衣物图片URL
  refreshAllClothesImages: function(clothes) {
    console.log('[outfit_create] 开始刷新所有衣物图片URL，衣物数量:', clothes.length);

    if (!clothes || !Array.isArray(clothes) || clothes.length === 0) {
      console.warn('[outfit_create] 没有衣物数据需要刷新');
      return Promise.resolve([]);
    }

    // 使用imageManager模块的getClothesImageUrls函数
    // 该函数会自动检查缓存，包括从其他模块的缓存中获取
    return imageManager.getClothesImageUrls(clothes)
      .then(updatedClothes => {
        // 统计抠图处理的衣物数量
        const validClothes = updatedClothes.filter(item => item); // 过滤掉可能的null项
        const processedCount = validClothes.filter(item => item.isProcessed).length;

        console.log(`[outfit_create] 所有衣物图片URL刷新完成，有效数量:${validClothes.length}/${updatedClothes.length}，抠图数量:${processedCount}`);

        if (validClothes.length < updatedClothes.length) {
          console.warn('[outfit_create] 部分刷新任务返回了无效数据，丢失数量:', updatedClothes.length - validClothes.length);
        }

        return validClothes;
      })
      .catch(err => {
        console.error('[outfit_create] 刷新图片URL过程中出错:', err);
        // 即使出错也返回原始衣物数据
        return clothes;
      });
  },

  // 刷新单个衣物的图片URL
  refreshImageUrl: function(item) {
    return new Promise((resolve, reject) => {
      if (!item || !item._id) {
        reject(new Error('无效的衣物数据'));
        return;
      }

      // 保存原始的isProcessed标志
      const wasProcessed = item.isProcessed || false;

      // 收集所有可能的文件ID
      const fileIDs = [];

      // 优先考虑抠图后的图片URL - 首选processedImageFileID
      let primaryFileID = null;
      let isProcessedImage = false;

      // 检查是否有processedImageFileID，优先使用
      if (item.processedImageFileID && item.processedImageFileID.startsWith('cloud://')) {
        primaryFileID = item.processedImageFileID;
        isProcessedImage = true;
        console.log(`优先使用抠图文件ID: ${item._id} - ${primaryFileID}`);
        fileIDs.push(primaryFileID);
      }
      // 次选processedImageUrl
      else if (item.processedImageUrl && item.processedImageUrl.startsWith('cloud://')) {
        primaryFileID = item.processedImageUrl;
        isProcessedImage = true;
        console.log(`使用抠图图片URL: ${item._id} - ${primaryFileID}`);
        fileIDs.push(primaryFileID);
      }

      // 其次考虑其他图片URL
      if (item.imageFileID && item.imageFileID.startsWith('cloud://')) {
        fileIDs.push(item.imageFileID);
      }
      if (item.fileID && item.fileID.startsWith('cloud://')) {
        fileIDs.push(item.fileID);
      }

      if (fileIDs.length === 0) {
        console.warn('衣物没有有效的云存储文件ID:', item._id);
        // 即使没有有效ID，也要保留原有的isProcessed标记
        item.isProcessed = wasProcessed;
        resolve(item);
        return;
      }

      // 首先尝试从本地缓存获取图片
      // 导入本地图片缓存模块
      const localImageCache = require('../../outfit/modules/localImageCache');

      // 检查primaryFileID是否在本地缓存中
      if (primaryFileID) {
        const localPath = localImageCache.getLocalCachedImage(primaryFileID);
        if (localPath) {
          console.log(`[outfit_create] 使用本地缓存的图片: ${item._id} - ${primaryFileID}`);
          item.tempImageUrl = localPath;
          item.isProcessed = isProcessedImage; // 根据是否使用抠图图片设置isProcessed标志
          resolve(item);
          return;
        }
      }

      // 检查其他fileID是否在本地缓存中
      for (const fileID of fileIDs) {
        if (fileID !== primaryFileID) { // 避免重复检查primaryFileID
          const localPath = localImageCache.getLocalCachedImage(fileID);
          if (localPath) {
            console.log(`[outfit_create] 使用本地缓存的图片: ${item._id} - ${fileID}`);
            item.tempImageUrl = localPath;
            item.isProcessed = wasProcessed; // 保持原有的isProcessed标记
            resolve(item);
            return;
          }
        }
      }

      console.log(`获取临时URL，文件ID列表:`, fileIDs);

      // 如果本地缓存中没有，则获取临时URL
      wx.cloud.getTempFileURL({
        fileList: fileIDs,
        success: res => {
          const fileList = res.fileList || [];
          console.log(`获取到 ${fileList.length} 个临时URL:`, fileList.map(f => f.fileID));

          if (fileList.length > 0) {
            // 如果存在抠图后的图片，则优先使用抠图后的图片URL
            if (isProcessedImage && primaryFileID) {
              // 查找抠图后图片对应的临时URL
              const processedFile = fileList.find(file => file.fileID === primaryFileID);
              if (processedFile && processedFile.tempFileURL) {
                item.tempImageUrl = processedFile.tempFileURL;
                item.isProcessed = true; // 标记为已抠图
                console.log(`已刷新抠图图片 ${item._id} 的临时URL (${primaryFileID}):`, item.tempImageUrl);

                // 缓存临时URL - 使用localImageCache模块
                localImageCache.downloadImageToCache(primaryFileID, processedFile.tempFileURL)
                  .then(savedFilePath => {
                    // 如果保存成功，使用保存的本地路径更新tempImageUrl
                    if (savedFilePath) {
                      item.tempImageUrl = savedFilePath;
                      console.log(`使用保存的本地路径更新抠图图片: ${item._id}`);
                    }
                  })
                  .catch(err => console.warn(`缓存图片失败: ${primaryFileID}`, err));

                resolve(item);
                return;
              } else {
                console.warn(`未找到抠图文件ID对应的临时URL: ${primaryFileID}`);
              }
            }

            // 如果没有找到抠图后的图片或没有指定优先使用抠图后的图片
            if (fileList[0].tempFileURL) {
              item.tempImageUrl = fileList[0].tempFileURL;

              // 保留原始isProcessed标记，不要覆盖
              if (wasProcessed) {
                item.isProcessed = true;
                console.log(`已刷新 ${item._id} 的临时URL (${fileList[0].fileID})，保留抠图标记`);
              } else {
                console.log(`已刷新 ${item._id} 的临时URL (${fileList[0].fileID})，非抠图图片`);
              }

              // 缓存临时URL - 使用localImageCache模块
              localImageCache.downloadImageToCache(fileList[0].fileID, fileList[0].tempFileURL)
                .then(savedFilePath => {
                  // 如果保存成功，使用保存的本地路径更新tempImageUrl
                  if (savedFilePath) {
                    item.tempImageUrl = savedFilePath;
                    console.log(`使用保存的本地路径更新图片: ${item._id}`);
                  }
                })
                .catch(err => console.warn(`缓存图片失败: ${fileList[0].fileID}`, err));

              resolve(item);
              return;
            }
          }

          console.warn(`未能获取到 ${item._id} 的临时URL`);
          // 无论成功与否，保留原始的isProcessed标记
          item.isProcessed = wasProcessed;
          resolve(item);
        },
        fail: err => {
          console.error('获取临时文件URL失败:', err);
          // 即使失败也保留原始的isProcessed标记
          item.isProcessed = wasProcessed;
          resolve(item); // 即使失败也返回原始项目
        }
      });
    });
  },

  // 使用测试数据
  useTestData: function() {
    const testClothes = clothesManager.generateTestClothes();

    // 更新类别数量
    const updatedCategories = clothesManager.updateCategoryCounts(
      testClothes,
      this.data.categories
    );

    this.setData({
      clothes: testClothes,
      filteredClothes: testClothes,
      categories: updatedCategories,
      isLoading: false
    });

    wx.showToast({
      title: '使用测试数据',
      icon: 'none'
    });
  },

  // 根据类别筛选衣物
  filterByCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    console.log('筛选类别:', category);

    // 如果点击的是当前已选中的类别，则取消选择（显示全部）
    if (this.data.currentCategory === category) {
      this.setData({
        currentCategory: null
      });

      const filteredClothes = clothesManager.filterByCategory(
        this.data.clothes,
        null
      );

      this.setData({
        filteredClothes
      });

      console.log('取消类别筛选，显示全部衣物');
      return;
    }

    // 设置当前选中的类别
    this.setData({
      currentCategory: category
    });

    // 使用clothesManager筛选衣物
    const filteredClothes = clothesManager.filterByCategory(
      this.data.clothes,
      category
    );

    // 更新筛选后的衣物列表
    this.setData({
      filteredClothes
    });

    // 如果筛选后没有衣物，显示提示
    if (filteredClothes.length === 0) {
      wx.showToast({
        title: '该类别暂无衣物',
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 将衣物添加到画布
  addToCanvas: function(e) {
    const clothingId = e.currentTarget.dataset.id;
    const item = this.data.filteredClothes.find(item => item._id === clothingId);

    if (!item) {
      console.error('未找到ID为', clothingId, '的衣物');
      wx.showToast({
        title: '未找到该衣物',
        icon: 'none'
      });
      return;
    }

    console.log('准备添加衣物到画布:', item, '是否抠图:', item.isProcessed ? '是' : '否');

    // 显示加载提示
    wx.showLoading({
      title: '加载图片...',
      mask: true
    });

    // 先刷新图片URL再添加到画布
    this.refreshImageUrl(item)
      .then(refreshedItem => {
        // 验证图片URL
        let validImageUrl = refreshedItem.tempImageUrl;
        let isProcessed = refreshedItem.isProcessed || false;

        // 如果没有tempImageUrl，尝试其他可能的图片URL字段
        if (!validImageUrl) {
          if (refreshedItem.processedImageUrl &&
             (refreshedItem.processedImageUrl.startsWith('http') || refreshedItem.processedImageUrl.startsWith('https') || refreshedItem.processedImageUrl.startsWith('wxfile://'))) {
            validImageUrl = refreshedItem.processedImageUrl;
            isProcessed = true;
          } else if (refreshedItem.imageUrl &&
                   (refreshedItem.imageUrl.startsWith('http') || refreshedItem.imageUrl.startsWith('https') || refreshedItem.imageUrl.startsWith('wxfile://'))) {
            validImageUrl = refreshedItem.imageUrl;
          } else if (refreshedItem.originalData && refreshedItem.originalData.imageUrl &&
                    (refreshedItem.originalData.imageUrl.startsWith('http') || refreshedItem.originalData.imageUrl.startsWith('https') || refreshedItem.originalData.imageUrl.startsWith('wxfile://'))) {
            validImageUrl = refreshedItem.originalData.imageUrl;
          }
        }

        // 确保URL是有效的
        if (!validImageUrl) {
          console.error('衣物缺少有效的图片URL:', refreshedItem);
          wx.showToast({
            title: '衣物图片加载失败',
            icon: 'none'
          });
          wx.hideLoading();
          return;
        }

        console.log('添加到画布，使用URL:', validImageUrl, '是否抠图:', isProcessed ? '是' : '否');

        // 确保refreshedItem有正确的isProcessed标记
        refreshedItem.isProcessed = isProcessed;

        // 创建画布项
        return canvasManager.createCanvasItem(
          refreshedItem,
          this.data.nextId,
          this.data.canvasItems.length + 1,
          this.data.canvasWidth,
          this.data.canvasHeight
        );
      })
      .then(canvasItem => {
        if (!canvasItem) {
          wx.showToast({
            title: '添加到画布失败',
            icon: 'none'
          });
          wx.hideLoading();
          return;
        }

        // 取消其他项目的活跃状态
        const updatedCanvasItems = canvasManager.updateItemsActiveState(
          this.data.canvasItems,
          null
        );

        // 添加新项目并设置为活跃状态
        this.setData({
          canvasItems: [...updatedCanvasItems, canvasItem],
          nextId: this.data.nextId + 1,
          activeItemId: canvasItem.id
        });

        // 显示成功提示
        wx.hideLoading();
        wx.showToast({
          title: canvasItem.isProcessed ? '已添加抠图衣物' : '已添加到画布',
          icon: 'success',
          duration: 1000
        });

        // 更新衣物列表中的项
        const updatedClothes = this.data.clothes.map(c =>
          c._id === canvasItem.clothingId ? c : c
        );

        const updatedFilteredClothes = this.data.filteredClothes.map(c =>
          c._id === canvasItem.clothingId ? c : c
        );

        this.setData({
          clothes: updatedClothes,
          filteredClothes: updatedFilteredClothes
        });
      })
      .catch(err => {
        // 隐藏加载提示
        wx.hideLoading();

        console.error('添加到画布失败:', err);
        wx.showToast({
          title: '添加到画布失败',
          icon: 'none'
        });
      });
  },

  // 画布点击事件
  canvasTap: function(e) {
    console.log('画布点击');

    // 检查是否点击的是控制按钮
    if (e.target && e.target.dataset && e.target.dataset.action) {
      console.log('点击的是控制按钮，不处理画布点击');
      return;
    }

    // 取消所有项目选中状态
    const updatedCanvasItems = canvasManager.updateItemsActiveState(
      this.data.canvasItems,
      null
    );

    this.setData({
      canvasItems: updatedCanvasItems,
      activeItemId: null
    });
  },

  // 项目点击事件
  itemTap: function(e) {
    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();

    const id = parseInt(e.currentTarget.dataset.id);
    console.log('项目点击:', id);

    // 更新项目激活状态
    const updatedCanvasItems = canvasManager.updateItemsActiveState(
      this.data.canvasItems,
      id
    );

    this.setData({
      canvasItems: updatedCanvasItems,
      activeItemId: id
    });

    // 显示轻微振动反馈
    wx.vibrateShort && wx.vibrateShort({
      type: 'light'
    });

    // 返回false阻止事件冒泡
    return false;
  },

  // 项目触摸开始
  itemTouchStart: function(e) {
    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();

    const id = parseInt(e.currentTarget.dataset.id);
    console.log('触摸开始:', id, '触摸点数:', e.touches.length);

    const item = this.data.canvasItems.find(item => item.id === id);

    if (!item) {
      console.error('未找到ID为', id, '的项目');
      return;
    }

    // 将该项目设为活跃，其他项目设为非活跃
    const updatedCanvasItems = canvasManager.updateItemsActiveState(
      this.data.canvasItems,
      id
    );

    // 初始化触摸状态
    let touchState = {
      canvasItems: updatedCanvasItems,
      activeItemId: id,
      isMoving: false,
      isScaling: false,
      isRotating: false,
      isSingleTouch: e.touches.length === 1
    };

    // 单指触摸 - 移动
    if (e.touches.length === 1) {
      touchState = {
        ...touchState,
        touchStartX: e.touches[0].clientX,
        touchStartY: e.touches[0].clientY,
        itemStartX: item.x,
        itemStartY: item.y,
        isMoving: true
      };
    }
    // 双指触摸 - 缩放和旋转
    else if (e.touches.length === 2) {
      // 计算两指之间的距离
      const distanceX = e.touches[1].clientX - e.touches[0].clientX;
      const distanceY = e.touches[1].clientY - e.touches[0].clientY;
      const fingerDistance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);

      // 计算两指形成的角度
      const angle = Math.atan2(distanceY, distanceX) * 180 / Math.PI;

      // 保存物品的初始宽高比，没有则计算
      const aspectRatio = item.aspectRatio || (item.width / item.height);

      touchState = {
        ...touchState,
        initialFingerDistance: fingerDistance,
        initialItemWidth: item.width,
        initialItemHeight: item.height,
        initialRotation: item.rotation || 0,
        previousAngle: angle,
        aspectRatio: aspectRatio, // 保存宽高比
        isScaling: true,
        isRotating: true
      };
    }

    this.setData(touchState);

    // 返回false阻止事件冒泡
    return false;
  },

  // 项目触摸移动
  itemTouchMove: function(e) {
    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();

    // 获取活跃项目
    const activeItem = this.data.canvasItems.find(item => item.id === this.data.activeItemId);

    if (!activeItem) {
      console.error('未找到活跃项目');
      return;
    }

    // 单指操作 - 移动
    if (e.touches.length === 1 && this.data.isSingleTouch && this.data.isMoving) {
      // 计算移动距离
      const moveX = e.touches[0].clientX - this.data.touchStartX;
      const moveY = e.touches[0].clientY - this.data.touchStartY;

      // 计算新位置，确保不超出画布边界
      const newPosition = canvasManager.calculateNewPosition(
        this.data.itemStartX,
        this.data.itemStartY,
        moveX,
        moveY,
        activeItem.width,
        activeItem.height,
        this.data.canvasWidth,
        this.data.canvasHeight
      );

      // 更新项目位置
      const updatedCanvasItems = this.data.canvasItems.map(item => {
        if (item.id === this.data.activeItemId) {
          return {
            ...item,
            x: newPosition.x,
            y: newPosition.y
          };
        }
        return item;
      });

      this.setData({
        canvasItems: updatedCanvasItems
      });
    }
    // 双指操作 - 缩放和旋转
    else if (e.touches.length === 2) {
      // 计算两指之间的距离
      const distanceX = e.touches[1].clientX - e.touches[0].clientX;
      const distanceY = e.touches[1].clientY - e.touches[0].clientY;
      const fingerDistance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);

      // 计算旋转角度
      const angle = Math.atan2(distanceY, distanceX) * 180 / Math.PI;

      // 缩放比例 - 使用平滑缩放，调整敏感度
      let scale = fingerDistance / this.data.initialFingerDistance;

      // 降低缩放敏感度，使手势操作更平滑
      scale = 1 + (scale - 1) * 0.8; // 降低80%的敏感度

      // 获取宽高比 - 使用保存的宽高比或从activeItem获取
      const aspectRatio = this.data.aspectRatio || activeItem.aspectRatio || (activeItem.width / activeItem.height);

      // 新尺寸 - 根据宽高比计算，确保图片不变形
      const newWidth = this.data.initialItemWidth * scale;
      const newHeight = newWidth / aspectRatio;

      // 限制最小和最大尺寸，同时保持比例
      const minSize = 60;
      const maxWidth = this.data.canvasWidth * 0.9;
      const maxHeight = this.data.canvasHeight * 0.9;

      // 应用尺寸限制，保持比例
      let finalWidth = newWidth;
      let finalHeight = newHeight;

      // 应用最小尺寸限制
      if (finalWidth < minSize) {
        finalWidth = minSize;
        finalHeight = minSize / aspectRatio;
      }

      if (finalHeight < minSize) {
        finalHeight = minSize;
        finalWidth = minSize * aspectRatio;
      }

      // 应用最大尺寸限制
      if (finalWidth > maxWidth) {
        finalWidth = maxWidth;
        finalHeight = maxWidth / aspectRatio;
      }

      if (finalHeight > maxHeight) {
        finalHeight = maxHeight;
        finalWidth = maxHeight * aspectRatio;
      }

      // 计算旋转角度变化 - 降低旋转敏感度
      const angleDiff = (angle - this.data.previousAngle) * 0.5; // 降低50%的旋转敏感度
      let newRotation = this.data.initialRotation + angleDiff;

      // 确保旋转角度在0-360度之间
      newRotation = ((newRotation % 360) + 360) % 360;

      // 更新项目尺寸和旋转角度
      const updatedCanvasItems = this.data.canvasItems.map(item => {
        if (item.id === this.data.activeItemId) {
          return {
            ...item,
            width: Math.round(finalWidth),
            height: Math.round(finalHeight),
            rotation: newRotation,
            aspectRatio: aspectRatio // 保存宽高比，确保后续操作时使用
          };
        }
        return item;
      });

      this.setData({
        canvasItems: updatedCanvasItems,
        previousAngle: angle,
        fingerDistance: fingerDistance
      });
    }

    // 返回false阻止事件冒泡
    return false;
  },

  // 项目触摸结束
  itemTouchEnd: function(e) {
    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();

    console.log('触摸结束', e.touches.length);

    // 结束所有触摸状态
    this.setData({
      isMoving: false,
      isScaling: false,
      isRotating: false,
      isSingleTouch: true
    });

    // 记录当前画布状态，便于调试
    this.debugCanvasItems();

    // 返回false阻止事件冒泡
    return false;
  },

  // 处理控制按钮点击
  handleControlBtnTap: function(e) {
    const id = parseInt(e.currentTarget.dataset.id);
    const action = e.currentTarget.dataset.action;

    console.log('控制按钮点击:', action, id);

    switch(action) {
      case 'delete':
        this.deleteItem(e);
        break;
      case 'increase':
        this.resizeItem({
          currentTarget: {
            dataset: {
              id: id,
              direction: 'increase'
            }
          }
        });
        break;
      case 'decrease':
        this.resizeItem({
          currentTarget: {
            dataset: {
              id: id,
              direction: 'decrease'
            }
          }
        });
        break;
      case 'rotateCW':
        this.rotateItem(e);
        break;
      case 'rotateCCW':
        this.rotateItem(e);
        break;
      case 'layerUp':
        this.adjustLayer({
          currentTarget: {
            dataset: {
              id: id,
              direction: 'up'
            }
          }
        });
        break;
      case 'layerDown':
        this.adjustLayer({
          currentTarget: {
            dataset: {
              id: id,
              direction: 'down'
            }
          }
        });
        break;
    }
  },

  // 删除项目
  deleteItem: function(e) {
    e.stopPropagation && e.stopPropagation();

    const id = parseInt(e.currentTarget.dataset.id);
    console.log('删除项目:', id);

    // 确认是否删除
    wx.showModal({
      title: '确认删除',
      content: '确定要删除该衣物吗？',
      success: (res) => {
        if (res.confirm) {
          const updatedCanvasItems = this.data.canvasItems.filter(item => item.id !== id);

          this.setData({
            canvasItems: updatedCanvasItems,
            activeItemId: null
          });

          wx.showToast({
            title: '已删除',
            icon: 'success',
            duration: 1000
          });
        }
      }
    });
  },

  // 调整图层顺序
  adjustLayer: function(e) {
    e.stopPropagation && e.stopPropagation();

    const id = parseInt(e.currentTarget.dataset.id);
    const direction = e.currentTarget.dataset.direction;
    console.log('调整图层:', id, direction);

    const item = this.data.canvasItems.find(item => item.id === id);

    if (!item) {
      console.error('未找到ID为', id, '的项目');
      return;
    }

    // 计算新的图层值
    const currentLayer = item.layer || 0;
    let newLayer = currentLayer;

    if (direction === 'up') {
      newLayer = currentLayer + 1;
    } else if (direction === 'down') {
      newLayer = Math.max(0, currentLayer - 1);
    }

    console.log('图层变更:', currentLayer, '->', newLayer);

    // 更新项目图层
    const updatedCanvasItems = this.data.canvasItems.map(item => {
      if (item.id === id) {
        return {
          ...item,
          layer: newLayer,
          zIndex: newLayer
        };
      }
      return item;
    });

    this.setData({
      canvasItems: updatedCanvasItems
    });

    // 显示轻微振动反馈
    wx.vibrateShort && wx.vibrateShort({
      type: 'light'
    });
  },

  // 调整项目大小
  resizeItem: function(e) {
    const id = parseInt(e.currentTarget.dataset.id);
    const direction = e.currentTarget.dataset.direction;
    console.log('调整大小:', id, direction);

    const item = this.data.canvasItems.find(item => item.id === id);

    if (!item) {
      console.error('未找到ID为', id, '的项目');
      return;
    }

    // 计算新尺寸
    const newSize = canvasManager.calculateNewSize(
      item.width,
      item.height,
      direction,
      this.data.canvasWidth,
      this.data.canvasHeight
    );

    console.log('新尺寸:', newSize);

    // 更新项目尺寸
    const updatedCanvasItems = this.data.canvasItems.map(item => {
      if (item.id === id) {
        return {
          ...item,
          width: newSize.width,
          height: newSize.height
        };
      }
      return item;
    });

    this.setData({
      canvasItems: updatedCanvasItems
    });

    // 阻止冒泡
    //e.stopPropagation();
  },

  // 旋转项目
  rotateItem: function(e) {
    // 从事件中获取 id 和 action
    const id = parseInt(e.currentTarget.dataset.id);
    const action = e.currentTarget.dataset.action;
    console.log('旋转项目:', id, '动作:', action);

    const item = this.data.canvasItems.find(item => item.id === id);

    if (!item) {
      console.error('未找到ID为', id, '的项目');
      return;
    }

    // 确定旋转方向
    let direction;
    if (action === 'rotateCW') {
      direction = 'clockwise';
    } else if (action === 'rotateCCW') {
      direction = 'counterclockwise';
    } else {
      // 如果 action 不是我们期望的值，使用默认值
      direction = 'clockwise';
    }

    console.log('旋转方向:', direction);

    // 计算新旋转角度
    const newRotation = canvasManager.calculateNewRotation(
      item.rotation || 0,
      direction
    );

    console.log('新旋转角度:', newRotation);

    // 更新项目旋转角度
    const updatedCanvasItems = this.data.canvasItems.map(item => {
      if (item.id === id) {
        return {
          ...item,
          rotation: newRotation
        };
      }
      return item;
    });

    this.setData({
      canvasItems: updatedCanvasItems
    });

    // 防止事件冒泡
    e.stopPropagation && e.stopPropagation();
  },

  // 更新搭配名称
  updateOutfitName: function(e) {
    // 如果用户输入了内容，标记为自定义名称
    const newName = e.detail.value.trim();
    const isCustomName = newName !== '' && newName !== '我的搭配' &&
      !this.data.outfitCategoryOptions.some(option =>
        newName === `${option.name}` || newName === `我的${option.name}`);

    this.setData({
      outfitName: newName,
      hasCustomName: isCustomName
    });
  },

  // 防止事件冒泡的空函数
  preventTap: function(e) {
    console.log('preventTap 被触发');
    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
    return false;
  },

  // 切换穿搭类型选择器显示状态
  toggleCategoryPicker: function(e) {
    console.log('toggleCategoryPicker 被触发, 当前状态:', this.data.showCategoryPicker);

    // 切换显示状态
    const newState = !this.data.showCategoryPicker;

    this.setData({
      showCategoryPicker: newState
    });

    console.log('切换后的状态:', newState);

    // 如果用户打开了类别选择器，添加振动反馈
    if (newState) {
      wx.vibrateShort({
        type: 'light'
      });

      // 设置一个定时器，如果用户5秒内没有选择，自动关闭下拉菜单
      this._closePickerTimer = setTimeout(() => {
        console.log('下拉菜单自动关闭');
        this.setData({
          showCategoryPicker: false
        });
      }, 5000);
    } else {
      // 如果用户关闭了菜单，清除定时器
      if (this._closePickerTimer) {
        clearTimeout(this._closePickerTimer);
        this._closePickerTimer = null;
      }
    }

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
    return false;
  },

  // 选择穿搭类型
  selectCategory: function(e) {
    console.log('selectCategory 被触发, 数据:', e.currentTarget.dataset);

    // 如果有定时器，清除它
    if (this._closePickerTimer) {
      clearTimeout(this._closePickerTimer);
      this._closePickerTimer = null;
    }

    // 获取类别值
    const categoryValue = e.currentTarget.dataset.category;
    console.log('选择的类别值:', categoryValue);

    if (!categoryValue) {
      console.error('未获取到类别值');
      return false;
    }

    // 从outfitCategoryOptions和customCategoryOptions查找匹配的类别配置
    let categoryOption = this.data.outfitCategoryOptions.find(option => option.value === categoryValue);
    if (!categoryOption) {
      categoryOption = this.data.customCategoryOptions.find(option => option.value === categoryValue);
      if (!categoryOption) {
        console.error('未找到匹配的类别选项');
        return false;
      }
    }

    console.log('找到的类别选项:', categoryOption);

    // 设置选中的类别，并更新搭配名称（如果用户尚未自定义名称）
    let newOutfitName = this.data.outfitName;
    // 只有在用户没有自定义名称时，才自动生成名称
    if (!this.data.hasCustomName) {
      newOutfitName = `我的${categoryOption.name}`;
    }

    // 使用类型名称作为category值
    const categoryName = categoryOption.name;

    // 更新数据，同时关闭下拉菜单
    this.setData({
      showCategoryPicker: false,
      outfitCategory: categoryName, // 使用名称作为category值
      outfitCategoryValue: categoryValue, // 保存内部value值
      outfitName: newOutfitName
    });

    console.log('更新后的状态:', {
      outfitCategory: this.data.outfitCategory,
      outfitCategoryValue: this.data.outfitCategoryValue,
      outfitName: this.data.outfitName
    });

    // 更新当前类型信息
    this.updateCurrentCategoryInfo();

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
    return false;
  },

  // 更新自定义类型名称
  updateCustomCategoryName: function(e) {
    this.setData({
      newCustomCategoryName: e.detail.value
    });
  },

  // 添加自定义类型
  addCustomCategory: function() {
    const name = this.data.newCustomCategoryName.trim();
    if (!name) {
      wx.showToast({
        title: '请输入类型名称',
        icon: 'none'
      });
      return;
    }

    // 检查是否已存在相同名称的类型
    const existsInPredefined = this.data.outfitCategoryOptions.some(option => option.name === name);
    const existsInCustom = this.data.customCategoryOptions.some(option => option.name === name);

    if (existsInPredefined || existsInCustom) {
      wx.showToast({
        title: '该类型名称已存在',
        icon: 'none'
      });
      return;
    }

    // 生成唯一的value用于内部标识，但在数据库中使用名称作为category
    const value = 'custom_' + Date.now();

    // 创建新的自定义类型
    const newCategory = {
      value: value,
      name: name,
      icon: '✨'
    };

    // 添加到自定义类型列表
    const customCategoryOptions = [...this.data.customCategoryOptions, newCategory];

    this.setData({
      customCategoryOptions,
      newCustomCategoryName: '',
      outfitCategory: name,  // 使用名称作为category值
      outfitName: `我的${name}` // 更新搭配名称
    });

    // 保存自定义类型到本地存储
    wx.setStorageSync('customOutfitCategories', customCategoryOptions);

    // 设置标记，通知页面刷新自定义类型
    wx.setStorageSync('needRefreshCustomCategories', true);

    // 保存到数据库
    this.saveCustomCategoriesToDB(customCategoryOptions);

    // 关闭类型选择器
    this.setData({
      showCategoryPicker: false
    });

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 显示成功提示
    wx.showToast({
      title: '添加成功',
      icon: 'success'
    });
  },

  // 保存自定义类型到数据库
  saveCustomCategoriesToDB: function(categories) {
    // 获取用户OpenID
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      console.error('未找到用户OpenID，无法保存自定义类型');
      return;
    }

    wx.cloud.callFunction({
      name: 'saveCustomOutfitCategories',
      data: {
        categories: categories
      },
      success: res => {
        console.log('保存自定义穿搭类型成功:', res);
      },
      fail: err => {
        console.error('保存自定义穿搭类型失败:', err);
      }
    });
  },

  // 加载用户自定义的穿搭类型
  loadCustomCategories: function() {
    // 从本地缓存中获取自定义类型
    const customCategories = wx.getStorageSync('customOutfitCategories') || [];
    console.log('从缓存加载自定义穿搭类型:', customCategories);

    if (customCategories.length > 0) {
      this.setData({
        customCategoryOptions: customCategories
      });
    }
  },

  // 更新当前类型信息
  updateCurrentCategoryInfo: function() {
    console.log('updateCurrentCategoryInfo 被调用, 当前类别:', this.data.outfitCategory);

    // 如果有outfitCategoryValue，优先使用它查找
    if (this.data.outfitCategoryValue) {
      // 先在预设类型中查找
      let categoryOption = this.data.outfitCategoryOptions.find(option =>
        option.value === this.data.outfitCategoryValue
      );

      // 如果预设类型中没有找到，则在自定义类型中查找
      if (!categoryOption) {
        categoryOption = this.data.customCategoryOptions.find(option =>
          option.value === this.data.outfitCategoryValue
        );
      }

      if (categoryOption) {
        console.log('通过value找到的类别选项:', categoryOption);
        this.setData({
          currentCategoryIcon: categoryOption.icon,
          currentCategoryName: categoryOption.name
        });
        return;
      }
    }

    // 如果通过value没找到，尝试通过名称查找
    // 先在预设类型中查找
    let categoryOption = this.data.outfitCategoryOptions.find(option =>
      option.name === this.data.outfitCategory
    );

    // 如果预设类型中没有找到，则在自定义类型中查找
    if (!categoryOption) {
      categoryOption = this.data.customCategoryOptions.find(option =>
        option.name === this.data.outfitCategory
      );
    }

    console.log('通过名称找到的类别选项:', categoryOption);

    if (categoryOption) {
      this.setData({
        currentCategoryIcon: categoryOption.icon,
        currentCategoryName: categoryOption.name,
        outfitCategoryValue: categoryOption.value // 更新value值
      });

      console.log('更新后的类别信息:', {
        currentCategoryIcon: this.data.currentCategoryIcon,
        currentCategoryName: this.data.currentCategoryName,
        outfitCategoryValue: this.data.outfitCategoryValue
      });
    } else {
      console.error('未找到匹配的类别选项');
      // 如果都没找到，使用当前类别名称
      this.setData({
        currentCategoryIcon: '✨',
        currentCategoryName: this.data.outfitCategory
      });
    }
  },

  // 清空画布
  clearCanvas: function() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空当前画布吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            canvasItems: [],
            activeItemId: null
          });
        }
      }
    });
  },

  // 保存搭配
  saveOutfit: function() {
    if (this.data.canvasItems.length === 0) {
      wx.showToast({
        title: '画布为空，无法保存',
        icon: 'none'
      });
      return;
    }

    if (this.data.isSaving) {
      return;
    }

    this.setData({
      isSaving: true
    });

    wx.showLoading({
      title: '保存中...',
    });

    // 将画布转为图片
    outfitManager.generateOutfitImage(
      'outfitCanvas',
      this.data.canvasItems,
      this.data.canvasWidth,
      this.data.canvasHeight
    )
      .then(imageFileID => {
        console.log('搭配图片已保存:', imageFileID);

        // 保存搭配数据到数据库
        console.log('保存搭配数据，类型:', this.data.outfitCategory);

        // 确保使用正确的类型名称
        const categoryName = this.data.outfitCategory;

        return outfitManager.saveOutfitToDatabase(
          this.data.outfitName,
          imageFileID,
          this.data.canvasItems,
          categoryName // 传递穿搭类型名称
        );
      })
      .then(res => {
        console.log('搭配数据已保存:', res);

        // 如果是来自AI推荐，自动设置为今日穿搭
        if (this.data.isFromAI && res && res.outfitId) {
          // 返回一个Promise，将这个搭配设置为今日穿搭
          const outfitDataObj = {
            _id: res.outfitId,
            name: this.data.outfitName,
            category: this.data.outfitCategory, // 使用类型名称
            type: this.data.outfitCategory, // 同时设置type字段，增加兼容性
            items: this.data.canvasItems || []
          };

          console.log('设置今日穿搭，数据:', outfitDataObj);

          // 调用saveTodayOutfit函数，传递完整的outfitData对象
          return outfitManager.saveTodayOutfit(outfitDataObj, this.data.userOpenId)
            .then(() => {
              console.log('已将AI推荐搭配设为今日穿搭');

              // 设置标记，通知页面刷新
              wx.setStorageSync('needRefreshOutfits', true);
              wx.setStorageSync('needRefreshCustomCategories', true);

              return res; // 返回原始结果以继续处理
            });
        } else {
          // 非AI推荐，也设置刷新标记
          wx.setStorageSync('needRefreshOutfits', true);
          wx.setStorageSync('needRefreshCustomCategories', true);

          // 清除搭配缓存，确保获取最新数据
          const openid = wx.getStorageSync('openid');
          if (openid) {
            const outfitsCacheKey = 'outfit_page_outfits_cache_' + openid;
            wx.removeStorage({
              key: outfitsCacheKey,
              success: () => {
                console.log('成功清除穿搭页面缓存');
              }
            });
          }
          return res; // 返回结果
        }
      })
      .then(res => {
        this.setData({
          isSaving: false
        });

        wx.hideLoading();

        // 显示成功提示，根据是否来自AI推荐显示不同的提示
        wx.showToast({
          title: this.data.isFromAI ? '已保存并设为今日穿搭' : '保存成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟返回
        setTimeout(() => {
          const pages = getCurrentPages();

          // 如果是来自AI推荐，跳转到今日穿搭页面
          if (this.data.isFromAI) {
            wx.switchTab({
              url: '/page/wardrobe/ootd_new/ootd_new'
            });
          } else if (pages.length > 1) {
            wx.navigateBack();
          } else {
            wx.navigateTo({
              url: '../outfit_category/outfit_category'
            });
          }
        }, 1500);
      })
      .catch(err => {
        console.error('保存搭配失败:', err);

        this.setData({
          isSaving: false
        });

        wx.hideLoading();

        wx.showToast({
          title: err.message,
          icon: 'none'
        });
      });
  },

  // 返回上一页
  goBack: function() {
    // 如果有未保存的搭配，提示用户
    if (this.data.canvasItems.length > 0) {
      wx.showModal({
        title: '提示',
        content: '你有未保存的搭配，确定要返回吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  // 调试函数：记录当前画布项目状态
  debugCanvasItems: function() {
    if (this.data.canvasItems.length > 0) {
      console.log('当前画布状态:', JSON.stringify(this.data.canvasItems.map(item => ({
        id: item.id,
        x: Math.round(item.x),
        y: Math.round(item.y),
        width: Math.round(item.width),
        height: Math.round(item.height),
        rotation: Math.round(item.rotation || 0),
        layer: item.layer || 0
      }))));
    }
  },

  // 页面显示时执行
  onShow: function() {
    console.log('outfit_create 页面 onShow');

    // 如果已经加载了衣物数据，则刷新图片URL
    if (this.data.clothes && this.data.clothes.length > 0 && !this.data.isLoading) {
      wx.showLoading({
        title: '刷新抠图图片...',
      });

      console.log('开始刷新衣物图片，优先使用抠图后的图片');

      // 刷新所有衣物的图片URL
      this.refreshAllClothesImages(this.data.clothes)
        .then(updatedClothes => {
          console.log('页面显示时刷新图片URL完成');

          // 统计抠图衣物数量
          const processedCount = updatedClothes.filter(item => item.isProcessed).length;
          console.log(`共有 ${processedCount}/${updatedClothes.length} 件衣物使用了抠图`);

          this.setData({
            clothes: updatedClothes,
            filteredClothes: clothesManager.filterByCategory(
              updatedClothes,
              this.data.currentCategory
            )
          });


        // 如果有AI推荐的衣物项目，且Canvas还没有添加过项目，则添加到画布
        if (this.aiRecommendedItems && this.aiRecommendedItems.length > 0 && this.data.canvasItems.length === 0) {
          console.log('检测到AI推荐衣物，1秒后添加到画布');
          wx.showToast({
            title: '1秒后添加AI推荐',
            icon: 'none',
            duration: 1000
          });

          // 延时5秒后执行添加操作
          setTimeout(() => {
            console.log('开始添加AI推荐衣物到画布');
            this.addAIRecommendedItems();
          }, 1000);
        }
          wx.hideLoading();
        })
        .catch(err => {
          console.error('页面显示时刷新图片URL失败:', err);
          wx.hideLoading();
        });
    }
  },

  // 添加AI推荐的衣物到画布
  addAIRecommendedItems: function() {
    if (!this.aiRecommendedItems || this.aiRecommendedItems.length === 0) {
      return;
    }

    console.log('正在添加AI推荐衣物到画布, 共', this.aiRecommendedItems.length, '件');

    // 计算画布中心位置
    const centerX = this.data.canvasWidth / 2;
    const centerY = this.data.canvasHeight / 2;

    // 创建添加衣物的Promise序列
    const addItemPromises = this.aiRecommendedItems.map((item, index) => {
      return new Promise((resolve, reject) => {
        // 在所有衣物中寻找匹配的项目
        const clothingItem = this.data.clothes.find(clothing => clothing._id === item.id);

        if (!clothingItem) {
          console.warn('未找到ID为', item.id, '的衣物');
          resolve();
          return;
        }

        // 刷新图片URL
        this.refreshImageUrl(clothingItem)
          .then(refreshedItem => {
            // 验证图片URL
            let validImageUrl = refreshedItem.tempImageUrl;
            let isProcessed = refreshedItem.isProcessed || false;

            // 如果没有tempImageUrl，尝试其他可能的图片URL字段
            if (!validImageUrl) {
              if (refreshedItem.processedImageUrl &&
                 (refreshedItem.processedImageUrl.startsWith('http') || refreshedItem.processedImageUrl.startsWith('https') || refreshedItem.processedImageUrl.startsWith('wxfile://'))) {
                validImageUrl = refreshedItem.processedImageUrl;
                isProcessed = true;
              } else if (refreshedItem.imageUrl &&
                       (refreshedItem.imageUrl.startsWith('http') || refreshedItem.imageUrl.startsWith('https') || refreshedItem.imageUrl.startsWith('wxfile://'))) {
                validImageUrl = refreshedItem.imageUrl;
              } else if (refreshedItem.originalData && refreshedItem.originalData.imageUrl &&
                        (refreshedItem.originalData.imageUrl.startsWith('http') || refreshedItem.originalData.imageUrl.startsWith('https') || refreshedItem.originalData.imageUrl.startsWith('wxfile://'))) {
                validImageUrl = refreshedItem.originalData.imageUrl;
              }
            }

            if (!validImageUrl) {
              console.warn('未找到有效的图片URL:', refreshedItem);
              resolve();
              return;
            }

            // 获取图片信息，确保正确设置宽高比
            wx.getImageInfo({
              src: validImageUrl,
              success: imgInfo => {
                const imgWidth = imgInfo.width;
                const imgHeight = imgInfo.height;
                const imageRatio = imgWidth / imgHeight;

                // 设置合理的初始尺寸，保持宽高比
                let initialWidth = 150;
                let initialHeight = initialWidth / imageRatio;

                // 如果高度过大，则以高度为基准调整宽度
                if (initialHeight > 250) {
                  initialHeight = 250;
                  initialWidth = initialHeight * imageRatio;
                }

                // 创建画布项目，确保保存宽高比信息
                const canvasItem = {
                  id: this.data.nextId,
                  clothingId: refreshedItem._id,
                  name: refreshedItem.name,
                  imageUrl: validImageUrl,
                  category: refreshedItem.category,
                  isProcessed: isProcessed,
                  // 根据索引排列衣物位置，在画布中心区域
                  x: centerX + (index % 2) * 50 - 25,
                  y: centerY + Math.floor(index / 2) * 80 - 40,
                  width: Math.round(initialWidth),
                  height: Math.round(initialHeight),
                  rotation: 0,
                  layerIndex: this.data.canvasItems.length + index,
                  layer: this.data.canvasItems.length + index,
                  zIndex: this.data.canvasItems.length + index,
                  aspectRatio: imageRatio, // 保存宽高比
                  originalImageSize: {
                    width: imgWidth,
                    height: imgHeight
                  }
                };

                // 添加到画布项目列表
                this.setData({
                  canvasItems: [...this.data.canvasItems, canvasItem],
                  nextId: this.data.nextId + 1
                });

                resolve();
              },
              fail: error => {
                console.error('获取图片信息失败:', error);

                // 创建默认画布项
                const canvasItem = {
                  id: this.data.nextId,
                  clothingId: refreshedItem._id,
                  name: refreshedItem.name,
                  imageUrl: validImageUrl,
                  category: refreshedItem.category,
                  isProcessed: isProcessed,
                  // 根据索引排列衣物位置，在画布中心区域
                  x: centerX + (index % 2) * 50 - 25,
                  y: centerY + Math.floor(index / 2) * 80 - 40,
                  width: 150,
                  height: 150,
                  rotation: 0,
                  layerIndex: this.data.canvasItems.length + index,
                  layer: this.data.canvasItems.length + index,
                  zIndex: this.data.canvasItems.length + index,
                  aspectRatio: 1 // 默认正方形
                };

                // 添加到画布项目列表
                this.setData({
                  canvasItems: [...this.data.canvasItems, canvasItem],
                  nextId: this.data.nextId + 1
                });

                resolve();
              }
            });
          })
          .catch(err => {
            console.error('刷新衣物图片URL失败:', err);
            resolve(); // 即使失败也继续处理下一个
          });
      });
    });

    // 执行所有添加操作
    Promise.all(addItemPromises)
      .then(() => {
        console.log('所有AI推荐衣物已添加到画布');
        // 清除缓存，避免重复添加
        this.aiRecommendedItems = null;

        // 重新渲染画布
        this.renderCanvas && this.renderCanvas();
      })
      .catch(err => {
        console.error('添加AI推荐衣物到画布失败:', err);
      });
  },

  // 页面卸载时清理资源
  onUnload: function() {
    console.log('outfit_create 页面 onUnload');

    // 清除可能存在的定时器
    if (this._closePickerTimer) {
      clearTimeout(this._closePickerTimer);
      this._closePickerTimer = null;
    }
  },

  // 应用主题样式
  applyThemeStyle: function(themeName) {
    console.log('应用新主题：', themeName);

    // 更新页面样式变量
    this.setData({
      themeStyle: themeName
    });

    // 保存主题设置到本地存储
    wx.setStorageSync('themeStyle', themeName);

    try {
      // 设置导航栏颜色
      if (themeName === 'autumn') {
        wx.setNavigationBarColor({
          frontColor: '#000000', // 黑色文字
          backgroundColor: this.data.colors.goldenBatter, // 金黄色背景
          animation: {
            duration: 300,
            timingFunc: 'easeIn'
          }
        });

        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: '创建搭配'
        });
      } else if (themeName === 'pinkBlue') {
        wx.setNavigationBarColor({
          frontColor: '#000000', // 黑色文字
          backgroundColor: this.data.pinkBlueColors.pinkLight, // 浅粉色背景
          animation: {
            duration: 300,
            timingFunc: 'easeIn'
          }
        });

        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: '创建搭配'
        });
      }
    } catch (error) {
      console.error('应用主题样式出错:', error);
    }
  },

  // 动态更新主题 (可被外部页面如settings.js调用)
  updateTheme: function(newTheme) {
    console.log('outfit_create页面接收到主题更新请求:', newTheme);
    if (newTheme !== this.data.themeStyle) {
      this.applyThemeStyle(newTheme);
    }
  },

  // 处理图片加载错误
  handleImageError: function(e) {
    const id = e.currentTarget.dataset.id;
    const type = e.currentTarget.dataset.type; // processed或original
    console.log(`图片加载错误: ${id}, 类型: ${type}`);

    // 查找对应的衣物
    const item = this.data.clothes.find(item => item._id === id);
    if (!item) {
      console.error('未找到ID为', id, '的衣物');
      return;
    }

    // 尝试重新获取临时URL
    console.log(`尝试重新获取衣物 ${id} 的临时URL`);
    wx.showLoading({
      title: '重新加载图片...',
      mask: true
    });

    this.refreshImageUrl(item)
      .then(refreshedItem => {
        // 更新衣物列表中的项
        const updatedClothes = this.data.clothes.map(c =>
          c._id === refreshedItem._id ? refreshedItem : c
        );

        const updatedFilteredClothes = this.data.filteredClothes.map(c =>
          c._id === refreshedItem._id ? refreshedItem : c
        );

        this.setData({
          clothes: updatedClothes,
          filteredClothes: updatedFilteredClothes
        });

        wx.hideLoading();
        console.log(`已重新获取衣物 ${id} 的临时URL`);
      })
      .catch(err => {
        console.error('重新获取临时URL失败:', err);
        wx.hideLoading();
      });
  },

  // 添加renderCanvas函数
  renderCanvas: function() {
    // 如果没有canvasItems，不进行渲染
    if (!this.data.canvasItems || this.data.canvasItems.length === 0) {
      return;
    }

    console.log('手动触发画布渲染');

    // 使用canvasManager的drawCanvas方法渲染画布
    canvasManager.drawCanvas(
      'outfitCanvas',
      this.data.canvasItems,
      this.data.canvasWidth,
      this.data.canvasHeight
    )
    .then(() => {
      console.log('画布渲染完成');
    })
    .catch(err => {
      console.error('画布渲染失败:', err);
    });
  },
});
