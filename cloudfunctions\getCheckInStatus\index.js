// 获取签到状态云函数
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const userOpenId = event.userOpenId || wxContext.OPENID

  if (!userOpenId) {
    return {
      success: false,
      error: '无法获取用户ID'
    }
  }

  try {
    // 获取当前日期（格式：YYYY-MM-DD）
    const today = new Date()
    const dateString = today.getFullYear() + '-' +
                     String(today.getMonth() + 1).padStart(2, '0') + '-' +
                     String(today.getDate()).padStart(2, '0')

    // 查询用户今日是否已签到
    const checkInsCollection = db.collection('checkIns')
    const todayCheckIn = await checkInsCollection.where({
      userOpenId: userOpenId,
      date: dateString
    }).get()

    // 查询用户最近7天的签到记录
    const sevenDaysAgo = new Date(today)
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6) // 7天前（包括今天）
    const sevenDaysAgoString = sevenDaysAgo.getFullYear() + '-' +
                             String(sevenDaysAgo.getMonth() + 1).padStart(2, '0') + '-' +
                             String(sevenDaysAgo.getDate()).padStart(2, '0')

    const recentCheckIns = await checkInsCollection.where({
      userOpenId: userOpenId,
      date: _.gte(sevenDaysAgoString)
    }).orderBy('date', 'asc').get()

    // 查询用户信息，检查是否是VIP会员
    const userInfo = await db.collection('users').where({
      _openid: userOpenId
    }).get()

    let isVIP = false
    if (userInfo.data && userInfo.data.length > 0) {
      const user = userInfo.data[0]
      // 检查用户是否是VIP会员且会员未过期
      isVIP = user.memberType === 'VIP' && new Date(user.memberExpireDate) > today
    }

    // 计算连续签到天数
    let consecutiveDays = 0

    // 如果今天已签到，直接使用今天的连续天数
    if (todayCheckIn.data && todayCheckIn.data.length > 0) {
      consecutiveDays = todayCheckIn.data[0].consecutiveDays || 0
    } else if (recentCheckIns.data && recentCheckIns.data.length > 0) {
      // 如果今天未签到，需要重新计算连续天数
      // 检查最近的签到记录是否是昨天
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayString = yesterday.getFullYear() + '-' +
                            String(yesterday.getMonth() + 1).padStart(2, '0') + '-' +
                            String(yesterday.getDate()).padStart(2, '0')

      const latestCheckIn = recentCheckIns.data[recentCheckIns.data.length - 1]

      // 如果最近的签到是昨天，则连续天数保持，否则重置为0
      if (latestCheckIn.date === yesterdayString) {
        consecutiveDays = latestCheckIn.consecutiveDays || 0
      } else {
        consecutiveDays = 0 // 断签了，重置为0
      }
    }

    // 构建过去7天的签到状态
    const last7Days = []
    for (let i = 0; i < 7; i++) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      const dateStr = date.getFullYear() + '-' +
                    String(date.getMonth() + 1).padStart(2, '0') + '-' +
                    String(date.getDate()).padStart(2, '0')

      // 查找该日期是否有签到记录
      const checkInRecord = recentCheckIns.data.find(record => record.date === dateStr)

      last7Days.unshift({
        date: dateStr,
        checked: !!checkInRecord,
        rewards: checkInRecord ? {
          energy: checkInRecord.energyReward || 0,
          storage: checkInRecord.storageReward || 0
        } : null
      })
    }

    return {
      success: true,
      todayCheckedIn: todayCheckIn.data && todayCheckIn.data.length > 0,
      consecutiveDays: consecutiveDays,
      isVIP: isVIP,
      last7Days: last7Days,
      // 计算下一次签到的奖励
      nextRewards: {
        energy: isVIP ? 20 : 10, // VIP会员体力值奖励翻倍
        storage: (consecutiveDays + 1) % 7 === 0 ? (isVIP ? 4 : 2) : 0 // 连续7天奖励存储空间
      }
    }
  } catch (error) {
    console.error('获取签到状态失败:', error)
    return {
      success: false,
      error: error.message || '获取签到状态失败'
    }
  }
}
