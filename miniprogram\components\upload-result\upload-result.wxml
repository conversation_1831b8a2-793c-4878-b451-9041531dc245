<view class="upload-result-container {{visible ? 'visible' : ''}}" catchtap="onClose">
  <view class="upload-result-card" catchtap="preventBubble">
    <!-- 标题 -->
    <view class="upload-result-title">
      <text>{{title}}</text>
    </view>

    <!-- 分隔线 -->
    <view class="divider"></view>

    <!-- 上传统计 -->
    <view class="upload-stats">
      <view class="stat-item success">
        <text class="stat-label">成功:</text>
        <text class="stat-value">{{successCount}}{{itemUnit}}</text>
      </view>
      <view class="stat-item failed" wx:if="{{failedCount > 0}}">
        <text class="stat-label">失败:</text>
        <text class="stat-value">{{failedCount}}{{itemUnit}}</text>
      </view>
    </view>

    <!-- 体力值返还 -->
    <view class="refund-info" wx:if="{{refundAmount > 0}}">
      <text>已返还 {{refundAmount}} 点体力值</text>
    </view>

    <!-- 类别统计 -->
    <view class="category-stats" wx:if="{{sortedCategories.length > 0}}">
      <view class="category-title">类别统计</view>
      <view class="category-list">
        <view class="category-item" wx:for="{{sortedCategories}}" wx:key="name">
          <text class="category-name">{{item.name}}:</text>
          <text class="category-count">{{item.count}}件</text>
        </view>
      </view>

      <!-- 未分类提示 -->
      <view class="uncategorized" wx:if="{{uncategorizedCount > 0}}">
        <text>未分类衣物: {{uncategorizedCount}}件</text>
      </view>
    </view>

    <!-- 失败提示 -->
    <view class="failed-tip" wx:if="{{successCount === 0 && failedCount > 0}}">
      <text>所有{{itemType}}处理失败，请检查{{itemType}}格式或网络连接</text>
    </view>

    <!-- 查看失败图片按钮 -->
    <view class="view-failed-btn" bindtap="viewFailedImages" wx:if="{{failedCount > 0 && failedItems.length > 0}}">
      <text>查看失败{{itemType}}</text>
    </view>

    <!-- 确认按钮 -->
    <view class="confirm-btn" bindtap="onClose">
      <text>确定</text>
    </view>
  </view>
</view>
