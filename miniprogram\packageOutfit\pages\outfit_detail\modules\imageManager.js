/**
 * 图片管理模块
 * 负责图片处理和错误处理
 */

// 引入本地图片缓存模块
const localImageCache = require('./localImageCache');

// 缓存相关常量
const URL_CACHE_EXPIRE_TIME = 540000; // 9分钟

// 图片加载失败重试相关常量
const MAX_RETRY_ATTEMPTS = 3; // 最大重试次数
const RETRY_DELAY = 1000; // 重试延迟时间（毫秒）

// 记录图片加载失败次数的对象
const imageLoadFailureCount = {};

/**
 * 获取默认图片URL
 * @returns {string} 默认图片URL
 */
function getDefaultImageUrl() {
  return localImageCache.getDefaultPreviewImageUrl() || 'https://mmbiz.qpic.cn/mmbiz_jpg/UicQ7HgWiaUb3Zib1Zia9PmHLpKhZKiaZhsGYJCibdIkjPtymh5xicSMN2u5yZlmIbp3icicVicYqA1CnNgwjcEJYJJhZmw/0?wx_fmt=jpeg';
}

/**
 * 处理图片加载错误
 * @param {Object} outfitData - 搭配数据
 * @param {string} type - 错误类型，'preview'或'item'
 * @param {number} index - 项目索引
 * @returns {Object} 更新后的搭配数据
 */
function handleImageError(outfitData, type, index) {
  if (!outfitData) return outfitData;

  const defaultImageUrl = getDefaultImageUrl();

  if (type === 'preview') {
    // 更新搭配预览图
    outfitData.previewImage = defaultImageUrl;
  } else if (type === 'item' && index !== undefined && outfitData.items && outfitData.items[index]) {
    // 更新衣物图片
    outfitData.items[index].imageUrl = defaultImageUrl;
  } else if (type === 'similar' && index !== undefined && outfitData.similarOutfits && outfitData.similarOutfits[index]) {
    // 更新相似搭配图片
    outfitData.similarOutfits[index].previewImage = defaultImageUrl;
  }

  return outfitData;
}

/**
 * 验证图像URL是否有效
 * @param {string} url - 图像URL
 * @returns {boolean} URL是否有效
 */
function isValidImageUrl(url) {
  if (!url) return false;

  // 如果是本地路径或有效URL格式，认为是有效的
  if (url.startsWith('/') ||
      url.startsWith('http') ||
      url.startsWith('https') ||
      url.startsWith('wxfile://')) {
    return true;
  }

  return false;
}

/**
 * 从缓存获取URL
 * @param {string} fileID - 文件ID
 * @returns {string|null} 缓存的临时URL，如果不存在或已过期则返回null
 */
function getURLFromCache(fileID) {
  if (!fileID) return null;

  const app = getApp();
  if (!app.globalData || !app.globalData.urlCache) return null;

  const cachedData = app.globalData.urlCache[fileID];
  if (!cachedData) return null;

  // 检查URL是否已过期（9分钟为临界值）
  const now = Date.now();
  if (now - cachedData.timestamp > 540000) { // 9分钟
    console.log(`缓存的URL已过期: ${fileID}`);
    return null;
  }

  console.log(`从缓存获取URL: ${fileID}`);
  return cachedData.url;
}

/**
 * 更新URL缓存
 * @param {string} fileID - 文件ID
 * @param {string} url - 临时URL
 */
function updateURLCache(fileID, url) {
  if (!fileID || !url) return;

  const app = getApp();
  if (!app.globalData) app.globalData = {};
  if (!app.globalData.urlCache) app.globalData.urlCache = {};

  app.globalData.urlCache[fileID] = {
    url: url,
    timestamp: Date.now()
  };

  console.log(`已更新URL缓存: ${fileID}`);
}

/**
 * 刷新临时文件URL
 * @param {string} fileID - 文件ID
 * @param {Function} callback - 回调函数，参数为新的临时URL或null
 * @param {number} retryCount - 重试次数
 */
function refreshTempFileURL(fileID, callback, retryCount = 0) {
  if (!fileID || !fileID.includes('cloud://')) {
    console.error('无效的fileID:', fileID);
    callback(null);
    return;
  }

  // 检查该fileID的失败次数是否已达到最大重试次数
  if (imageLoadFailureCount[fileID] && imageLoadFailureCount[fileID] >= MAX_RETRY_ATTEMPTS) {
    console.warn(`图片 ${fileID} 已达到最大重试次数(${MAX_RETRY_ATTEMPTS})，使用默认图片`);
    callback(null);
    return;
  }

  // 清除可能已过期的缓存
  const app = getApp();
  if (app.globalData && app.globalData.urlCache && app.globalData.urlCache[fileID]) {
    const cachedData = app.globalData.urlCache[fileID];
    const now = Date.now();
    // 如果缓存超过8分钟，则认为已过期
    if (now - cachedData.timestamp > 480000) { // 8分钟
      console.log(`缓存的URL已过期，强制刷新: ${fileID}`);
      delete app.globalData.urlCache[fileID]; // 删除已过期的缓存
    }
  }

  // 检查缓存（经过上一步处理，此处只会获取有效缓存）
  const cachedUrl = getURLFromCache(fileID);
  if (cachedUrl) {
    console.log(`使用缓存的临时URL: ${fileID}`);
    // 重置失败计数
    if (imageLoadFailureCount[fileID]) {
      delete imageLoadFailureCount[fileID];
    }
    callback(cachedUrl);
    return;
  }

  console.log('正在刷新临时文件URL, fileID:', fileID, '重试次数:', retryCount);

  // 直接调用云函数获取最新临时URL
  wx.cloud.getTempFileURL({
    fileList: [fileID],
    success: res => {
      console.log('刷新临时URL成功:', res);
      if (res.fileList && res.fileList.length > 0 && res.fileList[0].tempFileURL) {
        const newUrl = res.fileList[0].tempFileURL;

        // 更新缓存
        updateURLCache(fileID, newUrl);

        // 重置失败计数
        if (imageLoadFailureCount[fileID]) {
          delete imageLoadFailureCount[fileID];
        }

        // 返回新的URL
        callback(newUrl);
      } else {
        console.error('获取临时URL成功，但返回结果无效');

        // 增加失败计数
        imageLoadFailureCount[fileID] = (imageLoadFailureCount[fileID] || 0) + 1;

        // 如果重试次数小于最大重试次数，则进行重试
        if (retryCount < MAX_RETRY_ATTEMPTS - 1) {
          console.log(`尝试第${retryCount + 1}次重试获取临时URL，失败计数: ${imageLoadFailureCount[fileID]}`);
          setTimeout(() => {
            refreshTempFileURL(fileID, callback, retryCount + 1);
          }, RETRY_DELAY); // 延迟后重试
        } else {
          console.warn(`图片 ${fileID} 已达到最大重试次数(${MAX_RETRY_ATTEMPTS})，使用默认图片`);
          callback(null);
        }
      }
    },
    fail: err => {
      console.error('刷新临时URL失败:', err);

      // 增加失败计数
      imageLoadFailureCount[fileID] = (imageLoadFailureCount[fileID] || 0) + 1;

      // 如果重试次数小于最大重试次数，则进行重试
      if (retryCount < MAX_RETRY_ATTEMPTS - 1) {
        console.log(`尝试第${retryCount + 1}次重试获取临时URL，失败计数: ${imageLoadFailureCount[fileID]}`);
        setTimeout(() => {
          refreshTempFileURL(fileID, callback, retryCount + 1);
        }, RETRY_DELAY); // 延迟后重试
      } else {
        console.warn(`图片 ${fileID} 已达到最大重试次数(${MAX_RETRY_ATTEMPTS})，使用默认图片`);
        callback(null);
      }
    }
  });
}

/**
 * 清除过期的URL缓存
 */
function clearExpiredURLCache() {
  try {
    const app = getApp();
    if (!app.globalData) app.globalData = {};
    if (!app.globalData.urlCache) app.globalData.urlCache = {};

    const now = Date.now();
    let clearedCount = 0;

    // 检查每个缓存项是否过期
    Object.keys(app.globalData.urlCache).forEach(fileID => {
      const cachedData = app.globalData.urlCache[fileID];
      if (cachedData && (now - cachedData.timestamp > URL_CACHE_EXPIRE_TIME)) {
        // 删除过期项
        delete app.globalData.urlCache[fileID];
        clearedCount++;
      }
    });

    if (clearedCount > 0) {
      console.log(`已清除 ${clearedCount} 个过期的URL缓存项`);
    }
  } catch (err) {
    console.error('清除过期URL缓存出错:', err);
  }
}

/**
 * 检查并刷新所有图片
 * @param {Object} outfitData - 搭配数据
 * @param {Function} updateCallback - 更新回调函数，参数为更新后的outfitData
 */
function checkAndRefreshAllImages(outfitData, updateCallback) {
  if (!outfitData) return;

  // 收集所有需要刷新的fileID
  const fileIDsToRefresh = [];
  const fileIDMap = {}; // 保存fileID与图片位置的映射

  // 检查预览图
  const previewFileID = outfitData.imageFileID ||
                      (outfitData.previewImage && outfitData.previewImage.includes('cloud://') ?
                       outfitData.previewImage : null);

  if (previewFileID) {
    fileIDsToRefresh.push(previewFileID);
    fileIDMap[previewFileID] = { type: 'preview' };
  }

  // 检查衣物图片
  if (outfitData.items && Array.isArray(outfitData.items)) {
    outfitData.items.forEach((item, itemIndex) => {
      const itemFileID = item.imageFileID ||
                      item.fileID ||
                      (item.imageUrl && item.imageUrl.includes('cloud://') ?
                       item.imageUrl : null);

      if (itemFileID) {
        fileIDsToRefresh.push(itemFileID);
        fileIDMap[itemFileID] = { type: 'item', itemIndex };
      }
    });
  }

  // 如果没有需要刷新的图片，返回
  if (fileIDsToRefresh.length === 0) {
    console.log('没有需要刷新的图片');
    return;
  }

  console.log(`需要检查 ${fileIDsToRefresh.length} 个图片URL`);

  // 首先尝试使用本地图片缓存
  localImageCache.batchProcessImages(fileIDsToRefresh)
    .then(localPathMap => {
      if (Object.keys(localPathMap).length > 0) {
        console.log('使用本地图片缓存更新图片:', Object.keys(localPathMap).length, '个');

        // 使用本地缓存的图片更新数据
        let updateCount = 0;
        let needUpdate = false;

        // 更新预览图
        if (previewFileID && localPathMap[previewFileID]) {
          outfitData.previewImage = localPathMap[previewFileID];
          updateCount++;
          needUpdate = true;
        }

        // 更新衣物图片
        if (outfitData.items && Array.isArray(outfitData.items)) {
          outfitData.items.forEach((item, index) => {
            const itemFileID = item.imageFileID ||
                            item.fileID ||
                            (item.imageUrl && item.imageUrl.includes('cloud://') ?
                             item.imageUrl : null);

            if (itemFileID && localPathMap[itemFileID]) {
              outfitData.items[index].imageUrl = localPathMap[itemFileID];
              updateCount++;
              needUpdate = true;
            }
          });
        }

        // 需要更新时，调用更新回调
        if (needUpdate && updateCount > 0 && updateCallback) {
          console.log(`已使用本地缓存更新 ${updateCount} 个图片`);
          updateCallback(outfitData);
          return;
        }
      }

      // 如果本地缓存不完整，继续使用云存储临时URL
      // 批量获取临时URL
      wx.cloud.getTempFileURL({
        fileList: fileIDsToRefresh,
        success: res => {
          console.log('批量获取临时URL成功:', res);
          let updateCount = 0;
          let needUpdate = false;

          if (res.fileList && res.fileList.length > 0) {
            // 使用新获取的临时URL更新数据
            res.fileList.forEach(file => {
              if (!file.fileID || !file.tempFileURL) return;

              // 更新URL缓存
              updateURLCache(file.fileID, file.tempFileURL);

              // 尝试下载到本地缓存
              localImageCache.downloadImageToCache(file.fileID, file.tempFileURL)
                .then(localPath => {
                  console.log(`图片已缓存到本地: ${file.fileID} -> ${localPath}`);
                })
                .catch(err => {
                  console.warn(`缓存图片失败: ${file.fileID}`, err);
                });

              // 根据fileID找到对应的图片并更新
              const fileInfo = fileIDMap[file.fileID];
              if (fileInfo) {
                if (fileInfo.type === 'preview') {
                  // 更新搭配预览图
                  const oldUrl = outfitData.previewImage;
                  if (oldUrl !== file.tempFileURL) {
                    outfitData.previewImage = file.tempFileURL;
                    updateCount++;
                    needUpdate = true;
                  }
                } else if (fileInfo.type === 'item' && fileInfo.itemIndex !== undefined) {
                  // 更新衣物图片
                  const itemIndex = fileInfo.itemIndex;
                  if (outfitData.items[itemIndex]) {
                    const oldUrl = outfitData.items[itemIndex].imageUrl;
                    if (oldUrl !== file.tempFileURL) {
                      outfitData.items[itemIndex].imageUrl = file.tempFileURL;
                      updateCount++;
                      needUpdate = true;
                    }
                  }
                }
              }
            });

            // 需要更新时，调用更新回调
            if (needUpdate && updateCount > 0 && updateCallback) {
              console.log(`已更新 ${updateCount} 个图片URL`);
              updateCallback(outfitData);
            } else {
              console.log('所有图片URL均为最新，无需更新');
            }
          }
        },
        fail: err => {
          console.error('批量获取临时URL失败:', err);
        }
      });
    })
    .catch(err => {
      console.error('处理本地图片缓存失败:', err);

      // 如果本地缓存处理失败，直接使用云存储临时URL
      wx.cloud.getTempFileURL({
        fileList: fileIDsToRefresh,
        success: res => {
          // 处理成功返回的临时URL
          // ...同上
        }
      });
    });
}

/**
 * 获取临时文件URL
 * @param {Array} fileIDs - 文件ID数组
 * @returns {Promise<Object>} 包含fileID到临时URL映射的Promise
 */
function getTempFileURLs(fileIDs) {
  return new Promise((resolve, reject) => {
    if (!fileIDs || !Array.isArray(fileIDs) || fileIDs.length === 0) {
      resolve({});
      return;
    }

    // 首先检查缓存中是否有这些fileID的临时URL
    const fileIDToURL = {};
    const uncachedFileIDs = [];

    fileIDs.forEach(fileID => {
      const cachedUrl = getURLFromCache(fileID);
      if (cachedUrl) {
        fileIDToURL[fileID] = cachedUrl;
      } else {
        uncachedFileIDs.push(fileID);
      }
    });

    // 如果所有fileID都有缓存，直接返回
    if (uncachedFileIDs.length === 0) {
      resolve(fileIDToURL);
      return;
    }

    // 获取未缓存的临时URL
    wx.cloud.getTempFileURL({
      fileList: uncachedFileIDs,
      success: result => {
        if (result && result.fileList) {
          result.fileList.forEach(file => {
            if (file.fileID && file.tempFileURL) {
              fileIDToURL[file.fileID] = file.tempFileURL;
              // 更新缓存
              updateURLCache(file.fileID, file.tempFileURL);
            }
          });
        }

        resolve(fileIDToURL);
      },
      fail: err => {
        console.error('获取临时URL失败:', err);
        resolve(fileIDToURL); // 即使失败也返回已缓存的URL
      }
    });
  });
}

/**
 * 重置指定fileID的失败计数
 * @param {string} fileID - 文件ID
 */
function resetImageFailureCount(fileID) {
  if (fileID && imageLoadFailureCount[fileID]) {
    delete imageLoadFailureCount[fileID];
    console.log(`已重置图片 ${fileID} 的失败计数`);
  }
}

/**
 * 清除所有图片加载失败计数
 */
function clearAllImageFailureCounts() {
  Object.keys(imageLoadFailureCount).forEach(fileID => {
    delete imageLoadFailureCount[fileID];
  });
  console.log('已清除所有图片加载失败计数');
}

/**
 * 获取指定fileID的失败计数
 * @param {string} fileID - 文件ID
 * @returns {number} 失败计数
 */
function getImageFailureCount(fileID) {
  return fileID ? (imageLoadFailureCount[fileID] || 0) : 0;
}

// 导出模块
module.exports = {
  getDefaultImageUrl,
  handleImageError,
  isValidImageUrl,
  getURLFromCache,
  updateURLCache,
  refreshTempFileURL,
  checkAndRefreshAllImages,
  getTempFileURLs,
  clearExpiredURLCache,
  resetImageFailureCount,
  clearAllImageFailureCounts,
  getImageFailureCount
};