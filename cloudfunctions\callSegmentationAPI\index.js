// 云函数入口文件
const cloud = require('wx-server-sdk')
const request = require('request')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 云函数入口函数
exports.main = async (event, context) => {
  const { imageUrl } = event;
  
  if (!imageUrl) {
    return {
      success: false,
      message: '缺少图片URL参数'
    };
  }
  
  // 调用抠图API
  return new Promise((resolve, reject) => {
    request(
      {
        method: "POST",
        url: "https://techsz.aoscdn.com/api/tasks/visual/segmentation",
        headers: {
          "X-API-KEY": "wxmshz10xif8mfpvy",
        },
        formData: {
          sync: "1",
          image_url: imageUrl,
          type: "object",
          crop: "1",
        },
        json: true
      },
      function (error, response) {
        if (error) {
          console.error('抠图API请求失败:', error);
          resolve({
            success: false,
            message: '抠图API请求失败: ' + error.message,
            error: error
          });
          return;
        }
        
        if (!response || !response.body) {
          console.error('抠图API返回空响应');
          resolve({
            success: false,
            message: '抠图API返回空响应'
          });
          return;
        }
        
        // 检查API返回状态
        if (response.body.status !== 200) {
          console.error('抠图API返回错误状态:', response.body);
          resolve({
            success: false,
            message: '抠图API返回错误状态: ' + (response.body.message || '未知错误'),
            error: response.body
          });
          return;
        }
        
        // 检查处理进度
        if (response.body.data && response.body.data.progress >= 100 && response.body.data.state === 1) {
          console.log('抠图处理成功:', response.body);
          resolve({
            success: true,
            data: response.body.data
          });
        } else {
          console.error('抠图处理未完成或失败:', response.body);
          resolve({
            success: false,
            message: '抠图处理未完成或失败',
            error: response.body
          });
        }
      }
    );
  });
}
