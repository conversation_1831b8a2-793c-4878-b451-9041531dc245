// 会员兑换码云函数
// 功能：验证会员兑换码并为用户提供相应的会员权益（月度、季度、年度会员）
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 验证兑换码是否有效
 * @param {string} code - 用户输入的兑换码
 * @returns {Promise<Object>} - 兑换码信息
 */
async function validateCode(code) {
  try {
    // 查询兑换码是否存在
    const codeResult = await db.collection('redemptionCodes')
      .where({
        code: code,
        isActive: true,
        expirationDate: _.gt(new Date()), // 确保兑换码未过期
        rewardType: 'membership' // 确保是会员兑换码
      })
      .get()

    if (codeResult.data.length === 0) {
      return {
        isValid: false,
        message: '无效的会员兑换码或已过期'
      }
    }

    return {
      isValid: true,
      codeInfo: codeResult.data[0]
    }
  } catch (err) {
    console.error('验证会员兑换码失败:', err)
    throw new Error('验证会员兑换码时发生错误')
  }
}

/**
 * 检查用户是否已经使用过该兑换码
 * @param {string} openid - 用户openid
 * @param {string} codeId - 兑换码ID
 * @returns {Promise<boolean>} - 是否已使用
 */
async function checkIfCodeUsed(openid, codeId) {
  try {
    const result = await db.collection('redemptionHistory')
      .where({
        userId: openid,
        codeId: codeId
      })
      .count()

    return result.total > 0
  } catch (err) {
    console.error('检查兑换历史失败:', err)
    throw new Error('检查兑换历史时发生错误')
  }
}

/**
 * 应用会员兑换码奖励
 * @param {string} openid - 用户openid
 * @param {Object} codeInfo - 兑换码信息
 * @returns {Promise<Object>} - 应用奖励的结果
 */
async function applyMembershipReward(openid, codeInfo) {
  try {
    const userResult = await db.collection('users')
      .where({ _openid: openid })
      .get()

    if (userResult.data.length === 0) {
      throw new Error('用户不存在')
    }

    const userData = userResult.data[0]

    // 计算会员过期时间
    let membershipDays = 0
    let membershipType = 'VIP'

    // 根据会员类型设置天数
    switch (codeInfo.membershipType) {
      case 'weekly':
        membershipDays = 7
        break
      case 'monthly':
        membershipDays = 30
        break
      case 'quarterly':
        membershipDays = 90
        break
      case 'yearly':
        membershipDays = 365
        break
      default:
        membershipDays = 30 // 默认为月度会员
    }

    // 计算新的过期日期
    let newExpireDate

    // 如果用户已经是VIP会员且未过期，则在现有过期日期上增加天数
    if (userData.memberType === 'VIP' && userData.memberExpireDate && new Date(userData.memberExpireDate) > new Date()) {
      newExpireDate = new Date(userData.memberExpireDate)
      newExpireDate.setDate(newExpireDate.getDate() + membershipDays)
    } else {
      // 否则从当前日期开始计算
      newExpireDate = new Date()
      newExpireDate.setDate(newExpireDate.getDate() + membershipDays)
    }

    // 更新用户数据
    await db.collection('users')
      .doc(userData._id)
      .update({
        data: {
          memberType: membershipType,
          memberExpireDate: newExpireDate,
          memberDaysLeft: membershipDays,
          updatedAt: db.serverDate()
        }
      })

    // 记录兑换历史
    await db.collection('redemptionHistory').add({
      data: {
        userId: openid,
        codeId: codeInfo._id,
        codeName: codeInfo.name || '会员兑换码',
        codeValue: codeInfo.code,
        rewardType: 'membership',
        membershipType: codeInfo.membershipType,
        membershipDays: membershipDays,
        redeemedAt: new Date()
      }
    })

    // 如果是一次性兑换码，标记为已使用
    if (codeInfo.isOneTime) {
      await db.collection('redemptionCodes')
        .doc(codeInfo._id)
        .update({
          data: {
            isActive: false,
            usedAt: new Date(),
            usedCount: _.inc(1)
          }
        })
    } else {
      // 如果不是一次性兑换码，增加使用次数
      await db.collection('redemptionCodes')
        .doc(codeInfo._id)
        .update({
          data: {
            usedCount: _.inc(1)
          }
        })
    }

    return {
      success: true,
      membershipType: codeInfo.membershipType,
      membershipDays: membershipDays,
      expireDate: newExpireDate,
      userData: {
        ...userData,
        memberType: membershipType,
        memberExpireDate: newExpireDate,
        memberDaysLeft: membershipDays
      }
    }
  } catch (err) {
    console.error('应用会员奖励失败:', err)
    throw new Error('应用会员奖励时发生错误')
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    const { code } = event

    // 验证兑换码
    const validationResult = await validateCode(code)
    if (!validationResult.isValid) {
      return {
        success: false,
        message: validationResult.message
      }
    }

    const codeInfo = validationResult.codeInfo

    // 检查是否已使用过该兑换码（对于非一次性兑换码，允许所有用户使用；对于一次性兑换码，需检查该用户是否使用过）
    if (codeInfo.isOneTime) {
      const isUsed = await checkIfCodeUsed(openid, codeInfo._id)
      if (isUsed) {
        return {
          success: false,
          message: '您已经使用过该兑换码'
        }
      }
    }

    // 应用会员奖励
    const rewardResult = await applyMembershipReward(openid, codeInfo)

    return {
      success: true,
      message: '会员兑换成功！',
      ...rewardResult
    }
  } catch (error) {
    console.error('会员兑换码处理失败:', error)
    return {
      success: false,
      message: error.message || '兑换失败，请稍后再试'
    }
  }
}
