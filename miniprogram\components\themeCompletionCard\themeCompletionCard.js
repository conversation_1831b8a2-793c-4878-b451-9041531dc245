// components/themeCompletionCard/themeCompletionCard.js
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    themeStyle: {
      type: String,
      value: 'autumn'
    },
    colors: {
      type: Object,
      value: {}
    },
    pinkBlueColors: {
      type: Object,
      value: {}
    },
    blackWhiteColors: {
      type: Object,
      value: {}
    },
    memberDaysLeft: {
      type: Number,
      value: 30
    },
    taskImages: {
      type: Array,
      value: []
    }
  },
  
  data: {
    animationData: {},
    contentAnimationData: {},
    confettiAnimation: false
  },
  
  lifetimes: {
    attached: function() {
      // 创建动画实例
      this.maskAnimation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease'
      });
      
      this.contentAnimation = wx.createAnimation({
        duration: 400,
        timingFunction: 'ease'
      });
    }
  },
  
  observers: {
    'visible': function(visible) {
      if (visible) {
        this.showModal();
      } else {
        this.hideModal(false);
      }
    }
  },
  
  methods: {
    // 显示弹窗
    showModal: function() {
      // 背景动画
      this.maskAnimation.opacity(1).step();
      // 内容动画（从下向上弹出）
      this.contentAnimation.translateY(0).opacity(1).step();
      
      this.setData({
        animationData: this.maskAnimation.export(),
        contentAnimationData: this.contentAnimation.export()
      });
      
      // 启动五彩纸屑动画
      setTimeout(() => {
        this.setData({
          confettiAnimation: true
        });
      }, 300);
    },
    
    // 隐藏弹窗
    hideModal: function(triggerEvent = true) {
      // 背景动画
      this.maskAnimation.opacity(0).step();
      // 内容动画（向下退出）
      this.contentAnimation.translateY('80rpx').opacity(0).step();
      
      this.setData({
        animationData: this.maskAnimation.export(),
        contentAnimationData: this.contentAnimation.export(),
        confettiAnimation: false
      });
      
      if (triggerEvent) {
        // 延迟触发关闭事件，等待动画完成
        setTimeout(() => {
          this.triggerEvent('close');
        }, 300);
      }
    },
    
    // 点击遮罩层
    onMaskTap: function() {
      this.hideModal();
    },
    
    // 点击内容区域，阻止冒泡
    onContentTap: function(e) {
      return;
    },
    
    // 点击关闭按钮
    onCloseButtonTap: function() {
      this.hideModal();
    },
    
    // 点击查看会员权益按钮
    onViewBenefitsTap: function() {
      this.hideModal();
      setTimeout(() => {
        this.triggerEvent('viewBenefits');
      }, 300);
    }
  }
})
