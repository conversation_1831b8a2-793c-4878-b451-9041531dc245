/**
 * 体力值管理模块使用示例
 * 本文件展示了如何在实际场景中使用energyManager模块
 */

// 引入体力值管理模块
const energyManager = require('./energyManager');

/**
 * 示例：在AI抠图前检查并消耗体力值
 * @param {Function} onSuccess - 成功回调
 * @param {Function} onFailure - 失败回调
 */
function processImageWithEnergyCheck(onSuccess, onFailure) {
  // 检查并消耗体力值
  energyManager.consumeEnergyForAction('IMAGE_EXTRACTION')
    .then(result => {
      if (result.success) {
        console.log('体力值检查通过，已消耗', result.consumedEnergy, '点体力值');
        console.log('剩余体力值:', result.remainingEnergy);
        
        // 执行成功回调
        if (onSuccess) {
          onSuccess(result);
        }
      } else {
        console.log('体力值不足，无法执行操作');
        
        // 显示提示
        wx.showModal({
          title: '体力值不足',
          content: '您的体力值不足，无法执行AI抠图操作。请通过观看广告或完成每日任务获取更多体力值。',
          confirmText: '去获取',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 跳转到设置页面获取体力值
              wx.navigateTo({
                url: '/page/settings/settings'
              });
            }
          }
        });
        
        // 执行失败回调
        if (onFailure) {
          onFailure(result);
        }
      }
    })
    .catch(err => {
      console.error('体力值检查失败:', err);
      
      // 显示错误提示
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      
      // 执行失败回调
      if (onFailure) {
        onFailure(err);
      }
    });
}

/**
 * 示例：在AI创建搭配前检查并消耗体力值
 * @param {Function} onSuccess - 成功回调
 * @param {Function} onFailure - 失败回调
 */
function createOutfitWithEnergyCheck(onSuccess, onFailure) {
  // 检查并消耗体力值
  energyManager.consumeEnergyForAction('OUTFIT_CREATION')
    .then(result => {
      if (result.success) {
        console.log('体力值检查通过，已消耗', result.consumedEnergy, '点体力值');
        console.log('剩余体力值:', result.remainingEnergy);
        
        // 执行成功回调
        if (onSuccess) {
          onSuccess(result);
        }
      } else {
        console.log('体力值不足，无法执行操作');
        
        // 显示提示
        wx.showModal({
          title: '体力值不足',
          content: '您的体力值不足，无法执行AI创建搭配操作。请通过观看广告或完成每日任务获取更多体力值。',
          confirmText: '去获取',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 跳转到设置页面获取体力值
              wx.navigateTo({
                url: '/page/settings/settings'
              });
            }
          }
        });
        
        // 执行失败回调
        if (onFailure) {
          onFailure(result);
        }
      }
    })
    .catch(err => {
      console.error('体力值检查失败:', err);
      
      // 显示错误提示
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      
      // 执行失败回调
      if (onFailure) {
        onFailure(err);
      }
    });
}

/**
 * 示例：在AI分析衣柜前检查并消耗体力值
 * @param {Function} onSuccess - 成功回调
 * @param {Function} onFailure - 失败回调
 */
function analyzeWardrobeWithEnergyCheck(onSuccess, onFailure) {
  // 检查并消耗体力值
  energyManager.consumeEnergyForAction('WARDROBE_ANALYSIS')
    .then(result => {
      if (result.success) {
        console.log('体力值检查通过，已消耗', result.consumedEnergy, '点体力值');
        console.log('剩余体力值:', result.remainingEnergy);
        
        // 执行成功回调
        if (onSuccess) {
          onSuccess(result);
        }
      } else {
        console.log('体力值不足，无法执行操作');
        
        // 显示提示
        wx.showModal({
          title: '体力值不足',
          content: '您的体力值不足，无法执行AI分析衣柜操作。请通过观看广告或完成每日任务获取更多体力值。',
          confirmText: '去获取',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 跳转到设置页面获取体力值
              wx.navigateTo({
                url: '/page/settings/settings'
              });
            }
          }
        });
        
        // 执行失败回调
        if (onFailure) {
          onFailure(result);
        }
      }
    })
    .catch(err => {
      console.error('体力值检查失败:', err);
      
      // 显示错误提示
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      
      // 执行失败回调
      if (onFailure) {
        onFailure(err);
      }
    });
}

/**
 * 示例：直接减少体力值
 * @param {Number} amount - 要减少的体力值数量
 * @returns {Promise<Object>} 包含更新后体力值的Promise
 */
function decreaseUserEnergy(amount) {
  return energyManager.decreaseEnergy(amount)
    .then(result => {
      console.log('体力值减少成功，当前体力值:', result.catEnergy);
      
      // 显示提示
      wx.showToast({
        title: `消耗了${amount}点体力值`,
        icon: 'none'
      });
      
      return result;
    })
    .catch(err => {
      console.error('体力值减少失败:', err);
      
      // 显示错误提示
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      
      return Promise.reject(err);
    });
}

// 导出模块
module.exports = {
  processImageWithEnergyCheck,
  createOutfitWithEnergyCheck,
  analyzeWardrobeWithEnergyCheck,
  decreaseUserEnergy
};
