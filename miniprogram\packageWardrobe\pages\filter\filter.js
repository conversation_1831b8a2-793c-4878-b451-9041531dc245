const sensitiveWordsFilter = require('../common/sensitiveWordsFilter');
const userManager = require('../closet/modules/userManager');
const closetUtils = require('../closet/modules/closetUtils');
const downloadClothesImages = require('../closet/downloadClothesImages');
const limitManager = require('../common/limitManager');

// 缓存相关常量
const CLOTHES_CACHE_KEY = 'user_clothes_cache';
const CACHE_TIMESTAMP_KEY = 'clothes_cache_timestamp';
const CACHE_MAX_AGE = 3600000; // 缓存最长有效期为1小时(毫秒)
const USER_WARDROBES_CACHE_KEY = 'user_wardrobes_'; // 用户衣柜缓存键

Page({
  data: {
    // 基本数据
    userOpenId: '',
    isLoading: true,
    clothes: [],           // 所有衣物数据
    filteredClothes: [],   // 筛选后的衣物数据
    displayClothes: [],    // 当前显示的衣物数据

    // 搜索相关
    searchKeyword: '',     // 搜索关键词
    isSearchActive: false, // 是否有搜索关键词

    // 导航栏相关
    statusBarHeight: 0,    // 状态栏高度
    navBarHeight: 0,       // 导航栏高度

    // 筛选相关
    selectedCategory: 'latest', // 默认选中最新
    selectedTypeDetail: '',     // 默认不选择细分类
    typeDetails: [],            // 当前类别的所有细分类
    listTitle: '最新衣物',       // 列表标题

    // 新增筛选选项
    seasonOptions: ['春季', '夏季', '秋季', '冬季'],  // 季节选项
    styleOptions: [],      // 风格选项，将从数据中获取
    colorOptions: [],      // 颜色选项，将从数据中获取
    storageLocationOptions: [], // 存储位置选项，将从数据中获取
    allStyleOptions: [],   // 所有可能的风格选项
    allColorOptions: [],   // 所有可能的颜色选项
    allStorageLocationOptions: [], // 所有可能的存储位置选项
    selectedSeasons: [],   // 选中的季节
    selectedStyles: [],    // 选中的风格
    selectedColors: [],    // 选中的颜色
    selectedStorageLocations: [], // 选中的存储位置

    // 季节选中状态对象
    seasonIsSelected: {
      '春季': false,
      '夏季': false,
      '秋季': false,
      '冬季': false
    },

    // 筛选季节选中状态对象
    filterSeasonIsSelected: {
      '春季': false,
      '夏季': false,
      '秋季': false,
      '冬季': false
    },

    // 筛选风格选中状态对象
    filterStyleIsSelected: {},

    // 筛选颜色选中状态对象
    filterColorIsSelected: {},

    // 筛选存储位置选中状态对象
    filterStorageLocationIsSelected: {},

    // 衣柜筛选相关
    wardrobeOptions: [],   // 衣柜选项，将从数据中获取
    selectedWardrobe: '',  // 选中的衣柜ID
    filterWardrobeIsSelected: {}, // 衣柜选中状态对象

    // 显示在UI上的筛选值（已过滤掉undefined和null）
    displaySeasons: '',    // 用于显示的季节字符串
    displayStyles: '',     // 用于显示的风格字符串
    displayColors: '',     // 用于显示的颜色字符串
    displayStorageLocations: '', // 用于显示的存储位置字符串
    displayWardrobe: '',   // 用于显示的衣柜名称

    hasActiveFilters: false, // 是否有活动的筛选条件

    // 筛选面板展开状态
    activeFilterPanel: '', // 当前展开的筛选面板：'season'、'style'、'color'、'subcategory'、'storageLocation'、'wardrobe'

    // 分页相关
    pageSize: 20,              // 每页显示数量
    currentPage: 1,            // 当前页码
    isLoadingMore: false,      // 是否正在加载更多

    // 统计数据
    totalCount: 0,             // 所有衣物数量
    latestCount: 0,            // 最新衣物数量
    categories: [],            // 类别数据

    // 详情弹窗
    showDetail: false,         // 是否显示详情
    detailClothes: null,       // 详情数据

    // 默认图片
    tempPlaceholderUrl: '',    // 默认图片路径

    // 编辑相关
    showEditModal: false,      // 是否显示编辑弹窗
    editingClothing: {},       // 当前编辑的衣物数据
    editingId: '',             // 当前编辑的衣物ID
    originalClothing: {},      // 编辑前的原始衣物数据，用于检测变更
    fromDetailView: false,     // 是否从详情页面进入编辑界面
    editingFromDetailId: '',   // 从详情页进入编辑时的衣物ID

    // 选择器相关
    showCategoryPicker: false, // 是否显示类别选择器
    tempSelectedCategory: '',  // 临时选中的类别
    validCategories: ['上衣', '裤子', '裙子', '外套', '鞋子', '配饰'], // 有效的类别

    showDatePicker: false,     // 是否显示日期选择器
    datePickerType: '',        // 日期选择器类型：'lastWorn'、'purchased'
    datePickerTitle: '选择日期', // 日期选择器标题
    datePickerValue: '',       // 日期选择器当前值

    showSeasonPicker: false,   // 是否显示季节选择器
    tempSelectedSeason: [],    // 临时选中的季节

    // 历史值建议相关
    suggestions: {
      type_detail: [],
      color: [],
      style: [],
      size: [],
      brand: [],
      purchaseChannel: [],
      storageLocation: [],
      material: []
    },
    showingSuggestions: false,    // 是否显示建议
    currentSuggestionField: '',   // 当前显示建议的字段

    // 衣柜选择器相关
    wardrobes: [],               // 用户的所有衣柜
    wardrobeIsSelected: {},      // 衣柜选中状态
    showWardrobePicker: false,   // 是否显示衣柜选择器
    tempSelectedWardrobe: '',    // 临时选中的衣柜ID

    // 类别选择器相关
    userCategories: [],         // 用户的所有类别
    customCategoryValue: '',     // 自定义类别输入值

    // 批量编辑相关
    isSelectionMode: false,      // 是否处于选择模式
    selectedClothes: [],        // 已选中的衣物ID列表
    clothesIsSelected: {},      // 记录每件衣物的选中状态
    selectAll: false,           // 是否全选

    // 批量编辑弹窗
    showBatchEditModal: false,  // 是否显示批量编辑弹窗
    batchEditData: {},          // 批量编辑的数据
    isBatchEdit: false          // 是否是批量编辑模式
  },

  onLoad: function(options) {
    console.log('筛选页面加载开始...');

    // 获取状态栏和导航栏高度
    this.getSystemInfo();

    // 检查是否有初始类别筛选
    if (options && options.category) {
      this.setData({ selectedCategory: options.category });
    }

    // 添加搜索输入框焦点状态初始化
    this.setData({
      searchKeywordFocus: false
    });

    // 先从缓存获取userOpenId
    const cachedUserOpenId = wx.getStorageSync('userOpenId');

    if (cachedUserOpenId) {
      console.log('页面加载时从缓存获取到userOpenId:', cachedUserOpenId);

      this.setData({
        userOpenId: cachedUserOpenId
      });

      // 加载用户衣柜数据
      this.loadWardrobes().then((wardrobes) => {
        console.log('衣柜数据加载完成');

        // 初始化衣柜筛选选项
        this.initWardrobeOptions(wardrobes);

        // 从缓存加载衣物数据
        this.loadFromLocalCache();
      }).catch(err => {
        console.error('加载衣柜数据失败:', err);
      });
    } else {
      // 获取用户OpenID
      this.getUserOpenId();
    }
  },

  // 获取系统信息设置导航栏高度
  getSystemInfo: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;
    const navBarHeight = (systemInfo.platform === 'android') ? 48 : 44;

    this.setData({
      statusBarHeight: statusBarHeight,
      navBarHeight: navBarHeight
    });

    console.log('系统信息:', systemInfo);
    console.log('状态栏高度:', statusBarHeight);
    console.log('导航栏高度:', navBarHeight);
  },

  onShow: function() {
    // 每次显示页面时都重新加载衣柜数据
    wx.showToast({
      title: '长按衣服可批量编辑',
      icon: 'none'
    });
    this.loadWardrobes().then((wardrobes) => {
      console.log('页面显示时重新加载衣柜数据:', wardrobes);
      // 初始化衣柜筛选选项
      this.initWardrobeOptions(wardrobes);

      // 应用筛选条件，确保衣柜筛选生效
      this.applyAllFilters();
    }).catch(err => {
      console.error('加载衣柜数据失败:', err);
    });

    // 如果已有数据且无需立即刷新，则优先使用缓存
    if (this.data.clothes.length > 0 && !this.needsImmediateRefresh()) {
      // 已有数据，暂不刷新
      console.log('使用现有数据，不刷新');
      this.updateFilteredClothes();
      return;
    }

    // 需要刷新数据
    if (this.data.userOpenId) {
      this.loadClothesData();
    }
  },

  // 初始化筛选数据
  initFilterData: function(clothes) {
    // 提取所有的风格和颜色值
    const styles = new Set();
    const colors = new Set();

    clothes.forEach(item => {
      if (item.style) styles.add(item.style);
      if (item.color) colors.add(item.color);
    });

    // 初始化筛选季节选中状态
    const filterSeasonIsSelected = {};
    this.data.seasonOptions.forEach(season => {
      filterSeasonIsSelected[season] = false;
    });

    this.setData({
      styleOptions: Array.from(styles),
      colorOptions: Array.from(colors),
      filterSeasonIsSelected: filterSeasonIsSelected
    });
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 获取当前用户的OpenID
  getUserOpenId: function() {
    closetUtils.showLoading('加载中...');

    // 先尝试从本地缓存获取userOpenId
    const cachedUserOpenId = wx.getStorageSync('userOpenId');

    if (cachedUserOpenId) {
      console.log('从缓存获取到userOpenId:', cachedUserOpenId);

      this.setData({
        userOpenId: cachedUserOpenId
      });

      // 获取到OpenID后，从缓存加载衣物数据
      this.loadFromLocalCache();
      return;
    }

    // 缓存中没有，从云端获取
    console.log('缓存中没有userOpenId，从云端获取');

    userManager.getUserOpenId()
      .then(openid => {
        console.log('从云端获取到userOpenId:', openid);

        // 保存到缓存
        wx.setStorageSync('userOpenId', openid);

        this.setData({
          userOpenId: openid
        });

        // 获取到OpenID后，从缓存加载衣物数据
        this.loadFromLocalCache();
      })
      .catch(err => {
        console.error('获取用户OpenID失败:', err);
        closetUtils.hideLoading();
        closetUtils.showErrorToast('获取用户信息失败');
      });
  },

  // 从本地缓存加载衣物数据
  loadFromLocalCache: function() {
    const cacheKey = `${CLOTHES_CACHE_KEY}_${this.data.userOpenId}`;
    const cachedData = wx.getStorageSync(cacheKey);

    if (cachedData && cachedData.clothes && cachedData.clothes.length > 0) {
      console.log('从本地缓存加载衣物数据:', cachedData.clothes.length, '件');

      // 更新页面数据
      this.processClothesData(cachedData.clothes);

      // 更新加载状态
      this.setData({
        isLoading: false
      });

      // 加载图片
      this.loadClothesImages(cachedData.clothes);

      closetUtils.hideLoading();
      return true;
    }

    console.log('本地缓存不存在或为空，从云端获取数据');
    this.loadClothesFromCloud();
    return false;
  },

  // 从云端加载衣物数据
  loadClothesFromCloud: function() {
    // 调用云函数获取数据
    wx.cloud.callFunction({
      name: 'getAllClothes',
      data: {
        userOpenId: this.data.userOpenId
      }
    })
    .then(res => {
      closetUtils.hideLoading();

      // 处理返回的数据
      const result = res.result || {};

      if (!result.success) {
        console.error('获取衣物列表失败:', result.error || '未知错误');
        throw new Error('获取衣物列表失败: ' + (result.message || '未知错误'));
      }

      const allClothes = result.data || [];
      const totalClothes = result.total || 0;

      console.log(`成功获取所有衣物: ${allClothes.length}件，数据库总数: ${totalClothes}件`);

      // 更新本地缓存
      this.updateLocalCache(allClothes);

      // 处理数据并更新UI
      this.processClothesData(allClothes);

      // 更新加载状态
      this.setData({ isLoading: false });

      // 加载图片
      this.loadClothesImages(allClothes);
    })
    .catch(err => {
      console.error('获取衣物列表失败:', err);
      closetUtils.hideLoading();
      closetUtils.showErrorToast('获取衣物失败: ' + (err.message || '未知错误'));

      // 更新加载状态
      this.setData({ isLoading: false });
    });
  },

  // 更新本地缓存
  updateLocalCache: function(clothes) {
    if (!clothes || !this.data.userOpenId) {
      console.warn('updateLocalCache: 衣物数据为空或用户ID为空，不更新缓存');
      return;
    }

    try {
      const cacheKey = `${CLOTHES_CACHE_KEY}_${this.data.userOpenId}`;
      const cacheData = {
        clothes: clothes,
        timestamp: new Date().getTime()
      };

      // 更新缓存
      wx.setStorageSync(cacheKey, cacheData);
      wx.setStorageSync(CACHE_TIMESTAMP_KEY, cacheData.timestamp);

      console.log('本地衣物缓存已更新:', clothes.length, '件');
    } catch (err) {
      console.error('更新本地缓存失败:', err);
    }
  },

  // 处理衣物数据，分类并统计各类别数量
  processClothesData: function(clothes) {
    if (!clothes || clothes.length === 0) {
      this.setData({
        clothes: [],
        filteredClothes: [],
        displayClothes: [],
        totalCount: 0,
        latestCount: 0,
        categories: [],
        styleOptions: [],
        colorOptions: [],
        storageLocationOptions: [],
        allStyleOptions: [],
        allColorOptions: [],
        allStorageLocationOptions: []
      });
      return;
    }

    // 保存所有衣物数据
    this.setData({ clothes: clothes });

    // 统计总数量
    const totalCount = clothes.length;

    // 获取最近一个月的衣物作为最新衣物
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    const latestClothes = clothes.filter(item => {
      const createTime = item.createTime ? new Date(item.createTime) : null;
      return createTime && createTime > oneMonthAgo;
    });

    // 获取所有类别和统计各类别数量
    const categoryMap = new Map();
    clothes.forEach(item => {
      if (item.category) {
        if (!categoryMap.has(item.category)) {
          categoryMap.set(item.category, {
            category: item.category,
            name: item.category,
            count: 1
          });
        } else {
          categoryMap.get(item.category).count++;
        }
      }
    });

    const categories = Array.from(categoryMap.values());

    // 获取所有衣物的风格、颜色和季节选项（备用）
    const allStyleSet = new Set();
    const allColorSet = new Set();
    const allStorageLocationSet = new Set();

    clothes.forEach(item => {
      if (item.style) {
        allStyleSet.add(item.style);
      }
      if (item.color) {
        allColorSet.add(item.color);
      }
      if (item.storageLocation) {
        allStorageLocationSet.add(item.storageLocation);
      }
    });

    // 存储所有可能的筛选选项（用于需要显示所有选项的情况）
    this.setData({
      allStyleOptions: Array.from(allStyleSet),
      allColorOptions: Array.from(allColorSet),
      allStorageLocationOptions: Array.from(allStorageLocationSet)
    });

    // 更新状态
    this.setData({
      totalCount: totalCount,
      latestCount: latestClothes.length,
      categories: categories
    });

    // 根据当前所选类别更新筛选选项
    this.updateFilterOptionsByCategory(this.data.selectedCategory);

    // 应用所有筛选条件
    this.applyAllFilters();
  },

  // 初始化筛选季节选中状态对象
  initFilterSeasonIsSelected: function() {
    const filterSeasonIsSelected = {};
    this.data.seasonOptions.forEach(season => {
      filterSeasonIsSelected[season] = this.data.selectedSeasons.includes(season);
    });

    this.setData({
      filterSeasonIsSelected: filterSeasonIsSelected
    });
  },

  // 初始化筛选风格选中状态对象
  initFilterStyleIsSelected: function(styleOptions) {
    const filterStyleIsSelected = {};
    styleOptions.forEach(style => {
      filterStyleIsSelected[style] = this.data.selectedStyles.includes(style);
    });

    this.setData({
      filterStyleIsSelected: filterStyleIsSelected
    });
  },

  // 初始化筛选颜色选中状态对象
  initFilterColorIsSelected: function(colorOptions) {
    const filterColorIsSelected = {};
    colorOptions.forEach(color => {
      filterColorIsSelected[color] = this.data.selectedColors.includes(color);
    });

    this.setData({
      filterColorIsSelected: filterColorIsSelected
    });
  },

  // 初始化筛选存储位置选中状态对象
  initFilterStorageLocationIsSelected: function(storageLocationOptions) {
    const filterStorageLocationIsSelected = {};
    storageLocationOptions.forEach(location => {
      filterStorageLocationIsSelected[location] = this.data.selectedStorageLocations.includes(location);
    });

    this.setData({
      filterStorageLocationIsSelected: filterStorageLocationIsSelected
    });
  },

  // 加载衣物图片
  loadClothesImages: function(clothes) {
    if (!clothes || clothes.length === 0) {
      console.log('没有衣物数据，不加载图片');
      return;
    }

    console.log('筛选页面加载衣物图片...');

    // 下载图片
    downloadClothesImages.call(this, clothes)
      .then(() => {
        console.log('衣物图片加载完成');

        // 更新筛选数据
        this.applyAllFilters();
      })
      .catch(err => {
        console.error('加载衣物图片出错:', err);
      });
  },

  // 选择类别进行筛选
  selectCategory: function(e) {
    const category = e.currentTarget.dataset.category;

    // 打印调试信息
    console.log('筛选函数selectCategory被调用，选中类别:', category);

    // 更新选中的类别
    this.setData({
      selectedCategory: category,
      selectedTypeDetail: ''
    });

    // 根据所选类别更新筛选选项
    this.updateFilterOptionsByCategory(category);

    // 应用所有筛选条件
    this.applyAllFilters();
  },

  // 根据所选类别更新筛选选项
  updateFilterOptionsByCategory: function(category) {
    const clothes = this.data.clothes;
    const filteredClothes = category === 'all' ? clothes :
                           category === 'latest' ? clothes.filter(item => {
                             const oneMonthAgo = new Date();
                             oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
                             const createTime = item.createTime ? new Date(item.createTime) : null;
                             return createTime && createTime > oneMonthAgo;
                           }) :
                           clothes.filter(item => item.category === category);

    // 提取所选类别中的细分类
    const typeDetailSet = new Set();
    filteredClothes.forEach(item => {
      if (item.type_detail) {
        typeDetailSet.add(item.type_detail);
      }
    });

    // 提取所选类别中的风格
    const styleSet = new Set();
    filteredClothes.forEach(item => {
      if (item.style) {
        styleSet.add(item.style);
      }
    });

    // 提取所选类别中的颜色
    const colorSet = new Set();
    filteredClothes.forEach(item => {
      if (item.color) {
        colorSet.add(item.color);
      }
    });

    // 提取所选类别中的存储位置
    const storageLocationSet = new Set();
    filteredClothes.forEach(item => {
      if (item.storageLocation) {
        storageLocationSet.add(item.storageLocation);
      }
    });

    // 提取所选类别中的季节
    const seasonSet = new Set();
    const allSeasons = ['春季', '夏季', '秋季', '冬季'];
    allSeasons.forEach(season => {
      // 检查是否有任何衣物包含这个季节
      const hasSeasonItems = filteredClothes.some(item =>
        item.season && item.season.includes(season)
      );
      if (hasSeasonItems) {
        seasonSet.add(season);
      }
    });

    // 处理已选中但不在新选项中的筛选项
    const seasonOptions = Array.from(seasonSet);
    const styleOptions = Array.from(styleSet);
    const colorOptions = Array.from(colorSet);
    const storageLocationOptions = Array.from(storageLocationSet);

    // 筛选出仍然有效的已选季节
    const validSelectedSeasons = this.data.selectedSeasons.filter(
      season => seasonOptions.includes(season)
    );

    // 筛选出仍然有效的已选风格
    const validSelectedStyles = this.data.selectedStyles.filter(
      style => styleOptions.includes(style)
    );

    // 筛选出仍然有效的已选颜色
    const validSelectedColors = this.data.selectedColors.filter(
      color => colorOptions.includes(color)
    );

    // 筛选出仍然有效的已选存储位置
    const validSelectedStorageLocations = this.data.selectedStorageLocations.filter(
      location => storageLocationOptions.includes(location)
    );

    // 更新筛选选项和有效的选中项
    this.setData({
      typeDetails: Array.from(typeDetailSet),
      styleOptions: styleOptions,
      colorOptions: colorOptions,
      storageLocationOptions: storageLocationOptions,
      seasonOptions: seasonOptions,
      selectedSeasons: validSelectedSeasons,
      selectedStyles: validSelectedStyles,
      selectedColors: validSelectedColors,
      selectedStorageLocations: validSelectedStorageLocations,
      displaySeasons: this.getDisplayFilters(validSelectedSeasons),
      displayStyles: this.getDisplayFilters(validSelectedStyles),
      displayColors: this.getDisplayFilters(validSelectedColors),
      displayStorageLocations: this.getDisplayFilters(validSelectedStorageLocations)
    });

    // 初始化筛选状态对象
    this.initFilterSeasonIsSelected();
    this.initFilterStyleIsSelected(styleOptions);
    this.initFilterColorIsSelected(colorOptions);
    this.initFilterStorageLocationIsSelected(storageLocationOptions);
  },

  // 筛选细分类
  selectTypeDetail: function(e) {
    const typeDetail = e.currentTarget.dataset.type;
    if (typeDetail === undefined) return; // 防止添加undefined值

    // 更新选中的细分类并应用筛选
    this.setData({ selectedTypeDetail: typeDetail }, () => {
      // 不再自动关闭筛选面板
      // this.closeAllFilterPanels();
      this.applyAllFilters();
    });
  },

  // 切换季节筛选
  toggleSeasonFilter: function(e) {
    const season = e.currentTarget.dataset.season;
    if (!season) return; // 防止添加undefined值

    const selectedSeasons = [...this.data.selectedSeasons];

    // 检查是否已选中
    const index = selectedSeasons.indexOf(season);

    if (index === -1) {
      // 未选中，添加
      selectedSeasons.push(season);
    } else {
      // 已选中，移除
      selectedSeasons.splice(index, 1);
    }

    // 更新筛选季节选中状态对象
    const filterSeasonIsSelected = {...this.data.filterSeasonIsSelected};
    this.data.seasonOptions.forEach(s => {
      filterSeasonIsSelected[s] = selectedSeasons.includes(s);
    });

    // 更新选中的季节并应用筛选
    this.setData({
      selectedSeasons: selectedSeasons,
      filterSeasonIsSelected: filterSeasonIsSelected,
      displaySeasons: this.getDisplayFilters(selectedSeasons)
    }, () => {
      // 不再自动关闭筛选面板
      // this.closeAllFilterPanels();
      this.applyAllFilters();
    });
  },

  // 清除季节筛选
  clearSeasonFilter: function() {
    // 重置筛选季节选中状态
    const filterSeasonIsSelected = {...this.data.filterSeasonIsSelected};
    this.data.seasonOptions.forEach(season => {
      filterSeasonIsSelected[season] = false;
    });

    this.setData({
      selectedSeasons: [],
      filterSeasonIsSelected: filterSeasonIsSelected,
      displaySeasons: ''
    }, () => {
      // 不再自动关闭筛选面板
      // this.closeAllFilterPanels();
      this.applyAllFilters();
    });
  },

  // 切换风格筛选
  toggleStyleFilter: function(e) {
    const style = e.currentTarget.dataset.style;
    if (!style) return; // 防止添加undefined值

    const selectedStyles = [...this.data.selectedStyles];

    // 检查是否已选中
    const index = selectedStyles.indexOf(style);

    if (index === -1) {
      // 未选中，添加
      selectedStyles.push(style);
    } else {
      // 已选中，移除
      selectedStyles.splice(index, 1);
    }

    // 更新筛选风格选中状态对象
    const filterStyleIsSelected = {...this.data.filterStyleIsSelected};
    this.data.styleOptions.forEach(s => {
      filterStyleIsSelected[s] = selectedStyles.includes(s);
    });

    // 更新选中的风格并应用筛选
    this.setData({
      selectedStyles: selectedStyles,
      filterStyleIsSelected: filterStyleIsSelected,
      displayStyles: this.getDisplayFilters(selectedStyles)
    }, () => {
      // 不再自动关闭筛选面板
      // this.closeAllFilterPanels();
      this.applyAllFilters();
    });
  },

  // 清除风格筛选
  clearStyleFilter: function() {
    // 重置筛选风格选中状态
    const filterStyleIsSelected = {...this.data.filterStyleIsSelected};
    this.data.styleOptions.forEach(style => {
      filterStyleIsSelected[style] = false;
    });

    this.setData({
      selectedStyles: [],
      filterStyleIsSelected: filterStyleIsSelected,
      displayStyles: ''
    }, () => {
      // 不再自动关闭筛选面板
      // this.closeAllFilterPanels();
      this.applyAllFilters();
    });
  },

  // 切换颜色筛选
  toggleColorFilter: function(e) {
    const color = e.currentTarget.dataset.color;
    if (!color) return; // 防止添加undefined值

    const selectedColors = [...this.data.selectedColors];

    // 检查是否已选中
    const index = selectedColors.indexOf(color);

    if (index === -1) {
      // 未选中，添加
      selectedColors.push(color);
    } else {
      // 已选中，移除
      selectedColors.splice(index, 1);
    }

    // 更新筛选颜色选中状态对象
    const filterColorIsSelected = {...this.data.filterColorIsSelected};
    this.data.colorOptions.forEach(c => {
      filterColorIsSelected[c] = selectedColors.includes(c);
    });

    // 更新选中的颜色并应用筛选
    this.setData({
      selectedColors: selectedColors,
      filterColorIsSelected: filterColorIsSelected,
      displayColors: this.getDisplayFilters(selectedColors)
    }, () => {
      // 不再自动关闭筛选面板
      // this.closeAllFilterPanels();
      this.applyAllFilters();
    });
  },

  // 清除颜色筛选
  clearColorFilter: function() {
    // 重置筛选颜色选中状态
    const filterColorIsSelected = {...this.data.filterColorIsSelected};
    this.data.colorOptions.forEach(color => {
      filterColorIsSelected[color] = false;
    });

    this.setData({
      selectedColors: [],
      filterColorIsSelected: filterColorIsSelected,
      displayColors: ''
    }, () => {
      // 不再自动关闭筛选面板
      // this.closeAllFilterPanels();
      this.applyAllFilters();
    });
  },

  // 清除所有筛选条件
  clearAllFilters: function() {
    // 重置筛选季节选中状态
    const filterSeasonIsSelected = {};
    this.data.seasonOptions.forEach(season => {
      filterSeasonIsSelected[season] = false;
    });

    // 重置筛选风格选中状态
    const filterStyleIsSelected = {};
    this.data.styleOptions.forEach(style => {
      filterStyleIsSelected[style] = false;
    });

    // 重置筛选颜色选中状态
    const filterColorIsSelected = {};
    this.data.colorOptions.forEach(color => {
      filterColorIsSelected[color] = false;
    });

    // 重置筛选存储位置选中状态
    const filterStorageLocationIsSelected = {};
    this.data.storageLocationOptions.forEach(location => {
      filterStorageLocationIsSelected[location] = false;
    });

    // 重置衣柜筛选选中状态
    const filterWardrobeIsSelected = {};
    this.data.wardrobeOptions.forEach(wardrobe => {
      filterWardrobeIsSelected[wardrobe._id] = false;
    });

    this.setData({
      selectedTypeDetail: '',
      selectedSeasons: [],
      selectedStyles: [],
      selectedColors: [],
      selectedStorageLocations: [],
      selectedWardrobe: '',
      displaySeasons: '',
      displayStyles: '',
      displayColors: '',
      displayStorageLocations: '',
      displayWardrobe: '',
      filterSeasonIsSelected: filterSeasonIsSelected,
      filterStyleIsSelected: filterStyleIsSelected,
      filterColorIsSelected: filterColorIsSelected,
      filterStorageLocationIsSelected: filterStorageLocationIsSelected,
      filterWardrobeIsSelected: filterWardrobeIsSelected,
      activeFilterPanel: '' // 收起所有面板
    });

    // 应用所有筛选条件
    this.applyAllFilters();
  },

  // 获取用于显示的过滤值列表字符串
  getDisplayFilters: function(filterArray) {
    if (!filterArray || filterArray.length === 0) return '';

    // 过滤掉无效值，然后用/连接
    return filterArray.filter(item => item).join('/');
  },

  // 应用所有筛选条件
  applyAllFilters: function() {
    console.log('开始应用筛选条件...');
    console.log('当前选中的类别:', this.data.selectedCategory);
    console.log('当前衣物总数:', this.data.clothes.length);
    console.log('当前搜索关键词:', this.data.searchKeyword);
    console.log('当前选中的衣柜:', this.data.selectedWardrobe);
    console.log('当前衣柜选项:', this.data.wardrobeOptions);
    console.log('当前衣柜选中状态:', this.data.filterWardrobeIsSelected);

    const clothes = this.data.clothes;
    const category = this.data.selectedCategory;
    const typeDetail = this.data.selectedTypeDetail;
    const searchKeyword = this.data.searchKeyword;
    const selectedWardrobe = this.data.selectedWardrobe;

    // 过滤掉可能的无效值
    const selectedSeasons = this.data.selectedSeasons.filter(item => item);
    const selectedStyles = this.data.selectedStyles.filter(item => item);
    const selectedColors = this.data.selectedColors.filter(item => item);
    const selectedStorageLocations = this.data.selectedStorageLocations.filter(item => item);

    // 检查是否有活动的筛选条件
    const hasActiveFilters = (
      typeDetail !== '' ||
      selectedSeasons.length > 0 ||
      selectedStyles.length > 0 ||
      selectedColors.length > 0 ||
      selectedStorageLocations.length > 0 ||
      searchKeyword !== '' ||
      selectedWardrobe !== ''
    );

    // 首先按类别筛选
    let filteredClothes = [];
    let listTitle = '';
    let typeDetails = [];

    switch(category) {
      case 'latest':
        // 获取最近一个月的衣物
        const oneMonthAgo = new Date();
        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

        filteredClothes = clothes.filter(item => {
          const createTime = item.createTime ? new Date(item.createTime) : null;
          return createTime && createTime > oneMonthAgo;
        });
        listTitle = '最新衣物';
        break;

      case 'all':
        filteredClothes = [...clothes];
        listTitle = '所有衣物';
        break;

      default:
        filteredClothes = clothes.filter(item => item.category === category);
        listTitle = category;

        // 获取该类别下的所有细分类
        const detailSet = new Set();
        filteredClothes.forEach(item => {
          if (item.type_detail) {
            detailSet.add(item.type_detail);
          }
        });
        typeDetails = Array.from(detailSet);
        break;
    }

    // 如果有细分类筛选条件，则应用
    if (typeDetail !== '') {
      filteredClothes = filteredClothes.filter(item => item.type_detail === typeDetail);
      listTitle += ` - ${typeDetail}`;
    }

    // 应用搜索关键词筛选
    if (searchKeyword !== '') {
      // 按名称、类别、细分类、颜色、风格等多字段进行搜索
      filteredClothes = filteredClothes.filter(item => {
        const searchFields = [
          item.name,
          item.category,
          item.type_detail,
          item.color,
          item.style,
          item.brand,
          item.material,
          item.storageLocation,
          item.remark
        ];

        // 过滤掉null和undefined值，并确保只对字符串类型调用toLowerCase
        const searchTexts = searchFields
          .filter(field => field)
          .map(field => typeof field === 'string' ? field.toLowerCase() : String(field).toLowerCase());

        // 关键词也转为小写，然后检查是否有任何字段包含关键词
        const lowerKeyword = searchKeyword.toLowerCase();
        return searchTexts.some(text => text.includes(lowerKeyword));
      });

      // 在标题中显示搜索信息
      listTitle = `搜索"${searchKeyword}"`;
    }

    // 应用季节筛选
    if (selectedSeasons.length > 0) {
      filteredClothes = filteredClothes.filter(item => {
        return item.season && selectedSeasons.some(season =>
          season && item.season.includes(season)
        );
      });
      listTitle += ` | 季节:${selectedSeasons.join('/')}`;
    }

    // 应用风格筛选
    if (selectedStyles.length > 0) {
      filteredClothes = filteredClothes.filter(item => {
        return item.style && selectedStyles.includes(item.style);
      });
      listTitle += ` | 风格:${selectedStyles.join('/')}`;
    }

    // 应用颜色筛选
    if (selectedColors.length > 0) {
      filteredClothes = filteredClothes.filter(item => {
        return item.color && selectedColors.includes(item.color);
      });
      listTitle += ` | 颜色:${selectedColors.join('/')}`;
    }

    // 应用存储位置筛选
    if (selectedStorageLocations.length > 0) {
      filteredClothes = filteredClothes.filter(item => {
        return item.storageLocation && selectedStorageLocations.includes(item.storageLocation);
      });
      listTitle += ` | 存储位置:${selectedStorageLocations.join('/')}`;
    }

    // 应用衣柜筛选
    if (selectedWardrobe) {
      if (selectedWardrobe === 'unclassified') {
        // 筛选未分类衣物（wardrobeId为空或不存在）
        filteredClothes = filteredClothes.filter(item => {
          return !item.wardrobeId;
        });

        // 在标题中显示未分类信息
        listTitle += ` | 衣柜:未分类`;
      } else {
        // 筛选指定衣柜的衣物
        filteredClothes = filteredClothes.filter(item => {
          return item.wardrobeId === selectedWardrobe;
        });

        // 在标题中显示衣柜信息
        const wardrobeName = this.data.displayWardrobe || '选中衣柜';
        listTitle += ` | 衣柜:${wardrobeName}`;
      }
    }

    // 按创建时间排序（最新的在前面）
    filteredClothes.sort((a, b) => {
      const timeA = a.createTime ? new Date(a.createTime).getTime() : 0;
      const timeB = b.createTime ? new Date(b.createTime).getTime() : 0;
      return timeB - timeA;
    });

    // 更新状态
    this.setData({
      filteredClothes: filteredClothes,
      displayClothes: filteredClothes.slice(0, this.data.pageSize),
      currentPage: 1,
      listTitle: listTitle,
      typeDetails: typeDetails,
      hasActiveFilters: hasActiveFilters,

      // 确保显示变量与选中的筛选值保持一致
      displaySeasons: this.getDisplayFilters(selectedSeasons),
      displayStyles: this.getDisplayFilters(selectedStyles),
      displayColors: this.getDisplayFilters(selectedColors),
      displayStorageLocations: this.getDisplayFilters(selectedStorageLocations),
      displayWardrobe: this.data.displayWardrobe
    });
  },

  // 加载更多衣物
  loadMoreClothes: function() {
    if (this.data.isLoadingMore) return;

    const { currentPage, pageSize, filteredClothes } = this.data;
    const totalPages = Math.ceil(filteredClothes.length / pageSize);

    // 如果已经是最后一页，则不加载
    if (currentPage >= totalPages) return;

    // 显示加载中
    this.setData({ isLoadingMore: true });

    // 模拟加载延迟
    setTimeout(() => {
      // 计算下一页的数据
      const nextPage = currentPage + 1;
      const start = currentPage * pageSize;
      const end = Math.min(nextPage * pageSize, filteredClothes.length);
      const newDisplayClothes = this.data.displayClothes.concat(filteredClothes.slice(start, end));

      // 更新状态
      this.setData({
        currentPage: nextPage,
        displayClothes: newDisplayClothes,
        isLoadingMore: false
      });
    }, 300);
  },

  // 查看衣物详情
  viewClothesDetail: function(e) {
    // 如果处于选择模式，则切换选中状态
    if (this.data.isSelectionMode) {
      this.selectClothing(e);
      return;
    }

    const id = e.currentTarget.dataset.id;
    const clothes = this.data.clothes;

    // 找到对应的衣物
    const clothesItem = clothes.find(item => item._id === id);

    if (clothesItem) {
      // 获取衣物所属衣柜的名称
      const wardrobeId = clothesItem.wardrobeId;
      let wardrobeName = '';

      if (wardrobeId) {
        // 从本地缓存获取用户的衣柜数据
        const userWardrobesKey = 'user_wardrobes_' + this.data.userOpenId;
        const userWardrobes = wx.getStorageSync(userWardrobesKey) || [];

        // 查找匹配的衣柜
        const wardrobe = userWardrobes.find(w => w._id === wardrobeId);
        if (wardrobe) {
          wardrobeName = wardrobe.name || '默认衣柜';
        }
      }

      // 创建一个包含衣柜名称的衣物对象
      const clothingWithWardrobeName = {
        ...clothesItem,
        wardrobeName: wardrobeName
      };

      this.setData({
        showDetail: true,
        detailClothes: clothingWithWardrobeName
      });
    } else {
      closetUtils.showErrorToast('衣物不存在');
    }
  },

  // 处理衣物长按事件
  onClothesLongPress: function(e) {
    const clothingId = e.currentTarget.dataset.id;
    const clothingName = e.currentTarget.dataset.name || '衣物';

    // 显示操作菜单
    wx.showActionSheet({
      itemList: ['编辑', '删除', '收藏', '进入批量选择模式'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 编辑衣物
          this.editClothes({
            currentTarget: {
              dataset: {
                id: clothingId
              }
            }
          });
        } else if (res.tapIndex === 1) {
          // 删除衣物
          this.performDeleteClothes(clothingId);
        } else if (res.tapIndex === 2) {
          // 收藏衣物 - 如果有收藏功能的话
          // this.toggleFavorite(clothingId);
          wx.showToast({
            title: '收藏功能开发中',
            icon: 'none'
          });
        } else if (res.tapIndex === 3) {
          // 进入批量选择模式
          this.enterSelectionMode();
          // 选中当前长按的衣物
          this.toggleSelectClothing(clothingId);
        }
      }
    });
  },

  // 关闭衣物详情弹窗
  closeClothesDetail: function() {
    this.setData({
      showDetail: false,
      detailClothes: null
    });
  },

  // 从详情页删除衣物
  deleteClothesFromDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('准备删除衣物:', id);

    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: '你确定要删除这件衣物吗？删除后无法恢复。',
      success: res => {
        if (res.confirm) {
          // 用户点击确认，执行删除操作
          this.performDeleteClothes(id);
          wx.setStorageSync('fromFilterPage', true);
        }
      }
    });
  },

  // 执行删除衣物操作
  performDeleteClothes: function(id) {
    console.log('执行删除衣物:', id);

    // 显示加载提示
    closetUtils.showLoading('删除中...');

    // 调用云函数删除衣物
    wx.cloud.callFunction({
      name: 'deleteClothing',
      data: {
        clothingId: id,
        userOpenId: this.data.userOpenId
      }
    })
    .then(res => {
      wx.removeStorageSync('userLimitsCache');
      wx.removeStorageSync('userLimitsCacheExpiration');
      wx.setStorageSync('needRefreshWardrobeSummary', true);
      console.log('删除衣物云函数调用成功:', res);

      // 检查返回结果
      const result = res.result || {};

      if (result.success) {
        // 删除成功，关闭详情弹窗
        this.closeClothesDetail();

        // 从本地数据中移除已删除的衣物
        const updatedClothes = this.data.clothes.filter(item => item._id !== id);

        // 更新本地数据
        this.updateLocalCache(updatedClothes);

        // 更新状态并重新筛选
        this.setData({
          clothes: updatedClothes
        }, () => {
          // 处理数据并更新UI
          this.processClothesData(updatedClothes);
        });

        // 显示成功提示
        closetUtils.showSuccessToast('删除成功');

        // 更新用户衣服上限
        limitManager.updateClothesCount(this.data.userOpenId, -1)
          .then(updatedLimits => {
            console.log('衣物数量更新成功，当前数量:', updatedLimits.clothesCount);
          })
          .catch(err => {
            console.error('更新衣物数量失败:', err);
          });
      } else {
        // 删除失败
        console.error('删除衣物失败:', result.error || '未知错误');
        closetUtils.showErrorToast('删除失败: ' + (result.message || '未知错误'));
      }
    })
    .catch(err => {
      console.error('删除衣物失败:', err);
      closetUtils.showErrorToast('删除失败: ' + (err.message || '未知错误'));
    })
    .finally(() => {
      closetUtils.hideLoading();
    });
  },

  // 点击编辑按钮
  editClothes: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('编辑衣物:', id);

    // 查找当前衣物数据
    const clothing = this.data.clothes.find(item => item._id === id);
    if (!clothing) {
      console.error('未找到衣物数据:', id);
      closetUtils.showErrorToast('未找到衣物数据');
      return;
    }

    // 保存详情信息，用于编辑后恢复查看
    const previousClothesDetail = this.data.detailClothes;

    // 先关闭详情弹窗
    this.setData({
      showDetail: false,
      // 记录当前是从详情页面进入编辑界面的
      fromDetailView: true,
      // 保存编辑前的衣物ID，用于编辑后重新显示详情
      editingFromDetailId: id
    });

    // 显示编辑弹窗
    this.showEditClothingModal(clothing);
  },

  // 显示编辑衣物弹窗
  showEditClothingModal: function(clothing, fromDetailView = false, clothingId = '') {
    console.log('显示编辑衣物弹窗:', clothing, fromDetailView, clothingId);

    if (!clothing) {
      closetUtils.showErrorToast('衣物数据为空，无法编辑');
      return;
    }

    // 初始化编辑数据
    const editingClothing = {...clothing};

    // 初始化衣物类别
    if (!editingClothing.category) {
      editingClothing.category = '';
    }

    // 确保wardrobeName字段存在
    if (editingClothing.wardrobeId && !editingClothing.wardrobeName) {
      // 从本地缓存获取用户的衣柜数据
      const userWardrobesKey = 'user_wardrobes_' + this.data.userOpenId;
      try {
        const userWardrobes = wx.getStorageSync(userWardrobesKey) || [];
        // 查找匹配的衣柜
        const wardrobe = userWardrobes.find(w => w._id === editingClothing.wardrobeId);
        if (wardrobe) {
          editingClothing.wardrobeName = wardrobe.name || '默认衣柜';
          console.log('找到衣柜名称:', editingClothing.wardrobeName);
        }
      } catch (e) {
        console.error('获取衣柜名称失败:', e);
      }
    }

    // 初始化所有输入框焦点状态为false
    editingClothing.nameFocus = false;
    editingClothing.type_detailFocus = false;
    editingClothing.colorFocus = false;
    editingClothing.styleFocus = false;
    editingClothing.sizeFocus = false;
    editingClothing.brandFocus = false;
    editingClothing.purchaseChannelFocus = false;
    editingClothing.storageLocationFocus = false;
    editingClothing.materialFocus = false;
    editingClothing.priceFocus = false;
    editingClothing.wornCountFocus = false;
    editingClothing.remarkFocus = false;

    // 更新显示选中的季节
    const seasonSelections = {};
    if (editingClothing.season) {
      editingClothing.season.split('/').forEach(season => {
        seasonSelections[season] = true;
      });
    }

    // 更新显示选中的衣柜
    const wardrobeSelections = {};
    if (editingClothing.wardrobeId) {
      wardrobeSelections[editingClothing.wardrobeId] = true;
    }

    // 更新页面状态
    this.setData({
      editingClothing: editingClothing,
      editingId: clothing._id,
      originalClothing: {...clothing},  // 备份原始数据用于比较
      showEditModal: true,
      seasonIsSelected: seasonSelections,
      wardrobeIsSelected: wardrobeSelections,
      fromDetailView: fromDetailView,
      editingFromDetailId: clothingId,
      showingSuggestions: false
    });

    // 加载历史输入建议
    this.loadSuggestions();
  },

  // 隐藏编辑衣物弹窗
  hideEditClothingModal: function() {
    this.setData({
      showEditModal: false,
      editingClothing: {},
      editingId: '',
      showingSuggestions: false,
      currentSuggestionField: ''
    });
  },

  // 处理名称变化
  onNameChange: function(e) {
    this.setData({
      'editingClothing.name': e.detail.value
    });
  },

  // 处理颜色变化
  onColorChange: function(e) {
    this.setData({
      'editingClothing.color': e.detail.value
    });
  },

  // 处理风格变化
  onStyleChange: function(e) {
    this.setData({
      'editingClothing.style': e.detail.value
    });
  },

  // 处理细分类变化
  onTypeDetailChange: function(e) {
    console.log('onTypeDetailChange 被调用', e);
    const newValue = e.detail.value;

    // 直接设置值，确保能正确更新
    this.setData({
      'editingClothing.type_detail': newValue
    });

    console.log('细分类已更新为:', newValue, '当前值:', this.data.editingClothing.type_detail);
  },

  // 处理尺码变化
  onSizeChange: function(e) {
    this.setData({
      'editingClothing.size': e.detail.value
    });
    console.log('尺码已更新为:', e.detail.value);
  },

  // 处理品牌变化
  onBrandChange: function(e) {
    this.setData({
      'editingClothing.brand': e.detail.value
    });
    console.log('品牌已更新为:', e.detail.value);
  },

  // 处理购买渠道变化
  onPurchaseChannelChange: function(e) {
    this.setData({
      'editingClothing.purchaseChannel': e.detail.value
    });
    console.log('购买渠道已更新为:', e.detail.value);
  },

  // 处理存储位置变化
  onStorageLocationChange: function(e) {
    this.setData({
      'editingClothing.storageLocation': e.detail.value
    });
    console.log('存储位置已更新为:', e.detail.value);
  },

  // 处理材质变化
  onMaterialChange: function(e) {
    this.setData({
      'editingClothing.material': e.detail.value
    });
    console.log('材质已更新为:', e.detail.value);
  },

  // 处理备注变化
  onRemarkChange: function(e) {
    this.setData({
      'editingClothing.remark': e.detail.value
    });
    console.log('备注已更新为:', e.detail.value);
  },

  // 处理断舍离标记变化
  onWantToDiscardChange: function(e) {
    this.setData({
      'editingClothing.wantToDiscard': e.detail.value
    });
    console.log('断舍离标记已更新为:', e.detail.value);
  },

  // 处理价格变化
  onPriceChange: function(e) {
    // 记录函数调用
    console.log('onPriceChange函数被调用', e);
    // 获取价格值
    const price = e.detail.value;
    // 更新数据
    this.setData({
      'editingClothing.price': price
    });
    // 记录更新结果
    console.log('价格已更新为:', price);
  },

  // 处理穿着次数变化
  onWornCountChange: function(e) {
    this.setData({
      'editingClothing.wornCount': e.detail.value
    });
    console.log('穿着次数已更新为:', e.detail.value);
  },

  // 显示季节选择器
  showSeasonPicker: function() {
    let currentSeasons = [];

    if (this.data.editingClothing && this.data.editingClothing.season) {
      console.log('当前季节值:', this.data.editingClothing.season);
      console.log('季节值类型:', typeof this.data.editingClothing.season);

      // 如果是字符串，转换为数组
      if (typeof this.data.editingClothing.season === 'string') {
        currentSeasons = this.data.editingClothing.season.split('/').filter(s => s);
      } else if (Array.isArray(this.data.editingClothing.season)) {
        currentSeasons = [...this.data.editingClothing.season];
      }

      console.log('转换后的季节数组:', currentSeasons);
    }

    // 确保显示所有季节选项，而不只是当前筛选的季节
    const allSeasons = ['春季', '夏季', '秋季', '冬季'];

    // 初始化季节选中状态对象
    const seasonIsSelected = {};
    allSeasons.forEach(season => {
      seasonIsSelected[season] = currentSeasons.includes(season);
    });

    this.setData({
      // 编辑模式下使用完整的季节列表
      tempSelectedSeason: currentSeasons,
      seasonIsSelected: seasonIsSelected,
      showSeasonPicker: true
    });

    console.log('设置tempSelectedSeason后的值:', this.data.tempSelectedSeason);
  },

  // 隐藏季节选择器
  hideSeasonPicker: function() {
    this.setData({
      showSeasonPicker: false
    });
  },

  // 选择季节
  selectSeason: function(e) {
    const season = e.currentTarget.dataset.season;
    console.log('选择季节函数被调用 - 选中的季节:', season);
    console.log('选择前的tempSelectedSeason:', [...this.data.tempSelectedSeason]);

    const tempSelectedSeason = [...this.data.tempSelectedSeason];
    const index = tempSelectedSeason.indexOf(season);

    if (index === -1) {
      // 未选中，添加到选中列表
      tempSelectedSeason.push(season);
      console.log('添加', season, '到选中列表');
    } else {
      // 已选中，从选中列表移除
      tempSelectedSeason.splice(index, 1);
      console.log('从选中列表移除', season);
    }

    // 更新季节选中状态对象
    const seasonIsSelected = {...this.data.seasonIsSelected};
    // 编辑模式下使用完整的季节列表
    const allSeasons = ['春季', '夏季', '秋季', '冬季'];
    allSeasons.forEach(s => {
      seasonIsSelected[s] = tempSelectedSeason.includes(s);
    });

    this.setData({
      tempSelectedSeason: tempSelectedSeason,
      seasonIsSelected: seasonIsSelected
    });
  },

  // 确认季节选择
  confirmSeasonPicker: function() {
    console.log('确认季节选择, isBatchEdit =', this.data.isBatchEdit);
    console.log('当前选中季节:', this.data.tempSelectedSeason);

    // 检查是否是批量编辑模式
    if (this.data.isBatchEdit) {
      const seasonValue = this.data.tempSelectedSeason.join('/');
      console.log('批量编辑模式: 更新季节为', seasonValue);
      this.setData({
        'batchEditData.season': seasonValue,
        showSeasonPicker: false,
        isBatchEdit: false
      });
      return;
    }

    // 单个衣物编辑模式
    console.log('单个衣物编辑模式: 更新季节为', this.data.tempSelectedSeason.join('/'));
    this.setData({
      'editingClothing.season': this.data.tempSelectedSeason.join('/'),
      showSeasonPicker: false
    });
  },

  // 显示购买日期选择器
  showPurchaseDatePicker: function() {
    this.setData({
      showDatePicker: true,
      datePickerType: 'purchased',
      datePickerTitle: '选择购买日期',
      datePickerValue: this.data.editingClothing.purchaseDate || ''
    });
  },

  // 显示上次穿着日期选择器
  showLastWornDatePicker: function() {
    this.setData({
      showDatePicker: true,
      datePickerType: 'lastWorn',
      datePickerTitle: '选择上次穿着日期',
      datePickerValue: this.data.editingClothing.lastWornDate || ''
    });
  },

  // 隐藏日期选择器
  hideDatePicker: function() {
    this.setData({
      showDatePicker: false
    });
  },

  // 日期选择器值变化
  onDatePickerChange: function(e) {
    this.setData({
      datePickerValue: e.detail.value
    });
  },

  // 确认日期选择
  confirmDatePicker: function() {
    const type = this.data.datePickerType;
    const date = this.data.datePickerValue;

    if (type === 'purchased') {
      this.setData({
        'editingClothing.purchaseDate': date
      });
    } else if (type === 'lastWorn') {
      this.setData({
        'editingClothing.lastWornDate': date
      });
    }

    this.setData({
      showDatePicker: false
    });
  },

  // 显示类别选择器
  showCategoryPicker: function() {
    // 获取最新的用户衣物类别
    const categories = this.getUserClothesCategories();

    this.setData({
      userCategories: categories,
      tempSelectedCategory: this.data.editingClothing.category || '',
      showCategoryPicker: true
    });
  },

  // 隐藏类别选择器
  hideCategoryPicker: function() {
    console.log('隐藏类别选择器');
    this.setData({
      showCategoryPicker: false
    });
  },

  // 选择编辑衣物时的类别（与筛选功能区分）
  selectEditCategory: function(e) {
    const category = e.currentTarget.dataset.category;

    this.setData({
      tempSelectedCategory: category,
      customCategoryFocus: false
    });
  },

  // 确认类别选择
  confirmCategoryPicker: function() {
    console.log('确认类别选择:', this.data.tempSelectedCategory, ', isBatchEdit =', this.data.isBatchEdit);

    // 检查是否是批量编辑模式
    if (this.data.isBatchEdit) {
      let newCategory = this.data.tempSelectedCategory;

      // 如果选择了自定义类别，且输入了值，则使用自定义值
      if (newCategory === '自定义') {
        newCategory = this.data.customCategoryValue;
        console.log('使用自定义类别:', newCategory);

        // 将新类别添加到有效类别列表中
        if (!this.data.validCategories.includes(newCategory)) {
          const updatedValidCategories = [...this.data.validCategories, newCategory];
          this.setData({
            validCategories: updatedValidCategories,
            userCategories: updatedValidCategories
          });
          console.log('更新有效类别列表:', updatedValidCategories);
        }
      }

      // 更新批量编辑数据
      this.setData({
        'batchEditData.category': newCategory,
        showCategoryPicker: false,
        isBatchEdit: false,
        customCategoryValue: ''
      });
      return;
    }

    // 单个衣物编辑模式
    // 记录原始类别，用于判断是否发生变更
    const originalCategory = this.data.editingClothing.category;
    let newCategory = this.data.tempSelectedCategory;

    // 如果选择了自定义类别，且输入了值，则使用自定义值
    if (newCategory === '自定义' && this.data.customCategoryValue.trim()) {
      newCategory = this.data.customCategoryValue.trim();
      console.log('使用自定义类别:', newCategory);

      // 将新类别添加到有效类别列表中
      if (!this.data.validCategories.includes(newCategory)) {
        const updatedValidCategories = [...this.data.validCategories, newCategory];
        this.setData({
          validCategories: updatedValidCategories,
          userCategories: updatedValidCategories
        });
        console.log('更新有效类别列表:', updatedValidCategories);
      }
    }

    this.setData({
      'editingClothing.category': newCategory,
      showCategoryPicker: false,
      customCategoryValue: ''
    });
  },

  // 删除当前编辑的衣物
  deleteCurrentClothing: function() {
    const id = this.data.editingId;

    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: '你确定要删除这件衣物吗？删除后无法恢复。',
      success: res => {
        if (res.confirm) {
          // 用户点击确认，关闭编辑弹窗
          this.hideEditClothingModal();

          // 执行删除操作
          this.performDeleteClothes(id);
        }
      }
    });
  },

  // 保存编辑后的衣物信息
  saveClothingEdit: function() {
    const clothing = this.data.editingClothing;
    console.log('准备保存衣物信息:', clothing);

    if (!clothing) {
      console.error('无效的衣物数据');
      closetUtils.showErrorToast('无效的衣物数据');
      return;
    }

    // 数据验证
    if (!clothing.name) {
      console.log('名称为空');
      closetUtils.showErrorToast('请输入衣物名称');
      return;
    }

    if (!clothing.category) {
      console.log('类别为空');
      closetUtils.showErrorToast('请选择衣物类别');
      return;
    }


    // 敏感词检查
    const textFields = {
      name: clothing.name,
      type: clothing.type || '',
      color: clothing.color || '',
      style: clothing.style || '',
      type_detail: clothing.type_detail || ''
    };

    // 检查所有文本字段是否包含敏感词
    let hasSensitiveContent = false;
    let sensitiveFieldName = '';
    let sensitiveWords = [];

    for (const [field, value] of Object.entries(textFields)) {
      const checkResult = sensitiveWordsFilter.containsSensitiveWords(value);
      if (!checkResult.passed) {
        hasSensitiveContent = true;
        sensitiveFieldName = field;
        sensitiveWords = checkResult.words;
        break;
      }
    }

    if (hasSensitiveContent) {
      console.log('检测到敏感内容，字段:', sensitiveFieldName, '敏感词:', sensitiveWords);
      closetUtils.showErrorToast(`包含敏感内容，请修改后重试`);
      return;
    }

    // 价格验证和转换
    let price = undefined;
    if (clothing.price) {
      if (isNaN(Number(clothing.price))) {
        console.log('无效的价格:', clothing.price);
        closetUtils.showErrorToast('请输入有效的价格');
        return;
      }
      price = Number(clothing.price);
    }

    // 穿着次数验证和转换
    let wornCount = undefined;
    if (clothing.wornCount) {
      if (isNaN(Number(clothing.wornCount))) {
        console.log('无效的穿着次数:', clothing.wornCount);
        closetUtils.showErrorToast('请输入有效的穿着次数');
        return;
      }
      wornCount = Number(clothing.wornCount);
    }

    // 准备要更新的数据
    const updateData = {
      clothingId: this.data.editingId,
      name: clothing.name,
      type: clothing.type || '',
      type_detail: clothing.type_detail || '',
      category: clothing.category,
      color: clothing.color || '',
      style: clothing.style || '',
      season: clothing.season || '',
      price: price,
      purchaseDate: clothing.purchaseDate || '',
      lastWornDate: clothing.lastWornDate || '',
      wornCount: wornCount,
      size: clothing.size || '',
      brand: clothing.brand || '',
      purchaseChannel: clothing.purchaseChannel || '',
      storageLocation: clothing.storageLocation || '',
      material: clothing.material || '',
      remark: clothing.remark || '',
      wantToDiscard: clothing.wantToDiscard || false,
      userOpenId: this.data.userOpenId,
      wardrobeId: clothing.wardrobeId || wx.getStorageSync('last_used_wardrobe_' + this.data.userOpenId)
    };

    console.log('要更新的数据:', updateData);

    // 显示加载提示
    closetUtils.showLoading('保存中...');

    // 调用云函数更新衣物
    wx.cloud.callFunction({
      name: 'updateClothing',
      data: updateData
    })
    .then(res => {
      console.log('更新衣物云函数调用成功:', res);

      // 检查返回结果
      const result = res.result || {};

      if (result.success) {
        // 更新成功
        console.log('衣物更新成功');
        closetUtils.hideLoading();
        closetUtils.showSuccessToast('保存成功');

        // 更新本地数据
        this.updateLocalClothingData(this.data.editingId, updateData, false);

        // 关闭编辑弹窗
        this.hideEditClothingModal();

        // 如果是从详情页进入编辑的，则重新显示详情
        if (this.data.fromDetailView && this.data.editingFromDetailId) {
          const updatedClothing = this.data.clothes.find(item => item._id === this.data.editingFromDetailId);
          if (updatedClothing) {
            this.setData({
              showDetail: true,
              detailClothes: updatedClothing,
              fromDetailView: false,
              editingFromDetailId: ''
            });
          }
        }
      } else {
        // 更新失败
        console.error('衣物更新失败:', result.error || '未知错误');
        closetUtils.showErrorToast('更新失败: ' + (result.message || '未知错误'));
        closetUtils.hideLoading();
      }
    })
    .catch(err => {
      console.error('更新衣物失败:', err);
      closetUtils.showErrorToast('更新失败: ' + (err.message || '未知错误'));
      closetUtils.hideLoading();
    });
  },

  // 更新本地衣物数据，避免重新加载
  updateLocalClothingData: function(clothingId, updateData, categoryChanged = false) {
    if (!clothingId || !updateData) return;

    console.log('本地更新衣物:', clothingId, updateData);

    // 获取当前衣物列表的副本
    const updatedClothes = [...this.data.clothes];

    // 找到要更新的衣物索引
    const clothingIndex = updatedClothes.findIndex(item => item._id === clothingId);

    if (clothingIndex >= 0) {
      // 更新衣物数据
      updatedClothes[clothingIndex] = {
        ...updatedClothes[clothingIndex],
        ...updateData
      };

      // 更新UI和分类标记
      this.setData({ clothes: updatedClothes });
      this.processClothesData(updatedClothes, categoryChanged);

      // 更新本地缓存
      this.updateLocalCache(updatedClothes);

      // 重新加载历史值建议
      this.loadSuggestions();

      // 设置标记，通知closet页面需要刷新UI
      wx.setStorageSync('fromFilterPage', true);

      // 设置标记，通知wardrobe页面需要刷新数据
      wx.setStorageSync('needRefreshWardrobeSummary', true);
    }
  },

  // 阻止冒泡事件
  preventBubble: function(e) {
    // 如果正在显示建议，则隐藏
    if (this.data.showingSuggestions) {
      this.setData({
        showingSuggestions: false
      });
    }
  },

  // 进入选择模式
  enterSelectionMode: function() {
    // 初始化选中状态对象
    const clothesIsSelected = {};
    if (this.data.filteredClothes && this.data.filteredClothes.length > 0) {
      this.data.filteredClothes.forEach(item => {
        clothesIsSelected[item._id] = false;
      });
    }

    this.setData({
      isSelectionMode: true,
      selectedClothes: [],
      clothesIsSelected: clothesIsSelected,
      selectAll: false
    });
  },

  // 退出选择模式
  exitSelectionMode: function() {
    this.setData({
      isSelectionMode: false,
      selectedClothes: [],
      clothesIsSelected: {},
      selectAll: false
    });
  },

  // 切换选中状态
  toggleSelectClothing: function(clothingId) {
    const selectedClothes = [...this.data.selectedClothes];
    const clothesIsSelected = {...this.data.clothesIsSelected};
    const index = selectedClothes.indexOf(clothingId);

    if (index === -1) {
      // 添加到选中列表
      selectedClothes.push(clothingId);
      clothesIsSelected[clothingId] = true;
    } else {
      // 从选中列表移除
      selectedClothes.splice(index, 1);
      clothesIsSelected[clothingId] = false;
    }

    // 检查是否全选
    const selectAll = selectedClothes.length === this.data.filteredClothes.length;

    this.setData({
      selectedClothes: selectedClothes,
      clothesIsSelected: clothesIsSelected,
      selectAll: selectAll
    });
  },

  // 选择/取消选择单个衣物
  selectClothing: function(e) {
    const clothingId = e.currentTarget.dataset.id;
    this.toggleSelectClothing(clothingId);
  },

  // 全选/取消全选
  toggleSelectAll: function() {
    const currentSelectAll = this.data.selectAll;
    const newSelectAll = !currentSelectAll;

    // 创建新的选中状态对象
    const clothesIsSelected = {};

    if (newSelectAll) {
      // 全选 - 获取所有可见衣物的ID
      const allClothesIds = this.data.filteredClothes.map(item => item._id);

      // 设置所有衣物为选中状态
      this.data.filteredClothes.forEach(item => {
        clothesIsSelected[item._id] = true;
      });

      this.setData({
        selectedClothes: allClothesIds,
        clothesIsSelected: clothesIsSelected,
        selectAll: true
      });
    } else {
      // 取消全选 - 设置所有衣物为非选中状态
      this.data.filteredClothes.forEach(item => {
        clothesIsSelected[item._id] = false;
      });

      this.setData({
        selectedClothes: [],
        clothesIsSelected: clothesIsSelected,
        selectAll: false
      });
    }
  },

  // 显示批量编辑弹窗
  showBatchEditModal: function() {
    if (this.data.selectedClothes.length === 0) {
      wx.showToast({
        title: '请先选择衣物',
        icon: 'none'
      });
      return;
    }

    // 初始化批量编辑数据
    this.setData({
      batchEditData: {
        category: '',
        type_detail: '',
        color: '',
        style: '',
        season: '',
        wardrobeId: '',
        wardrobeName: '',
        brand: '',
        material: '',
        size: '',
        storageLocation: '',
        purchaseChannel: ''
      },
      showBatchEditModal: true
    });
  },

  // 隐藏批量编辑弹窗
  hideBatchEditModal: function() {
    this.setData({
      showBatchEditModal: false,
      batchEditData: {}
    });
  },

  // 批量编辑字段变化处理
  onBatchEditChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    // 更新对应字段
    const batchEditData = {...this.data.batchEditData};
    batchEditData[field] = value;

    this.setData({
      batchEditData: batchEditData
    });
  },

  // 显示类别选择器（批量编辑）
  showBatchCategoryPicker: function() {
    console.log('调用批量编辑类别选择器');

    // 获取最新的用户衣物类别
    const categories = this.getUserClothesCategories();
    console.log('获取到的类别列表:', categories);

    // 使用现有的类别选择器，但标记为批量编辑模式
    this.setData({
      userCategories: categories,
      tempSelectedCategory: this.data.batchEditData.category || '',
      showCategoryPicker: true,
      isBatchEdit: true,
      pickerTitle: '选择类别'
    });

    console.log('设置 isBatchEdit =', true);
  },

  // 显示季节选择器（批量编辑）
  showBatchSeasonPicker: function() {
    console.log('调用批量编辑季节选择器');
    let currentSeasons = [];

    // 如果有已选的季节，则解析它
    if (this.data.batchEditData.season) {
      console.log('当前季节值:', this.data.batchEditData.season);
      console.log('季节值类型:', typeof this.data.batchEditData.season);

      if (typeof this.data.batchEditData.season === 'string') {
        currentSeasons = this.data.batchEditData.season.split('/').filter(s => s);
      } else if (Array.isArray(this.data.batchEditData.season)) {
        currentSeasons = [...this.data.batchEditData.season];
      }

      console.log('解析后的季节数组:', currentSeasons);
    }

    // 初始化季节选中状态对象
    const seasonIsSelected = {};
    const seasonOptions = ['春季', '夏季', '秋季', '冬季'];
    seasonOptions.forEach(season => {
      seasonIsSelected[season] = currentSeasons.includes(season);
    });

    // 使用现有的季节选择器，但标记为批量编辑模式
    this.setData({
      tempSelectedSeason: currentSeasons,
      seasonIsSelected: seasonIsSelected,
      showSeasonPicker: true,
      isBatchEdit: true
    });

    console.log('设置 isBatchEdit =', true);
    console.log('设置 tempSelectedSeason =', currentSeasons);
    console.log('设置 seasonIsSelected =', seasonIsSelected);
  },

  // 显示衣柜选择器（批量编辑）
  showBatchWardrobePicker: function() {
    console.log('调用批量编辑衣柜选择器');
    // 显示加载提示
    wx.showLoading({
      title: '加载衣柜...',
      mask: true
    });

    // 从本地缓存获取衣柜列表
    const cacheKey = 'user_wardrobes_' + this.data.userOpenId;
    const cachedWardrobes = wx.getStorageSync(cacheKey) || [];
    console.log('从缓存获取衣柜列表:', cachedWardrobes);

    if (cachedWardrobes && cachedWardrobes.length > 0) {
      // 如果缓存中有衣柜数据，直接使用
      let currentWardrobe = this.data.batchEditData.wardrobeId || null;
      console.log('当前选中衣柜 ID:', currentWardrobe);

      const wardrobeIsSelected = {};
      cachedWardrobes.forEach(wardrobe => {
        wardrobeIsSelected[wardrobe._id] = (currentWardrobe === wardrobe._id);
      });

      // 使用现有的衣柜选择器，但标记为批量编辑模式
      this.setData({
        wardrobes: cachedWardrobes,
        tempSelectedWardrobe: currentWardrobe,
        wardrobeIsSelected: wardrobeIsSelected,
        showWardrobePicker: true,
        isBatchEdit: true
      });

      console.log('设置 isBatchEdit =', true);
      console.log('设置 tempSelectedWardrobe =', currentWardrobe);
      console.log('设置 wardrobeIsSelected =', wardrobeIsSelected);

      // 隐藏加载提示
      wx.hideLoading();
    } else {
      // 如果缓存中没有衣柜数据，从服务器加载
      this.loadWardrobes()
        .then(wardrobes => {
          console.log('批量编辑模式下加载衣柜成功:', wardrobes.length, '个衣柜');

          let currentWardrobe = this.data.batchEditData.wardrobeId || null;

          const wardrobeIsSelected = {};
          if (wardrobes && wardrobes.length > 0) {
            wardrobes.forEach(wardrobe => {
              wardrobeIsSelected[wardrobe._id] = (currentWardrobe === wardrobe._id);
            });
          }

          // 使用现有的衣柜选择器，但标记为批量编辑模式
          this.setData({
            tempSelectedWardrobe: currentWardrobe,
            wardrobeIsSelected: wardrobeIsSelected,
            showWardrobePicker: true,
            isBatchEdit: true
          });

          console.log('设置 isBatchEdit =', true);
          console.log('设置 tempSelectedWardrobe =', currentWardrobe);
          console.log('设置 wardrobeIsSelected =', wardrobeIsSelected);

          // 隐藏加载提示
          wx.hideLoading();
        })
        .catch(err => {
          console.error('加载衣柜失败:', err);
          wx.hideLoading();
          wx.showToast({
            title: '加载衣柜失败',
            icon: 'none'
          });
        });
    }
  },

  // 批量更新衣物
  saveBatchEdit: function() {
    const batchEditData = this.data.batchEditData;
    const selectedClothes = this.data.selectedClothes;

    if (selectedClothes.length === 0) {
      wx.showToast({
        title: '请先选择衣物',
        icon: 'none'
      });
      return;
    }

    // 检查是否有字段被修改
    let hasChanges = false;
    for (const key in batchEditData) {
      if (batchEditData[key] !== '') {
        hasChanges = true;
        break;
      }
    }

    if (!hasChanges) {
      wx.showToast({
        title: '请至少修改一个字段',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    closetUtils.showLoading('批量更新中...');

    // 准备更新数据 - 只包含有值的字段
    const updateData = {};
    for (const key in batchEditData) {
      if (batchEditData[key] !== '') {
        updateData[key] = batchEditData[key];
      }
    }

    // 添加衣物ID列表
    updateData.clothingIds = selectedClothes;

    // 调用云函数批量更新
    wx.cloud.callFunction({
      name: 'batchUpdateClothes',
      data: updateData
    })
    .then(res => {
      if (res.result && res.result.success) {
        closetUtils.hideLoading();
        closetUtils.showSuccessToast(`成功更新${res.result.updated}件衣物`);

        // 更新本地数据
        this.updateLocalClothesAfterBatchEdit(selectedClothes, updateData);

        // 关闭批量编辑弹窗
        this.hideBatchEditModal();

        // 退出选择模式
        this.exitSelectionMode();

        // 设置标记，提示首页需要刷新数据
        wx.setStorageSync('needRefreshWardrobeSummary', true);
        wx.setStorageSync('fromFilterPage', true);
      } else {
        throw new Error(res.result?.message || '批量更新失败');
      }
    })
    .catch(err => {
      console.error('批量更新衣物失败:', err);
      closetUtils.hideLoading();
      closetUtils.showErrorToast('批量更新失败: ' + (err.message || '未知错误'));
    });
  },

  // 批量更新本地衣物数据
  updateLocalClothesAfterBatchEdit: function(clothingIds, updateData) {
    if (!clothingIds || !Array.isArray(clothingIds) || clothingIds.length === 0) {
      return;
    }

    // 创建更新数据对象 - 移除clothingIds字段
    const dataToUpdate = {...updateData};
    delete dataToUpdate.clothingIds;

    // 更新clothes数组中的数据
    const clothes = [...this.data.clothes];
    let hasUpdates = false;

    clothes.forEach(item => {
      if (clothingIds.includes(item._id)) {
        // 更新找到的衣物
        for (const key in dataToUpdate) {
          if (dataToUpdate[key] !== undefined) {
            item[key] = dataToUpdate[key];
            hasUpdates = true;
          }
        }
      }
    });

    if (hasUpdates) {
      // 更新本地数据
      this.setData({ clothes: clothes });
      this.processClothesData(clothes);

      // 更新本地缓存
      this.updateLocalCache(clothes);
    }
  },

  // 显示批量删除确认对话框
  showBatchDeleteConfirm: function() {
    if (this.data.selectedClothes.length === 0) {
      wx.showToast({
        title: '请先选择衣物',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认批量删除',
      content: `确定要删除选中的${this.data.selectedClothes.length}件衣物吗？此操作不可撤销。`,
      confirmColor: '#e74c3c',
      success: res => {
        if (res.confirm) {
          this.batchDeleteClothes();
        }
      }
    });
  },

  // 批量删除衣物
  batchDeleteClothes: function() {
    const selectedClothes = this.data.selectedClothes;

    if (selectedClothes.length === 0) {
      wx.showToast({
        title: '请先选择衣物',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    closetUtils.showLoading('批量删除中...');

    // 调用云函数批量删除
    wx.cloud.callFunction({
      name: 'batchDeleteClothes',
      data: {
        clothingIds: selectedClothes
      }
    })
    .then(res => {
      if (res.result && res.result.success) {
        closetUtils.hideLoading();
        closetUtils.showSuccessToast(`成功删除${res.result.deleted}件衣物`);

        // 更新本地数据
        this.updateLocalClothesAfterBatchDelete(selectedClothes);

        // 退出选择模式
        this.exitSelectionMode();

        // 设置标记，提示首页需要刷新数据
        wx.setStorageSync('needRefreshWardrobeSummary', true);
        wx.setStorageSync('fromFilterPage', true);
        

        // 更新用户衣服上限
        limitManager.updateClothesCount(this.data.userOpenId, -res.result.deleted)
          .then(updatedLimits => {
            console.log('衣物数量更新成功，当前数量:', updatedLimits.clothesCount);
            // 设置全局标记，指示限制数据需要刷新
            const app = getApp();
            app.globalData.limitDataNeedRefresh = true;
          })
          .catch(err => {
            console.error('更新衣物数量失败:', err);
          });
      } else {
        throw new Error(res.result?.message || '批量删除失败');
      }
    })
    .catch(error => {
      console.error('批量删除衣物失败:', error);
      closetUtils.hideLoading();
      closetUtils.showErrorToast('删除失败: ' + (error.message || '未知错误'));
    });
  },

  // 批量删除后更新本地衣物数据
  updateLocalClothesAfterBatchDelete: function(clothingIds) {
    if (!clothingIds || !Array.isArray(clothingIds) || clothingIds.length === 0) {
      return;
    }

    // 从clothes数组中移除已删除的衣物
    const updatedClothes = this.data.clothes.filter(item => !clothingIds.includes(item._id));

    // 更新本地数据
    this.setData({ clothes: updatedClothes });
    this.processClothesData(updatedClothes);

    // 更新本地缓存
    this.updateLocalCache(updatedClothes);
  },

  // 获取用户所有衣物的类别
  getUserClothesCategories: function() {
    console.log('获取用户所有衣物的类别');

    // 默认类别
    const categoriesSet = new Set(['上衣', '裤子', '裙子', '外套', '鞋子', '配饰']);

    // 从当前页面的衣物数据中提取类别
    if (this.data.clothes && Array.isArray(this.data.clothes)) {
      this.data.clothes.forEach(item => {
        if (item.category) {
          categoriesSet.add(item.category);
        }
      });
    }

    // 尝试从缓存中获取更多类别
    try {
      const cacheKey = `user_clothes_cache_${this.data.userOpenId}`;
      const clothesCache = wx.getStorageSync(cacheKey);

      if (clothesCache) {
        // 处理不同的缓存结构
        const clothesArray = clothesCache.clothes || clothesCache;

        if (Array.isArray(clothesArray)) {
          clothesArray.forEach(item => {
            if (item.category && item.category) {
              categoriesSet.add(item.category);
            }
          });
        }
      }
    } catch (err) {
      console.error('从缓存获取类别失败:', err);
    }

    // 将Set转换为数组
    const categoriesArray = Array.from(categoriesSet);
    console.log('用户衣物类别列表:', categoriesArray);

    // 更新有效类别列表
    this.setData({
      validCategories: categoriesArray
    });

    return categoriesArray;
  },

  // 跳转到衣物详情页
  navigateToDetail: function(e) {
    const id = e.currentTarget.dataset.id;

    // 关闭当前弹窗
    this.closeClothesDetail();

    // 跳转到详情页
    wx.navigateTo({
      url: `../detail/detail?id=${id}`,
      fail: function(err) {
        console.error('跳转到详情页失败:', err);
        closetUtils.showErrorToast('打开详情页失败');
      }
    });
  },

  // 切换筛选面板
  toggleFilterPanel: function(e) {
    const panelType = e.currentTarget.dataset.type;

    // 如果点击的是已经展开的面板，则收起
    if (this.data.activeFilterPanel === panelType) {
      this.setData({ activeFilterPanel: '' });
    } else {
      // 否则展开点击的面板，收起其他面板
      this.setData({ activeFilterPanel: panelType });
    }
  },

  // 收起所有筛选面板
  closeAllFilterPanels: function() {
    this.setData({ activeFilterPanel: '' });
  },

  // 在应用筛选条件后收起面板 - 已不再使用，由各筛选函数直接处理
  applyFilter: function(callback) {
    console.log('此函数已废弃，请直接调用setData并在回调中处理筛选逻辑');
    // 为了向后兼容，保留旧的实现
    if (typeof callback === 'function') {
      callback();
    }
    this.closeAllFilterPanels();
    this.applyAllFilters();
  },

  // 切换存储位置筛选
  toggleStorageLocationFilter: function(e) {
    const location = e.currentTarget.dataset.location;
    if (!location) return; // 防止添加undefined值

    const selectedStorageLocations = [...this.data.selectedStorageLocations];

    // 检查是否已选中
    const index = selectedStorageLocations.indexOf(location);

    if (index === -1) {
      // 未选中，添加
      selectedStorageLocations.push(location);
    } else {
      // 已选中，移除
      selectedStorageLocations.splice(index, 1);
    }

    // 更新筛选存储位置选中状态对象
    const filterStorageLocationIsSelected = {...this.data.filterStorageLocationIsSelected};
    this.data.storageLocationOptions.forEach(loc => {
      filterStorageLocationIsSelected[loc] = selectedStorageLocations.includes(loc);
    });

    // 更新选中的存储位置并应用筛选
    this.setData({
      selectedStorageLocations: selectedStorageLocations,
      filterStorageLocationIsSelected: filterStorageLocationIsSelected,
      displayStorageLocations: this.getDisplayFilters(selectedStorageLocations)
    }, () => {
      // 不再自动关闭筛选面板
      // this.closeAllFilterPanels();
      this.applyAllFilters();
    });
  },

  // 清除存储位置筛选
  clearStorageLocationFilter: function() {
    // 重置筛选存储位置选中状态
    const filterStorageLocationIsSelected = {...this.data.filterStorageLocationIsSelected};
    this.data.storageLocationOptions.forEach(location => {
      filterStorageLocationIsSelected[location] = false;
    });

    this.setData({
      selectedStorageLocations: [],
      filterStorageLocationIsSelected: filterStorageLocationIsSelected,
      displayStorageLocations: ''
    }, () => {
      // 不再自动关闭筛选面板
      // this.closeAllFilterPanels();
      this.applyAllFilters();
    });
  },

  // 显示历史值建议
  showSuggestions: function(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({
      showingSuggestions: true,
      currentSuggestionField: field
    });

    // 在设置当前字段后立即加载该字段的建议
    this.loadSuggestions();
  },

  // 选择历史值建议
  selectSuggestion: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.currentTarget.dataset.value;

    // 根据字段类型调用相应的onChange函数
    switch(field) {
      case 'type_detail':
        this.setData({
          'editingClothing.type_detail': value,
          showingSuggestions: false
        });
        break;
      case 'color':
        this.setData({
          'editingClothing.color': value,
          showingSuggestions: false
        });
        break;
      case 'style':
        this.setData({
          'editingClothing.style': value,
          showingSuggestions: false
        });
        break;
      case 'size':
        this.setData({
          'editingClothing.size': value,
          showingSuggestions: false
        });
        break;
      case 'brand':
        this.setData({
          'editingClothing.brand': value,
          showingSuggestions: false
        });
        break;
      case 'purchaseChannel':
        this.setData({
          'editingClothing.purchaseChannel': value,
          showingSuggestions: false
        });
        break;
      case 'storageLocation':
        this.setData({
          'editingClothing.storageLocation': value,
          showingSuggestions: false
        });
        break;
      case 'material':
        this.setData({
          'editingClothing.material': value,
          showingSuggestions: false
        });
        break;
    }
  },

  // 加载历史值建议
  loadSuggestions: function() {
    // 获取所有衣物数据
    const allClothes = this.data.clothes || [];

    // 检查当前正在编辑的字段
    const currentField = this.data.currentSuggestionField;

    // 如果没有当前编辑字段，则不加载建议
    if (!currentField) {
      console.log('没有正在编辑的字段，不加载建议');
      return;
    }

    // 获取当前编辑衣物的类别
    const currentCategory = this.data.editingClothing?.category;
    console.log('为字段加载建议:', currentField, '当前类别:', currentCategory);

    // 初始化建议数组结构，但只填充当前编辑的字段
    const suggestions = {
      type_detail: [],
      color: [],
      style: [],
      size: [],
      brand: [],
      purchaseChannel: [],
      storageLocation: [],
      material: []
    };

    // 遍历所有衣物，仅收集当前编辑字段的唯一值
    // 且只收集与当前衣物同类别的历史值
    allClothes.forEach(item => {
      // 如果有类别且与当前编辑衣物类别不同，则跳过
      if (currentCategory && item.category !== currentCategory) {
        return;
      }

      const value = item[currentField];
      if (value && !suggestions[currentField].includes(value)) {
        suggestions[currentField].push(value);
      }
    });

    // 设置到data中
    this.setData({
      suggestions: suggestions
    });
  },

  // 显示衣柜选择器
  showWardrobePicker: function() {
    let currentWardrobe = null;

    if (this.data.editingClothing && this.data.editingClothing.wardrobeId) {
      currentWardrobe = this.data.editingClothing.wardrobeId;
    }

    // 从本地缓存获取衣柜列表
    const cacheKey = 'user_wardrobes_' + this.data.userOpenId;
    const cachedWardrobes = wx.getStorageSync(cacheKey) || [];

    console.log('从缓存获取衣柜列表:', cachedWardrobes);

    const wardrobeIsSelected = {};
    if (cachedWardrobes && cachedWardrobes.length > 0) {
      cachedWardrobes.forEach(wardrobe => {
        wardrobeIsSelected[wardrobe._id] = (currentWardrobe === wardrobe._id);
      });
    }

    this.setData({
      wardrobes: cachedWardrobes,
      tempSelectedWardrobe: currentWardrobe,
      wardrobeIsSelected: wardrobeIsSelected,
      showWardrobePicker: true
    });
  },

  // 隐藏衣柜选择器
  hideWardrobePicker: function() {
    this.setData({
      showWardrobePicker: false
    });
  },

  // 选择衣柜
  selectWardrobe: function(e) {
    const wardrobeId = e.currentTarget.dataset.wardrobeid;

    // 只允许选择一个衣柜，更新所有选择状态
    const wardrobeIsSelected = {};
    this.data.wardrobes.forEach(wardrobe => {
      wardrobeIsSelected[wardrobe._id] = (wardrobe._id === wardrobeId);
    });

    this.setData({
      tempSelectedWardrobe: wardrobeId,
      wardrobeIsSelected: wardrobeIsSelected
    });
  },

  // 确认衣柜选择
  confirmWardrobePicker: function() {
    console.log('确认衣柜选择, isBatchEdit =', this.data.isBatchEdit);

    // 找到选中的衣柜名称以显示
    let wardrobeName = '';
    let wardrobeId = this.data.tempSelectedWardrobe;

    if (wardrobeId) {
      const selectedWardrobe = this.data.wardrobes.find(w => w._id === wardrobeId);
      if (selectedWardrobe) {
        wardrobeName = selectedWardrobe.name;
        console.log('选中衣柜:', wardrobeName, '(ID:', wardrobeId, ')');
      }
    }

    // 检查是否是批量编辑模式
    if (this.data.isBatchEdit) {
      console.log('批量编辑模式: 更新衣柜为', wardrobeName);
      this.setData({
        'batchEditData.wardrobeId': wardrobeId,
        'batchEditData.wardrobeName': wardrobeName,
        showWardrobePicker: false,
        isBatchEdit: false
      });
      return;
    }

    // 单个衣物编辑模式
    console.log('单个衣物编辑模式: 更新衣柜为', wardrobeName);
    this.setData({
      'editingClothing.wardrobeId': wardrobeId,
      'editingClothing.wardrobeName': wardrobeName,
      showWardrobePicker: false
    });
  },

  // 获取用户衣物类别
  getUserClothesCategories: function() {
    // 从所有衣物数据中提取类别
    const categories = new Set();

    this.data.clothes.forEach(item => {
      if (item.category) {
        categories.add(item.category);
      }
    });

    // 转换为数组并返回
    return Array.from(categories);
  },

  // 自定义类别输入处理
  onCustomCategoryInput: function(e) {
    console.log('自定义类别输入:', e.detail.value);
    this.setData({
      customCategoryValue: e.detail.value
    });
  },

  // 加载用户衣柜
  loadWardrobes: function() {
    const db = wx.cloud.database();

    return new Promise((resolve, reject) => {
      // 从本地缓存获取userOpenId
      const userOpenId = wx.getStorageSync('userOpenId');

      if (!userOpenId) {
        console.error('未能从缓存中获取userOpenId');
        reject(new Error('未能从缓存中获取userOpenId'));
        return;
      }

      console.log('从缓存获取到userOpenId:', userOpenId);

      // 先尝试从缓存加载衣柜数据
      const cacheKey = USER_WARDROBES_CACHE_KEY + userOpenId;
      const cachedWardrobes = wx.getStorageSync(cacheKey);

      if (cachedWardrobes && cachedWardrobes.length > 0) {
        console.log('从缓存加载衣柜列表:', cachedWardrobes);

        this.setData({
          wardrobes: cachedWardrobes,
          userOpenId: userOpenId // 同时更新组件中的userOpenId
        });

        resolve(cachedWardrobes);
        return;
      }

      // 缓存不存在或为空，从云端获取
      console.log('从云端获取衣柜列表，userOpenId:', userOpenId);

      // 查询用户的所有衣柜
      db.collection('wardrobes')
        .where({
          _openid: userOpenId
        })
        .get()
        .then(res => {
          console.log('成功获取衣柜列表:', res.data);

          this.setData({
            wardrobes: res.data,
            userOpenId: userOpenId // 同时更新组件中的userOpenId
          });

          // 将衣柜数据保存到缓存
          wx.setStorageSync(cacheKey, res.data);

          resolve(res.data);
        })
        .catch(err => {
          console.error('获取衣柜列表失败:', err);
          reject(err);
        });
    });
  },

  // 初始化衣柜筛选选项
  initWardrobeOptions: function(wardrobes) {
    if (!wardrobes) {
      console.log('没有衣柜数据，不初始化衣柜筛选选项');
      return;
    }

    console.log('初始化衣柜筛选选项:', wardrobes);

    // 确保衣柜数据是数组
    const wardrobeArray = Array.isArray(wardrobes) ? wardrobes : [];

    // 如果没有衣柜数据，也初始化空数组
    if (wardrobeArray.length === 0) {
      console.log('衣柜数组为空，初始化空的筛选选项');
      this.setData({
        wardrobeOptions: [],
        filterWardrobeIsSelected: {}
      });
      return;
    }

    // 初始化衣柜选中状态对象
    const filterWardrobeIsSelected = {};
    wardrobeArray.forEach(wardrobe => {
      if (wardrobe && wardrobe._id) {
        filterWardrobeIsSelected[wardrobe._id] = false;
      }
    });

    console.log('设置衣柜筛选选项:', wardrobeArray);

    this.setData({
      wardrobeOptions: wardrobeArray,
      filterWardrobeIsSelected: filterWardrobeIsSelected
    });
  },

  // 选择衣柜筛选
  selectWardrobeFilter: function(e) {
    const wardrobeId = e.currentTarget.dataset.wardrobeid;

    // 只允许选择一个衣柜，更新所有选择状态
    const filterWardrobeIsSelected = {};
    this.data.wardrobeOptions.forEach(wardrobe => {
      filterWardrobeIsSelected[wardrobe._id] = (wardrobe._id === wardrobeId);
    });

    // 找到选中衣柜的名称
    let wardrobeName = '';
    const selectedWardrobe = this.data.wardrobeOptions.find(w => w._id === wardrobeId);
    if (selectedWardrobe) {
      wardrobeName = selectedWardrobe.name;
    }

    // 更新选中的衣柜并应用筛选
    this.setData({
      selectedWardrobe: wardrobeId,
      filterWardrobeIsSelected: filterWardrobeIsSelected,
      displayWardrobe: wardrobeName,
      activeFilterPanel: '',
      // 重置分页
      currentPage: 1
    });

    // 应用所有筛选条件
    this.applyAllFilters();
  },

  // 选择未分类衣物
  selectUnclassifiedWardrobe: function() {
    // 重置所有衣柜选中状态
    const filterWardrobeIsSelected = {};
    this.data.wardrobeOptions.forEach(wardrobe => {
      filterWardrobeIsSelected[wardrobe._id] = false;
    });

    // 更新选中状态为未分类
    this.setData({
      selectedWardrobe: 'unclassified',
      filterWardrobeIsSelected: filterWardrobeIsSelected,
      displayWardrobe: '未分类',
      activeFilterPanel: '',
      // 重置分页
      currentPage: 1
    });

    // 应用所有筛选条件
    this.applyAllFilters();
  },

  // 清除衣柜筛选
  clearWardrobeFilter: function() {
    // 重置所有衣柜选中状态
    const filterWardrobeIsSelected = {};
    this.data.wardrobeOptions.forEach(wardrobe => {
      filterWardrobeIsSelected[wardrobe._id] = false;
    });

    // 更新状态并应用筛选
    this.setData({
      selectedWardrobe: '',
      filterWardrobeIsSelected: filterWardrobeIsSelected,
      displayWardrobe: '',
      activeFilterPanel: '',
      // 重置分页
      currentPage: 1
    });

    // 应用所有筛选条件
    this.applyAllFilters();
  },

  // 处理搜索输入
  onSearchInput: function(e) {
    const keyword = e.detail.value;

    this.setData({
      searchKeyword: keyword,
      isSearchActive: keyword !== '',
      // 重置分页
      currentPage: 1
    });

    // 应用所有筛选条件，包括搜索
    this.applyAllFilters();
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      isSearchActive: false,
      // 重置分页
      currentPage: 1
    });

    // 重新应用筛选条件
    this.applyAllFilters();
  },

  // 检测设备是iOS还是Android
  checkIsIOS: function() {
    const systemInfo = wx.getSystemInfoSync();
    return systemInfo.platform === 'ios' || systemInfo.system.toLowerCase().includes('ios');
  },

  // iOS设备特殊处理焦点
  handleIOSFocus: function(e) {
    if (!this.checkIsIOS()) return;

    // 获取当前输入框
    const field = e.currentTarget.dataset.field;

    // 根据字段名确定更新的对象
    let dataPath;
    if (field.startsWith('editingClothing.')) {
      // 处理衣物编辑字段
      const actualField = field.replace('editingClothing.', '');
      dataPath = `editingClothing.${actualField}Focus`;
    } else if (field === 'customCategory') {
      // 处理自定义类别字段
      dataPath = 'customCategoryFocus';
    } else if (field === 'searchKeyword') {
      // 处理搜索关键词字段
      dataPath = 'searchKeywordFocus';
    } else {
      // 其他字段
      dataPath = `${field}Focus`;
    }

    // iOS设备上，先让输入框失去焦点再重新聚焦，解决输入延迟问题
    setTimeout(() => {
      this.setData({
        [dataPath]: true
      });
    }, 50);
  },

  // 选择自定义类别输入时处理iOS设备焦点问题
  selectEditCategoryForIOS: function(e) {
    const category = e.currentTarget.dataset.category;

    this.setData({
      tempSelectedCategory: category,
      customCategoryFocus: false
    });

    // 如果选择自定义类别，并且是iOS设备，等待页面渲染后设置焦点
    if (category === '自定义' && this.checkIsIOS()) {
      setTimeout(() => {
        this.setData({
          customCategoryFocus: true
        });
      }, 300);
    }
  },
});