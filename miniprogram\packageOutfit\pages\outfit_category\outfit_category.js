// page/wardrobe/outfit/outfit_category/outfit_category.js

// 引入图片管理模块
const imageManager = require('./modules/imageManager');
// 引入搭配管理模块
const outfitManager = require('./modules/outfitManager');
// 引入本地图片缓存模块
const localImageCache = require('./modules/localImageCache');

Page({
  data: {
    // 定义颜色常量 - 秋季色彩方案
    colors: {
      cowhide_cocoa: '#442D1C',   // 深棕色 Cowhide Cocoa
      spiced_wine: '#74301C',     // 红棕色 Spiced Wine
      toasted_caramel: '#84592B', // 焦糖色 Toasted Caramel
      olive_harvest: '#9D9167',   // 橄榄色 Olive Harvest
      golden_batter: '#E8D1A7',   // 金黄色 Golden Batter
    },
    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',      // 深粉色
      pinkMedium: '#EEA0B2',    // 中粉色
      pinkLight: '#F9C9D6',     // 浅粉色
      blueLight: '#CBE0F9',     // 浅蓝色
      blueMedium: '#97C8E5',    // 中蓝色
      blueDark: '#5EA0D0',      // 深蓝色
    },
    // 黑白色系配色
    blackWhiteColors: {
      black: '#000000',         // 纯黑
      darkGray: '#333333',      // 深灰
      mediumGray: '#666666',    // 中灰
      lightGray: '#CCCCCC',     // 浅灰
      white: '#FFFFFF',         // 纯白
      offWhite: '#F5F5F5',      // 灰白
    },
    // 主题风格
    themeStyle: 'autumn', // 默认秋季风格
    isLoading: true,
    userOpenId: '',

    // 类别信息
    category: '',         // 类别ID
    categoryName: '',     // 类别名称
    outfits: [],          // 该类别的搭配列表

    // 图片缓存控制
    imageRefreshNeeded: false // 标记图片是否需要刷新
  },

  // URL检查定时器
  urlCheckTimer: null,

  onLoad: function(options) {
    // 初始化云环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-3gi97kso9ab01185',
        traceUser: true,
      });
    }

    // 清除过期的URL缓存
    imageManager.clearExpiredURLCache();

    // 确保本地图片缓存目录存在
    localImageCache.ensureCacheDir();

    // 清除过期的本地图片缓存
    localImageCache.clearExpiredCache();

    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });
      // 应用主题样式
      this.applyThemeStyle(savedTheme);
    }

    // 获取类别参数
    if (options && options.category) {
      const category = options.category;

      // 设置类别名称
      const categoryNames = {
        'daily': '日常穿搭',
        'work': '职业穿搭',
        'party': '派对穿搭',
        'sport': '运动穿搭',
        'seasonal': '季节穿搭'
      };

      // 检查是否是预设类型
      if (categoryNames[category]) {
        this.setData({
          category: category,
          categoryName: categoryNames[category]
        });
      }
      // 检查是否是自定义类型ID (以custom_开头)
      else if (category.startsWith('custom_')) {
        // 从本地缓存获取自定义类型
        const customCategories = wx.getStorageSync('customOutfitCategories') || [];
        const customCategory = customCategories.find(item => item.value === category);

        if (customCategory) {
          this.setData({
            category: customCategory.name, // 使用名称作为category值
            categoryName: customCategory.name
          });
        } else {
          // 如果在本地缓存中找不到，尝试从数据库获取
          wx.cloud.callFunction({
            name: 'getCustomOutfitCategories',
            success: res => {
              if (res.result && res.result.success && res.result.data) {
                const customCategory = res.result.data.find(item => item.value === category);
                if (customCategory) {
                  this.setData({
                    category: customCategory.name, // 使用名称作为category值
                    categoryName: customCategory.name
                  });
                }
              }
            }
          });

          // 先设置一个默认名称
          this.setData({
            category: category,
            categoryName: '自定义穿搭'
          });
        }
      }
      // 可能是直接使用自定义类型名称
      else {
        // 检查是否是自定义类型名称
        const customCategories = wx.getStorageSync('customOutfitCategories') || [];
        const customCategory = customCategories.find(item => item.name === category);

        if (customCategory) {
          // 是自定义类型名称
          this.setData({
            category: category,
            categoryName: category
          });
        } else {
          // 未知类型，使用传入的值
          this.setData({
            category: category,
            categoryName: category || '穿搭'
          });
        }
      }

      // 获取用户OpenID
      this.getUserOpenId();
    } else {
      wx.showToast({
        title: '缺少类别参数',
        icon: 'none'
      });

      // 延迟返回
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onShow: function() {
    console.log('页面显示，尝试获取最新数据');

    // 如果已经有类别信息，尝试获取最新数据
    if (this.data.category && this.data.userOpenId) {
      // 启动定时器，定期检查图片是否需要刷新（每5分钟检查一次）
      this.startURLCheckTimer();
    }
  },

  onHide: function() {
    // 页面隐藏时清除定时器
    this.clearURLCheckTimer();
  },

  onUnload: function() {
    // 页面卸载时清除定时器
    this.clearURLCheckTimer();
  },

  // 启动URL检查定时器
  startURLCheckTimer: function() {
    // 清除可能存在的旧定时器
    this.clearURLCheckTimer();

    // 创建新定时器，每5分钟检查一次
    this.urlCheckTimer = setInterval(() => {
      this.refreshImages();
    }, 300000); // 5分钟

    console.log('已启动图片检查定时器');
  },

  // 清除URL检查定时器
  clearURLCheckTimer: function() {
    if (this.urlCheckTimer) {
      clearInterval(this.urlCheckTimer);
      this.urlCheckTimer = null;
      console.log('已清除图片检查定时器');
    }
  },

  // 刷新所有图片
  refreshImages: function() {
    console.log('开始刷新图片');

    if (!this.data.outfits || this.data.outfits.length === 0) {
      console.log('没有搭配数据，不需要刷新图片');
      return;
    }

    this.setData({
      imageRefreshNeeded: true
    }, () => {
      // 使用imageManager处理所有搭配的图片
      imageManager.processOutfitsImages(this.data.outfits)
        .then(processedOutfits => {
          console.log('图片刷新完成，已处理', processedOutfits.length, '个搭配');

          // 更新数据
                  this.setData({
            outfits: processedOutfits,
            imageRefreshNeeded: false
          });
        })
        .catch(err => {
          console.error('刷新图片出错:', err);
                  this.setData({
            imageRefreshNeeded: false
          });
        });
    });
  },

  // 获取默认图片
  getDefaultImage: function(isItem = false) {
    return isItem ?
      imageManager.getDefaultItemImageUrl() :
      imageManager.getDefaultPreviewImageUrl();
  },

  // 获取用户OpenID
  getUserOpenId: function() {
    const that = this;

    console.log('开始获取用户OpenID');

    // 设置加载状态
    wx.showLoading({
      title: '加载中...',
    });

    // 尝试从本地缓存获取OpenID
    const openid = wx.getStorageSync('openid');
    if (openid) {
      console.log('从本地存储获取到OpenID:', openid);
      that.setData({
        userOpenId: openid
      });

      // 获取搭配数据
      that.getOutfitsByCategory();
      return;
    }

    // 如果本地没有，则调用云函数获取
    wx.cloud.callFunction({
      name: 'login',
      data: {},
      success: res => {
        console.log('云函数获取到的openid:', res.result.openid);
        const openid = res.result.openid;

        if (openid) {
          // 存储OpenID到本地
          wx.setStorageSync('openid', openid);

          that.setData({
            userOpenId: openid
          });

          // 获取搭配数据
          that.getOutfitsByCategory();
        } else {
          console.error('云函数返回的openid为空');
          // 即使没有openid，也尝试获取搭配数据（可能是公共数据）
          that.getOutfitsByCategory();
        }
      },
      fail: err => {
        console.error('获取OpenID失败:', err);

        // 即使获取OpenID失败，也尝试获取搭配数据（可能是公共数据）
        that.getOutfitsByCategory();
      }
    });
  },

  // 获取指定类别的搭配数据
  getOutfitsByCategory: function() {
    const that = this;

    // 设置加载状态
    that.setData({
      isLoading: true
    });

    console.log('开始获取搭配数据，类别:', that.data.category);

    // 先尝试从缓存获取数据
    if (that.data.userOpenId) {
      // 使用outfitManager模块的缓存功能
      const cachedOutfits = outfitManager.loadFromLocalCache(that.data.userOpenId);

      if (cachedOutfits && cachedOutfits.length > 0) {
        console.log('从本地缓存获取到搭配数据，总数:', cachedOutfits.length);

        // 过滤指定类别的搭配
        const filteredOutfits = outfitManager.getOutfitsByCategory(cachedOutfits, that.data.category);
        console.log('从缓存过滤类别后的搭配数量:', filteredOutfits.length);

        if (filteredOutfits.length > 0) {
          // 处理搭配数据
          let outfits = filteredOutfits.map(outfit => {
            // 添加格式化后的日期
            if (outfit.createTime) {
              outfit.createTimeFormatted = that.formatDate(outfit.createTime);
            }

            // 确保outfit有id字段
            if (!outfit.id && outfit._id) {
              outfit.id = outfit._id;
            }

            // 确保outfit有类型字段
            if (!outfit.type && outfit.category) {
              outfit.type = outfit.category;
            } else if (!outfit.type && !outfit.category) {
              // 默认类型为日常
              outfit.type = 'daily';
              outfit.category = 'daily';
            }

            return outfit;
          });

          // 更新状态
          that.setData({
            outfits: outfits,
            isLoading: false
          });

          // 初始化图片
          that.initOutfitImages(outfits);

          // 隐藏加载提示
          wx.hideLoading();

          return; // 使用缓存数据后直接返回，不再进行云数据库请求
        }
      }
    }

    // 如果缓存不可用或没有对应的类别数据，则从云数据库获取
    const db = wx.cloud.database();

    // 查询条件：用户的指定类别搭配
    const query = {
      category: that.data.category
    };

    // 如果有用户OpenID，添加到查询条件
    if (that.data.userOpenId) {
      query._openid = that.data.userOpenId;
    }

    console.log('从云数据库查询搭配，查询条件:', query);

    // 获取搭配
    db.collection('outfits')
      .where(query)
      .orderBy('createTime', 'desc')
      .get()
      .then(res => {
        console.log('获取搭配成功:', res.data);

        // 处理搭配数据
        let outfits = res.data || [];

        // 如果没有搭配数据，显示空状态提示
        if (outfits.length === 0) {
          console.log('当前类别没有搭配数据');

          // 更新状态
          that.setData({
            outfits: [],
            isLoading: false
          });

          // 显示提示
          wx.showToast({
            title: '暂无搭配',
            icon: 'none',
            duration: 2000
          });

          // 隐藏加载提示
          wx.hideLoading();

          return;
        }

        console.log('获取到', outfits.length, '条搭配数据');

        // 格式化搭配数据，确保有必要的字段
        outfits = outfits.map(outfit => {
          // 添加格式化后的日期
          if (outfit.createTime) {
            outfit.createTimeFormatted = that.formatDate(outfit.createTime);
          }

          // 确保outfit有id字段
          if (!outfit.id && outfit._id) {
            outfit.id = outfit._id;
          }

          // 确保outfit有类型字段
          if (!outfit.type && outfit.category) {
            outfit.type = outfit.category;
          } else if (!outfit.type && !outfit.category) {
            // 默认类型为日常
            outfit.type = 'daily';
            outfit.category = 'daily';
          }

          return outfit;
        });

        // 检查是否需要获取完整的衣物数据
        const needFetchClothes = outfits.some(outfit =>
          outfit.items && Array.isArray(outfit.items) &&
          outfit.items.some(item => item.clothingId && (!item.name || !item.imageUrl || !item.imageFileID))
        );

        if (needFetchClothes) {
          console.log('需要获取衣物详细数据');

          // 收集所有需要获取的衣物ID
          const allClothingIds = [];
          outfits.forEach(outfit => {
            if (outfit.items && Array.isArray(outfit.items)) {
              outfit.items.forEach(item => {
                if (item && item.clothingId && (!item.name || !item.imageUrl || !item.imageFileID)) {
                  allClothingIds.push(item.clothingId);
                }
              });
            }
          });

          if (allClothingIds.length > 0) {
            console.log('准备获取', allClothingIds.length, '个衣物的详细数据');

            // 获取衣物数据
            that.getClothesData(allClothingIds)
              .then(clothesData => {
                console.log('获取到', clothesData.length, '个衣物详细数据');

                // 更新每个搭配的衣物项数据
                outfits.forEach(outfit => {
                  if (outfit.items && Array.isArray(outfit.items)) {
                    outfit.items = outfit.items.map(item => {
                      if (item && item.clothingId) {
                        const clothingData = clothesData.find(c => c && c._id === item.clothingId);
                        if (clothingData) {
                          // 优先使用抠图后的图片 (processedImageFileID)
                          const imageFileID = clothingData.processedImageFileID || clothingData.imageFileID || clothingData.imageUrl || null;

                          // 合并衣物数据
                          return {
                            ...item,
                            name: item.name || clothingData.name,
                            type: item.type || clothingData.type || clothingData.category,
                            category: item.category || clothingData.category,
                            // 优先使用抠图后的图片URL或文件ID
                            imageUrl: item.imageUrl || clothingData.processedImageUrl || clothingData.imageUrl || clothingData.processedImageFileID,
                            // 保存原始fileID和抠图后的fileID用于后续刷新
                            imageFileID: imageFileID,
                            processedImageFileID: clothingData.processedImageFileID || null,
                            originalImageFileID: clothingData.imageFileID || null,
                            originalClothing: clothingData
                          };
                        }
                      }
                      return item;
                    });
                  }
                });

                // 处理所有搭配的图片URL
                return imageManager.processOutfitsImages(outfits);
              })
              .then(processedOutfits => {
                console.log('图片处理完成，更新UI');

                // 更新数据
                that.setData({
                  outfits: processedOutfits,
                  isLoading: false
                });

                // 隐藏加载提示
                wx.hideLoading();
              })
              .catch(err => {
                console.error('处理搭配衣物详细数据出错:', err);

                // 即使出错，也尝试处理图片并显示搭配数据
                imageManager.processOutfitsImages(outfits)
                  .then(processedOutfits => {
                    that.setData({
                      outfits: processedOutfits,
                      isLoading: false
                    });

                    // 隐藏加载提示
                    wx.hideLoading();
                  })
                  .catch(imgErr => {
                    console.error('处理搭配图片出错:', imgErr);
                    that.setData({
                      outfits: outfits,
                      isLoading: false
                    });
                    wx.hideLoading();
                  });
              });
          } else {
            // 即使没有需要获取的衣物ID，也需要处理图片
            imageManager.processOutfitsImages(outfits)
              .then(processedOutfits => {
                console.log('图片处理完成，更新UI');

                // 更新数据
                that.setData({
                  outfits: processedOutfits,
                  isLoading: false
                });

                // 隐藏加载提示
                wx.hideLoading();
              })
              .catch(err => {
                console.error('处理搭配图片出错:', err);

                // 即使出错，也尝试显示搭配数据
                that.setData({
                  outfits: outfits,
                  isLoading: false
                });

                // 隐藏加载提示
                wx.hideLoading();
              });
          }
        } else {
          // 不需要获取衣物详细数据，直接处理图片
          imageManager.processOutfitsImages(outfits)
            .then(processedOutfits => {
              console.log('图片处理完成，更新UI');

              // 更新数据
              that.setData({
                outfits: processedOutfits,
                isLoading: false
              });

              // 隐藏加载提示
              wx.hideLoading();
            })
            .catch(err => {
              console.error('处理搭配图片出错:', err);

              // 即使出错，也尝试显示搭配数据
              that.setData({
                outfits: outfits,
                isLoading: false
              });

              // 隐藏加载提示
              wx.hideLoading();
            });
        }

        // 更新状态
        that.setData({
          outfits: outfits,
          isLoading: false
        });

        // 初始化搭配图片
        that.initOutfitImages(outfits);

        // 隐藏加载提示
        wx.hideLoading();

        // 将搭配数据更新到本地缓存，供下次使用
        if (that.data.userOpenId) {
          // 先获取所有已有的缓存数据
          const cachedOutfits = outfitManager.loadFromLocalCache(that.data.userOpenId) || [];

          // 将新数据与已有数据合并
          let allOutfits = [...cachedOutfits];

          // 移除相同类别的旧数据
          allOutfits = allOutfits.filter(item => item.category !== that.data.category);

          // 添加新数据
          allOutfits = [...allOutfits, ...outfits];

          // 更新缓存
         // outfitManager.updateLocalCache(that.data.userOpenId, allOutfits);
         //onsole.log('已更新本地缓存数据，总数:', allOutfits.length);
        }
      })
      .catch(err => {
        console.error('获取搭配失败:', err);

        // 更新状态
        that.setData({
          outfits: [],
          isLoading: false
        });

        // 显示错误提示
        wx.showToast({
          title: '获取搭配失败',
          icon: 'none',
          duration: 2000
        });

        // 隐藏加载提示
        wx.hideLoading();
      });
  },

  // 获取衣物数据
  getClothesData: function(clothingIds) {
    return new Promise((resolve, reject) => {
      if (!clothingIds || !Array.isArray(clothingIds) || clothingIds.length === 0) {
        resolve([]);
        return;
      }

      const db = wx.cloud.database();
      const _ = db.command;

      // 查询衣物数据
      db.collection('clothes')
        .where({
          _id: _.in(clothingIds)
        })
        .get()
        .then(res => {
          console.log('获取衣物数据成功:', res.data);
          resolve(res.data || []);
        })
        .catch(err => {
          console.error('获取衣物数据失败:', err);
          reject(err);
        });
    });
  },

  // 应用主题样式
  applyThemeStyle: function(themeName) {
    console.log('搭配页面应用主题:', themeName);

    try {
      // 设置导航栏颜色
      if (themeName === 'autumn') {
        // 设置秋季主题导航栏
        wx.setNavigationBarColor({
          frontColor: '#000000', // 黑色文字
          backgroundColor: this.data.colors.golden_batter, // 金黄色背景
          animation: {
            duration: 300,
            timingFunc: 'easeIn'
          }
        });

        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: this.data.categoryName
        });

      } else if (themeName === 'pinkBlue') {
        // 设置粉蓝主题导航栏
        wx.setNavigationBarColor({
          frontColor: '#000000', // 黑色文字
          backgroundColor: this.data.pinkBlueColors.pinkLight, // 浅粉色背景
          animation: {
            duration: 300,
            timingFunc: 'easeIn'
          }
        });

        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: this.data.categoryName
        });
      } else if (themeName === 'blackWhite') {
        // 设置黑白主题导航栏
        wx.setNavigationBarColor({
          frontColor: '#000000', // 黑色文字
          backgroundColor: this.data.blackWhiteColors.white, // 白色背景
          animation: {
            duration: 300,
            timingFunc: 'easeIn'
          }
        });

        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: this.data.categoryName
        });
      }
    } catch (error) {
      console.error('应用主题样式出错:', error);
    }
  },

  // 跳转到搭配详情页面
  navigateToOutfitDetail: function(e) {
    const outfitId = e.currentTarget.dataset.id;

    // 跳转到详情页
    wx.navigateTo({
      url: `../outfit_detail/outfit_detail?id=${outfitId}`
    });
  },

  // 长按搭配项
  onLongPressOutfit: function(e) {
    const outfitId = e.currentTarget.dataset.id;
    const outfitName = e.currentTarget.dataset.name;

    // 显示操作菜单
    wx.showActionSheet({
      itemList: ['编辑搭配', '删除搭配'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 编辑搭配
          this.editOutfit(outfitId);
        } else if (res.tapIndex === 1) {
          // 删除搭配
          this.confirmDeleteOutfit(outfitId, outfitName);
        }
      }
    });
  },

  // 编辑搭配
  editOutfit: function(outfitId) {
    // 跳转到编辑页面
    wx.navigateTo({
      url: `../outfit_edit/outfit_edit?id=${outfitId}`
    });
  },

  // 确认删除搭配
  confirmDeleteOutfit: function(outfitId, outfitName) {
    wx.showModal({
      title: '删除搭配',
      content: `确定要删除「${outfitName || '未命名搭配'}」吗？`,
      confirmText: '删除',
      confirmColor: '#E64340',
      success: (res) => {
        if (res.confirm) {
          this.deleteOutfit(outfitId);
        }
      }
    });
  },

  // 删除搭配
  deleteOutfit: function(outfitId) {
    const that = this;

    // 显示加载提示
    wx.showLoading({
      title: '删除中...',
    });

    // 从云数据库删除
    const db = wx.cloud.database();
    db.collection('outfits')
      .doc(outfitId)
      .remove()
      .then(() => {
        console.log('删除搭配成功:', outfitId);

        // 更新本地数据，移除已删除的搭配
        const updatedOutfits = that.data.outfits.filter(outfit =>
          (outfit._id !== outfitId) && (outfit.id !== outfitId)
        );

        that.setData({
          outfits: updatedOutfits
        });

        // 显示成功提示
      wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('删除搭配失败:', err);

        // 显示错误提示
        wx.showToast({
          title: '删除失败',
          icon: 'none'
        });
      })
      .finally(() => {
        // 隐藏加载提示
        wx.hideLoading();
      });
  },

  // 创建新搭配
  goToCreateOutfit: function() {
    // 跳转到创建页面
        wx.navigateTo({
      url: `../outfit_create/outfit_create?category=${this.data.category}`
    });
  },

  // 处理图片加载错误
  handleImageError: function(e) {
    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;
    const itemIndex = e.currentTarget.dataset.itemIndex;

    console.log('图片加载错误:', type, index, itemIndex);

    // 获取默认图片
    const defaultImage = this.getDefaultImage(type === 'item');

    if (type === 'preview') {
      // 更新搭配预览图
      this.setData({
        [`outfits[${index}].previewImage`]: defaultImage
      });
    } else if (type === 'item' && itemIndex !== undefined) {
      // 更新衣物图片
      this.setData({
        [`outfits[${index}].items[${itemIndex}].imageUrl`]: defaultImage
      });
    }
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 格式化日期
  formatDate: function(timestamp) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    return `${year}.${month}.${day}`;
  },

  // 查看搭配详情 (为了兼容wxml中的bindtap="viewOutfitDetail")
  viewOutfitDetail: function(e) {
    this.navigateToOutfitDetail(e);
  },

  // 初始化搭配图片
  initOutfitImages: function(outfits) {
    console.log('初始化搭配图片，数量:', outfits ? outfits.length : 0);

    if (!outfits || outfits.length === 0) {
      console.log('没有搭配数据，不需要初始化图片');
      return;
    }

    // 使用imageManager处理所有搭配的图片
    imageManager.processOutfitsImages(outfits)
      .then(processedOutfits => {
        console.log('搭配图片初始化完成，已处理', processedOutfits.length, '个搭配');

        // 更新数据
        this.setData({
          outfits: processedOutfits,
          imageRefreshNeeded: false
        });
      })
      .catch(err => {
        console.error('初始化搭配图片出错:', err);
        console.log('尝试使用原始数据显示');

        // 即使出错，也尝试显示原始数据
        this.setData({
          outfits: outfits,
          imageRefreshNeeded: false
        });
      });
  }
});

// 处理图片加载错误
