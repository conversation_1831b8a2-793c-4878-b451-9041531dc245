.upload-result-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.upload-result-container.visible {
  opacity: 1;
  visibility: visible;
}

.upload-result-card {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
  box-sizing: border-box;
  max-height: 80vh;
  overflow-y: auto;
}

.upload-result-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  color: #333;
  margin-bottom: 20rpx;
}

.divider {
  height: 2rpx;
  background-color: #eee;
  margin: 20rpx 0;
}

.upload-stats {
  display: flex;
  justify-content: space-around;
  margin: 30rpx 0;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-label {
  font-size: 30rpx;
  color: #666;
  margin-right: 10rpx;
}

.stat-value {
  font-size: 34rpx;
  font-weight: bold;
}

.success .stat-value {
  color: #07c160;
}

.failed .stat-value {
  color: #fa5151;
}

.refund-info {
  text-align: center;
  font-size: 28rpx;
  color: #ff9500;
  margin: 20rpx 0;
}

.category-stats {
  margin: 30rpx 0;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
}

.category-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  text-align: center;
  position: relative;
}

.category-title::before,
.category-title::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 2rpx;
  background-color: #ddd;
}

.category-title::before {
  left: 30%;
  transform: translateX(-100%);
}

.category-title::after {
  right: 30%;
  transform: translateX(100%);
}

.category-list {
  max-height: 300rpx;
  overflow-y: auto;
}

.category-item {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
  font-size: 28rpx;
  color: #666;
}

.category-name {
  flex: 1;
}

.category-count {
  font-weight: bold;
  color: #333;
}

.uncategorized {
  margin-top: 15rpx;
  font-size: 26rpx;
  color: #ff9500;
  text-align: center;
}

.failed-tip {
  text-align: center;
  font-size: 28rpx;
  color: #fa5151;
  margin: 20rpx 0;
}

.view-failed-btn {
  margin-top: 20rpx;
  height: 80rpx;
  background-color: #fa5151;
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
}

.confirm-btn {
  margin-top: 20rpx;
  height: 80rpx;
  background-color: #000;
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
}

/* 适配小屏幕 */
@media screen and (max-height: 700px) {
  .upload-result-card {
    max-height: 70vh;
  }

  .category-list {
    max-height: 200rpx;
  }
}
