/**
 * 画布管理模块
 * 负责画布项目操作、绘制和布局管理
 */

/**
 * 创建新的画布项
 * @param {Object} clothing - 衣物数据
 * @param {number} nextId - 下一个ID值
 * @param {number} zIndex - 层级
 * @param {number} canvasWidth - 画布宽度
 * @param {number} canvasHeight - 画布高度
 * @returns {Promise<Object>} 新的画布项
 */
function createCanvasItem(clothing, nextId, zIndex, canvasWidth, canvasHeight) {
  return new Promise((resolve, reject) => {
    if (!clothing) {
      console.error('缺少衣物数据，无法创建画布项');
      reject(new Error('缺少衣物数据'));
      return;
    }

    // 获取有效的图片URL，按优先级尝试不同字段
    let imageUrl = null;
    let isProcessedImage = false;

    // 按优先级尝试不同的图片URL字段
    // 1. 如果有抠图标记，优先使用抠图后的图片URL（tempImageUrl或processedImageUrl）
    if (clothing.isProcessed) {
      if (clothing.tempImageUrl && (clothing.tempImageUrl.startsWith('http') || clothing.tempImageUrl.startsWith('https') || clothing.tempImageUrl.startsWith('wxfile://'))) {
        imageUrl = clothing.tempImageUrl;
        isProcessedImage = true;
        console.log('使用抠图后的临时URL');
      } else if (clothing.processedImageUrl && (clothing.processedImageUrl.startsWith('http') || clothing.processedImageUrl.startsWith('https') || clothing.processedImageUrl.startsWith('wxfile://'))) {
        imageUrl = clothing.processedImageUrl;
        isProcessedImage = true;
        console.log('使用抠图后的处理图片URL');
      }
    }

    // 2. 如果没有抠图标记或找不到抠图图片，尝试其他图片URL
    if (!imageUrl) {
      if (clothing.tempImageUrl && (clothing.tempImageUrl.startsWith('http') || clothing.tempImageUrl.startsWith('https') || clothing.tempImageUrl.startsWith('wxfile://'))) {
        imageUrl = clothing.tempImageUrl;
        console.log('使用最新刷新的临时URL');
      } else if (clothing.processedImageUrl && (clothing.processedImageUrl.startsWith('http') || clothing.processedImageUrl.startsWith('https') || clothing.processedImageUrl.startsWith('wxfile://'))) {
        imageUrl = clothing.processedImageUrl;
        isProcessedImage = true;
        console.log('使用处理后的图片URL');
      } else if (clothing.originalImageUrl && (clothing.originalImageUrl.startsWith('http') || clothing.originalImageUrl.startsWith('https') || clothing.originalImageUrl.startsWith('wxfile://'))) {
        imageUrl = clothing.originalImageUrl;
        console.log('使用原始图片URL');
      } else if (clothing.imageUrl && (clothing.imageUrl.startsWith('http') || clothing.imageUrl.startsWith('https') || clothing.imageUrl.startsWith('wxfile://'))) {
        imageUrl = clothing.imageUrl;
        console.log('使用原始图片URL');
      } else if (clothing.originalData && clothing.originalData.imageUrl &&
                (clothing.originalData.imageUrl.startsWith('http') || clothing.originalData.imageUrl.startsWith('https') || clothing.originalData.imageUrl.startsWith('wxfile://'))) {
        imageUrl = clothing.originalData.imageUrl;
        console.log('使用原始数据中的图片URL');
      }
    }

    if (!imageUrl) {
      console.error('衣物缺少有效的图片URL:', clothing);
      reject(new Error('衣物缺少有效的图片URL'));
      return;
    }

    console.log('创建画布项，获取图片信息:', imageUrl);

    // 获取图片实际尺寸以保持原始比例
    wx.getImageInfo({
      src: imageUrl,
      success: function(imgInfo) {
        console.log('获取到图片信息:', imgInfo.width, 'x', imgInfo.height);

        // 计算图片宽高比
        const imageRatio = imgInfo.width / imgInfo.height;

        // 设置初始尺寸，保持宽高比，适合画布
        let initialWidth, initialHeight;

        // 标准尺寸
        const standardSize = 180;

        // 根据图片方向确定尺寸
        if (imageRatio >= 1) {  // 宽图或方形图
          initialWidth = standardSize;
          initialHeight = standardSize / imageRatio;
        } else {  // 高图
          initialHeight = standardSize;
          initialWidth = standardSize * imageRatio;
        }

        // 确保尺寸在合理范围内
        initialWidth = Math.min(initialWidth, canvasWidth * 0.7);
        initialHeight = Math.min(initialHeight, canvasHeight * 0.7);

        // 计算初始位置 - 根据画布大小居中放置
        const initialX = (canvasWidth - initialWidth) / 2;
        const initialY = (canvasHeight - initialHeight) / 2;

        console.log('画布项初始尺寸:', initialWidth, 'x', initialHeight, '位置:', initialX, ',', initialY);

        // 返回标准化的画布项
        const canvasItem = {
          id: nextId,
          clothingId: clothing._id,
          imageUrl: imageUrl,
          x: initialX,
          y: initialY,
          width: initialWidth,
          height: initialHeight,
          naturalWidth: imgInfo.width,    // 保存原始宽度
          naturalHeight: imgInfo.height,  // 保存原始高度
          zIndex: zIndex,
          layer: zIndex,
          active: true,
          rotation: 0,
          isProcessed: isProcessedImage, // 添加抠图标记
          aspectRatio: imageRatio,       // 保存宽高比
          // 保存原始衣物信息，以便后续使用
          originalClothing: {
            id: clothing._id,
            name: clothing.name || '',
            category: clothing.category || '',
            type: clothing.type || ''
          }
        };

        resolve(canvasItem);
      },
      fail: function(error) {
        console.error('获取图片信息失败:', error);

        // 使用默认尺寸
        const initialWidth = 150;
        const initialHeight = 150;
        const initialX = (canvasWidth - initialWidth) / 2;
        const initialY = (canvasHeight - initialHeight) / 2;

        // 创建一个默认的画布项
        const canvasItem = {
          id: nextId,
          clothingId: clothing._id,
          imageUrl: imageUrl,
          x: initialX,
          y: initialY,
          width: initialWidth,
          height: initialHeight,
          zIndex: zIndex,
          layer: zIndex,
          active: true,
          rotation: 0,
          isProcessed: isProcessedImage,
          aspectRatio: 1,  // 默认正方形
          // 保存原始衣物信息，以便后续使用
          originalClothing: {
            id: clothing._id,
            name: clothing.name || '',
            category: clothing.category || '',
            type: clothing.type || ''
          }
        };

        resolve(canvasItem);
      }
    });
  });
}

/**
 * 更新画布项的激活状态
 * @param {Array} canvasItems - 画布项数组
 * @param {number} activeId - 活跃项ID
 * @returns {Array} 更新后的画布项数组
 */
function updateItemsActiveState(canvasItems, activeId) {
  return canvasItems.map(item => ({
    ...item,
    active: item.id === activeId
  }));
}

/**
 * 计算新位置，确保不超出画布边界
 * @param {number} startX - 起始X坐标
 * @param {number} startY - 起始Y坐标
 * @param {number} moveX - X方向移动距离
 * @param {number} moveY - Y方向移动距离
 * @param {number} width - 项目宽度
 * @param {number} height - 项目高度
 * @param {number} canvasWidth - 画布宽度
 * @param {number} canvasHeight - 画布高度
 * @returns {Object} 新位置 {x, y}
 */
function calculateNewPosition(startX, startY, moveX, moveY, width, height, canvasWidth, canvasHeight) {
  // 计算新位置
  let newX = startX + moveX;
  let newY = startY + moveY;

  // 限制在画布内
  newX = Math.max(0, Math.min(newX, canvasWidth - width));
  newY = Math.max(0, Math.min(newY, canvasHeight - height));

  return { x: newX, y: newY };
}

/**
 * 计算调整大小后的尺寸
 * @param {number} currentWidth - 当前宽度
 * @param {number} currentHeight - 当前高度
 * @param {string} direction - 调整方向 "increase" 或 "decrease"
 * @param {number} canvasWidth - 画布宽度
 * @param {number} canvasHeight - 画布高度
 * @returns {Object} 新尺寸 {width, height}
 */
function calculateNewSize(currentWidth, currentHeight, direction, canvasWidth, canvasHeight) {
  // 确保输入值有效
  currentWidth = Number(currentWidth) || 150;
  currentHeight = Number(currentHeight) || 150;

  // 保持宽高比
  const aspectRatio = currentWidth / currentHeight;

  // 调整比例
  const scaleFactorIncrease = 1.2;  // 放大20%
  const scaleFactorDecrease = 0.8;  // 缩小20%

  let newWidth = currentWidth;
  let newHeight = currentHeight;

  // 根据方向调整大小
  if (direction === 'increase') {
    newWidth = currentWidth * scaleFactorIncrease;
    newHeight = newHeight * scaleFactorIncrease;
  } else if (direction === 'decrease') {
    newWidth = currentWidth * scaleFactorDecrease;
    newHeight = currentHeight * scaleFactorDecrease;
  }

  // 限制最小尺寸
  const minSize = 60;
  if (newWidth < minSize) {
    newWidth = minSize;
    newHeight = minSize / aspectRatio;
  }
  if (newHeight < minSize) {
    newHeight = minSize;
    newWidth = minSize * aspectRatio;
  }

  // 限制最大尺寸
  const maxWidth = canvasWidth * 0.9;
  const maxHeight = canvasHeight * 0.9;
  if (newWidth > maxWidth) {
    newWidth = maxWidth;
    newHeight = maxWidth / aspectRatio;
  }
  if (newHeight > maxHeight) {
    newHeight = maxHeight;
    newWidth = maxHeight * aspectRatio;
  }

  console.log(`调整大小: ${Math.round(currentWidth)}x${Math.round(currentHeight)} -> ${Math.round(newWidth)}x${Math.round(newHeight)}`);

  return {
    width: Math.round(newWidth),
    height: Math.round(newHeight)
  };
}

/**
 * 计算新的旋转角度
 * @param {number} currentRotation - 当前旋转角度
 * @param {string} direction - 旋转方向 "clockwise" 或 "counterclockwise"
 * @returns {number} 新的旋转角度
 */
function calculateNewRotation(currentRotation, direction) {
  // 确保当前旋转角度是有效数字
  let newRotation = Number(currentRotation) || 0;

  // 每次旋转的角度
  const rotationStep = 15;

  // 根据方向旋转
  if (direction === 'clockwise') {
    newRotation += rotationStep;
  } else if (direction === 'counterclockwise') {
    newRotation -= rotationStep;
  }

  // 限制旋转范围在0-360度
  newRotation = ((newRotation % 360) + 360) % 360;

  console.log(`旋转: ${currentRotation} -> ${newRotation} 度`);

  return newRotation;
}

/**
 * 绘制画布内容
 * @param {string} canvasId - 画布ID
 * @param {Array} canvasItems - 画布项数组
 * @param {number} canvasWidth - 画布宽度
 * @param {number} canvasHeight - 画布高度
 * @returns {Promise<void>} 完成绘制的Promise
 */
function drawCanvas(canvasId, canvasItems, canvasWidth, canvasHeight) {
  return new Promise((resolve, reject) => {
    try {
      const ctx = wx.createCanvasContext(canvasId);

      // 绘制白色背景
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      // 按z-index排序画布项
      const sortedItems = [...canvasItems].sort((a, b) => a.zIndex - b.zIndex);

      // 创建一个新数组来存储调整后的画布项
      const adjustedItems = [];

      // 绘制每个项目的计数
      let drawCount = 0;
      const totalItems = sortedItems.length;

      // 如果没有项目，直接显示一个白色背景
      if (totalItems === 0) {
        ctx.draw(true, () => {
          console.log('画布已绘制完成 - 空白画布');
          resolve();
        });
        return;
      }

      console.log(`开始绘制画布，共 ${totalItems} 个项目`);

      // 绘制完成回调
      const onDrawComplete = () => {
        drawCount++;
        console.log(`项目 ${drawCount}/${totalItems} 已完成绘制`);

        if (drawCount >= totalItems) {
          // 所有项目都已绘制完成

          // 更新原始canvasItems数组中的项目
          if (adjustedItems.length > 0) {
            console.log(`需要调整 ${adjustedItems.length} 个项目的尺寸`);

            // 更新原始数组中的项目
            adjustedItems.forEach(({index, item}) => {
              canvasItems[index] = item;
            });

            console.log('已更新画布项目尺寸以保持宽高比');
          }

          ctx.draw(true, () => {
            console.log('全部画布项目已完成绘制');
            resolve();
          });
        }
      };

      // 绘制每个项目
      sortedItems.forEach((item, index) => {
        // 加载图片
        wx.getImageInfo({
          src: item.imageUrl,
          success: imgInfo => {
            console.log(`加载图片成功：${index+1}/${totalItems} - 尺寸: ${imgInfo.width}x${imgInfo.height}`);

            // 保存当前状态
            ctx.save();

            // 移动到项目中心
            const centerX = item.x + item.width / 2;
            const centerY = item.y + item.height / 2;
            ctx.translate(centerX, centerY);

            // 旋转
            if (item.rotation) {
              ctx.rotate(item.rotation * Math.PI / 180);
            }

            // 获取图片的原始宽高比 - 优先使用item中存储的，否则使用图片信息
            const aspectRatio = item.aspectRatio ||
                               (item.naturalWidth && item.naturalHeight ? item.naturalWidth / item.naturalHeight : null) ||
                               imgInfo.width / imgInfo.height;

            // 如果画布项的宽高比与图片原始宽高比不匹配，调整绘制尺寸保持比例
            let drawWidth = item.width;
            let drawHeight = item.height;

            // 检查当前宽高与原始宽高比的差距
            const currentRatio = item.width / item.height;
            const ratioDifference = Math.abs(currentRatio - aspectRatio);
            let needsAdjustment = false;

            // 如果比例差异超过一定阈值（0.05或5%），调整绘制尺寸
            if (ratioDifference > 0.05) {
              console.log(`项目 ${index+1} 宽高比不匹配，进行调整 - 当前: ${currentRatio.toFixed(2)}, 原始: ${aspectRatio.toFixed(2)}`);

              // 以宽度为基准，调整高度
              drawHeight = drawWidth / aspectRatio;
              needsAdjustment = true;
            }

            // 绘制图像时保持原始比例
            ctx.drawImage(imgInfo.path, -drawWidth / 2, -drawHeight / 2, drawWidth, drawHeight);

            // 恢复状态
            ctx.restore();

            // 将调整后的项目添加到新数组
            if (needsAdjustment) {
              // 创建一个新对象，保持中心点不变
              const adjustedItem = {...item};
              adjustedItem.height = drawHeight;
              // 重新计算y坐标以保持中心点不变
              adjustedItem.y = centerY - drawHeight / 2;
              // 保存原始宽高比
              adjustedItem.aspectRatio = aspectRatio;

              adjustedItems.push({
                index: sortedItems.indexOf(item),
                item: adjustedItem
              });

              console.log(`项目 ${item.id} 尺寸已调整: ${item.width}x${item.height} -> ${drawWidth}x${drawHeight}`);
            }

            // 调用绘制完成回调
            onDrawComplete();
          },
          fail: (error) => {
            console.error(`图片加载失败：${index+1}/${totalItems}`, error);

            // 图片加载失败时使用占位符
            ctx.save();
            ctx.translate(item.x + item.width / 2, item.y + item.height / 2);

            // 旋转
            if (item.rotation) {
              ctx.rotate(item.rotation * Math.PI / 180);
            }

            // 绘制占位符
            ctx.fillStyle = '#F0F0F0';
            ctx.fillRect(-item.width / 2, -item.height / 2, item.width, item.height);
            ctx.fillStyle = '#CCCCCC';
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('图片加载失败', 0, 0);

            ctx.restore();

            // 调用绘制完成回调
            onDrawComplete();
          }
        });
      });
    } catch (error) {
      console.error('画布绘制过程发生错误:', error);
      reject(error);
    }
  });
}

/**
 * 计算新的图层值
 * @param {number} currentLayer - 当前图层值
 * @param {string} direction - 调整方向 "up" 或 "down"
 * @returns {number} 新的图层值
 */
function calculateNewLayer(currentLayer, direction) {
  // 确保当前图层值是有效数字
  let newLayer = Number(currentLayer) || 0;

  // 图层调整步长
  const layerStep = 1;

  // 根据方向调整图层
  if (direction === 'up') {
    newLayer += layerStep;
  } else if (direction === 'down') {
    newLayer -= layerStep;
  }

  // 确保图层值不小于0
  newLayer = Math.max(0, newLayer);

  console.log(`调整图层: ${currentLayer} -> ${newLayer}`);

  return newLayer;
}

module.exports = {
  createCanvasItem,
  updateItemsActiveState,
  calculateNewPosition,
  calculateNewSize,
  calculateNewRotation,
  drawCanvas,
  calculateNewLayer
};
