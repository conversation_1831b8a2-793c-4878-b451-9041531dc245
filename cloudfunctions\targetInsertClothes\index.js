const cloud = require('wx-server-sdk');
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 示例数据
const sampleData = {
  category: "鞋子",
  name: "跑丢的猫咪",
  flag: "clothespulsfive",
  color: "棕色",
  imageFileID: "cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/微信图片_20250319075206.jpg",
  processedImageFileID: "cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/微信图片_20250319075206.jpg"
};

// 随机选择分类
const categories = ["鞋子", "上衣", "裙子", "外套", "配饰"];

exports.main = async (event, context) => {
  const { openid } = event;
  
  if (!openid) {
    return {
      success: false,
      message: '请提供用户OpenID'
    };
  }
  
  try {
    // 验证用户是否存在
    const userResult = await db.collection('users')
      .where({
        _openid: openid
      })
      .get();
    
    if (!userResult.data || userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    // 随机选择分类
    const randomCategory = categories[Math.floor(Math.random() * categories.length)];
    
    // 创建新数据
    const newData = {
      ...sampleData,
      _openid: openid,
      category: randomCategory
    };
    
    // 插入数据
    await db.collection('clothes').add({
      data: newData
    });
    
    return {
      success: true,
      message: '数据插入成功'
    };
    
  } catch (err) {
    console.error('指定插入数据失败:', err);
    return {
      success: false,
      message: err.message || '指定插入数据失败'
    };
  }
}; 