// 兑换码云函数
// 功能：验证兑换码并为用户提供相应的奖励（增加衣物容量或搭配数量）
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 验证兑换码是否有效
 * @param {string} code - 用户输入的兑换码
 * @returns {Promise<Object>} - 兑换码信息
 */
async function validateCode(code) {
  try {
    // 查询兑换码是否存在
    const codeResult = await db.collection('redemptionCodes')
      .where({
        code: code,
        isActive: true,
        expirationDate: _.gt(new Date()) // 确保兑换码未过期
      })
      .get()

    if (codeResult.data.length === 0) {
      return {
        isValid: false,
        message: '无效的兑换码或已过期'
      }
    }

    return {
      isValid: true,
      codeInfo: codeResult.data[0]
    }
  } catch (err) {
    console.error('验证兑换码失败:', err)
    throw new Error('验证兑换码时发生错误')
  }
}

/**
 * 检查用户是否已经使用过该兑换码
 * @param {string} openid - 用户openid
 * @param {string} codeId - 兑换码ID
 * @returns {Promise<boolean>} - 是否已使用
 */
async function checkIfCodeUsed(openid, codeId) {
  try {
    const result = await db.collection('redemptionHistory')
      .where({
        userId: openid,
        codeId: codeId
      })
      .count()

    return result.total > 0
  } catch (err) {
    console.error('检查兑换历史失败:', err)
    throw new Error('检查兑换历史时发生错误')
  }
}

/**
 * 应用兑换码奖励
 * @param {string} openid - 用户openid
 * @param {Object} codeInfo - 兑换码信息
 * @returns {Promise<Object>} - 应用奖励的结果
 */
async function applyReward(openid, codeInfo) {
  try {
    const userResult = await db.collection('users')
      .where({ _openid: openid })
      .get()

    if (userResult.data.length === 0) {
      throw new Error('用户不存在')
    }

    const userData = userResult.data[0]
    const updateData = {}
    if (userData.clothesLimit === undefined) {
      userData.clothesLimit = 30
    }
    if (userData.outfitsLimit === undefined) {
      userData.outfitsLimit = 10
    }

    // 根据兑换码类型应用不同的奖励
    if (codeInfo.rewardType === 'clothes') {
      // 增加衣物容量
      updateData.clothesLimit = userData.clothesLimit + codeInfo.rewardAmount
    } else if (codeInfo.rewardType === 'outfits') {
      // 增加搭配容量
      updateData.outfitsLimit = userData.outfitsLimit + codeInfo.rewardAmount
    } else if (codeInfo.rewardType === 'both') {
      // 同时增加衣物和搭配容量
      updateData.clothesLimit = userData.clothesLimit + codeInfo.clothesRewardAmount
      updateData.outfitsLimit = userData.outfitsLimit + codeInfo.outfitsRewardAmount
    }

    // 更新用户数据
    await db.collection('users')
      .doc(userData._id)
      .update({
        data: updateData
      })

    // 记录兑换历史
    await db.collection('redemptionHistory').add({
      data: {
        userId: openid,
        codeId: codeInfo._id,
        codeName: codeInfo.name || '兑换码',
        codeValue: codeInfo.code,
        rewardType: codeInfo.rewardType,
        rewardAmount: codeInfo.rewardAmount,
        clothesRewardAmount: codeInfo.clothesRewardAmount,
        outfitsRewardAmount: codeInfo.outfitsRewardAmount,
        redeemedAt: new Date()
      }
    })

    // 如果是一次性兑换码，标记为已使用
    if (codeInfo.isOneTime) {
      await db.collection('redemptionCodes')
        .doc(codeInfo._id)
        .update({
          data: {
            isActive: false,
            usedAt: new Date()
          }
        })
    }

    return {
      success: true,
      rewardType: codeInfo.rewardType,
      rewardAmount: codeInfo.rewardAmount,
      clothesRewardAmount: codeInfo.clothesRewardAmount,
      outfitsRewardAmount: codeInfo.outfitsRewardAmount,
      userData: {
        ...userData,
        ...updateData
      }
    }
  } catch (err) {
    console.error('应用奖励失败:', err)
    throw new Error('应用奖励时发生错误')
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    const { code } = event

    // 验证兑换码
    const validationResult = await validateCode(code)
    if (!validationResult.isValid) {
      return {
        success: false,
        message: validationResult.message
      }
    }

    const codeInfo = validationResult.codeInfo

    // 检查是否已使用过该兑换码（对于非一次性兑换码，允许所有用户使用；对于一次性兑换码，需检查该用户是否使用过）
    if (!codeInfo.isOneTime) {
      const isUsed = await checkIfCodeUsed(openid, codeInfo._id)
      if (isUsed) {
        return {
          success: false,
          message: '您已经使用过该兑换码'
        }
      }
    }

    // 应用奖励
    const rewardResult = await applyReward(openid, codeInfo)
    
    return {
      success: true,
      message: '兑换成功！',
      ...rewardResult
    }
  } catch (error) {
    console.error('兑换码处理失败:', error)
    return {
      success: false,
      message: error.message || '兑换失败，请稍后再试'
    }
  }
} 