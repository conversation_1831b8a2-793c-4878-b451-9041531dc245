<!--components/themeCompletionCard/themeCompletionCard.wxml-->
<view class="theme-completion-modal {{visible ? 'visible' : 'hidden'}}" animation="{{animationData}}" catchtap="onMaskTap">
  <view class="theme-completion-container" animation="{{contentAnimationData}}" catchtap="onContentTap" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.95)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.97)' : 'white')}}; {{themeStyle === 'blackWhite' ? 'border: 2px solid ' + blackWhiteColors.black + ';' : ''}}">
    
    <!-- 五彩纸屑动画 -->
    <view class="confetti-container {{confettiAnimation ? 'animate' : ''}}">
      <view class="confetti confetti-1"></view>
      <view class="confetti confetti-2"></view>
      <view class="confetti confetti-3"></view>
      <view class="confetti confetti-4"></view>
      <view class="confetti confetti-5"></view>
      <view class="confetti confetti-6"></view>
      <view class="confetti confetti-7"></view>
      <view class="confetti confetti-8"></view>
      <view class="confetti confetti-9"></view>
      <view class="confetti confetti-10"></view>
    </view>
    
    <!-- 标题 -->
    <view class="theme-completion-header" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}};">
      <view class="theme-completion-title">
        <text class="theme-completion-icon">🎉</text>
        <text>恭喜完成所有任务！</text>
      </view>
      <view class="theme-completion-subtitle">您已成功收集了所有卡片</view>
    </view>
    
    <!-- 卡片展示区域 -->
    <view class="theme-completion-cards">
      <block wx:for="{{taskImages}}" wx:key="index">
        <view class="theme-completion-card-item">
          <image class="theme-completion-card-image" src="{{item}}" mode="aspectFill"></image>
          <view class="theme-completion-card-check">✓</view>
        </view>
      </block>
    </view>
    
    <!-- 奖励信息 -->
    <view class="theme-completion-reward" style="color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}};">
      <view class="theme-completion-reward-icon">🎁</view>
      <view class="theme-completion-reward-text">
        <text>获得了一个月的VIP会员奖励！</text>
        <text class="theme-completion-reward-days">会员有效期增加30天</text>
      </view>
    </view>
    
    <!-- 按钮区域 -->
    <view class="theme-completion-buttons">
      <view class="theme-completion-button theme-completion-button-secondary" bindtap="onCloseButtonTap" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; border-color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}};">
        我知道了
      </view>
      <view class="theme-completion-button theme-completion-button-primary" bindtap="onViewBenefitsTap" style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkMedium : blackWhiteColors.black)}}; color: white;">
        查看会员权益
      </view>
    </view>
    
    <!-- 提示信息 -->
    <view class="theme-completion-hint" style="color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.gray)}};">
      您可以继续收集新的卡片获取更多奖励
    </view>
  </view>
</view>
