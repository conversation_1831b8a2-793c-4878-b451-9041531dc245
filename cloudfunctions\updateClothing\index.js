// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 检查必要参数
  if (!event.clothingId) {
    return {
      success: false,
      message: '缺少衣物ID'
    }
  }
  
  try {
    // 构建更新数据对象
    const updateData = {}
    
    // 只更新提供的字段
    if (event.name !== undefined) updateData.name = event.name
    if (event.category !== undefined) updateData.category = event.category
    if (event.type !== undefined) updateData.type = event.type
    if (event.type_detail !== undefined) updateData.type_detail = event.type_detail
    if (event.color !== undefined) updateData.color = event.color
    if (event.style !== undefined) updateData.style = event.style
    if (event.warmthLevel !== undefined) updateData.warmthLevel = event.warmthLevel
    if (event.scenes !== undefined) updateData.scenes = event.scenes
    if (event.price !== undefined) updateData.price = event.price
    if (event.wantToDiscard !== undefined) updateData.wantToDiscard = event.wantToDiscard
    
    // 添加新字段的支持
    if (event.purchaseDate !== undefined) updateData.purchaseDate = event.purchaseDate
    if (event.lastWornDate !== undefined) updateData.lastWornDate = event.lastWornDate
    if (event.wornCount !== undefined) updateData.wornCount = event.wornCount
    if (event.season !== undefined) updateData.season = event.season
    if (event.hiden !== undefined) updateData.hiden = event.hiden
    if (event.brand !== undefined) updateData.brand = event.brand
    if (event.material !== undefined) updateData.material = event.material
    if (event.size !== undefined) updateData.size = event.size
    if (event.style !== undefined) updateData.style = event.style
    if (event.style_detail !== undefined) updateData.style_detail = event.style_detail
    if (event.storageLocation !== undefined) updateData.storageLocation = event.storageLocation
    if (event.purchaseChannel !== undefined) updateData.purchaseChannel = event.purchaseChannel
    if (event.remark !== undefined) updateData.remark = event.remark
    if (event.wardrobeId !== undefined) updateData.wardrobeId = event.wardrobeId
    if (event.todos !== undefined) updateData.todos = event.todos
    
    // 添加更新时间
    updateData.updateTime = db.serverDate()
    
    // 执行更新操作
    const result = await db.collection('clothes').doc(event.clothingId).update({
      data: updateData
    })
    
    return {
      success: true,
      updated: result.stats.updated,
      message: '更新成功'
    }
  } catch (error) {
    console.error('更新衣物失败:', error)
    return {
      success: false,
      message: '更新失败: ' + error.message
    }
  }
} 