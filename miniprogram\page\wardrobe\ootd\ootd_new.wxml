<view class="container {{themeStyle === 'autumn' ? 'theme-autumn' : 'theme-pinkBlue'}}">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner" style="border-color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}}; border-top-color: transparent;"></view>
    <view class="loading-text" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view class="main-content" wx:if="{{!isLoading}}">
    <!-- 顶部区域 -->
    <view class="top-section">
      <view class="title-container">
        <view class="section-title" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">今日穿搭</view>
        <view class="section-subtitle" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">选择您今天的穿搭</view>
      </view>
      <view class="date-display" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}}; border: 1rpx solid {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
        {{currentDate}}
      </view>
    </view>

    <!-- 穿搭类型选择区域 -->
    <view class="outfit-type-selector">
      <view class="selector-title" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">选择穿搭类型</view>
      <scroll-view scroll-x="true" class="type-scroll-view">
        <block wx:for="{{Object.keys(categoryNames)}}" wx:key="*this">
          <view
            class="type-item {{selectedType === item ? 'selected' : ''}}"
            bindtap="switchType"
            data-type="{{item}}"
            style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}}; background-color: {{selectedType === item ? (themeStyle === 'autumn' ? colors.toastedCaramel : pinkBlueColors.pinkMedium) : 'transparent'}};"
          >
            {{categoryNames[item]}}
          </view>
        </block>
      </scroll-view>
    </view>

    <!-- 切换到单件衣物选择按钮 -->
    <view class="toggle-clothing-button" bindtap="toggleClothingSection" style="background-color: {{themeStyle === 'autumn' ? colors.spicedWine : pinkBlueColors.pinkDark}}; color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};">
      {{showClothingSection ? '选择穿搭' : '选择单件衣物'}}
    </view>

    <!-- 直接上传图片按钮 -->
    <view class="upload-image-button" bindtap="chooseImage" style="background-color: {{themeStyle === 'autumn' ? colors.oliveHarvest : pinkBlueColors.blueDark}}; color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};">
      上传图片作为穿搭
    </view>

    <!-- 穿搭列表区域 -->
    <view class="outfits-display" wx:if="{{!showClothingSection}}">
      <!-- 如果已有今日穿搭，显示一个提示 -->
      <view wx:if="{{hasTodayOutfit && todayOutfit}}" class="today-outfit-tip" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
        <text wx:if="{{todayOutfit.isDirectUpload}}">您今天已选择上传图片作为穿搭</text>
        <text wx:elif="{{todayOutfit.isMultipleClothings}}">您今天已选择多件衣物组合作为穿搭</text>
        <text wx:elif="{{todayOutfit.isSingleClothing}}">您今天已选择单件衣物作为穿搭</text>
        <text wx:else>您今天已选择穿搭: {{todayOutfit.outfitName || '今日穿搭'}}</text>
      </view>

      <block wx:if="{{filteredOutfits.length > 0}}">
        <swiper class="outfits-swiper" circular="true" previous-margin="40rpx" next-margin="40rpx">
          <block wx:for="{{filteredOutfits}}" wx:key="_id">
            <swiper-item>
              <view
                class="outfit-card {{(todayOutfit && (item._id || item.id) === todayOutfit.outfitId) ? 'today-selected' : ''}} {{(highlightedOutfitId && (item._id || item.id) === highlightedOutfitId) ? 'highlighted' : ''}}"
                bindtap="selectOutfit"
                data-id="{{item._id || item.id}}"
                style="background-color: {{themeStyle === 'autumn' ? colors.toastedCaramel : pinkBlueColors.pinkMedium}};"
              >
                <!-- 今日穿搭标记 -->
                <view
                  wx:if="{{todayOutfit && (item._id || item.id) === todayOutfit.outfitId}}"
                  class="today-outfit-badge"
                  style="background-color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};"
                >
                  今日穿搭
                </view>

                <!-- 搭配预览图 -->
                <view class="outfit-preview">
                  <image
                    src="{{item.previewImage || '/image/outfit-icon.png'}}"
                    mode="aspectFill"
                    class="preview-image"
                    binderror="handleImageError"
                    data-index="{{index}}"
                    data-type="preview"
                    lazy-load="true"
                    show-menu-by-longpress="true"
                  ></image>
                </view>

                <!-- 搭配信息 -->
                <view class="outfit-info">
                  <view class="outfit-name" style="color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};">{{item.name || '未命名搭配'}}</view>

                  <!-- 衣物列表 -->
                  <view class="outfit-items" wx:if="{{item.items && item.items.length > 0}}">
                    <scroll-view scroll-x="true" class="items-scroll-view">
                      <block wx:for="{{item.items}}" wx:for-item="clothingItem" wx:for-index="itemIndex" wx:key="id">
                        <view class="item-thumbnail">
                          <image
                            src="{{clothingItem.imageUrl || '/image/short-dress.png'}}"
                            mode="aspectFit"
                            class="item-image"
                            binderror="handleImageError"
                            data-item-index="{{itemIndex}}"
                            data-outfit-index="{{index}}"
                            data-type="item"
                            lazy-load="true"
                          ></image>
                        </view>
                      </block>
                    </scroll-view>
                  </view>
                </view>
              </view>
            </swiper-item>
          </block>
        </swiper>
      </block>

      <!-- 空状态展示 -->
      <view class="empty-state" wx:if="{{filteredOutfits.length === 0}}">
        <view class="empty-icon">👕</view>
        <view class="empty-text" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
          暂无{{categoryNames[selectedType] || ''}}穿搭
        </view>
        <view class="create-button" bindtap="goToCreateOutfit" style="background-color: {{themeStyle === 'autumn' ? colors.spicedWine : pinkBlueColors.pinkDark}}; color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};">
          创建新穿搭
        </view>
      </view>
    </view>

    <!-- 单件衣物选择区域 -->
    <view class="clothing-section" wx:if="{{showClothingSection}}">
      <!-- 如果已有今日穿搭，显示一个提示 -->
      <view wx:if="{{hasTodayOutfit && todayOutfit}}" class="today-outfit-tip" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
        <text wx:if="{{todayOutfit.isDirectUpload}}">您今天已选择上传图片作为穿搭</text>
        <text wx:elif="{{todayOutfit.isMultipleClothings}}">您今天已选择多件衣物组合作为穿搭</text>
        <text wx:elif="{{todayOutfit.isSingleClothing}}">您今天已选择单件衣物作为穿搭</text>
        <text wx:else>您今天已选择穿搭: {{todayOutfit.outfitName || '今日穿搭'}}</text>
      </view>

      <!-- 衣物分类选择 -->
      <view class="clothing-category-selector">
        <view class="selector-title" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">选择衣物分类</view>
        <scroll-view scroll-x="true" class="category-scroll-view">
          <view
            class="category-item {{selectedClothingCategory === '全部' ? 'selected' : ''}}"
            bindtap="switchClothingCategory"
            data-category="全部"
            style="background-color: {{selectedClothingCategory === '全部' ? (themeStyle === 'autumn' ? colors.toastedCaramel : pinkBlueColors.pinkMedium) : 'transparent'}}; color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
            全部
          </view>
          <block wx:for="{{clothingCategories}}" wx:key="*this">
            <view
              class="category-item {{selectedClothingCategory === item ? 'selected' : ''}}"
              bindtap="switchClothingCategory"
              data-category="{{item}}"
              style="background-color: {{selectedClothingCategory === item ? (themeStyle === 'autumn' ? colors.toastedCaramel : pinkBlueColors.pinkMedium) : 'transparent'}}; color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
              {{item}}
            </view>
          </block>
        </scroll-view>
      </view>

      <!-- 衣物列表 -->
      <view class="clothing-grid">
        <block wx:if="{{filteredClothes.length > 0}}">
          <view class="clothing-list">
            <block wx:for="{{filteredClothes}}" wx:key="_id">
              <view
                class="clothing-card"
                bindtap="selectClothing"
                data-index="{{index}}"
                style="background-color: {{themeStyle === 'autumn' ? colors.toastedCaramel : pinkBlueColors.pinkMedium}};">
                <!-- 衣物图片 -->
                <view class="clothing-image-container">
                  <image
                    src="{{item.imageUrl || '/image/short-dress.png'}}"
                    mode="aspectFit"
                    class="clothing-image"
                    lazy-load="true"
                    show-menu-by-longpress="true">
                  </image>
                </view>
                <!-- 衣物名称 -->
                <view class="clothing-name" style="color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};">
                  {{item.name || '未命名衣物'}}
                </view>
                <!-- 衣物分类 -->
                <view class="clothing-category-tag" style="background-color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
                  {{item.category || '未分类'}}
                </view>
              </view>
            </block>
          </view>
        </block>

        <!-- 空状态展示 -->
        <view class="empty-state" wx:if="{{filteredClothes.length === 0}}">
          <view class="empty-icon">👚</view>
          <view class="empty-text" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
            暂无{{selectedClothingCategory === '全部' ? '' : selectedClothingCategory}}衣物
          </view>
          <view class="create-button" bindtap="goToCreateClothing" style="background-color: {{themeStyle === 'autumn' ? colors.spicedWine : pinkBlueColors.pinkDark}}; color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};">
            添加新衣物
          </view>
        </view>
      </view>
    </view>

    <!-- 选择按钮 -->

  </view>

  <!-- 确认穿搭对话框 -->
  <view class="confirm-dialog-overlay {{showConfirmDialog ? 'show' : ''}}" bindtap="hideConfirmDialog">
    <view class="confirm-dialog" catchtap="preventBubble">
      <view class="confirm-dialog-header">
        <view class="confirm-dialog-title" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">确认穿搭选择</view>
      </view>
      <view class="confirm-dialog-content">
        <view class="outfit-preview-small">
          <image
            src="{{selectedOutfit.previewImage || '/image/outfit-icon.png'}}"
            mode="aspectFit"
            class="preview-image"
          ></image>
        </view>
        <view class="confirm-outfit-name" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
          {{selectedOutfit.name || '未命名搭配'}}
        </view>
        <view class="confirm-dialog-message">
          是否确认选择该套搭配作为{{isToday ? '今日' : '所选日期的'}}穿搭？
        </view>
      </view>
      <view class="confirm-dialog-buttons">
        <view class="confirm-dialog-button cancel" bindtap="hideConfirmDialog">取消</view>
        <view class="confirm-dialog-button confirm" bindtap="confirmTodayOutfit">确认</view>
      </view>
    </view>
  </view>

  <!-- 确认单件衣物对话框 -->
  <view class="confirm-dialog-overlay {{showClothingConfirmDialog ? 'show' : ''}}" bindtap="cancelSelectClothing">
    <view class="confirm-dialog" catchtap="preventBubble">
      <view class="confirm-dialog-header">
        <view class="confirm-dialog-title" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">确认单件衣物选择</view>
      </view>
      <view class="confirm-dialog-content">
        <view class="outfit-preview-small">
          <image
            src="{{selectedClothing.imageUrl || selectedClothing.processedImageFileID || selectedClothing.imageFileID || '/image/short-dress.png'}}"
            mode="aspectFit"
            class="preview-image"
          ></image>
        </view>
        <view class="confirm-outfit-name" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
          {{selectedClothing.name || '未命名衣物'}}
        </view>
        <view class="confirm-dialog-message">
          是否确认选择该件衣物作为{{isToday ? '今日' : '所选日期的'}}穿搭？
        </view>
      </view>
      <view class="confirm-dialog-buttons">
        <view class="confirm-dialog-button cancel" bindtap="cancelSelectClothing">取消</view>
        <view class="confirm-dialog-button confirm" bindtap="confirmSelectClothing">确认</view>
      </view>
    </view>
  </view>

  <!-- 确认上传图片对话框 -->
  <view class="confirm-dialog-overlay {{showUploadConfirmDialog ? 'show' : ''}}" bindtap="cancelUploadImage">
    <view class="confirm-dialog" catchtap="preventBubble">
      <view class="confirm-dialog-header">
        <view class="confirm-dialog-title" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">确认上传图片</view>
      </view>
      <view class="confirm-dialog-content">
        <view class="outfit-preview-small">
          <image
            src="{{uploadedImage}}"
            mode="aspectFit"
            class="preview-image"
          ></image>
        </view>
        <view class="confirm-outfit-name" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : pinkBlueColors.pinkDark}};">
          直接上传图片
        </view>
        <view class="confirm-dialog-message">
          是否确认使用该图片作为{{isToday ? '今日' : '所选日期的'}}穿搭？
        </view>
      </view>
      <view class="confirm-dialog-buttons">
        <view class="confirm-dialog-button cancel" bindtap="cancelUploadImage">取消</view>
        <view class="confirm-dialog-button confirm" bindtap="confirmUploadImage">确认</view>
      </view>
    </view>
  </view>
</view>
