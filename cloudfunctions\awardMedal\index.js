// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const userOpenId = event.userOpenId || wxContext.OPENID
  const medalId = event.medalId

  // 验证参数
  if (!medalId) {
    return {
      success: false,
      error: '缺少勋章ID'
    }
  }

  try {
    // 检查勋章是否存在
    const medalResult = await db.collection('medals')
      .doc(medalId)
      .get()

    if (!medalResult.data) {
      return {
        success: false,
        error: '勋章不存在'
      }
    }

    // 检查用户是否已获得该勋章
    const userMedalResult = await db.collection('user_medals')
      .where({
        userOpenId: userOpenId,
        medalId: medalId
      })
      .get()

    if (userMedalResult.data && userMedalResult.data.length > 0) {
      return {
        success: true,
        data: userMedalResult.data[0],
        message: '用户已获得该勋章'
      }
    }

    // 获取勋章全局编号
    const globalNumberResult = await cloud.callFunction({
      name: 'getMedalGlobalNumber',
      data: {
        medalId: medalId
      }
    })

    if (!globalNumberResult.result || !globalNumberResult.result.success) {
      console.error('获取勋章全局编号失败:', globalNumberResult)
      return {
        success: false,
        error: '获取勋章全局编号失败'
      }
    }

    const globalNumber = globalNumberResult.result.globalNumber

    // 为用户添加勋章
    const result = await db.collection('user_medals').add({
      data: {
        userOpenId: userOpenId,
        medalId: medalId,
        medalName: medalResult.data.name,
        medalDescription: medalResult.data.description,
        medalImageFileID: medalResult.data.imageFileID,
        earnedTime: db.serverDate(),
        createTime: db.serverDate(),
        globalNumber: globalNumber
      }
    })



    return {
      success: true,
      data: {
        _id: result._id,
        medalId: medalId,
        medalName: medalResult.data.name
      },
      message: '勋章授予成功'
    }
  } catch (error) {
    console.error('授予勋章失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
