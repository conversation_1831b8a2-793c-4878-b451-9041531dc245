// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    return {
      success: false,
      error: '未获取到用户身份'
    }
  }
  
  // 获取自定义类型数据
  const { categories } = event
  
  if (!categories || !Array.isArray(categories)) {
    return {
      success: false,
      error: '无效的类型数据'
    }
  }
  
  try {
    // 查询是否已存在记录
    const existingRecord = await db.collection('customOutfitCategories')
      .where({
        _openid: openid
      })
      .get()
    
    if (existingRecord.data && existingRecord.data.length > 0) {
      // 更新现有记录
      const result = await db.collection('customOutfitCategories')
        .where({
          _openid: openid
        })
        .update({
          data: {
            categories: categories,
            updateTime: db.serverDate()
          }
        })
      
      return {
        success: true,
        updated: true,
        result
      }
    } else {
      // 创建新记录
      const result = await db.collection('customOutfitCategories')
        .add({
          data: {
            _openid: openid,
            categories: categories,
            createTime: db.serverDate(),
            updateTime: db.serverDate()
          }
        })
      
      return {
        success: true,
        created: true,
        result
      }
    }
  } catch (error) {
    console.error('保存自定义穿搭类型失败:', error)
    return {
      success: false,
      error: error.message || '保存失败'
    }
  }
}
