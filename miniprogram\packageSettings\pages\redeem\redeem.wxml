<view class="container">
  <!-- 头部信息 -->
  <view class="header">
    <view class="title">兑换码</view>
    <view class="subtitle">输入兑换码获取容量或会员权益</view>
  </view>

  <!-- 切换标签 -->
  <view class="tab-container">
    <view class="tab {{activeTab === 'capacity' ? 'active' : ''}}" bindtap="switchTab" data-tab="capacity">容量兑换</view>
    <view class="tab {{activeTab === 'membership' ? 'active' : ''}}" bindtap="switchTab" data-tab="membership">会员兑换</view>
  </view>

  <!-- 容量兑换区域 -->
  <view class="tab-content" wx:if="{{activeTab === 'capacity'}}">
    <!-- 当前容量信息 -->
    <view class="info-card">
      <view class="info-title">当前容量</view>
      <view class="info-content">
        <view class="info-item">
          <text class="info-label">衣物容量:</text>
          <text class="info-value">{{limits.clothesCount}} / {{limits.clothesLimit}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">搭配容量:</text>
          <text class="info-value">{{limits.outfitsCount}} / {{limits.outfitsLimit}}</text>
        </view>
      </view>
    </view>

    <!-- 兑换码输入区域 -->
    <view class="redeem-section">
      <view class="input-group">
        <input class="redeem-input"
          type="text"
          placeholder="请输入或粘贴容量兑换码"
          value="{{redeemCode}}"
          bindinput="onRedeemCodeInput"
          disabled="{{isRedeeming}}"
        />
        <view class="paste-btn" bindtap="pasteFromClipboard">粘贴</view>
      </view>
      <button class="redeem-btn" bindtap="submitRedeemCode" disabled="{{!redeemCode || isRedeeming}}">
        {{isRedeeming ? '兑换中...' : '兑换'}}
      </button>
    </view>
  </view>

  <!-- 会员兑换区域 -->
  <view class="tab-content" wx:if="{{activeTab === 'membership'}}">
    <!-- 当前会员信息 -->
    <view class="info-card">
      <view class="info-title">当前会员状态</view>
      <view class="info-content">
        <view class="info-item">
          <text class="info-label">会员类型:</text>
          <text class="info-value {{userInfo.memberType === 'VIP' ? 'vip-text' : ''}}">{{userInfo.memberType === 'VIP' ? 'VIP会员' : '普通会员'}}</text>
        </view>
        <view class="info-item" wx:if="{{userInfo.memberType === 'VIP'}}">
          <text class="info-label">到期时间:</text>
          <text class="info-value">{{userInfo.memberDaysLeft}}天后</text>
        </view>
      </view>
    </view>

    <!-- 会员兑换码输入区域 -->
    <view class="redeem-section">
      <view class="input-group">
        <input class="redeem-input"
          type="text"
          placeholder="请输入或粘贴会员兑换码"
          value="{{membershipCode}}"
          bindinput="onMembershipCodeInput"
          disabled="{{isRedeemingMembership}}"
        />
        <view class="paste-btn" bindtap="pasteFromClipboardMembership">粘贴</view>
      </view>
      <button class="redeem-btn membership-btn" bindtap="submitMembershipCode" disabled="{{!membershipCode || isRedeemingMembership}}">
        {{isRedeemingMembership ? '兑换中...' : '兑换'}}
      </button>
    </view>

    <!-- 会员兑换提示 -->
    <view class="tips-card">
      <view class="tips-title">兑换说明</view>
      <view class="tips-content">
        <view class="tip-item">• 兑换码区分大小写，请准确输入</view>
        <view class="tip-item">• 会员兑换码可兑换周会员、月度、季度或年度会员</view>
        <view class="tip-item">• 如果您已是VIP会员，兑换后会在现有会员期限上增加相应天数</view>
      </view>
    </view>
  </view>

  <!-- 兑换历史 -->
  <view class="history-section" wx:if="{{redeemHistory.length > 0}}">
    <view class="section-title">兑换记录</view>
    <view class="history-list">
      <view class="history-item" wx:for="{{redeemHistory}}" wx:key="index">
        <view class="history-main">
          <view class="history-name">{{item.codeName}}</view>
          <view class="history-time">{{formatDateTime(item.redeemedAt)}}</view>
        </view>
        <view class="history-detail">
          <block wx:if="{{item.rewardType === 'clothes'}}">
            <text class="reward-text">+{{item.rewardAmount}} 衣物容量</text>
          </block>
          <block wx:elif="{{item.rewardType === 'outfits'}}">
            <text class="reward-text">+{{item.rewardAmount}} 搭配容量</text>
          </block>
          <block wx:elif="{{item.rewardType === 'both'}}">
            <text class="reward-text">+{{item.clothesRewardAmount}} 衣物容量</text>
            <text class="reward-text">+{{item.outfitsRewardAmount}} 搭配容量</text>
          </block>
          <block wx:elif="{{item.rewardType === 'membership'}}">
            <text class="reward-text">
              {{item.membershipType === 'weekly' ? '周会员' :
                (item.membershipType === 'monthly' ? '月度会员' :
                (item.membershipType === 'quarterly' ? '季度会员' : '年度会员'))}}
              ({{item.membershipDays}}天)
            </text>
          </block>
        </view>
      </view>
    </view>
  </view>

  <!-- 兑换结果弹窗 -->
  <view class="modal" wx:if="{{showResult}}">
    <view class="modal-mask" bindtap="closeResultModal"></view>
    <view class="modal-content">
      <view class="modal-header {{redeemResult.success ? 'success' : 'fail'}}">
        {{redeemResult.success ? '兑换成功' : '兑换失败'}}
      </view>
      <view class="modal-body">
        <view class="modal-message">{{redeemResult.message}}</view>

        <!-- 成功情况下显示奖励详情 -->
        <block wx:if="{{redeemResult.success}}">
          <view class="reward-info">
            <block wx:if="{{redeemResult.rewardType === 'clothes'}}">
              <view class="reward-item">
                <text class="reward-label">衣物容量:</text>
                <text class="reward-value">+{{redeemResult.rewardAmount}}</text>
              </view>
            </block>
            <block wx:elif="{{redeemResult.rewardType === 'outfits'}}">
              <view class="reward-item">
                <text class="reward-label">搭配容量:</text>
                <text class="reward-value">+{{redeemResult.rewardAmount}}</text>
              </view>
            </block>
            <block wx:elif="{{redeemResult.rewardType === 'both'}}">
              <view class="reward-item">
                <text class="reward-label">衣物容量:</text>
                <text class="reward-value">+{{redeemResult.clothesRewardAmount}}</text>
              </view>
              <view class="reward-item">
                <text class="reward-label">搭配容量:</text>
                <text class="reward-value">+{{redeemResult.outfitsRewardAmount}}</text>
              </view>
            </block>
            <block wx:elif="{{redeemResult.rewardType === 'membership'}}">
              <view class="reward-item">
                <text class="reward-label">会员类型:</text>
                <text class="reward-value">
                  {{redeemResult.membershipType === 'weekly' ? '周会员' :
                    (redeemResult.membershipType === 'monthly' ? '月度会员' :
                    (redeemResult.membershipType === 'quarterly' ? '季度会员' : '年度会员'))}}
                </text>
              </view>
              <view class="reward-item">
                <text class="reward-label">会员天数:</text>
                <text class="reward-value">{{redeemResult.membershipDays}}天</text>
              </view>
              <view class="reward-item">
                <text class="reward-label">到期时间:</text>
                <text class="reward-value">{{formatDate(redeemResult.expireDate)}}</text>
              </view>
            </block>
          </view>
        </block>
      </view>
      <view class="modal-footer">
        <button class="modal-btn" bindtap="closeResultModal">确定</button>
      </view>
    </view>
  </view>
</view>