/* page/wardrobe/outfit/outfit_create/outfit_create.wxss */

/* 全局容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 5rpx solid;
  border-radius: 50%;
  margin-bottom: 20rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  letter-spacing: 1rpx;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* 顶部工具栏 */
.toolbar {
  display: flex;
  flex-wrap: nowrap; /* 防止元素换行 */
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-radius: 0 0 30rpx 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

/* 返回按钮 */
.back-button {
  display: flex;
  align-items: center;
  padding: 10rpx;
  flex-shrink: 0; /* 防止被压缩 */
}

.back-icon {
  width: 0;
  height: 0;
  border-top: 15rpx solid transparent;
  border-bottom: 15rpx solid transparent;
  border-right: 24rpx solid;
  transform: rotate(180deg);
  margin-right: 10rpx;
}

/* 搭配名称输入框 */
.outfit-name-input {
  flex: 1;
  min-width: 120rpx; /* 最小宽度 */
  height: 60rpx;
  border-bottom: 2rpx solid;
  padding: 0 20rpx;
  margin: 0 20rpx;
  font-size: 28rpx;
  background-color: transparent;
}

/* 穿搭类型选择器容器 */
.category-selector-container {
  position: relative;
  margin: 0 20rpx;
  z-index: 999; /* 非常高的z-index，确保在遮罩层之上 */
}

/* 穿搭类型选择器 */
.category-selector {
  display: flex;
  align-items: center;
  height: 60rpx;
  border: 2rpx solid;
  border-radius: 30rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  background-color: inherit; /* 继承父元素背景色 */
}

.selected-category {
  display: flex;
  align-items: center;
}

.category-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
}

.category-name {
  font-size: 26rpx;
}

.dropdown-arrow {
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid;
  margin-left: 10rpx;
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 900; /* 设置为比下拉菜单低的层级 */
}

/* 类型选择下拉菜单容器 */
.category-dropdown-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000; /* 确保在遮罩层之上 */
  pointer-events: none; /* 默认不拦截点击事件 */
}

/* 类型选择下拉菜单 */
.category-dropdown {
  position: absolute;
  top: 138rpx; /* 根据顶部工具栏高度调整 */
  right: 170rpx; /* 右侧位置调整 */
  width: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.3);
  pointer-events: auto; /* 恢复点击事件监听 */
  max-height: 70vh;
  overflow-y: auto;
}

.category-option {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  transition: all 0.2s ease;
}

.category-option.active {
  font-weight: bold;
}

.option-hover {
  opacity: 0.8;
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.option-name {
  font-size: 26rpx;
  text-align: center;
  width: 100%;
}

/* 自定义类型输入区域 */
.custom-category-input-area {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.custom-category-input {
  height: 60rpx;
  border: 1px solid;
  border-radius: 10rpx;
  padding: 0 20rpx;
  margin-bottom: 15rpx;
  font-size: 26rpx;
  background-color: rgba(255, 255, 255, 0.1);
}

.add-custom-category-btn {
  height: 60rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  transition: all 0.2s ease;
}

.add-custom-category-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 保存按钮 */
.save-button {
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  flex-shrink: 0; /* 防止被压缩 */
}

/* 中间内容区域 */
.content-section {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 垂直布局样式 - 新增 */
.vertical-layout {
  flex-direction: column;
}

/* 左侧衣柜面板 */
.closet-panel {
  width: 40%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

/* 上方衣柜面板 - 新增 */
.top-panel {
  width: 100%;
  height: 22%; /* 进一步减少高度比例，只展示一行即可 */
  border-right: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 类别筛选 */
.category-filter {
  padding: 5rpx 10rpx; /* 进一步减少内边距 */
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 5rpx; /* 减少底部边距 */
}

.categories-scroll {
  white-space: nowrap;
  width: 100%;
  padding: 2rpx 0;
}

.category-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 6rpx; /* 进一步减少内边距 */
  margin: 0 5rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  transition: all 0.3s ease;
  position: relative;
  width: 65rpx;
  height: 70rpx; /* 减小高度 */
  box-sizing: border-box;
}

.category-item:first-child {
  margin-left: 15rpx;
}

.category-item:last-child {
  margin-right: 15rpx;
}

.category-item.active {
  font-weight: 500;
  transform: scale(1.05);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.category-text {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.category-count {
  margin-left: 5rpx;
  font-size: 20rpx;
  opacity: 0.8;
}

/* 衣物列表 */
.clothes-scroll {
  flex: 1;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  padding: 10rpx 0;
}

.clothes-grid {
  display: inline-flex;
  flex-wrap: nowrap;
  padding: 0 10rpx;
}

.clothes-item {
  width: 150rpx;
  min-width: 150rpx;
  margin: 0 10rpx;
  border-radius: 16rpx;
  overflow: hidden;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.clothes-item:first-child {
  margin-left: 15rpx;
}

.clothes-item:last-child {
  margin-right: 15rpx;
}

.clothes-item-hover {
  transform: scale(0.97);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.clothes-image {
  width: 95%;
  height: 120rpx;
  object-fit: contain;
  margin: 10rpx 0 5rpx 0;
}

.clothes-name {
  font-size: 18rpx;
  padding: 6rpx 4rpx;
  width: 100%;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: rgba(0, 0, 0, 0.1);
}

.empty-hint {
  display: inline-block;
  padding: 20rpx 30rpx;
  text-align: center;
  font-size: 24rpx;
  white-space: normal;
  width: 100%;
}

/* 右侧画布面板 */
.canvas-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15rpx; /* 减少内边距，增加可用空间 */
  overflow: hidden;
  background-color: #f0f0f0;
}

/* 下方画布面板 - 新增 */
.bottom-panel {
  height: 78%; /* 增加画布区域的比例 */
  padding-top: 15rpx;
}

.canvas-container {
  position: relative;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx; /* 减少底部间距 */
  overflow: hidden;
  max-height: calc(100% - 80rpx); /* 确保画布高度不超过父容器 */
  width: 90%; /* 增加宽度利用率 */
}

/* 画布项目 */
.canvas-item {
  position: absolute;
  cursor: move;
  border: 1px solid transparent;
  transition: border-color 0.2s ease;
  touch-action: none;
  box-sizing: border-box;
}

.canvas-item.active {
  border: 2px dashed #007aff;
  box-shadow: 0 0 10rpx rgba(0, 122, 255, 0.5);
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  pointer-events: none; /* 防止图片干扰触摸事件 */
}

/* 控制按钮 */
.control-btn {
  position: absolute;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.3);
  z-index: 10;
  font-weight: bold;
  transform: translate(-50%, -50%);
  transition: all 0.2s ease;
  user-select: none;
  line-height: 1;
  padding: 10rpx;
  margin: -10rpx;
}

.control-btn:active {
  transform: translate(-50%, -50%) scale(0.9);
  opacity: 0.8;
}

.delete-btn {
  color: #ff3b30;
  font-size: 36rpx;
  background-color: #fff0f0;
  border: 2rpx solid #ffcccc;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  transform: none;
  font-weight: bold;
  font-size: 36px;
  padding: 15rpx;
  margin: -15rpx;
}

.size-increase-btn {
  color: #007aff;
  background-color: #f0f8ff;
  border: 2rpx solid #ccddff;
  padding: 12rpx;
  margin: -12rpx;
}

.size-decrease-btn {
  color: #007aff;
  background-color: #f0f8ff;
  border: 2rpx solid #ccddff;
  padding: 12rpx;
  margin: -12rpx;
}

.rotate-cw-btn {
  color: #007aff;
  background-color: #f0f8ff;
  border: 2rpx solid #ccddff;
  padding: 12rpx;
  margin: -12rpx;
}

.rotate-ccw-btn {
  color: #007aff;
  background-color: #f0f8ff;
  border: 2rpx solid #ccddff;
  padding: 12rpx;
  margin: -12rpx;
}

.layer-up-btn {
  color: #34c759;
  background-color: #f0fff0;
  border: 2rpx solid #ccffcc;
  font-size: 36rpx;
  width: 46px;
  height: 46px;
  line-height: 46px;
  text-align: center;
  transform: none;
  padding: 12rpx;
  margin: -12rpx;
}

.layer-down-btn {
  color: #34c759;
  background-color: #f0fff0;
  border: 2rpx solid #ccffcc;
  font-size: 36rpx;
  width: 46px;
  height: 46px;
  line-height: 46px;
  text-align: center;
  transform: none;
  padding: 12rpx;
  margin: -12rpx;
}

/* 画布提示 */
.canvas-hint {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 28rpx;
  text-align: center;
  line-height: 1.6;
  opacity: 0.6;
}

/* 画布操作按钮 */
.canvas-action-button {
  padding: 12rpx 50rpx; /* 稍微减小按钮大小 */
  border-radius: 40rpx;
  font-size: 28rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: opacity 0.2s ease, transform 0.2s ease;
  margin-top: 10rpx; /* 减少顶部间距 */
}

.canvas-action-button:active {
  opacity: 0.9;
  transform: scale(0.97);
}

/* 图层信息 */
.layer-info {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  z-index: 10;
}

/* 添加抠图后图片的样式 */
.clothes-image.processed-image {
  box-shadow: 0 0 8rpx rgba(255, 255, 255, 0.5);
}

/* 抠图标签样式 */
.processed-tag {
  font-size: 16rpx;
  background-color: rgba(255, 100, 100, 0.8);
  color: white;
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
  margin-left: 4rpx;
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}

/* 抠图状态图标 */
.processed-icon {
  position: absolute;
  top: 2rpx;
  right: 2rpx;
  width: 30rpx;
  height: 30rpx;
  background-color: #ff5a5a;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  z-index: 2;
  box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.3);
}

/* 适配于深色主题 */
.theme-autumn .clothes-image.processed-image {
  box-shadow: 0 0 8rpx rgba(232, 209, 167, 0.7);
}
