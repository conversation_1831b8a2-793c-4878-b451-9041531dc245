<!-- 添加WXS模块用于数字格式化 -->
<wxs module="utils">
  function formatNumber(price, wornCount) {
    if (price && wornCount && wornCount > 0) {
      var value = price / wornCount;
      return value.toFixed(2);
    }
    return "0.00";
  }

  function formatPrice(price) {
    if (price) {
      return parseFloat(price).toFixed(2);
    }
    return "0.00";
  }

  module.exports = {
    formatNumber: formatNumber,
    formatPrice: formatPrice
  };
</wxs>

<!-- 添加WXS模块用于排序处理 -->
<wxs module="sortUtils">
  function getSortLabel(sortType, sortOptions) {
    if (!sortType) return '排序';

    for (var i = 0; i < sortOptions.length; i++) {
      if (sortOptions[i].value === sortType) {
        return sortOptions[i].label;
      }
    }

    return '排序';
  }

  module.exports = {
    getSortLabel: getSortLabel
  };
</wxs>

<!-- 自定义导航栏 -->
<mp-navigation-bar
  back="{{true}}"
  background="{{navBackground}}"
  color="{{navFrontColor}}"
  title="{{navTitle}}">

</mp-navigation-bar>

<view class="category-container" style="background-color: {{themeStyle === 'autumn' ? colors.lightTaupe : (themeStyle === 'blackWhite' ? blackWhiteColors.veryLightGray : pinkBlueColors.pinkLight)}};">
  <!-- Loading state -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner" style="border-top-color: {{themeStyle === 'autumn' ? colors.darkOlive : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)}}; border-color: {{themeStyle === 'autumn' ? colors.lightTaupe : (themeStyle === 'blackWhite' ? blackWhiteColors.lightGray : pinkBlueColors.blueLight)}};"></view>
    <view class="loading-text" style="color: {{themeStyle === 'autumn' ? colors.darkOlive : (themeStyle === 'blackWhite' ? blackWhiteColors.darkGray : pinkBlueColors.pinkDark)}};">加载中...</view>
  </view>

  <!-- 主要内容，仅在非加载状态下显示 -->
  <block wx:if="{{!isLoading}}">


    <!-- 筛选按钮区域 -->
    <scroll-view scroll-x="true" class="filter-buttons-container" style="background-color: {{themeStyle === 'autumn' ? '#f2e7d5' : (themeStyle === 'blackWhite' ? blackWhiteColors.veryLightGray : pinkBlueColors.pinkLight)}}; border-color: {{themeStyle === 'autumn' ? '#d5c3a5' : (themeStyle === 'blackWhite' ? blackWhiteColors.lightGray : pinkBlueColors.pinkMedium)}};">
      <view class="filter-buttons">
        <view class="filter-button {{currentSubcategory === '' ? 'active' : ''}}"
              bindtap="filterBySubcategory"
              data-subcategory=""
              style="background-color: {{currentSubcategory === '' ? (themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)) : 'transparent'}}; color: {{currentSubcategory === '' ? '#ffffff' : (themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark))}}; border-color: {{themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)}};">
          全部
        </view>
        <block wx:for="{{subcategories}}" wx:key="*this">
          <view class="filter-button {{currentSubcategory === item ? 'active' : ''}}"
                bindtap="filterBySubcategory"
                data-subcategory="{{item}}"
                style="background-color: {{currentSubcategory === item ? (themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)) : 'transparent'}}; color: {{currentSubcategory === item ? '#ffffff' : (themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark))}}; border-color: {{themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)}};">
            {{item}}
          </view>
        </block>
      </view>
    </scroll-view>

    <!-- 类别主体内容 -->
    <view class="category-body {{categoryBodyAnimation}}" style="background-color: {{themeStyle === 'autumn' ? '#e9d7bd' : (themeStyle === 'blackWhite' ? blackWhiteColors.white : pinkBlueColors.blueLight)}}; border-color: {{themeStyle === 'autumn' ? '#d5c3a5' : (themeStyle === 'blackWhite' ? blackWhiteColors.lightGray : pinkBlueColors.blueMedium)}};" bindtouchstart="touchStart" bindtouchend="touchEnd">
      <scroll-view scroll-y="true" class="clothes-scroll-view" bindscrolltolower="loadMoreClothes" bindscroll="onScroll">
        <!-- 网格视图 -->
        <view class="clothes-grid" wx:if="{{viewType === 'grid'}}">
          <block wx:if="{{filteredClothes.length > 0}}">
            <view class="clothes-grid-row">
              <block wx:for="{{displayedClothes}}" wx:key="_id">
                <view class="grid-item {{isSelectionMode && clothesIsSelected[item._id] ? 'selected' : ''}}"
                      data-id="{{item._id}}"
                      bindtap="{{isSelectionMode ? 'selectClothing' : 'viewClothesDetail'}}"
                      bindlongpress="onClothesLongPress"
                      data-name="{{item.name || ((item.color || '') + ' ' + (item.style || ''))}}"
                      data-want-to-discard="{{item.wantToDiscard}}"
                      data-worn-count="{{item.wornCount || 0}}">
                  <view class="grid-item-image-container">
                    <!-- 选择模式下显示复选框 -->
                    <view class="selection-checkbox" wx:if="{{isSelectionMode}}">
                      <icon type="{{clothesIsSelected[item._id] ? 'success' : 'circle'}}"
                            size="22"
                            color="{{clothesIsSelected[item._id] ? '#ffffff' : '#999'}}"></icon>
                    </view>
                    <image class="grid-item-image" src="{{item.tempImageUrl || '/images/clothes_placeholder.png'}}" mode="aspectFit" lazy-load="true" binderror="handleImageError" data-index="{{index}}" data-id="{{item._id}}"></image>
                    <view class="price-tag" wx:if="{{item.price && !hidePrices}}">¥{{utils.formatPrice(item.price)}}</view>

                    <view class="discard-mark" wx:if="{{item.wantToDiscard}}">断舍离</view>
                  </view>
                  <view class="grid-item-info">
                    <text class="grid-item-name">{{item.name || ((item.color || '') + ' ' + (item.style || ''))}}</text>
                    <text class="grid-item-color">{{item.color || '无颜色'}}</text>
                  </view>
                </view>
              </block>
            </view>
          </block>
          <block wx:else>
            <view class="no-data-tip">暂无{{currentSubcategory ? currentSubcategory + '细分类的' : ''}}{{category}}数据</view>
          </block>
        </view>

        <!-- 详细视图 -->
        <view class="clothes-detail-list" wx:if="{{viewType === 'detail'}}">
          <block wx:if="{{filteredClothes.length > 0}}">
            <block wx:for="{{displayedClothes}}" wx:key="_id">
              <view class="detail-list-item {{isSelectionMode && clothesIsSelected[item._id] ? 'selected' : ''}}"
                    data-id="{{item._id}}"
                    bindtap="{{isSelectionMode ? 'selectClothing' : 'viewClothesDetail'}}"
                    bindlongpress="onClothesLongPress"
                    data-name="{{item.name || ((item.color || '') + ' ' + (item.style || ''))}}"
                    data-want-to-discard="{{item.wantToDiscard}}"
                    data-worn-count="{{item.wornCount || 0}}">
                <view class="detail-item-left">
                  <!-- 选择模式下显示复选框 -->
                  <view class="selection-checkbox" wx:if="{{isSelectionMode}}">
                    <icon type="{{clothesIsSelected[item._id] ? 'success' : 'circle'}}"
                          size="22"
                          color="{{clothesIsSelected[item._id] ? '#ffffff' : '#999'}}"></icon>
                  </view>
                  <image class="detail-item-image" src="{{item.tempImageUrl || '/images/clothes_placeholder.png'}}" mode="aspectFit" lazy-load="true" binderror="handleImageError" data-index="{{index}}" data-id="{{item._id}}"></image>
                  <view class="discard-mark small" wx:if="{{item.wantToDiscard}}">断舍离</view>
                </view>
                <view class="detail-item-right">
                  <view class="detail-item-title">{{item.name || ((item.color || '') + ' ' + (item.style || ''))}}</view>

                  <view class="detail-item-info-section">
                    <view class="detail-item-row" wx:if="{{item.type_detail}}">
                      <text class="detail-item-label">细分类:</text>
                      <text class="detail-item-value">{{item.type_detail}}</text>
                    </view>

                    <view class="detail-item-row" wx:if="{{item.color}}">
                      <text class="detail-item-label">颜色:</text>
                      <text class="detail-item-value">{{item.color}}</text>
                    </view>

                    <view class="detail-item-row" wx:if="{{item.price}}">
                      <text class="detail-item-label">价格:</text>
                      <text class="detail-item-value" wx:if="{{!hidePrices}}">¥{{item.price}}</text>
                      <text class="detail-item-value" wx:else>******</text>
                    </view>

                    <view class="detail-item-row" wx:if="{{item.purchaseDate}}">
                      <text class="detail-item-label">购买时间:</text>
                      <text class="detail-item-value">{{item.purchaseDate}}</text>
                    </view>

                    <view class="detail-item-row" wx:if="{{item.price && item.wornCount && item.wornCount > 0}}">
                      <text class="detail-item-label">每次成本:</text>
                      <text class="detail-item-value" wx:if="{{!hidePrices}}">¥{{utils.formatNumber(item.price, item.wornCount)}}/次</text>
                      <text class="detail-item-value" wx:else>******</text>
                    </view>

                    <view class="detail-item-row" wx:if="{{item.storageLocation}}">
                      <text class="detail-item-label">存储位置:</text>
                      <text class="detail-item-value">{{item.storageLocation}}</text>
                    </view>

                    <view class="detail-item-row" wx:if="{{item.remark}}">
                      <text class="detail-item-label">备注:</text>
                      <text class="detail-item-value">{{item.remark}}</text>
                    </view>

                    <!-- 新增待办事项显示 -->
                    <block wx:if="{{item.todos && item.todos.length > 0}}">
                      <view class="detail-item-row detail-todos-row">
                        <text class="detail-item-label">待办事项:</text>
                        <view class="detail-todos-list">
                          <block wx:for="{{item.todos}}" wx:for-item="todo" wx:key="index">
                            <view class="detail-todo-item {{todo.completed ? 'completed' : ''}}">
                              <view class="detail-todo-status">
                                <icon type="{{todo.completed ? 'success' : 'circle'}}" size="14" color="{{todo.completed ? '#4CAF50' : '#999'}}"/>
                              </view>
                              <view class="detail-todo-content">
                                <text class="detail-todo-title">{{todo.title}}</text>
                                <text class="detail-todo-date" wx:if="{{todo.dueDate}}">期限: {{todo.dueDate}}</text>
                              </view>
                            </view>
                          </block>
                        </view>
                      </view>
                    </block>
                  </view>
                </view>
              </view>
            </block>
          </block>
          <block wx:else>
            <view class="no-data-tip">暂无{{currentSubcategory ? currentSubcategory + '细分类的' : ''}}{{category}}数据</view>
          </block>
        </view>

        <!-- 加载更多指示器 -->
        <view class="loading-more" wx:if="{{isLoadingMore}}">
          <view class="loading-spinner-small"></view>
          <text class="loading-more-text">加载更多...</text>
        </view>
      </scroll-view>
    </view>
  </block>

  <!-- 底部信息栏 - 正常模式 -->
  <view class="category-bottom-bar" wx:if="{{!isSelectionMode}}" style="background-color: {{themeStyle === 'autumn' ? '#f2e7d5' : (themeStyle === 'blackWhite' ? blackWhiteColors.veryLightGray : pinkBlueColors.pinkLight)}}; border-color: {{themeStyle === 'autumn' ? '#d5c3a5' : (themeStyle === 'blackWhite' ? blackWhiteColors.lightGray : pinkBlueColors.pinkMedium)}};">
    <view class="clothes-count">
      <text class="count-number">{{filteredClothes.length}}</text>
      <text class="count-label">件{{category}}{{currentSubcategory ? ' · ' + currentSubcategory : ''}}</text>
    </view>
    <!-- 视图切换按钮 -->
    <view class="view-toggle-btn" bindtap="toggleViewType" style="background-color: {{viewType === 'detail' ? (themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)) : 'transparent'}}; color: {{viewType === 'detail' ? '#ffffff' : (themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark))}}; border-color: {{themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)}};">
      <view class="view-icon">
        <text>{{viewType === 'grid' ? '📋' : '📊'}}</text>
      </view>
      <text>{{viewType === 'grid' ? '列表视图' : '网格视图'}}</text>
    </view>
    <!-- 排序按钮 -->
    <view class="sort-btn" bindtap="showSortPicker" style="background-color: {{currentSortType ? (themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)) : 'transparent'}}; color: {{currentSortType ? '#ffffff' : (themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark))}}; border-color: {{themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)}};">
      <view class="sort-icon">
        <text>🔄</text>
      </view>
      <text>{{sortUtils.getSortLabel(currentSortType, sortOptions)}}</text>
    </view>
    <view class="season-filter-btn" bindtap="filterCurrentSeason" style="background-color: {{showingCurrentSeason ? (themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)) : 'transparent'}}; color: {{showingCurrentSeason ? '#ffffff' : (themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark))}}; border-color: {{themeStyle === 'autumn' ? '#8d7a56' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)}};">
      <view class="season-icon">
        <block wx:if="{{currentSeason === '春'}}">🌱</block>
        <block wx:elif="{{currentSeason === '夏'}}">☀️</block>
        <block wx:elif="{{currentSeason === '秋'}}">🍂</block>
        <block wx:elif="{{currentSeason === '冬'}}">❄️</block>
      </view>
      <text>当季衣服</text>
    </view>
  </view>

  <!-- 底部工具栏 - 选择模式 -->
  <view class="selection-toolbar" wx:if="{{isSelectionMode}}" style="background-color: {{themeStyle === 'autumn' ? '#f2e7d5' : (themeStyle === 'blackWhite' ? blackWhiteColors.veryLightGray : pinkBlueColors.pinkLight)}}; border-color: {{themeStyle === 'autumn' ? '#d5c3a5' : (themeStyle === 'blackWhite' ? blackWhiteColors.lightGray : pinkBlueColors.pinkMedium)}};">
    <view class="selection-count">
      <text class="count-number">{{selectedClothes.length}}</text>
      <text class="count-label">件已选中</text>
    </view>
    <view class="selection-actions">
      <view class="selection-btn select-all-btn" bindtap="toggleSelectAll">
        <view class="toolbar-checkbox {{selectAll ? 'selected' : ''}}" style="{{themeStyle === 'blackWhite' && selectAll ? 'background-color: ' + blackWhiteColors.black + ';' : ''}}">
          <icon type="{{selectAll ? 'success' : 'circle'}}" size="22" color="{{selectAll ? '#ffffff' : (themeStyle === 'blackWhite' ? blackWhiteColors.mediumGray : '#999')}}"></icon>
        </view>
        <text>{{selectAll ? '取消全选' : '全选'}}</text>
      </view>
      <view class="selection-btn edit" bindtap="showBatchEditModal" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.darkGray + '; color: ' + blackWhiteColors.white + ';' : ''}}">
        <text>批量编辑</text>
      </view>
      <view class="selection-btn delete" bindtap="showBatchDeleteConfirm" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.darkGray + '; color: ' + blackWhiteColors.white + ';' : ''}}">
        <text>批量删除</text>
      </view>
      <view class="selection-btn cancel" bindtap="exitSelectionMode" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.lightGray + '; color: ' + blackWhiteColors.black + ';' : ''}}">
        <text>取消</text>
      </view>
    </view>
  </view>

  <!-- 滑动提示（只在首次加载时显示） -->
  <view class="swipe-hint" wx:if="{{showSwipeHint}}">
    需要大幅度水平滑动才能切换细分类
    <text>\n请勿与垂直滚动混淆</text>
  </view>

  <!-- 衣物详情弹窗 -->
  <view class="card-detail-modal" wx:if="{{clothesDetail}}" bindtap="closeClothesDetail">
    <view class="modal-content" catchtap="preventBubble" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.veryLightGray + ';' : ''}}">
      <view class="modal-header" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.white + '; border-color: ' + blackWhiteColors.lightGray + ';' : ''}}">
        <view class="title-container" title="{{clothesDetail.name || ((clothesDetail.color || '') + ' ' + (clothesDetail.style || ''))}}">
          <text class="modal-title" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.black + ';' : ''}}">{{clothesDetail.name || ((clothesDetail.color || '') + ' ' + (clothesDetail.style || ''))}}</text>
        </view>
        <view class="header-actions">
          <view class="store-btn {{themeStyle === 'autumn' ? 'autumn' : ''}} {{!clothesDetail.hiden ? 'vip-feature' : ''}}" bindtap="{{clothesDetail.hiden ? 'retrieveClothing' : 'storeClothing'}}" data-id="{{clothesDetail._id}}" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.white + '; border-color: ' + blackWhiteColors.lightGray + ';' : ''}}">
            <image class="store-icon" src="/image/box-icon.png" mode="aspectFit"></image>
            <text class="store-text" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.black + ';' : ''}}">{{clothesDetail.hiden ? '取出换季箱' : '存入换季箱'}}</text>

          </view>
          <view class="close-btn" bindtap="closeClothesDetail" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.mediumGray + ';' : ''}}">×</view>
        </view>
      </view>
      <view class="modal-body" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.veryLightGray + ';' : ''}}">
        <!-- 图片和价格信息卡片 -->
        <view class="detail-card image-card" bindtap="handleImageCardClick" bindlongpress="handleImageCardLongPress" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.white + '; border-color: ' + blackWhiteColors.lightGray + ';' : ''}}">
          <view class="item-image-container">
            <image class="item-image" src="{{clothesDetail.tempImageUrl || '/images/clothes_placeholder.png'}}" mode="aspectFit" lazy-load="true" binderror="handleImageError" data-id="{{clothesDetail._id}}"></image>
            <view class="price-tag" wx:if="{{clothesDetail.price && !hidePrices}}" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.black + ';' : ''}}">¥{{utils.formatPrice(clothesDetail.price)}}</view>
            <view class="cost-effectiveness-tag" wx:if="{{clothesDetail.price && clothesDetail.wornCount && clothesDetail.wornCount > 0 && !hidePrices}}" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.darkGray + ';' : ''}}">¥{{utils.formatNumber(clothesDetail.price, clothesDetail.wornCount)}}/次</view>
            <view class="discard-mark" wx:if="{{clothesDetail.wantToDiscard}}">断舍离</view>
          </view>
        </view>

        <!-- 基本信息卡片 -->
        <view class="detail-card" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.white + '; border-color: ' + blackWhiteColors.lightGray + ';' : ''}}">
          <view class="card-title" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.black + '; background-color: ' + blackWhiteColors.veryLightGray + ';' : ''}}">基本信息</view>
          <view class="card-content">
            <view class="detail-row">
              <text class="detail-label" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.darkGray + ';' : ''}}">类别</text>
              <text class="detail-value" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.black + ';' : ''}}">{{clothesDetail.category || '未分类'}}</text>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.type_detail}}">
              <text class="detail-label" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.darkGray + ';' : ''}}">细分类</text>
              <text class="detail-value" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.black + ';' : ''}}">{{clothesDetail.type_detail}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.darkGray + ';' : ''}}">颜色</text>
              <text class="detail-value" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.black + ';' : ''}}">{{clothesDetail.color || '未知'}}</text>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.size}}">
              <text class="detail-label" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.darkGray + ';' : ''}}">尺码</text>
              <text class="detail-value" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.black + ';' : ''}}">{{clothesDetail.size}}</text>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.material}}">
              <text class="detail-label" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.darkGray + ';' : ''}}">材质</text>
              <text class="detail-value" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.black + ';' : ''}}">{{clothesDetail.material}}</text>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.style}}">
              <text class="detail-label" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.darkGray + ';' : ''}}">风格</text>
              <text class="detail-value" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.black + ';' : ''}}">{{clothesDetail.style}}</text>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.wardrobeName}}">
              <text class="detail-label" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.darkGray + ';' : ''}}">所属衣柜</text>
              <text class="detail-value" style="{{themeStyle === 'blackWhite' ? 'color: ' + blackWhiteColors.black + ';' : ''}}">{{clothesDetail.wardrobeName}}</text>
            </view>
          </view>
        </view>



        <!-- 购买信息卡片 -->
        <view class="detail-card" wx:if="{{clothesDetail.brand || clothesDetail.price || clothesDetail.purchaseDate || clothesDetail.purchaseChannel}}">
          <view class="card-title">购买信息</view>
          <view class="card-content">
            <view class="detail-row" wx:if="{{clothesDetail.brand}}">
              <text class="detail-label">品牌</text>
              <text class="detail-value">{{clothesDetail.brand}}</text>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.purchaseChannel}}">
              <text class="detail-label">购买渠道</text>
              <text class="detail-value">{{clothesDetail.purchaseChannel}}</text>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.price}}">
              <text class="detail-label">价格</text>
              <view class="detail-value-with-icon">
                <text class="detail-value" wx:if="{{!hidePrices}}">¥{{utils.formatPrice(clothesDetail.price)}}</text>
                <text class="detail-value" wx:else>******</text>
                <view class="price-eye-icon" bindtap="togglePriceVisibility">
                  <text class="eye-icon">{{hidePrices ? '👁️' : '👁️‍🗨️'}}</text>
                </view>
              </view>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.purchaseDate}}">
              <text class="detail-label">购买时间</text>
              <text class="detail-value">{{clothesDetail.purchaseDate}}</text>
            </view>
          </view>
        </view>

        <!-- 使用信息卡片 -->
        <view class="detail-card" wx:if="{{clothesDetail.wornCount || clothesDetail.season || clothesDetail.storageLocation}}">
          <view class="card-title">使用信息</view>
          <view class="card-content">
            <view class="detail-row" wx:if="{{clothesDetail.wornCount}}">
              <text class="detail-label">穿着次数</text>
              <text class="detail-value">{{clothesDetail.wornCount}}次</text>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.price && clothesDetail.wornCount && clothesDetail.wornCount > 0}}">
              <text class="detail-label">每次成本</text>
              <text class="detail-value" wx:if="{{!hidePrices}}">¥{{utils.formatNumber(clothesDetail.price, clothesDetail.wornCount)}}/次</text>
              <text class="detail-value" wx:else>******</text>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.storageLocation}}">
              <text class="detail-label">存储位置</text>
              <text class="detail-value">{{clothesDetail.storageLocation}}</text>
            </view>
            <view class="detail-row" wx:if="{{clothesDetail.season}}">
              <text class="detail-label">适用季节</text>
              <text class="detail-value">{{clothesDetail.season}}</text>
            </view>
          </view>
        </view>

        <!-- 备注信息卡片 -->
        <view class="detail-card" wx:if="{{clothesDetail.remark}}">
          <view class="card-title">备注</view>
          <view class="card-content">
            <view class="detail-row">
              <text class="detail-value remark-value">{{clothesDetail.remark}}</text>
            </view>
          </view>
        </view>
                <!-- 待办事项卡片 -->
        <view class="detail-card" wx:if="{{clothesDetail.todos && clothesDetail.todos.length > 0}}">
          <view class="card-title">待办事项</view>
          <view class="card-content">
            <view class="detail-todos-list">
              <block wx:for="{{clothesDetail.todos}}" wx:key="index">
                <view class="detail-todo-item {{item.completed ? 'completed' : ''}}">
                  <view class="detail-todo-status">
                    <icon type="{{item.completed ? 'success' : 'circle'}}" size="16" color="{{item.completed ? '#4CAF50' : '#999'}}"/>
                  </view>
                  <view class="detail-todo-content">
                    <view class="detail-todo-title">{{item.title}}</view>
                    <view class="detail-todo-date" wx:if="{{item.dueDate}}">期限: {{item.dueDate}}</view>
                  </view>
                </view>
              </block>
            </view>
          </view>
        </view>

        <view class="modal-actions">
          <button class="action-btn delete-btn" bindtap="deleteClothesFromDetail" data-id="{{clothesDetail._id}}" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.darkGray + '; color: ' + blackWhiteColors.white + ';' : ''}}">删除</button>
          <button class="action-btn edit" bindtap="editClothes" data-id="{{clothesDetail._id}}" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.black + '; color: ' + blackWhiteColors.white + ';' : ''}}">编辑</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 奖励弹窗 -->
  <view class="card-detail-modal reward-modal" wx:if="{{showRewardModal}}" bindtap="closeRewardModal">
    <view class="modal-content reward-content" catchtap="preventBubble">
      <view class="modal-header">
        <view class="title-container">
          <text class="modal-title">恭喜获得奖励</text>
        </view>
        <view class="header-actions">
          <view class="close-btn" bindtap="closeRewardModal">×</view>
        </view>
      </view>
      <view class="modal-body">
        <view class="item-image-container">
          <image class="item-image" src="{{rewardImage || '/images/clothes_placeholder.png'}}" mode="aspectFit" lazy-load="true" binderror="handleImageError"></image>
        </view>
        <view class="reward-message">
          <text>{{rewardMessage}}</text>
        </view>
        <view class="modal-actions">
          <button class="action-btn confirm-btn" bindtap="confirmReward">确定</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 编辑衣物弹窗 -->
  <view class="edit-modal-overlay" wx:if="{{showEditModal}}">
    <view class="edit-modal-content" catchtap="preventBubble">
      <view class="edit-modal-header">
        <text class="edit-modal-title">编辑衣物</text>

        <view class="close-btn" bindtap="hideEditClothingModal">×</view>
      </view>
      <view class="edit-modal-body">
        <!-- 基本信息 -->
        <view class="edit-section">
          <view class="edit-row">
            <view class="edit-label">名称</view>
            <input class="edit-input" type="text" value="{{editingClothing.name}}" bindinput="onNameChange"
                  data-field="name" bindtap="handleIOSFocus" focus="{{editingClothing.nameFocus}}" placeholder="请输入衣物名称"/>
          </view>
          <view class="edit-row">
            <view class="edit-label">类别</view>
            <view class="edit-value" bindtap="showCategoryPicker">
              {{editingClothing.category || '请选择类别'}}
              <text class="arrow-right">▶</text>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">细分类</view>
            <view class="edit-input-container">
              <input class="edit-input" type="text" value="{{editingClothing.type_detail}}" bindinput="onTypeDetailChange"
                    bindfocus="showSuggestions" data-field="type_detail" bindtap="handleIOSFocus"
                    focus="{{editingClothing.type_detailFocus}}" placeholder="请输入细分类，如：长袖、短袖等"/>
              <!-- 历史值气泡 -->
              <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.type_detail.length > 0 && currentSuggestionField === 'type_detail'}}">
                <scroll-view scroll-x="true" class="bubbles-scroll">
                  <view class="bubble-container">
                    <view class="suggestion-bubble" wx:for="{{suggestions.type_detail}}" wx:key="*this" bindtap="selectSuggestion" data-field="type_detail" data-value="{{item}}">
                      {{item}}
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">颜色</view>
            <view class="edit-input-container">
              <input class="edit-input" type="text" value="{{editingClothing.color}}"
                     bindinput="onColorChange"
                     bindfocus="showSuggestions"
                     data-field="color"
                     bindtap="handleIOSFocus"
                     focus="{{editingClothing.colorFocus}}"
                     placeholder="请输入颜色"/>
              <!-- 历史值气泡 -->
              <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.color.length > 0 && currentSuggestionField === 'color'}}">
                <scroll-view scroll-x="true" class="bubbles-scroll">
                  <view class="bubble-container">
                    <view class="suggestion-bubble" wx:for="{{suggestions.color}}" wx:key="*this" bindtap="selectSuggestion" data-field="color" data-value="{{item}}">
                      {{item}}
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">风格</view>
            <view class="edit-input-container">
              <input class="edit-input" type="text" value="{{editingClothing.style}}"
                     bindinput="onStyleChange"
                     bindfocus="showSuggestions"
                     data-field="style"
                     bindtap="handleIOSFocus"
                     focus="{{editingClothing.styleFocus}}"
                     placeholder="请输入风格"/>
              <!-- 历史值气泡 -->
              <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.style.length > 0 && currentSuggestionField === 'style'}}">
                <scroll-view scroll-x="true" class="bubbles-scroll">
                  <view class="bubble-container">
                    <view class="suggestion-bubble" wx:for="{{suggestions.style}}" wx:key="*this" bindtap="selectSuggestion" data-field="style" data-value="{{item}}">
                      {{item}}
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">尺码</view>
            <view class="edit-input-container">
              <input class="edit-input" type="text" value="{{editingClothing.size}}"
                     bindinput="onSizeChange"
                     bindfocus="showSuggestions"
                     data-field="size"
                     bindtap="handleIOSFocus"
                     focus="{{editingClothing.sizeFocus}}"
                     placeholder="请输入尺码，如：S、M、L、XL等"/>
              <!-- 历史值气泡 -->
              <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.size.length > 0 && currentSuggestionField === 'size'}}">
                <scroll-view scroll-x="true" class="bubbles-scroll">
                  <view class="bubble-container">
                    <view class="suggestion-bubble" wx:for="{{suggestions.size}}" wx:key="*this" bindtap="selectSuggestion" data-field="size" data-value="{{item}}">
                      {{item}}
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">品牌</view>
            <view class="edit-input-container">
              <input class="edit-input" type="text" value="{{editingClothing.brand}}"
                     bindinput="onBrandChange"
                     bindfocus="showSuggestions"
                     data-field="brand"
                     bindtap="handleIOSFocus"
                     focus="{{editingClothing.brandFocus}}"
                     placeholder="请输入品牌名称"/>
              <!-- 历史值气泡 -->
              <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.brand.length > 0 && currentSuggestionField === 'brand'}}">
                <scroll-view scroll-x="true" class="bubbles-scroll">
                  <view class="bubble-container">
                    <view class="suggestion-bubble" wx:for="{{suggestions.brand}}" wx:key="*this" bindtap="selectSuggestion" data-field="brand" data-value="{{item}}">
                      {{item}}
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">购买渠道</view>
            <view class="edit-input-container">
              <input class="edit-input" type="text" value="{{editingClothing.purchaseChannel}}"
                     bindinput="onPurchaseChannelChange"
                     bindfocus="showSuggestions"
                     data-field="purchaseChannel"
                     bindtap="handleIOSFocus"
                     focus="{{editingClothing.purchaseChannelFocus}}"
                     placeholder="请输入购买渠道，如：淘宝、线下店等"/>
              <!-- 历史值气泡 -->
              <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.purchaseChannel.length > 0 && currentSuggestionField === 'purchaseChannel'}}">
                <scroll-view scroll-x="true" class="bubbles-scroll">
                  <view class="bubble-container">
                    <view class="suggestion-bubble" wx:for="{{suggestions.purchaseChannel}}" wx:key="*this" bindtap="selectSuggestion" data-field="purchaseChannel" data-value="{{item}}">
                      {{item}}
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">存储位置</view>
            <view class="edit-input-container">
              <input class="edit-input" type="text" value="{{editingClothing.storageLocation}}"
                     bindinput="onStorageLocationChange"
                     bindfocus="showSuggestions"
                     data-field="storageLocation"
                     bindtap="handleIOSFocus"
                     focus="{{editingClothing.storageLocationFocus}}"
                     placeholder="请输入存储位置，如：卧室衣柜等"/>
              <!-- 历史值气泡 -->
              <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.storageLocation.length > 0 && currentSuggestionField === 'storageLocation'}}">
                <scroll-view scroll-x="true" class="bubbles-scroll">
                  <view class="bubble-container">
                    <view class="suggestion-bubble" wx:for="{{suggestions.storageLocation}}" wx:key="*this" bindtap="selectSuggestion" data-field="storageLocation" data-value="{{item}}">
                      {{item}}
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">材质</view>
            <view class="edit-input-container">
              <input class="edit-input" type="text" value="{{editingClothing.material}}"
                     bindinput="onMaterialChange"
                     bindfocus="showSuggestions"
                     data-field="material"
                     bindtap="handleIOSFocus"
                     focus="{{editingClothing.materialFocus}}"
                     placeholder="请输入材质，如：棉、麻、丝等"/>
              <!-- 历史值气泡 -->
              <view class="suggestion-bubbles" wx:if="{{showingSuggestions && suggestions.material.length > 0 && currentSuggestionField === 'material'}}">
                <scroll-view scroll-x="true" class="bubbles-scroll">
                  <view class="bubble-container">
                    <view class="suggestion-bubble" wx:for="{{suggestions.material}}" wx:key="*this" bindtap="selectSuggestion" data-field="material" data-value="{{item}}">
                      {{item}}
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">价格</view>
            <input class="edit-input" type="digit" value="{{editingClothing.price}}"
                   bindinput="onPriceChange"
                   data-field="price"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.priceFocus}}"
                   placeholder="请输入价格"/>
          </view>
          <view class="edit-row">
            <view class="edit-label">穿着次数</view>
            <input class="edit-input" type="number" value="{{editingClothing.wornCount}}"
                   bindinput="onWornCountChange"
                   data-field="wornCount"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.wornCountFocus}}"
                   placeholder="请输入穿着次数"/>
          </view>
          <view class="edit-row">
            <view class="edit-label">上次穿着</view>
            <view class="edit-value" bindtap="showLastWornDatePicker">
              {{editingClothing.lastWornDate || '未记录'}}
              <text class="arrow-right">▶</text>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">购买时间</view>
            <view class="edit-value" bindtap="showPurchaseDatePicker">
              {{editingClothing.purchaseDate || '未记录'}}
              <text class="arrow-right">▶</text>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">季节</view>
            <view class="edit-value" bindtap="showSeasonPicker">
              {{editingClothing.season || '请选择季节'}}
              <text class="arrow-right">▶</text>
            </view>
          </view>
          <!-- 添加所属衣柜选择 -->
          <view class="edit-row">
            <view class="edit-label">所属衣柜</view>
            <view class="edit-value" bindtap="showWardrobePicker">
              {{editingClothing.wardrobeName || '请选择衣柜'}}
              <text class="arrow-right">▶</text>
            </view>
          </view>
          <view class="edit-row">
            <view class="edit-label">备注</view>
            <input class="edit-textarea" value="{{editingClothing.remark}}"
                   bindinput="onRemarkChange"
                   data-field="remark"
                   bindtap="handleIOSFocus"
                   focus="{{editingClothing.remarkFocus}}"
                   placeholder="请输入备注信息，如洗涤注意事项、搭配建议等"
                   maxlength="200"
                   auto-height="true" />
          </view>

          <!-- 衣物待办事项部分 -->
          <view class="edit-section-title">衣物待办事项</view>
          <view class="todos-container">
            <!-- 待办事项列表 -->
            <block wx:if="{{editingClothing.todos && editingClothing.todos.length > 0}}">
              <view class="todo-list">
                <view class="todo-item {{item.completed ? 'completed' : ''}}"
                      wx:for="{{editingClothing.todos}}"
                      wx:key="index">
                  <view class="todo-checkbox" bindtap="toggleTodoComplete" data-index="{{index}}">
                    <icon type="{{item.completed ? 'success' : 'circle'}}" size="18" color="{{item.completed ? '#4CAF50' : '#999'}}"/>
                  </view>
                  <view class="todo-content" bindtap="showTodoModal" data-index="{{index}}">
                    <view class="todo-title">{{item.title}}</view>
                    <view class="todo-date" wx:if="{{item.dueDate}}">期限: {{item.dueDate}}</view>
                  </view>
                  <view class="todo-actions">
                    <view class="todo-delete" bindtap="deleteTodo" data-index="{{index}}">×</view>
                  </view>
                </view>
              </view>
            </block>
            <view class="todo-add-btn" bindtap="showTodoModal">+ 添加待办事项</view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 - 移到edit-modal-content底部并固定 -->
      <view class="edit-actions">
        <button class="action-btn delete" bindtap="deleteCurrentClothing">删除</button>
        <button class="action-btn save" bindtap="saveClothingEdit">保存</button>
      </view>
    </view>
  </view>

  <!-- 类别选择器弹窗 -->
  <view class="category-picker-overlay" wx:if="{{showCategoryPicker}}">
    <view class="category-picker-content" catchtap="preventBubble">
      <view class="category-picker-header">
        <text class="category-picker-title">选择类别</text>
        <view class="close-btn" bindtap="hideCategoryPicker">×</view>
      </view>
      <!-- 将类别选项包装在scroll-view中 -->
      <scroll-view scroll-y="true" style="max-height: 800rpx;" class="category-options-scroll">
        <view class="category-options">
          <!-- 自定义选项放在最上方并突出显示 -->
          <view class="category-option custom-option {{tempSelectedCategory === '自定义' ? 'selected' : ''}}"
                bindtap="selectCategory" data-category="自定义">自定义</view>
          <!-- 动态显示所有用户类别 -->
          <block wx:for="{{userCategories}}" wx:key="*this">
            <view class="category-option {{tempSelectedCategory === item ? 'selected' : ''}}"
                  bindtap="selectCategory" data-category="{{item}}">{{item}}</view>
          </block>
        </view>
      </scroll-view>

      <!-- 自定义类别输入框 -->
      <view class="custom-category-input" wx:if="{{tempSelectedCategory === '自定义'}}">
        <input class="edit-input" type="text" value="{{customCategoryValue}}"
               bindinput="onCustomCategoryInput" placeholder="请输入自定义类别"/>
      </view>

      <view class="category-picker-actions">
        <button class="action-btn cancel" bindtap="hideCategoryPicker">取消</button>
        <button class="action-btn confirm" bindtap="confirmCategoryPicker">确认</button>
      </view>
    </view>
  </view>

  <!-- 日期选择器弹窗 -->
  <view class="date-picker-overlay" wx:if="{{showDatePicker}}">
    <view class="date-picker-content" catchtap="preventBubble">
      <view class="date-picker-header">
        <text class="date-picker-title">{{datePickerTitle}}</text>
        <view class="close-btn" bindtap="hideDatePicker">×</view>
      </view>
      <view class="date-picker-body">
        <picker mode="date" value="{{datePickerValue}}" bindchange="onDatePickerChange">
          <view class="date-picker-current">{{datePickerValue || '请选择日期'}}</view>
        </picker>
      </view>
      <view class="date-picker-actions">
        <button class="action-btn cancel" bindtap="hideDatePicker">取消</button>
        <button class="action-btn confirm" bindtap="confirmDatePicker">确认</button>
      </view>
    </view>
  </view>

  <!-- 季节选择器弹窗 -->
  <view class="season-picker-overlay" wx:if="{{showSeasonPicker}}">
    <view class="season-picker-content" catchtap="preventBubble">
      <view class="season-picker-header">
        <text class="season-picker-title">选择季节</text>
        <view class="close-btn" bindtap="hideSeasonPicker">×</view>
      </view>
      <!-- 添加清除按钮 -->
      <view class="clear-season-btn" bindtap="clearSeasonSelection">
        清除季节选择
      </view>
      <view class="season-options">
        <view class="season-option {{seasonIsSelected['春季'] ? 'selected' : ''}}"
              bindtap="selectSeason" data-season="春季">
          <icon type="{{seasonIsSelected['春季'] ? 'success' : 'circle'}}" size="20" color="{{seasonIsSelected['春季'] ? '#07c160' : '#999'}}"></icon>
          <text>春季</text>
        </view>
        <view class="season-option {{seasonIsSelected['夏季'] ? 'selected' : ''}}"
              bindtap="selectSeason" data-season="夏季">
          <icon type="{{seasonIsSelected['夏季'] ? 'success' : 'circle'}}" size="20" color="{{seasonIsSelected['夏季'] ? '#07c160' : '#999'}}"></icon>
          <text>夏季</text>
        </view>
        <view class="season-option {{seasonIsSelected['秋季'] ? 'selected' : ''}}"
              bindtap="selectSeason" data-season="秋季">
          <icon type="{{seasonIsSelected['秋季'] ? 'success' : 'circle'}}" size="20" color="{{seasonIsSelected['秋季'] ? '#07c160' : '#999'}}"></icon>
          <text>秋季</text>
        </view>
        <view class="season-option {{seasonIsSelected['冬季'] ? 'selected' : ''}}"
              bindtap="selectSeason" data-season="冬季">
          <icon type="{{seasonIsSelected['冬季'] ? 'success' : 'circle'}}" size="20" color="{{seasonIsSelected['冬季'] ? '#07c160' : '#999'}}"></icon>
          <text>冬季</text>
        </view>
      </view>
      <view class="season-picker-actions">
        <button class="action-btn cancel" bindtap="hideSeasonPicker">取消</button>
        <button class="action-btn confirm" bindtap="confirmSeasonPicker">确认</button>
      </view>
    </view>
  </view>

  <!-- 衣柜选择器弹窗 -->
  <view class="season-picker-overlay" wx:if="{{showWardrobePicker}}">
    <view class="season-picker-content" catchtap="preventBubble">
      <view class="season-picker-header">
        <text class="season-picker-title">选择衣柜</text>
        <view class="close-btn" bindtap="hideWardrobePicker">×</view>
      </view>
      <scroll-view scroll-y="true" class="wardrobe-options-scroll" catchtouchmove="preventBubble">
        <view class="season-options">
          <block wx:for="{{wardrobes}}" wx:key="_id">
            <view class="season-option {{wardrobeIsSelected[item._id] ? 'selected' : ''}}"
                  bindtap="selectWardrobe" data-wardrobeid="{{item._id}}">
              <icon type="{{wardrobeIsSelected[item._id] ? 'success' : 'circle'}}" size="20" color="{{wardrobeIsSelected[item._id] ? '#07c160' : '#999'}}"></icon>
              <text>{{item.name}}</text>
            </view>
          </block>
        </view>
      </scroll-view>
      <view class="season-picker-actions">
        <button class="action-btn cancel" bindtap="hideWardrobePicker">取消</button>
        <button class="action-btn confirm" bindtap="confirmWardrobePicker">确认</button>
      </view>
    </view>
  </view>

  <!-- 新增排序选择弹窗 -->
  <view class="sort-picker-modal" wx:if="{{showSortPicker}}" bindtap="hideSortPicker">
    <view class="sort-picker-content" catchtap="preventBubble">
      <view class="sort-picker-header">
        <text class="sort-picker-title">选择排序方式</text>
        <view class="close-btn" bindtap="hideSortPicker">×</view>
      </view>
      <view class="sort-picker-body">
        <view class="sort-option {{currentSortType === '' ? 'active' : ''}}"
              bindtap="selectSortType"
              data-sort-type="">
          <text>默认排序</text>
        </view>
        <block wx:for="{{sortOptions}}" wx:key="value">
          <view class="sort-option {{currentSortType === item.value ? 'active' : ''}}"
                bindtap="selectSortType"
                data-sort-type="{{item.value}}">
            <text>{{item.label}}</text>
            <view class="sort-direction" wx:if="{{currentSortType === item.value}}">
              <view class="sort-direction-btn {{sortAscending ? 'active' : ''}}"
                    catchtap="toggleSortDirection"
                    data-direction="asc">
                <text>↑</text>
              </view>
              <view class="sort-direction-btn {{!sortAscending ? 'active' : ''}}"
                    catchtap="toggleSortDirection"
                    data-direction="desc">
                <text>↓</text>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>
</view>

<!-- 排序选择器弹窗 -->
<view class="sort-picker-overlay" wx:if="{{showSortPicker}}">
  <view class="sort-picker-content" catchtap="preventBubble">
    <view class="sort-picker-header">
      <text class="sort-picker-title">选择排序方式</text>
      <view class="close-btn" bindtap="hideSortPicker">×</view>
    </view>
    <view class="sort-options">
      <!-- 动态显示所有排序选项 -->
      <block wx:for="{{sortOptions}}" wx:key="value">
        <view class="sort-option {{currentSortType === item.value ? 'selected' : ''}}"
              bindtap="selectSortType" data-sort-type="{{item.value}}">
          <view class="sort-option-label">{{item.label}}</view>
          <!-- 方向指示器，仅在选中时显示 -->
          <view class="sort-direction" wx:if="{{currentSortType === item.value}}">
            <view class="direction-btn {{sortAscending ? 'active' : ''}}"
                  catchtap="toggleSortDirection" data-direction="asc">↑</view>
            <view class="direction-btn {{!sortAscending ? 'active' : ''}}"
                  catchtap="toggleSortDirection" data-direction="desc">↓</view>
          </view>
        </view>
      </block>
    </view>
  </view>
</view>

<!-- 奖励弹窗 -->
<view class="reward-modal-overlay" wx:if="{{showRewardModal}}">
  <view class="reward-modal-content">
    <view class="reward-image-container">
      <image class="reward-image" src="{{rewardImage}}" mode="aspectFit" binderror="handleImageError"></image>
    </view>
    <view class="reward-message">{{rewardMessage}}</view>
    <button class="reward-confirm-btn" bindtap="confirmReward">确认</button>
  </view>
</view>

<!-- 待办事项弹窗 -->
<view class="todo-modal-overlay" wx:if="{{showTodoModal}}">
  <view class="todo-modal-content" catchtap="preventBubble">
    <view class="todo-modal-header">
      <text class="todo-modal-title">{{editingTodoIndex >= 0 ? '编辑待办事项' : '添加待办事项'}}</text>
      <view class="close-btn" bindtap="hideTodoModal">×</view>
    </view>
    <view class="todo-modal-body">
      <view class="todo-form">
        <view class="todo-form-row">
          <view class="todo-form-label">事项内容</view>
          <input class="todo-form-input" type="text" value="{{currentTodo.title}}"
                 bindinput="onTodoTitleInput" data-field="todoTitle" bindtap="handleIOSFocus"
                 focus="{{editingClothing.todoTitleFocus}}" placeholder="请输入待办事项内容" maxlength="50"/>
        </view>
        <view class="todo-form-row">
          <view class="todo-form-label">截止日期</view>
          <picker mode="date" value="{{currentTodo.dueDate}}" bindchange="onTodoDateChange">
            <view class="todo-date-picker">
              {{currentTodo.dueDate || '选择日期'}}
            </view>
          </picker>
        </view>
      </view>
    </view>
    <view class="todo-modal-actions">
      <button class="action-btn cancel" bindtap="hideTodoModal">取消</button>
      <button class="action-btn confirm" bindtap="saveTodo">保存</button>
    </view>
  </view>
</view>

<!-- 批量编辑弹窗 -->
<view class="batch-edit-modal-overlay" wx:if="{{showBatchEditModal}}">
  <view class="batch-edit-modal-content" catchtap="preventBubble">
    <view class="batch-edit-modal-header">
      <text class="batch-edit-modal-title">批量编辑 ({{selectedClothes.length}}件衣物)</text>
      <view class="close-btn" bindtap="hideBatchEditModal">×</view>
    </view>
    <view class="batch-edit-modal-body">
      <view class="batch-edit-tip">注意：只有填写的字段会被更新，空白字段不会修改原有值</view>

      <!-- 基本信息 -->
      <view class="edit-section">
        <view class="edit-row">
          <view class="edit-label">类别</view>
          <view class="edit-value" bindtap="showBatchCategoryPicker">
            {{batchEditData.category || '不修改'}}
            <text class="arrow-right">▶</text>
          </view>
        </view>
        <view class="edit-row">
          <view class="edit-label">细分类</view>
          <input class="edit-input" type="text" value="{{batchEditData.type_detail}}"
                 bindinput="onBatchEditChange" data-field="type_detail"
                 placeholder="不修改"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">颜色</view>
          <input class="edit-input" type="text" value="{{batchEditData.color}}"
                 bindinput="onBatchEditChange" data-field="color"
                 placeholder="不修改"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">风格</view>
          <input class="edit-input" type="text" value="{{batchEditData.style}}"
                 bindinput="onBatchEditChange" data-field="style"
                 placeholder="不修改"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">尺码</view>
          <input class="edit-input" type="text" value="{{batchEditData.size}}"
                 bindinput="onBatchEditChange" data-field="size"
                 placeholder="不修改"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">品牌</view>
          <input class="edit-input" type="text" value="{{batchEditData.brand}}"
                 bindinput="onBatchEditChange" data-field="brand"
                 placeholder="不修改"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">购买渠道</view>
          <input class="edit-input" type="text" value="{{batchEditData.purchaseChannel}}"
                 bindinput="onBatchEditChange" data-field="purchaseChannel"
                 placeholder="不修改"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">存储位置</view>
          <input class="edit-input" type="text" value="{{batchEditData.storageLocation}}"
                 bindinput="onBatchEditChange" data-field="storageLocation"
                 placeholder="不修改"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">材质</view>
          <input class="edit-input" type="text" value="{{batchEditData.material}}"
                 bindinput="onBatchEditChange" data-field="material"
                 placeholder="不修改"/>
        </view>
        <view class="edit-row">
          <view class="edit-label">季节</view>
          <view class="edit-value" bindtap="showBatchSeasonPicker">
            {{batchEditData.season || '不修改'}}
            <text class="arrow-right">▶</text>
          </view>
        </view>
        <view class="edit-row">
          <view class="edit-label">所属衣柜</view>
          <view class="edit-value" bindtap="showBatchWardrobePicker">
            {{batchEditData.wardrobeName || '不修改'}}
            <text class="arrow-right">▶</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="batch-edit-actions">
      <button class="action-btn cancel" bindtap="hideBatchEditModal">取消</button>
      <button class="action-btn save" bindtap="saveBatchEdit">保存</button>
    </view>
  </view>

  <!-- 图片编辑器组件 -->
  <image-editor
    show="{{showImageEditor}}"
    imageUrl="{{editingImageUrl}}"
    bind:confirm="onImageEditConfirm"
    bind:cancel="onImageEditCancel">
  </image-editor>
</view>