/* 页面容器 */
.page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}

/* 勋章容器 */
.medals-container {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

/* 勋章分区 */
.medals-section {
  background-color: white;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.medals-section-title {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
}

.medals-section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #a4e75a;
  border-radius: 4rpx;
}

/* 勋章网格 */
.medals-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
  justify-content: flex-start;
}

/* 勋章项 */
.medal-item {
  width: 200rpx;
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.medal-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.medal-item-unearned {
  background-color: rgba(255, 255, 255, 0.5);
}

/* 勋章图片容器 */
.medal-image-container {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.medal-image {
  width: 100rpx;
  height: 100rpx;
  object-fit: contain;
  transition: transform 0.3s ease;
  cursor: pointer;
  position: relative;
  filter: drop-shadow(0 0 5rpx rgba(255, 215, 0, 0.3));
}

.medal-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.9) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-25deg);
  animation: shimmer 4s infinite ease-in-out;
  z-index: 2;
  box-shadow: 0 0 10rpx 3rpx rgba(255, 255, 255, 0.2);
}

.medal-image:active {
  transform: scale(0.95);
}

.medal-image:active::after {
  opacity: 1;
}

.medal-image-unearned {
  opacity: 0.5;
  filter: grayscale(100%);
}

.medal-image-unearned::after {
  display: none;
}

/* 勋章名称 */
.medal-name {
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 5rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* 勋章日期 */
.medal-date {
  font-size: 22rpx;
  text-align: center;
  color: #999;
}

/* 勋章提示 */
.medal-hint {
  font-size: 22rpx;
  text-align: center;
  color: #999;
  font-style: italic;
}

/* 无勋章提示 */
.no-medals-message {
  width: 100%;
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}

/* 勋章详情模态框 */
.medal-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.medal-detail-modal.visible {
  opacity: 1;
  visibility: visible;
}

.medal-detail-container {
  width: 85%;
  max-width: 650rpx;
  background-color: white;
  border-radius: 20rpx;
  padding: 70rpx 50rpx 50rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
  position: relative;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 勋章详情标题 */
.medal-detail-header {
  text-align: center;
  margin-bottom: 50rpx;
  width: 100%;
}

.medal-detail-title {
  font-size: 42rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  background-color: #000;
  color: #fff !important;
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  position: relative;
}

.medal-title-text {
  display: inline-block;
  padding: 0 5rpx;
}

/* Decorative horizontal lines */
.medal-detail-title::before,
.medal-detail-title::after {
  content: '';
  display: inline-block;
  width: 70rpx;
  height: 3rpx;
  background: linear-gradient(90deg, rgba(255,255,255,0.2), #fff, rgba(255,255,255,0.2));
  margin: 0 15rpx;
  position: relative;
  vertical-align: middle;
  box-shadow: 0 0 5rpx rgba(255, 255, 255, 0.7);
}

.medal-detail-title::before {
  margin-right: 20rpx;
}

.medal-detail-title::after {
  margin-left: 20rpx;
}

/* 勋章详情图片 */
.medal-detail-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;
  position: relative;
}

/* Removed the gold outer glow */

.medal-detail-image {
  width: 320rpx;
  height: 320rpx;
  object-fit: contain;
  transition: transform 0.3s ease;
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.medal-detail-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.9) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-25deg);
  animation: shimmer 4s infinite ease-in-out;
  box-shadow: 0 0 15rpx 5rpx rgba(255, 255, 255, 0.3);
}

@keyframes shimmer {
  0% {
    left: -150%;
  }
  50% {
    left: 150%;
  }
  50.01% {
    left: -150%;
  }
  100% {
    left: -150%;
  }
}

/* Removed the gold glow animation */

.medal-detail-image:active {
  transform: scale(0.95);
}

.medal-detail-image-unearned {
  opacity: 0.5;
  filter: grayscale(100%);
}

.medal-detail-image-unearned::after {
  display: none;
}

.medal-image-hint {
  font-size: 24rpx;
  color: #888;
  margin-top: 20rpx;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 勋章信息卡片 - 新样式 */
.medal-detail-info-card {
  width: 90%;
  background-color: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 勋章描述容器 */
.medal-description-container {
  font-size: 32rpx;
  line-height: 1.6;
  text-align: center;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

/* 勋章提示容器 */
.medal-hint-container {
  font-size: 28rpx;
  text-align: center;
  color: #666;
  font-style: italic;
  margin-bottom: 20rpx;
}

/* 勋章编号容器 */
.medal-number-container {
  background-color: #333;
  border-radius: 10rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  margin-top: 20rpx;
}

/* 勋章全局编号 */
.medal-global-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #FFD700;
  text-align: center;
}

/* 获得时间 */
.medal-earned-time {
  font-size: 24rpx;
  color: #ccc;
  text-align: center;
}

/* Removed medal-detail-button styles */

/* 关闭按钮 */
.medal-detail-close-button {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 50rpx;
  color: white;
  cursor: pointer;
  background-color: black;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.3);
  z-index: 10;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.medal-detail-close-button:active {
  transform: scale(0.95);
  background-color: #333;
  box-shadow: 0 3rpx 6rpx rgba(0, 0, 0, 0.2);
}

/* 主题样式 */
.page-autumn {
  /* 秋季主题特定样式 */
}

.page-pinkBlue {
  /* 粉蓝主题特定样式 */
}

.page-blackWhite {
  /* 黑白主题特定样式 */
}
