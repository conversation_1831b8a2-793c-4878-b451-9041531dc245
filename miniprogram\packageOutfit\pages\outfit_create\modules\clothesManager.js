/**
 * 衣物管理模块
 * 负责衣物数据的加载、筛选和分类处理
 */

/**
 * 加载用户的衣物数据
 * @param {string} userOpenId - 用户OpenID
 * @returns {Promise<Object>} 包含衣物数据和分类计数的对象
 */
function loadClothes(userOpenId) {
  return new Promise((resolve, reject) => {
    if (!userOpenId) {
      console.error('[loadClothes] 未获取到用户OpenID，无法获取衣物列表');
      reject(new Error('未获取到用户OpenID，无法获取衣物列表'));
      return;
    }
    
    console.log('[loadClothes] 开始加载用户衣物数据，用户ID:', userOpenId);
    
    // 同时获取类别数据和完整衣物数据
    Promise.all([
      getAllClothingCategories(userOpenId),
      getAllClothes(userOpenId)
    ])
      .then(([categoryData, clothesData]) => {
        console.log('[loadClothes] Promise.all 完成，获取到数据:',
          '类别数据数量:', categoryData.length,
          '衣物数据数量:', clothesData.length);
        
        if (categoryData.length === 0 || clothesData.length === 0) {
          console.warn('[loadClothes] 未找到该用户的衣物数据或数据不完整，OpenID:', userOpenId);
        }
        
        // 处理衣物数据，确保字段一致性
        console.log('[loadClothes] 开始规范化处理衣物数据');
        const clothes = normalizeClothesData(clothesData);
        
        // 检查数据是否一致
        if (clothes.length !== clothesData.length) {
          console.warn('[loadClothes] 规范化处理后的衣物数量与原始数据不一致:',
            '原始数量:', clothesData.length,
            '处理后数量:', clothes.length);
        } else {
          console.log('[loadClothes] 规范化处理完成，数据数量保持一致:', clothes.length);
        }
        
        // 输出一些数据样例以便调试
        if (clothes.length > 0) {
          console.log('[loadClothes] 处理后的第一个衣物数据:', 
            '名称:', clothes[0].name,
            '类别:', clothes[0].category);
        }
        
        // 返回衣物数据和类别数据
        console.log('[loadClothes] 返回最终数据，衣物数量:', clothes.length, '类别数据数量:', categoryData.length);
        resolve({
          clothes,
          categoryData
        });
      })
      .catch(err => {
        console.error('[loadClothes] 获取衣物数据失败:', err);
        reject(err);
      });
  });
}

/**
 * 获取所有衣物的类别信息，解决数据量超过20条的限制
 * @param {String} userOpenId - 用户OpenID
 * @return {Promise<Array>} 包含所有衣物类别信息的Promise
 */
function getAllClothingCategories(userOpenId) {
  return new Promise((resolve, reject) => {
    console.log('[getAllClothingCategories] 开始获取所有衣物类别数据，用户ID:', userOpenId);
    
    // 调用云函数获取所有衣物类别数据
    wx.cloud.callFunction({
      name: 'getAllClothingCategories',
      data: {
        userOpenId: userOpenId
      }
    })
    .then(res => {
      console.log('[getAllClothingCategories] 云函数调用成功，返回数据:', res);
      
      // 检查云函数是否成功执行
      if (!res || !res.result) {
        console.error('[getAllClothingCategories] 云函数返回结果无效:', res);
        reject(new Error('获取衣物类别数据失败，返回结果无效'));
        return;
      }
      
      // 检查云函数执行结果是否成功
      if (!res.result.success) {
        console.error('[getAllClothingCategories] 云函数执行失败:', res.result.message);
        reject(new Error(res.result.message || '获取衣物类别数据失败'));
        return;
      }
      
      const allData = res.result.data || [];
      const total = res.result.total || 0;
      
      console.log('[getAllClothingCategories] 成功获取所有衣物类别数据，总数:', allData.length);
      
      // 验证获取的数据量是否符合预期
      if (allData.length < total) {
        console.warn('[getAllClothingCategories] 获取的类别数据少于预期:',
          '预期:', total,
          '实际:', allData.length,
          '差异:', total - allData.length);
      } else if (allData.length > total) {
        console.warn('[getAllClothingCategories] 获取的类别数据多于预期:',
          '预期:', total,
          '实际:', allData.length,
          '差异:', allData.length - total);
      } else {
        console.log('[getAllClothingCategories] 获取的类别数据与预期一致:', total);
      }
      
      resolve(allData);
    })
    .catch(err => {
      console.error('[getAllClothingCategories] 调用云函数获取衣物类别数据失败:', err);
      
      // 如果云函数调用失败，尝试使用小程序API获取（作为备用方案）
      console.log('[getAllClothingCategories] 尝试使用小程序API作为备用方案');
      fallbackGetAllClothingCategories(userOpenId)
        .then(fallbackData => {
          console.log('[getAllClothingCategories] 备用方案成功获取类别数据，数量:', fallbackData.length);
          resolve(fallbackData);
        })
        .catch(fallbackErr => {
          console.error('[getAllClothingCategories] 备用方案也失败:', fallbackErr);
          reject(fallbackErr);
        });
    });
  });
}

/**
 * 备用方案：使用小程序API获取所有衣物类别数据
 * @param {String} userOpenId - 用户OpenID
 * @return {Promise<Array>} 包含所有衣物类别信息的Promise
 */
function fallbackGetAllClothingCategories(userOpenId) {
  return new Promise((resolve, reject) => {
    const db = wx.cloud.database();
    const MAX_LIMIT = 20; // 小程序端限制为20
    
    console.log('[fallbackGetAllClothingCategories] 开始获取所有衣物类别数据，用户ID:', userOpenId);
    
    // 构建查询条件 - 同时支持_openid和openid两种字段名
    let query = db.command.or([
      { _openid: userOpenId },
      { openid: userOpenId }
    ]);
    
    // 先获取总数，以确定需要分几次获取
    db.collection('clothes')
      .where(query)
      .count()
      .then(res => {
        const total = res.total;
        console.log('[fallbackGetAllClothingCategories] 需要统计的衣物总数:', total);
        
        // 如果没有数据，直接返回空数组
        if (total === 0) {
          console.log('[fallbackGetAllClothingCategories] 没有数据，返回空数组');
          resolve([]);
          return;
        }
        
        // 计算需要分几次获取
        const batchTimes = Math.ceil(total / MAX_LIMIT);
        console.log('[fallbackGetAllClothingCategories] 需要分', batchTimes, '次获取类别数据');
        
        const tasks = [];
        
        // 创建获取任务
        for (let i = 0; i < batchTimes; i++) {
          const skipCount = i * MAX_LIMIT;
          console.log(`[fallbackGetAllClothingCategories] 创建第${i+1}批查询任务, skip:${skipCount}, limit:${MAX_LIMIT}`);
          
          const promise = db.collection('clothes')
            .where(query)
            .skip(skipCount)
            .limit(MAX_LIMIT)
            .field({ category: true }) // 只获取类别字段，减少数据量
            .get()
            .then(res => {
              console.log(`[fallbackGetAllClothingCategories] 第${i+1}批类别数据获取成功，本批数量:`, res.data.length);
              return res;
            });
            
          tasks.push(promise);
        }
        
        // 等待所有任务完成
        Promise.all(tasks)
          .then(results => {
            // 将多次请求的结果合并
            let allData = [];
            results.forEach((result, index) => {
              console.log(`[fallbackGetAllClothingCategories] 合并第${index+1}批类别数据，数量:`, result.data.length);
              allData = allData.concat(result.data);
            });
            
            console.log('[fallbackGetAllClothingCategories] 成功获取所有衣物类别数据，总数:', allData.length);
            
            // 验证获取的数据量是否符合预期
            if (allData.length < total) {
              console.warn('[fallbackGetAllClothingCategories] 获取的类别数据少于预期:',
                '预期:', total,
                '实际:', allData.length,
                '差异:', total - allData.length);
            }
            
            resolve(allData);
          })
          .catch(err => {
            console.error('[fallbackGetAllClothingCategories] 批量获取衣物类别失败:', err);
            reject(err);
          });
      })
      .catch(err => {
        console.error('[fallbackGetAllClothingCategories] 获取衣物总数失败:', err);
        reject(err);
      });
  });
}

/**
 * 获取所有衣物的完整数据，解决数据量超过20条的限制
 * @param {String} userOpenId - 用户OpenID
 * @return {Promise<Array>} 包含所有衣物完整数据的Promise
 */
function getAllClothes(userOpenId) {
  return new Promise((resolve, reject) => {
    console.log('[getAllClothes] 开始获取所有衣物数据，用户ID:', userOpenId);
    
    // 调用云函数获取所有衣物数据
    wx.cloud.callFunction({
      name: 'getAllClothes',
      data: {
        userOpenId: userOpenId
      }
    })
    .then(res => {
      console.log('[getAllClothes] 云函数调用成功，返回数据:', res);
      
      // 检查云函数是否成功执行
      if (!res || !res.result) {
        console.error('[getAllClothes] 云函数返回结果无效:', res);
        reject(new Error('获取衣物数据失败，返回结果无效'));
        return;
      }
      
      // 检查云函数执行结果是否成功
      if (!res.result.success) {
        console.error('[getAllClothes] 云函数执行失败:', res.result.message);
        reject(new Error(res.result.message || '获取衣物数据失败'));
        return;
      }
      
      const allData = res.result.data || [];
      const total = res.result.total || 0;
      
      console.log('[getAllClothes] 成功获取所有衣物完整数据，总数:', allData.length);
      
      // 验证获取的数据量是否符合预期
      if (allData.length < total) {
        console.warn('[getAllClothes] 获取的衣物数量少于预期:',
          '预期:', total,
          '实际:', allData.length,
          '差异:', total - allData.length);
      } else if (allData.length > total) {
        console.warn('[getAllClothes] 获取的衣物数量多于预期:',
          '预期:', total,
          '实际:', allData.length,
          '差异:', allData.length - total);
      } else {
        console.log('[getAllClothes] 获取的衣物数量与预期一致:', total);
      }
      
      // 输出一些数据示例，便于调试
      if (allData.length > 0) {
        console.log('[getAllClothes] 数据示例:', allData[0]);
      }
      
      resolve(allData);
    })
    .catch(err => {
      console.error('[getAllClothes] 调用云函数获取衣物数据失败:', err);
      
      // 如果云函数调用失败，尝试使用小程序API获取（作为备用方案）
      console.log('[getAllClothes] 尝试使用小程序API作为备用方案');
      fallbackGetAllClothes(userOpenId)
        .then(fallbackData => {
          console.log('[getAllClothes] 备用方案成功获取衣物数据，数量:', fallbackData.length);
          resolve(fallbackData);
        })
        .catch(fallbackErr => {
          console.error('[getAllClothes] 备用方案也失败:', fallbackErr);
          reject(fallbackErr);
        });
    });
  });
}

/**
 * 备用方案：使用小程序API获取所有衣物数据
 * @param {String} userOpenId - 用户OpenID
 * @return {Promise<Array>} 包含所有衣物完整数据的Promise
 */
function fallbackGetAllClothes(userOpenId) {
  return new Promise((resolve, reject) => {
    const db = wx.cloud.database();
    const MAX_LIMIT = 20; // 小程序端限制为20
    
    console.log('[fallbackGetAllClothes] 开始获取所有衣物数据，用户ID:', userOpenId);
    
    // 构建查询条件 - 同时支持_openid和openid两种字段名
    let query = db.command.or([
      { _openid: userOpenId },
      { openid: userOpenId }
    ]);
    
    // 先获取总数，以确定需要分几次获取
    db.collection('clothes')
      .where(query)
      .count()
      .then(res => {
        const total = res.total;
        console.log('[fallbackGetAllClothes] 需要获取的衣物总数:', total);
        
        // 如果没有数据，直接返回空数组
        if (total === 0) {
          console.log('[fallbackGetAllClothes] 没有数据，返回空数组');
          resolve([]);
          return;
        }
        
        // 计算需要分几次获取
        const batchTimes = Math.ceil(total / MAX_LIMIT);
        console.log('[fallbackGetAllClothes] 需要分', batchTimes, '次获取数据');
        
        const tasks = [];
        
        // 创建获取任务
        for (let i = 0; i < batchTimes; i++) {
          const skipCount = i * MAX_LIMIT;
          console.log(`[fallbackGetAllClothes] 创建第${i+1}批查询任务, skip:${skipCount}, limit:${MAX_LIMIT}`);
          
          const promise = db.collection('clothes')
            .where(query)
            .skip(skipCount)
            .limit(MAX_LIMIT)
            .orderBy('createTime', 'desc')
            .get()
            .then(res => {
              console.log(`[fallbackGetAllClothes] 第${i+1}批数据获取成功，本批数量:`, res.data.length);
              return res;
            });
            
          tasks.push(promise);
        }
        
        // 等待所有任务完成
        Promise.all(tasks)
          .then(results => {
            // 将多次请求的结果合并
            let allData = [];
            results.forEach((result, index) => {
              console.log(`[fallbackGetAllClothes] 合并第${index+1}批数据，数量:`, result.data.length);
              allData = allData.concat(result.data);
            });
            
            console.log('[fallbackGetAllClothes] 成功获取所有衣物完整数据，总数:', allData.length);
            
            // 验证获取的数据量是否符合预期
            if (allData.length < total) {
              console.warn('[fallbackGetAllClothes] 获取的衣物数量少于预期:',
                '预期:', total,
                '实际:', allData.length,
                '差异:', total - allData.length);
            }
            
            resolve(allData);
          })
          .catch(err => {
            console.error('[fallbackGetAllClothes] 批量获取衣物失败:', err);
            reject(err);
          });
      })
      .catch(err => {
        console.error('[fallbackGetAllClothes] 获取衣物总数失败:', err);
        reject(err);
      });
  });
}

/**
 * 生成测试衣物数据用于开发和测试
 * @returns {Array} 测试衣物数据数组
 */
function generateTestClothes() {
  return [
    {
      _id: 'test1',
      name: '白色T恤',
      category: '上衣',
      type: 'T恤',
      color: '白色',
      tempImageUrl: 'https://picsum.photos/200/200?random=1'
    },
    {
      _id: 'test2',
      name: '牛仔裤',
      category: '裤子',
      type: '牛仔裤',
      color: '蓝色',
      tempImageUrl: 'https://picsum.photos/200/200?random=2'
    },
    {
      _id: 'test3',
      name: '黑色外套',
      category: '外套',
      type: '夹克',
      color: '黑色',
      tempImageUrl: 'https://picsum.photos/200/200?random=3'
    },
    {
      _id: 'test4',
      name: '运动鞋',
      category: '鞋子',
      type: '运动鞋',
      color: '白色',
      tempImageUrl: 'https://picsum.photos/200/200?random=4'
    },
    {
      _id: 'test5',
      name: '花裙子',
      category: '裙子',
      type: '连衣裙',
      color: '花色',
      tempImageUrl: 'https://picsum.photos/200/200?random=5'
    }
  ];
}

/**
 * 标准化衣物数据，确保所有字段存在和一致
 * @param {Array} clothesData - 原始衣物数据
 * @returns {Array} 标准化后的衣物数据
 */
function normalizeClothesData(clothesData) {
  if (!clothesData || !Array.isArray(clothesData)) {
    console.error('无效的衣物数据:', clothesData);
    return [];
  }
  
  return clothesData.map(item => {
    // 确保item是有效对象
    if (!item || typeof item !== 'object') {
      console.warn('跳过无效的衣物数据项');
      return null;
    }
    
    // 确定是否有抠图图片 - 重点关注processedImageFileID
    const hasProcessedImage = !!item.processedImageUrl || !!item.processedImageFileID;
    
    // 正确保存抠图图片URL和文件ID - 首选processedImageFileID
    let processedImageUrl = '';
    if (item.processedImageFileID) {
      processedImageUrl = item.processedImageFileID;
      console.log(`衣物 ${item._id || 'new'} 使用processedImageFileID: ${processedImageUrl}`);
    } else if (item.processedImageUrl) {
      processedImageUrl = item.processedImageUrl;
      console.log(`衣物 ${item._id || 'new'} 使用processedImageUrl: ${processedImageUrl}`);
    }
    
    // 原始图片URL
    const originalImageUrl = (item.imageFileID ? item.imageFileID : null) || 
                            (item.fileID ? item.fileID : null) ||
                            (item.imageUrl ? item.imageUrl : null) ||
                            '';
    
    // 根据是否有抠图，记录优先使用的图片URL
    if (hasProcessedImage) {
      console.log(`衣物 ${item._id || 'new'} 是抠图图片，优先显示抠图`);
    } else {
      console.log(`衣物 ${item._id || 'new'} 是原始图片: ${originalImageUrl.substring(0, 50)}${originalImageUrl.length > 50 ? '...' : ''}`);
    }
    
    return {
      _id: item._id || `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: item.name || item.clothingName || '未命名',
      category: item.category || item.clothingCategory || '未分类',
      type: item.type || item.clothingType || '',
      color: item.color || '',
      style: item.style || '',
      processedImageUrl: processedImageUrl, // 抠图后的图片URL
      processedImageFileID: item.processedImageFileID || '', // 保留原始processedImageFileID
      originalImageUrl: originalImageUrl, // 原始图片URL
      imageFileID: item.imageFileID || '',
      tempImageUrl: item.tempImageUrl || '', // 临时URL，初始为空
      // 添加原始数据和抠图标志
      originalData: item,
      isProcessed: hasProcessedImage // 标记是否为抠图处理过的图片
    };
  }).filter(item => item !== null); // 过滤掉无效项
}

/**
 * 按类别筛选衣物
 * @param {Array} clothes - 衣物数组
 * @param {String} category - 类别名称，如果为null则返回所有衣物
 * @returns {Array} 筛选后的衣物数组
 */
function filterByCategory(clothes, category) {
  if (!clothes || !Array.isArray(clothes)) {
    console.warn('无效的衣物数据用于筛选');
    return [];
  }
  
  // 如果类别为null或undefined或空字符串，返回所有衣物
  if (category === null || category === undefined || category === '') {
    console.log('返回所有衣物，无筛选');
    return clothes;
  }
  
  console.log(`筛选类别: "${category}", 衣物总数: ${clothes.length}`);
  
  // 筛选匹配类别的衣物
  const filtered = clothes.filter(item => {
    // 确保item和item.category存在
    if (!item || !item.category) {
      return false;
    }
    
    // 忽略大小写比较
    return item.category.toLowerCase() === category.toLowerCase();
  });
  
  console.log(`筛选后衣物数量: ${filtered.length}`);
  return filtered;
}

/**
 * 更新类别的计数信息
 * @param {Array} clothes - 衣物数据数组
 * @param {Array} categories - 类别数组
 * @returns {Array} 更新计数后的类别数组
 */
function updateCategoryCounts(clothes, categories) {
  if (!clothes || !Array.isArray(clothes) || !categories || !Array.isArray(categories)) {
    console.warn('[updateCategoryCounts] 无效的数据用于更新类别计数');
    return categories || [];
  }
  
  console.log('[updateCategoryCounts] 开始计算类别数量，衣物数量:', clothes.length, '类别数量:', categories.length);
  
  const updatedCategories = [...categories];
  
  // 重置所有计数
  updatedCategories.forEach(cat => {
    cat.count = 0;
  });
  
  // 先找到全部类别项
  const allCategory = updatedCategories.find(cat => cat.id === 0);
  
  // 使用Map优化类别统计，提高性能
  const categoryMap = new Map();
  // 初始化类别映射
  for (let i = 1; i < updatedCategories.length; i++) {
    if (updatedCategories[i].category) {
      categoryMap.set(updatedCategories[i].category.toLowerCase(), i);
    }
  }
  
  // 统计衣物总数
  let totalCount = 0;
  let uncategorizedCount = 0;
  
  // 计算每个类别的衣物数量
  clothes.forEach(item => {
    if (!item) return;
    
    // 总数统计 - 所有有效衣物
    totalCount++;
    
    // 如果衣物有类别，增加对应类别计数
    if (item.category) {
      const categoryLower = item.category.toLowerCase();
      const categoryIndex = categoryMap.get(categoryLower);
      
      if (categoryIndex !== undefined) {
        updatedCategories[categoryIndex].count++;
      } else {
        console.log(`[updateCategoryCounts] 衣物 ${item._id || item.name} 的类别 ${item.category} 不在预定义类别中`);
        uncategorizedCount++;
      }
    } else {
      uncategorizedCount++;
    }
  });
  
  // 更新全部类别的计数
  if (allCategory) {
    allCategory.count = totalCount;
  }
  
  // 验证总数是否正确，打印详细日志
  const sumOfCategories = updatedCategories.slice(1).reduce((sum, cat) => sum + cat.count, 0);
  if (sumOfCategories !== totalCount) {
    console.warn('[updateCategoryCounts] 类别数量总和与有效衣物总数不匹配:',
      '类别总和:', sumOfCategories,
      '有效衣物总数:', totalCount,
      '未分类衣物:', uncategorizedCount,
      '原始衣物总数:', clothes.length);
  } else {
    console.log('[updateCategoryCounts] 类别数量总和与总数匹配:', totalCount);
  }
  
  console.log('[updateCategoryCounts] 更新后的类别统计:', updatedCategories.map(c => `${c.name}: ${c.count}`).join(', '));
  
  return updatedCategories;
}

module.exports = {
  loadClothes,
  generateTestClothes,
  filterByCategory,
  updateCategoryCounts,
  normalizeClothesData,
  getAllClothingCategories,
  getAllClothes,
  fallbackGetAllClothes,
  fallbackGetAllClothingCategories
};
