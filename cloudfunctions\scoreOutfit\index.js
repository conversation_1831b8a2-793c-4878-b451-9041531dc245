// 云函数入口文件
const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// API配置
const API_URL = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'
const API_KEY = 'sk-144cfa99383f4c29bfe781a7d74452aa' // 注意: 实际开发中应使用环境变量或云环境参数存储密钥

// 云函数入口函数
exports.main = async (event, context) => {
  // 获取图片URL
  const imageUrl = event.imageUrl
  
  if (!imageUrl) {
    return {
      success: false,
      error: '未提供图片URL'
    }
  }
  
  try {
    // 构建请求数据
    const requestData = {
      model: "qwen-vl-max",
      messages: [{
        role: "user",
        content: [
          {
            type: "text",
            text: "请对这套穿搭进行专业评分和点评。评分标准为1-10分，从搭配协调性、色彩搭配、风格统一性、场合适配性等方面进行评价。请以JSON格式返回，包含以下字段：score（总分，1-10的数字）、colorScore（色彩搭配分，1-10的数字）、styleScore（风格统一性分，1-10的数字）、occasionScore（场合适配性分，1-10的数字）、comment（总体评价，100字以内）、styleType（风格类型）、suggestion（改进建议，50字以内）。请确保只返回JSON格式，不要有其他文字说明。"
          },
          {
            type: "image_url",
            image_url: {
              url: imageUrl
            }
          }
        ]
      }]
    }
    
    // 发送API请求
    const response = await axios({
      method: 'post',
      url: API_URL,
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      data: requestData,
      timeout: 30000 // 设置30秒超时
    })
    
    // 解析API响应
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      const content = response.data.choices[0].message.content

      // 尝试从文本中提取JSON
      try {
        // 查找文本中的JSON部分
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        let scoreData = null;
        
        if (jsonMatch) {
          // 尝试解析找到的JSON
          scoreData = JSON.parse(jsonMatch[0]);
          
          return {
            success: true,
            data: scoreData,
            rawResponse: content
          }
        } else {
          // 如果没有找到JSON，使用正则匹配提取信息
          const scoreMatch = content.match(/总分[：:]\s*(\d+)/);
          const colorMatch = content.match(/色彩搭配[：:]\s*(\d+)/);
          const styleMatch = content.match(/风格统一性[：:]\s*(\d+)/);
          const occasionMatch = content.match(/场合适配性[：:]\s*(\d+)/);
          const commentMatch = content.match(/总体评价[：:]\s*([^]*?)(?=改进建议|$)/);
          const styleTypeMatch = content.match(/风格类型[：:]\s*([^]*?)(?=总体评价|$)/);
          const suggestionMatch = content.match(/改进建议[：:]\s*([^]*?)(?=$)/);
          
          scoreData = {
            score: scoreMatch ? parseInt(scoreMatch[1]) : 7,
            colorScore: colorMatch ? parseInt(colorMatch[1]) : 7,
            styleScore: styleMatch ? parseInt(styleMatch[1]) : 7,
            occasionScore: occasionMatch ? parseInt(occasionMatch[1]) : 7,
            comment: commentMatch ? commentMatch[1].trim() : "这是一套协调的穿搭。",
            styleType: styleTypeMatch ? styleTypeMatch[1].trim() : "休闲风格",
            suggestion: suggestionMatch ? suggestionMatch[1].trim() : "可以尝试添加配饰提升整体效果。"
          };
          
          return {
            success: true,
            data: scoreData,
            rawResponse: content
          }
        }
      } catch (jsonError) {
        return {
          success: false,
          error: '无法解析JSON响应',
          rawResponse: content
        }
      }
    }
    
    return {
      success: false,
      error: '无效的API响应',
      rawResponse: response.data
    }
  } catch (error) {
    console.error('API请求错误:', error)
    return {
      success: false,
      error: error.message || '请求失败',
      stack: error.stack
    }
  }
}
