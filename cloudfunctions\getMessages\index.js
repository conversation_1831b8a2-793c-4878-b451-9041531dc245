// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const messagesCollection = db.collection('messages')

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 定义响应结构
    const response = {
      success: true,
      data: [],
      error: null
    }
    
    // 获取所有公开消息
    const queryResult = await messagesCollection
      .where({
        visible: true // 只获取可见的消息
      })
      .orderBy('createTime', 'desc') // 按创建时间降序排列
      .limit(100) // 最多获取100条消息
      .get()
    
    // 处理消息列表
    response.data = queryResult.data.map(msg => {
      const processedMsg = { ...msg }
      
      // 检查是否有图片相关字段
      if (msg.imageUrl) {
        // 如果有直接URL，确保它可用
        processedMsg.hasImage = true
      } else if (msg.imageFileID) {
        // 如果有云存储fileID
        processedMsg.hasImage = true
      }
      
      return processedMsg
    })
    
    return response
  } catch (err) {
    console.error('获取消息列表失败:', err)
    return {
      success: false,
      data: [],
      error: err.message
    }
  }
} 