/* page/wardrobe/add/add.wxss */

.container {
  padding-bottom: 120rpx;
}

.page-title {
  font-size: 36rpx;
  color: #5d4037;
  text-align: center;
  margin-bottom: 40rpx;
  font-weight: 400;
  letter-spacing: 2rpx;
}

/* URL输入弹窗 */
.url-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
}

.url-input-container {
  width: 80%;
  background-color: #f8f5f2;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.url-input-title {
  font-size: 32rpx;
  color: #5d4037;
  text-align: center;
  margin-bottom: 30rpx;
  font-weight: 400;
}

.url-input {
  margin-bottom: 30rpx;
}

.btn-group {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.btn-group .btn {
  flex: 1;
  margin: 0;
}

/* 图片上传区域 */
.image-upload-section {
  margin-bottom: 40rpx;
}

.image-container {
  position: relative;
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #efebe9;
  margin-bottom: 20rpx;
}

.clothes-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  display: flex;
  justify-content: flex-end;
}

.rechoose-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
}

.upload-placeholder {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  background-color: #efebe9;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #d7ccc8;
}

.upload-icon {
  width: 80rpx;
  height: 80rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23a1887f'%3E%3Cpath d='M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z' /%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 20rpx;
}

.upload-text {
  font-size: 28rpx;
  color: #8d6e63;
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(105, 87, 76, 0.05);
}

.section-title {
  font-size: 32rpx;
  color: #5d4037;
  margin-bottom: 30rpx;
  font-weight: 400;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 6rpx;
  height: 30rpx;
  background-color: #8d6e63;
  border-radius: 3rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #5d4037;
  margin-bottom: 16rpx;
}

.required {
  color: #F44336;
  margin-left: 6rpx;
}

.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border: 1px solid #d7ccc8;
  border-radius: 8rpx;
  background-color: #ffffff;
}

.picker-text {
  font-size: 28rpx;
  color: #5d4037;
}

.picker-arrow {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238d6e63'%3E%3Cpath d='M7,10L12,15L17,10H7Z' /%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 标签相关样式 */
.tag-input-container {
  display: flex;
  gap: 20rpx;
}

.tag-input {
  flex: 1;
}

.add-tag-btn {
  padding: 0 30rpx;
  line-height: 84rpx;
  background-color: #8d6e63;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.tag {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #efebe9;
  border-radius: 8rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  color: #5d4037;
}

.tag-remove {
  margin-left: 10rpx;
  width: 32rpx;
  height: 32rpx;
  line-height: 28rpx;
  text-align: center;
  border-radius: 50%;
  background-color: #d7ccc8;
  color: #5d4037;
  font-size: 28rpx;
  font-weight: bold;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #f8f5f2;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 20rpx;
  z-index: 10;
}

.bottom-buttons .btn {
  flex: 1;
  margin: 0;
}
