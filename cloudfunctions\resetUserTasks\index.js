// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 获取用户openid，优先使用传入的，否则使用上下文中的
  const userOpenId = event.userOpenId || wxContext.OPENID
  
  // 获取任务ID
  const { taskId } = event
  
  // 参数验证
  if (!userOpenId) {
    return {
      success: false,
      error: '无法获取用户ID'
    }
  }
  
  try {
    // 查询用户任务进度
    const tasksCollection = db.collection('tasks')
    let userTasks = await tasksCollection.where({
      userOpenId: userOpenId,
      taskId: taskId
    }).get()
    
    // 如果用户没有任务记录，返回成功（没有需要重置的内容）
    if (!userTasks.data || userTasks.data.length === 0) {
      return {
        success: true,
        message: '没有找到需要重置的任务记录'
      }
    }
    
    // 更新现有任务记录，清空hasDone数组
    const _id = userTasks.data[0]._id
    
    const updateData = {
      hasDone: [], // 清空hasDone数组
      currentProgress: 0, // 重置当前进度
      updateTime: db.serverDate()
    }
    
    // 更新数据库
    await tasksCollection.doc(_id).update({
      data: updateData
    })
    
    // 返回成功结果
    return {
      success: true,
      message: '任务进度已重置',
      data: {
        taskId: taskId,
        hasDone: [],
        currentProgress: 0
      }
    }
  } catch (error) {
    console.error('重置用户任务进度失败:', error)
    
    return {
      success: false,
      error: error.message || '重置任务进度失败'
    }
  }
}
