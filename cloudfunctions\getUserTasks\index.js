// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 获取用户openid，优先使用传入的，否则使用上下文中的
  const userOpenId = event.userOpenId || wxContext.OPENID
  
  if (!userOpenId) {
    return {
      success: false,
      error: '无法获取用户ID'
    }
  }
  
  try {
    // 查询用户任务进度
    const tasksCollection = db.collection('tasks')
    let userTasks = await tasksCollection.where({
      userOpenId: userOpenId
    }).get()
    
    // 如果用户没有任务记录，创建一个新的
    if (!userTasks.data || userTasks.data.length === 0) {
      const defaultTaskData = {
        userOpenId: userOpenId,
        totalProgress: 1, // 默认从第一个主题开始
        currentProgress: 0, // 默认从第0步开始
        taskId: 'task1', // 默认任务ID
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }
      
      // 创建新记录
      await tasksCollection.add({
        data: defaultTaskData
      })
      
      return {
        success: true,
        data: defaultTaskData
      }
    }
    
    // 检查任务记录是否包含taskId，如果没有则添加
    const userTask = userTasks.data[0]
    if (!userTask.taskId) {
      // 更新记录，添加taskId
      const taskId = `task${userTask.totalProgress || 1}`
      await tasksCollection.doc(userTask._id).update({
        data: {
          taskId: taskId,
          updateTime: db.serverDate()
        }
      })
      
      // 更新返回数据
      userTask.taskId = taskId
    }
    
    // 返回用户任务进度
    return {
      success: true,
      data: userTask
    }
  } catch (error) {
    console.error('获取用户任务进度失败:', error)
    
    return {
      success: false,
      error: error.message || '获取任务进度失败'
    }
  }
} 