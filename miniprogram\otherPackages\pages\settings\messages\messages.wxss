/* page/settings/messages/messages.wxss */
page {
  /* 秋季色谱 */
  --cowhide-cocoa: #442D1C;
  --spiced-wine: #74301C;
  --toasted-caramel: #84592B;
  --olive-harvest: #9D9167;
  --golden-batter: #E8D1A7;

  /* 粉蓝色谱 */
  --pink-dark: #D47C99;
  --pink-medium: #EEA0B2;
  --pink-light: #F9C9D6;
  --blue-light: #CBE0F9;
  --blue-medium: #97C8E5;
  --blue-dark: #5EA0D0;

  /* 黑白色谱 */
  --black: #000000;
  --dark-gray: #333333;
  --medium-gray: #666666;
  --light-gray: #CCCCCC;
  --white: #FFFFFF;
  --off-white: #dee3ec;

  background-color: #f7f7f7;
  height: 100%;
  width: 100%;
}

/* 页面容器 */
.page {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 32rpx;
  position: relative;
}

/* 主题样式 */
.page-autumn {
  background-color: var(--golden-batter);
}

.page-pinkBlue {
  background-color: var(--pink-light);
  background-image: linear-gradient(to bottom, white, var(--pink-light));
}

.page-blackWhite {
  background-color: var(--off-white);
  background-image: none;
}

/* 装饰元素 */
.decoration-element {
  position: absolute;
  z-index: 1;
  border-radius: 50%;
  opacity: 0.6;
}

.decoration-1 {
  top: 10%;
  right: 10%;
  width: 200rpx;
  height: 200rpx;
}

.decoration-2 {
  top: 30%;
  left: 5%;
  width: 160rpx;
  height: 160rpx;
}

.decoration-3 {
  bottom: 15%;
  right: 20%;
  width: 180rpx;
  height: 180rpx;
}

/* 页面内容 */
.page-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* 页面标题 */
.page-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 清空按钮 */
.clear-button {
  font-size: 26rpx;
  font-weight: normal;
  padding: 10rpx 20rpx;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 30rpx;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 5rpx solid rgba(0, 0, 0, 0.1);
  border-top-color: var(--spiced-wine);
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
}

.page-pinkBlue .loading-spinner {
  border-top-color: var(--pink-dark);
}

.page-blackWhite .loading-spinner {
  border-top-color: var(--black);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 消息列表 */
.message-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 消息项 */
.message-item {
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.3s ease;
}

.message-item:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 消息图片图标 */
.message-image-icon {
  font-size: 24rpx;
  margin-left: 12rpx;
}

/* 未读消息提示点 */
.unread-dot {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

/* 已读和未读消息样式差异 */
.message-unread {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.message-read {
  opacity: 0.8;
}

/* 消息头部 */
.message-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 16rpx;
}

.message-title {
  font-size: 30rpx;
  font-weight: bold;
  padding-right: 24rpx;
  display: flex;
  align-items: center;
}

.message-time {
  font-size: 24rpx;
  opacity: 0.7;
  margin-top: 8rpx;
}

/* 消息内容 */
.message-content {
  font-size: 28rpx;
  line-height: 1.5;
  word-wrap: break-word;
}

/* 空消息提示 */
.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
  opacity: 0.7;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 30rpx;
}

/* 消息详情弹窗样式 */
.message-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.message-detail-container {
  width: 80%;
  max-width: 600rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  border-radius: 24rpx;
  overflow: hidden;
  padding: 32rpx;
  position: relative;
  box-sizing: border-box;
}

.message-detail-header {
  margin-bottom: 24rpx;
}

.message-detail-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.message-detail-time {
  font-size: 24rpx;
  opacity: 0.7;
}

.message-detail-content {
  font-size: 30rpx;
  line-height: 1.6;
  margin-bottom: 32rpx;
  flex: 1;
  overflow-y: auto;
}

.message-detail-image-container {
  width: 100%;
  margin: 20rpx 0 30rpx;
  display: flex;
  justify-content: center;
  overflow: hidden;
  border-radius: 12rpx;
}

.message-detail-image {
  width: 100%;
  height: auto;
  border-radius: 12rpx;
}

/* 图片加载中样式 */
.image-loading {
  width: 100%;
  height: 300rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 12rpx;
}

.image-loading .loading-spinner {
  width: 50rpx;
  height: 50rpx;
  border-width: 4rpx;
}

.image-loading .loading-text {
  font-size: 26rpx;
  margin-top: 16rpx;
  opacity: 0.7;
}

.message-detail-button {
  height: 80rpx;
  border-radius: 40rpx;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  margin-top: 16rpx;
  transition: all 0.3s ease;
}

.message-detail-button:active {
  opacity: 0.8;
  transform: scale(0.98);
}