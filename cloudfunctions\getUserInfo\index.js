// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  // 优先使用传入的用户ID，如果没有则使用当前调用者的OpenID
  const userOpenId = event.userOpenId || wxContext.OPENID

  if (!userOpenId) {
    return {
      success: false,
      error: 'openid_missing',
      message: '未能获取有效的用户ID'
    }
  }

  try {
    console.log(`正在获取用户信息，OpenID: ${userOpenId}`)

    // 查询用户信息
    const userCollection = db.collection('users')
    const userResult = await userCollection.where({
      _openid: userOpenId
    }).get()

    // 查询用户的衣物数量
    const clothesCollection = db.collection('clothes')
    const clothesResult = await clothesCollection.where({
      _openid: userOpenId
    }).count()

    const clothesCount = clothesResult.total || 0

    // 检查用户是否存在
    if (userResult.data && userResult.data.length > 0) {
      const userData = userResult.data[0]

      // 更新用户的最后登录时间
      await userCollection.doc(userData._id).update({
        data: {
          lastLogin: new Date(),
          updatedAt: new Date()
        }
      })

      // 返回用户信息
      return {
        success: true,
        data: {
          ...userData,
          clothesCount,
          isNewUser: false // 已有用户记录，不是新用户
        }
      }
    } else {
      // 新用户，创建用户记录
      const newUser = {
        _openid: userOpenId,
        createTime: new Date(),
        lastLogin: new Date(),
        updatedAt: new Date(),
        nickName: '新用户',
        avatarUrl: '',
        gender: 0,
        title: '初级养猫人', // 当前选中的称号
        titles: ['初级养猫人'], // 称号数组，包含用户可选的称号
        catEnergy: 50, // 猫咪体力值，默认值为50，最大值为500
        memberType: 'VIP', // 会员类型，默认为VIP会员
        memberExpireDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 会员过期时间，默认为15天后
        preferences: {
          theme: 'light',
          notificationsEnabled: true
        },
        clothesCount: 0,
        outfitsCount: 0,
        clothesLimit: 30,
        outfitsLimit: 10
      }

      const createResult = await userCollection.add({
        data: newUser
      })

      return {
        success: true,
        data: {
          ...newUser,
          _id: createResult._id,
          clothesCount,
          isNewUser: true // 新创建的用户
        }
      }
    }
  } catch (err) {
    console.error('获取用户信息失败:', err)
    return {
      success: false,
      error: 'db_error',
      message: '数据库操作失败',
      detail: err.message
    }
  }
}