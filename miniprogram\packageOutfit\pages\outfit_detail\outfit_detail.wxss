/* page/wardrobe/outfit/outfit_detail/outfit_detail.wxss */

/* Color variables based on autumn palette */
page {
  --cowhide-cocoa: #442D1C;
  --spiced-wine: #74301C;
  --toasted-caramel: #84592B;
  --olive-harvest: #9D9167;
  --golden-batter: #E8D1A7;
}

/* 全局容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  padding: 32rpx;
  position: relative;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 5rpx solid;
  border-radius: 50%;
  margin-bottom: 20rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  letter-spacing: 1rpx;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding-bottom: 120rpx;
  position: relative;
  z-index: 1;
}

/* 顶部区域 */
.top-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
}

/* 返回按钮 */
.back-button {
  display: flex;
  align-items: center;
  padding: 10rpx;
}

.back-icon {
  width: 20rpx;
  height: 20rpx;
  border-top: 3rpx solid;
  border-right: 3rpx solid;
  transform: rotate(-135deg);
  margin-right: 10rpx;
}

/* 搭配标题 */
.outfit-title {
  font-size: 34rpx;
  font-weight: 600;
  flex: 1;
  text-align: center;
  margin: 0 20rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.share-button, .edit-button, .delete-button {
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-button {
  background-color: #84592B; /* 使用toasted_caramel颜色 */
  color: #E8D1A7; /* 使用golden_batter颜色 */
}

/* 搭配详情卡片 */
.outfit-detail-card {
  width: 100%;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: visible;
  position: relative;
  z-index: 2;
}

/* 小屏幕设备的搭配详情卡片 */
.outfit-detail-card.small-screen {
  flex-direction: column;
}

/* 搭配预览图 */
.outfit-preview {
  width: 100%; /* 宽度占满父容器 */
  /* 高度现在由内联样式动态控制 */
  border-radius: 20rpx;
  overflow: hidden;
  margin-right: 0;
  flex-shrink: 0;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.12);
}

/* 小屏幕设备的搭配预览图 - 高度现在由JS控制 */
.outfit-preview.small-screen {
  width: 100%;
  margin-right: 0;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 18rpx;
  position: relative;
  z-index: 3;
  transition: transform 0.3s ease;
}

.preview-image:active {
  transform: scale(0.98);
}

/* AI评分标签 */
.ai-score-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.ai-score-value {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  line-height: 1;
}

.ai-score-label {
  font-size: 18rpx;
  color: white;
  margin-top: 4rpx;
}

/* 搭配信息 */
.outfit-info {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 200rpx;
  max-width: 100%;
  overflow: visible;
  padding: 10rpx 5rpx;
}

/* 小屏幕设备的搭配信息 */
.outfit-info.small-screen {
  width: 100%;
}

.info-row {
  display: flex;
  margin-bottom: 15rpx;
  flex-wrap: wrap;
}

.info-label {
  font-size: 26rpx;
  font-weight: 500;
  min-width: 140rpx;
  max-width: 140rpx;
}

/* 小屏幕上标签样式调整 */
.outfit-detail-card.small-screen .info-label {
  min-width: 120rpx;
  max-width: 120rpx;
}

.info-value {
  font-size: 26rpx;
  flex: 1;
  min-width: 0;
  word-break: break-all;
}

/* 标签 */
.tags-container {
  display: flex;
  margin-top: 10rpx;
  flex-wrap: wrap;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

.tag {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 15rpx;
  margin-bottom: 10rpx;
}

/* 添加覆盖样式，调整标签在小屏下的外观 */
.outfit-detail-card.small-screen .tag {
  margin-right: 10rpx;
  margin-bottom: 8rpx;
  padding: 4rpx 12rpx;
}

/* 段落标题 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

/* 搭配组成部分 */
.outfit-items-section {
  margin-bottom: 30rpx;
  overflow: visible;
}

.outfit-items-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 10rpx;
  overflow: visible;
}

.outfit-item-card {
  border-radius: 20rpx;
  overflow: visible;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  background-color: transparent;
  position: relative;
  z-index: 2;
}

.item-image {
  width: 100%;
  height: 240rpx;
  object-fit: contain;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  position: relative;
  z-index: 3;
}

.item-info {
  padding: 15rpx;
  /* 移除CSS变量，使用内联样式 */
  /* background-color: var(--olive-harvest); */
  border-radius: 0 0 20rpx 20rpx; /* 只对下方应用圆角 */
  width: 100%;
  box-sizing: border-box;
}

.item-name {
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.item-type {
  font-size: 22rpx;
  opacity: 0.9;
}

/* AI评分按钮 */
.ai-score-button-container {
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
}

.ai-score-button {
  padding: 15rpx 30rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.ai-score-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* AI评分结果 */
.ai-score-section {
  margin-bottom: 30rpx;
  overflow: visible;
}

.ai-score-cards-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0 10rpx;
}

.ai-score-card {
  border-radius: 24rpx;
  overflow: visible;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.ai-score-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
  font-size: 28rpx;
  font-weight: 600;
}

/* 主分数卡片 */
.main-score-card .main-score-content {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.total-score {
  font-size: 70rpx;
  font-weight: bold;
  line-height: 1;
}

.total-score-label {
  font-size: 24rpx;
  margin-top: 10rpx;
}

.style-type-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.style-type-label {
  font-size: 24rpx;
  margin-bottom: 10rpx;
}

.style-type {
  font-size: 32rpx;
  font-weight: 500;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}

/* 分项评分卡片 */
.detail-score-card .score-details {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
}

.score-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.score-item-label {
  font-size: 24rpx;
  margin-top: 10rpx;
}

.score-item-value {
  font-size: 48rpx;
  font-weight: 600;
  line-height: 1;
}

/* 评价卡片 */
.comment-card .comment-content {
  padding: 30rpx;
}

.comment-text {
  font-size: 28rpx;
  line-height: 1.6;
}

/* 建议卡片 */
.suggestion-card .suggestion-content {
  padding: 30rpx;
}

.suggestion-text {
  font-size: 28rpx;
  line-height: 1.6;
}

/* 相似搭配推荐 */
.similar-outfits-section {
  margin-bottom: 30rpx;
  overflow: visible;
}

.similar-outfits-scroll {
  white-space: nowrap;
  padding: 10rpx;
  overflow: visible;
}

.similar-outfit-card {
  display: inline-block;
  width: 220rpx;
  margin-right: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.similar-outfit-image {
  width: 100%;
  height: 280rpx;
  object-fit: cover;
  position: relative;
  z-index: 3;
}

.similar-outfit-name {
  font-size: 24rpx;
  font-weight: 500;
  padding: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.similar-outfit-date {
  font-size: 20rpx;
  padding: 0 10rpx 10rpx 10rpx;
  opacity: 0.9;
}

/* 图片预览浮层 */
.image-preview-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.preview-content {
  width: 100%;
  height: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.fullscreen-image {
  width: 100%;
  height: 80%;
  max-height: 80vh;
  object-fit: contain;
}

.preview-actions {
  display: flex;
  margin-top: 30rpx;
  width: 100%;
  justify-content: center;
}

.preview-action-btn {
  margin: 0 20rpx;
  padding: 25rpx 50rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: white;
  border: none;
}

.save-btn {
  background-color: #34c759;
}

.close-btn {
  background-color: #ff3b30;
}

/* 按钮按下时的效果 */
.preview-action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* AI评分弹窗样式 */
.ai-score-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.ai-score-modal-content {
  width: 90%;
  max-width: 650rpx;
  max-height: 90vh;
  background-color: #f8f8f8;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(50rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.ai-score-modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  background-color: white;
}

.ai-score-modal-title {
  font-size: 36rpx;
  font-weight: 600;
}

.ai-score-modal-close {
  font-size: 48rpx;
  line-height: 1;
  padding: 0 10rpx;
  color: #999;
  cursor: pointer;
}

.ai-score-modal .ai-score-cards-container {
  padding: 20rpx;
  overflow-y: auto;
  max-height: calc(90vh - 100rpx);
  background-color: #f0f0f0;
}

/* 查看AI分析按钮 */
.view-ai-score-button-container {
  margin: 20rpx 0;
  display: flex;
  justify-content: center;
}

.view-ai-score-button {
  padding: 15rpx 30rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.view-ai-score-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}