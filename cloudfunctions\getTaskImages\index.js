// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 获取任务ID参数，如果未提供则使用默认值
  const taskId = event.taskId || 'default'
  
  try {
    // 查询任务图片集合
    const taskImagesCollection = db.collection('taskImages')
    const result = await taskImagesCollection.where({
      taskId: taskId,  // 根据taskId查询
      active: true     // 只获取激活状态的图片
    }).limit(10).get()  // 限制最多返回10张图片
    
    // 检查是否找到任务图片
    if (!result.data || result.data.length === 0) {
      console.log(`数据库中没有找到任务 ${taskId} 的图片`)
      
      // 如果数据库中没有图片，创建默认图片记录
      await createDefaultTaskImages(taskId)
      
      // 重新查询
      const retryResult = await taskImagesCollection.where({
        taskId: taskId,
        active: true
      }).limit(10).get()
      
      // 如果仍然没有找到任务图片，返回错误
      if (!retryResult.data || retryResult.data.length === 0) {
        return {
          success: false,
          error: `无法获取任务 ${taskId} 的图片`
        }
      }
      
      return {
        success: true,
        data: retryResult.data
      }
    }
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    console.error('获取任务图片失败:', error)
    
    return {
      success: false,
      error: error.message || '获取任务图片失败'
    }
  }
}

// 创建默认任务图片记录
async function createDefaultTaskImages(taskId) {
  console.log(`创建任务 ${taskId} 的默认图片记录`)
  
  // 获取数据库引用
  const db = cloud.database()
  const taskImagesCollection = db.collection('taskImages')
  
  // 默认图片文件ID
  // 注意：这些fileID需要实际上传到云存储并获取真实的fileID
  const defaultFileIDs = [
    'cloud://prod-4gvnydkn1e6d964a.7072-prod-4gvnydkn1e6d964a-1318127641/task_images/step1.png',
    'cloud://prod-4gvnydkn1e6d964a.7072-prod-4gvnydkn1e6d964a-1318127641/task_images/step2.png',
    'cloud://prod-4gvnydkn1e6d964a.7072-prod-4gvnydkn1e6d964a-1318127641/task_images/step3.png',
    'cloud://prod-4gvnydkn1e6d964a.7072-prod-4gvnydkn1e6d964a-1318127641/task_images/step4.png',
    'cloud://prod-4gvnydkn1e6d964a.7072-prod-4gvnydkn1e6d964a-1318127641/task_images/step5.png',
    'cloud://prod-4gvnydkn1e6d964a.7072-prod-4gvnydkn1e6d964a-1318127641/task_images/step6.png'
  ]
  
  // 创建默认图片记录
  try {
    // 批量添加默认图片记录
    const tasks = defaultFileIDs.map((fileID, index) => {
      return taskImagesCollection.add({
        data: {
          taskId: taskId,   // 关联的任务ID
          fileID: fileID,
          step: index + 1,
          title: `任务 ${taskId} 步骤 ${index + 1}`,
          description: `任务 ${taskId} 步骤 ${index + 1} 的描述`,
          active: true,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      })
    })
    
    // 等待所有添加操作完成
    await Promise.all(tasks)
    console.log(`任务 ${taskId} 的默认图片记录创建成功`)
  } catch (error) {
    console.error(`创建任务 ${taskId} 的默认图片记录失败:`, error)
    throw error
  }
} 