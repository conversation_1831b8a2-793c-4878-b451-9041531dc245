/* 衣柜页面样式 */
page {
  background-color: #f5f5f5;
  height: 100%;
}

.wardrobe-container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 衣柜主体 */
.wardrobe-body {
  position: relative;
  width: 100%;
  /* 3:4比例 */
  padding-top: 133.33%;
  background-color: #e9d7bd;
  border-radius: 20rpx;
  overflow: hidden;
  border: 2rpx solid #d5c3a5;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

/* 左侧衣柜部分 */
.left-section {
  position: absolute;
  top: 0;
  left: 0;
  width: 66.67%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 2rpx solid #d5c3a5;
  padding: 4rpx;
  box-sizing: border-box;
  background-color: #e9d7bd;
}

/* 上部衣柜 */
.upper-cabinet {
  height: 75%;
  border: 2rpx solid #d5c3a5;
  border-radius: 8rpx;
  margin-bottom: 4rpx;
  position: relative;
  overflow: hidden;
  background-color: #e9d7bd;
}

/* 衣柜内部 */
.cabinet-interior {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 4rpx;
  box-sizing: border-box;
}

.rod-area {
  display: flex;
  height: 100%;
}

.left-half, .right-half {
  width: 50%;
  height: 100%;
  padding: 2rpx;
  box-sizing: border-box;
}

.left-half {
  border-right: 2rpx solid #d5c3a5;
}

.section-title {
  text-align: center;
  font-size: 24rpx;
  color: #8d7a56;
  margin-bottom: 2rpx;
}

.clothes-list {
  height: calc(100% - 30rpx);
}

.clothes-item {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 6rpx;
  border: 2rpx solid #d5c3a5;
  padding: 2rpx;
  margin-bottom: 2rpx;
}

.clothes-image {
  width: 60rpx;
  height: 80rpx;
  margin-right: 4rpx;
  border-radius: 4rpx;
}

.clothes-info {
  display: flex;
  flex-direction: column;
}

.clothes-name {
  font-size: 22rpx;
  font-weight: 500;
  color: #8d7a56;
}

.clothes-color {
  font-size: 20rpx;
  color: #a18e6b;
}

/* 左侧推拉门 */
.left-door, .right-door {
  position: absolute;
  top: 0;
  height: 100%;
  width: 50%;
  background-color: #e9d7bd;
  transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
  z-index: 10;
}

.left-door {
  left: 0;
  border-right: 2rpx solid #d5c3a5;
}

.right-door {
  right: 0;
}

.left-door.door-open {
  transform: translateX(-100%);
  opacity: 0.9;
}

.right-door.door-open {
  transform: translateX(100%);
  opacity: 0.9;
}

.door-handle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 120rpx;
  background-color: #c4a14c;
  border-radius: 2rpx;
}

.door-handle.left {
  left: 8rpx;
}

.door-handle.right {
  right: 8rpx;
}

.door-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.door-hint {
  font-size: 24rpx;
  color: #8d7a56;
}

/* 抽屉组 */
.drawers-section {
  height: 25%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.drawer {
  height: 32%;
  border: 2rpx solid #d5c3a5;
  border-radius: 8rpx;
  margin-bottom: 4rpx;
  position: relative;
  background-color: #e9d7bd;
  display: flex;
  align-items: center;
}

.drawer:last-child {
  margin-bottom: 0;
}

.drawer.active {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.drawer-handle {
  width: 120rpx;
  height: 4rpx;
  background-color: #b2a080;
  border-radius: 4rpx;
  margin: 0 auto;
}

.drawer-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #f2e7d5;
  border: 2rpx solid #d5c3a5;
  border-top: none;
  border-radius: 0 0 8rpx 8rpx;
  transition: max-height 0.3s;
  max-height: 0;
  overflow: hidden;
  z-index: 10;
}

.drawer-content.open {
  max-height: 300rpx;
}

.drawer-items {
  white-space: nowrap;
  padding: 10rpx;
}

.drawer-item {
  display: inline-block;
  background-color: white;
  border-radius: 6rpx;
  padding: 6rpx;
  margin-right: 10rpx;
  border: 2rpx solid #d5c3a5;
  width: 120rpx;
  text-align: center;
}

.drawer-item-image {
  width: 80rpx;
  height: 100rpx;
  border-radius: 4rpx;
  margin-bottom: 4rpx;
}

.drawer-item-name, .drawer-item-color {
  font-size: 20rpx;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.drawer-item-name {
  color: #8d7a56;
  font-weight: 500;
}

.drawer-item-color {
  color: #a18e6b;
}

/* 右侧衣柜部分 */
.right-section {
  position: absolute;
  top: 0;
  right: 0;
  width: 33.33%;
  height: 100%;
  background-color: #e9d7bd;
}

/* 玻璃推拉门 */
.glass-door {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
  z-index: 10;
}

.glass-door.door-open {
  transform: translateX(75%);
  opacity: 0.8;
}

.upper-glass, .lower-glass {
  flex: 1;
  position: relative;
  overflow: hidden;
  border: 2rpx solid #d5c3a5;
  background-color: #e9d7bd;
}

.upper-glass {
  border-bottom: none;
}

.middle-separator {
  height: 24rpx;
  background-color: #e9d7bd;
  border-left: 2rpx solid #d5c3a5;
  border-right: 2rpx solid #d5c3a5;
}

.glass-texture {
  position: absolute;
  inset: 0;
  display: flex;
}

.glass-texture::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, 
    rgba(224, 224, 224, 0.7) 0%, 
    rgba(224, 224, 224, 0.7) 4.8%, 
    rgba(255, 255, 255, 0.7) 5%, 
    rgba(255, 255, 255, 0.7) 9.8%,
    rgba(224, 224, 224, 0.7) 10%
  );
  background-size: 10% 100%;
}

/* 门后内容 */
.door-content-area {
  position: absolute;
  inset: 0;
  padding: 8rpx;
  box-sizing: border-box;
}

.special-clothes-list {
  height: calc(100% - 30rpx);
  padding-top: 8rpx;
}

.special-clothes-item {
  background-color: white;
  border-radius: 6rpx;
  border: 2rpx solid #d5c3a5;
  padding: 8rpx;
  margin-bottom: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.special-clothes-image {
  width: 120rpx;
  height: 150rpx;
  border-radius: 4rpx;
  margin-bottom: 4rpx;
}

.special-clothes-name, .special-clothes-color {
  font-size: 22rpx;
  text-align: center;
}

.special-clothes-name {
  color: #8d7a56;
  font-weight: 500;
}

.special-clothes-color {
  color: #a18e6b;
}

/* 门控制按钮 */
.door-toggle {
  position: absolute;
  right: 8rpx;
  bottom: 8rpx;
  z-index: 20;
}

.toggle-btn {
  background-color: #f2e7d5;
  border: 2rpx solid #d5c3a5;
  border-radius: 50%;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.icon-left:before, .icon-right:before {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-top: 8rpx solid transparent;
  border-bottom: 8rpx solid transparent;
}

.icon-left:before {
  border-right: 12rpx solid #8d7a56;
}

.icon-right:before {
  border-left: 12rpx solid #8d7a56;
}

/* 衣柜底座 */
.wardrobe-base {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8rpx;
  background-color: #a18e6b;
  border-top: 2rpx solid #8d7a56;
}

/* 底部功能按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
  margin-bottom: 20rpx;
}

.add-btn, .create-btn {
  margin: 0 15rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 2.2;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.add-btn {
  background-color: #a18e6b;
  color: white;
}

.create-btn {
  background-color: white;
  color: #8d7a56;
  border: 2rpx solid #d5c3a5;
}

/* 操作提示 */
.tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.tips text {
  font-size: 24rpx;
  color: #8d7a56;
  margin-bottom: 4rpx;
}

/* 衣物详情弹窗 */
.card-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 30rpx;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 2rpx solid #f1f1f1;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.modal-body {
  padding: 30rpx;
}

.item-image-container {
  padding: 10rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  border: 2rpx solid #f1f1f1;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
}

.item-image {
  height: 400rpx;
  border-radius: 6rpx;
}

.item-details {
  margin-bottom: 40rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 2rpx solid #f1f1f1;
}

.detail-label {
  color: #999;
  font-size: 28rpx;
}

.detail-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 2.6;
}

.add-outfit {
  background-color: #a18e6b;
  color: white;
}

.edit {
  background-color: white;
  color: #333;
  border: 2rpx solid #ddd;
} 