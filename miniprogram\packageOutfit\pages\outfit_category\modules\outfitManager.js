/**
 * 搭配管理模块
 * 负责outfit页面搭配数据的获取和处理
 */

// 导入图片管理模块
const imageManager = require('./imageManager');

// 缓存相关常量
const OUTFITS_CACHE_KEY = 'index_page_outfits_cache_';
const OUTFITS_CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7天

/**
 * 获取用户的所有搭配
 * @param {string} userOpenId - 用户的OpenID
 * @returns {Promise<Array>} 包含所有搭配的Promise
 */
function getAllOutfits(userOpenId) {
  return new Promise((resolve, reject) => {
    if (!userOpenId) {
      console.error('用户OpenID不能为空');
      reject(new Error('用户OpenID不能为空'));
      return;
    }
    
    console.log('开始获取用户所有搭配，用户ID:', userOpenId);
    
    // 尝试从缓存获取数据
    if (shouldUseCache()) {
      const cachedOutfits = loadFromLocalCache(userOpenId);
      if (cachedOutfits && cachedOutfits.length > 0) {
        console.log('使用本地缓存数据，搭配数量:', cachedOutfits.length);
        resolve(cachedOutfits);
        return;
      }
    }
    
    const db = wx.cloud.database();
    
    // 查询用户的所有搭配
    db.collection('outfits')
      .where({
        _openid: userOpenId
      })
      .get()
      .then(res => {
        console.log('获取用户搭配成功:', res.data);
        
        // 检查返回的数据是否有效
        if (!res || !res.data) {
          console.warn('用户没有搭配数据');
          resolve([]);
          return;
        }
        
        // 处理每个搭配的数据
        const outfits = res.data;
        
        // 如果没有搭配，直接返回空数组
        if (!outfits || outfits.length === 0) {
          console.warn('用户没有搭配');
          resolve([]);
          return;
        }
        
        // 处理每个搭配数据
        const processedOutfits = outfits.map(outfit => {
          // 添加格式化后的日期
          if (outfit.createTime) {
            outfit.createTimeFormatted = formatDate(outfit.createTime);
          }
          
          // 确保outfit有id字段
          if (!outfit.id && outfit._id) {
            outfit.id = outfit._id;
          }
          
          // 确保outfit有类型字段
          if (!outfit.type && outfit.category) {
            outfit.type = outfit.category;
          } else if (!outfit.type && !outfit.category) {
            // 默认类型为日常
            outfit.type = 'daily';
            outfit.category = 'daily';
          }
          
          return outfit;
        });
        
        // 检查是否需要获取完整的衣物数据
        const needFetchClothes = processedOutfits.some(outfit => 
          outfit.items && Array.isArray(outfit.items) && 
          outfit.items.some(item => item.clothingId && (!item.name || !item.imageUrl))
        );
        
        if (needFetchClothes) {
          // 收集所有需要获取的衣物ID
          const allClothingIds = [];
          processedOutfits.forEach(outfit => {
            if (outfit.items && Array.isArray(outfit.items)) {
              outfit.items.forEach(item => {
                if (item && item.clothingId && (!item.name || !item.imageUrl)) {
                  allClothingIds.push(item.clothingId);
                }
              });
            }
          });
          
          if (allClothingIds.length > 0) {
            // 获取衣物数据
            return getClothesData(allClothingIds)
              .then(clothesData => {
                // 更新每个搭配的衣物项数据
                processedOutfits.forEach(outfit => {
                  if (outfit.items && Array.isArray(outfit.items)) {
                    outfit.items = outfit.items.map(item => {
                      if (item && item.clothingId) {
                        const clothingData = clothesData.find(c => c && c._id === item.clothingId);
                        if (clothingData) {
                          // 优先使用抠图后的图片 (processedImageFileID)
                          const imageFileID = clothingData.processedImageFileID || clothingData.imageFileID || clothingData.imageUrl || null;
                          
                          // 合并衣物数据
                          return {
                            ...item,
                            name: item.name || clothingData.name,
                            type: item.type || clothingData.type || clothingData.category,
                            category: item.category || clothingData.category,
                            // 优先使用抠图后的图片URL或文件ID
                            imageUrl: item.imageUrl || clothingData.processedImageUrl || clothingData.imageUrl || clothingData.processedImageFileID,
                            // 保存原始fileID和抠图后的fileID用于后续刷新
                            imageFileID: imageFileID,
                            processedImageFileID: clothingData.processedImageFileID || null,
                            originalImageFileID: clothingData.imageFileID || null,
                            originalClothing: clothingData
                          };
                        }
                      }
                      return item;
                    });
                  }
                });
                
                // 处理所有搭配的图片URL
                return processAllOutfitsImageUrls(processedOutfits);
              });
          }
        }
        
        // 处理所有搭配的图片URL
        return processAllOutfitsImageUrls(processedOutfits);
      })
      .then(processedOutfits => {
        // 确保每个搭配都有预览图和默认图片
        processedOutfits.forEach(outfit => {
          ensureDefaultImages(outfit);
        });
        
        // 保存到本地缓存
        updateLocalCache(userOpenId, processedOutfits);
        
        resolve(processedOutfits);
      })
      .catch(err => {
        console.error('获取用户搭配失败:', err);
        reject(err);
      });
  });
}

/**
 * 获取衣物数据
 * @param {Array} clothingIds - 衣物ID数组
 * @returns {Promise<Array>} 包含衣物数据的Promise
 */
function getClothesData(clothingIds) {
  return new Promise((resolve, reject) => {
    if (!clothingIds || !Array.isArray(clothingIds) || clothingIds.length === 0) {
      resolve([]);
      return;
    }
    
    console.log('获取衣物数据，ID数量:', clothingIds.length);
    
    const db = wx.cloud.database();
    const _ = db.command;
    
    // 查询条件：衣物ID在指定数组中
    db.collection('clothes')
      .where({
        _id: _.in(clothingIds)
      })
      .get()
      .then(res => {
        console.log('获取衣物数据成功:', res.data);
        resolve(res.data || []);
      })
      .catch(err => {
        console.error('获取衣物数据失败:', err);
        resolve([]);
      });
  });
}

/**
 * 处理所有搭配数据中的图片URL
 * @param {Array} outfits - 搭配数据数组
 * @returns {Promise<Array>} 处理后的搭配数据数组
 */
function processAllOutfitsImageUrls(outfits) {
  // 直接使用imageManager模块的方法处理图片
  return imageManager.processOutfitsImages(outfits);
}

/**
 * 确保搭配数据有默认图片
 * @param {Object} outfitData - 搭配数据
 */
function ensureDefaultImages(outfitData) {
  if (!outfitData) return;
  
  // 确保有预览图
  if (!outfitData.previewImage || !imageManager.isValidImageUrl(outfitData.previewImage)) {
    outfitData.previewImage = imageManager.getDefaultPreviewImageUrl();
  }
  
  // 确保每个衣物项有图片
  if (outfitData.items && Array.isArray(outfitData.items)) {
    outfitData.items.forEach(item => {
      if (!item.imageUrl || !imageManager.isValidImageUrl(item.imageUrl)) {
        item.imageUrl = imageManager.getDefaultItemImageUrl();
      }
    });
  }
}

/**
 * 根据分类获取搭配
 * @param {Array} allOutfits - 所有搭配数据
 * @param {string} category - 搭配分类
 * @returns {Array} 筛选后的搭配数组
 */
function getOutfitsByCategory(allOutfits, category) {
  if (!allOutfits || !Array.isArray(allOutfits)) {
    return [];
  }
  
  return allOutfits.filter(outfit => {
    // 检查多个可能的类型字段
    return (outfit.category === category) || 
           (outfit.type === category);
  });
}

/**
 * 是否应该使用缓存
 * @returns {boolean} 是否应该使用缓存
 */
function shouldUseCache() {
  // 获取全局缓存控制状态
  const app = getApp();
  
  // 如果全局设置明确禁用了缓存，返回false
  if (app && app.globalData && app.globalData.useLocalCache === false) {
    return false;
  }
  
  // 默认使用缓存
  return true;
}

/**
 * 从本地缓存加载搭配数据
 * @param {string} userOpenId - 用户的OpenID
 * @returns {Array|null} 缓存的搭配数据，如果不存在或已过期则返回null
 */
function loadFromLocalCache(userOpenId) {
  if (!userOpenId) return null;
  
  // 缓存键包含用户ID，确保不同用户有不同的缓存
  const cacheKey = `${OUTFITS_CACHE_KEY}${userOpenId}`;
  
  try {
    const cachedData = wx.getStorageSync(cacheKey);
    if (!cachedData) return null;
    
    // 检查缓存是否已过期
    const now = Date.now();
    if (now - cachedData.timestamp > OUTFITS_CACHE_TTL) {
      console.log('搭配缓存已过期');
      // 删除过期缓存
      wx.removeStorageSync(cacheKey);
      return null;
    }
    
    // 有效的缓存数据
    return cachedData.outfits;
  } catch (err) {
    console.error('从本地缓存加载搭配数据失败:', err);
    return null;
  }
}

/**
 * 更新本地缓存
 * @param {string} userOpenId - 用户的OpenID
 * @param {Array} outfits - 搭配数据
 */
function updateLocalCache(userOpenId, outfits) {
  if (!userOpenId || !outfits) return;
  
  // 缓存键包含用户ID，确保不同用户有不同的缓存
  const cacheKey = `${OUTFITS_CACHE_KEY}${userOpenId}`;
  
  try {
    const cacheData = {
      outfits: outfits,
      timestamp: Date.now()
    };
    
    // 存储到本地缓存
    wx.setStorageSync(cacheKey, cacheData);
    console.log('已更新搭配本地缓存');
  } catch (err) {
    console.error('更新搭配本地缓存失败:', err);
  }
}

/**
 * 清除搭配缓存
 * @param {string} userOpenId - 用户的OpenID，如果为空则清除所有用户的缓存
 */
function clearOutfitsCache(userOpenId) {
  try {
    if (userOpenId) {
      // 清除指定用户的缓存
      const cacheKey = `${OUTFITS_CACHE_KEY}${userOpenId}`;
      wx.removeStorageSync(cacheKey);
      console.log(`已清除用户 ${userOpenId} 的搭配缓存`);
    } else {
      // 清除所有与搭配相关的缓存
      const allKeys = wx.getStorageInfoSync().keys;
      let clearedCount = 0;
      
      allKeys.forEach(key => {
        if (key.startsWith(OUTFITS_CACHE_KEY)) {
          wx.removeStorageSync(key);
          clearedCount++;
        }
      });
      
      console.log(`已清除所有用户的搭配缓存，共 ${clearedCount} 项`);
    }
  } catch (err) {
    console.error('清除搭配缓存失败:', err);
  }
}

/**
 * 格式化日期
 * @param {number|string|Date} date - 日期（时间戳、字符串或Date对象）
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
  if (!date) return '';
  
  let dateObj;
  if (date instanceof Date) {
    dateObj = date;
  } else if (typeof date === 'number') {
    dateObj = new Date(date);
  } else if (typeof date === 'string') {
    dateObj = new Date(date);
  } else {
    return '';
  }
  
  const year = dateObj.getFullYear();
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
  const day = dateObj.getDate().toString().padStart(2, '0');
  
  return `${year}.${month}.${day}`;
}

/**
 * 生成模拟搭配数据（用于开发测试）
 * @returns {Array} 模拟的搭配数据
 */
function generateMockOutfits() {
  // 模拟衣物图片
  const mockImages = [
    '/image/mock/clothing_1.jpg',
    '/image/mock/clothing_2.jpg',
    '/image/mock/clothing_3.jpg',
    '/image/mock/clothing_4.jpg',
    '/image/mock/clothing_5.jpg'
  ];
  
  // 模拟搭配预览图
  const mockPreviewImages = [
    '/image/mock/outfit_1.jpg',
    '/image/mock/outfit_2.jpg',
    '/image/mock/outfit_3.jpg',
    '/image/mock/outfit_4.jpg',
    '/image/mock/outfit_5.jpg',
    '/image/mock/outfit_6.jpg'
  ];
  
  // 生成模拟搭配
  return [
    {
      id: 'daily-1',
      _id: 'daily-1',
      name: '休闲日常搭配',
      category: 'daily',
      previewImage: mockPreviewImages[0],
      items: [
        { id: 'd1', name: '白色T恤', imageUrl: mockImages[0] },
        { id: 'd2', name: '牛仔裤', imageUrl: mockImages[1] },
        { id: 'd3', name: '运动鞋', imageUrl: mockImages[2] }
      ],
      createTime: new Date('2023-03-01').getTime(),
      createTimeFormatted: '2023.03.01'
    },
    {
      id: 'daily-2',
      _id: 'daily-2',
      name: '舒适居家搭配',
      category: 'daily',
      previewImage: mockPreviewImages[1],
      items: [
        { id: 'd4', name: '卫衣', imageUrl: mockImages[3] },
        { id: 'd5', name: '休闲裤', imageUrl: mockImages[4] },
        { id: 'd6', name: '拖鞋', imageUrl: mockImages[0] }
      ],
      createTime: new Date('2023-03-02').getTime(),
      createTimeFormatted: '2023.03.02'
    },
    {
      id: 'work-1',
      _id: 'work-1',
      name: '商务正装',
      category: 'work',
      previewImage: mockPreviewImages[2],
      items: [
        { id: 'w1', name: '西装外套', imageUrl: mockImages[1] },
        { id: 'w2', name: '衬衫', imageUrl: mockImages[2] },
        { id: 'w3', name: '西裤', imageUrl: mockImages[3] },
        { id: 'w4', name: '皮鞋', imageUrl: mockImages[4] }
      ],
      createTime: new Date('2023-03-03').getTime(),
      createTimeFormatted: '2023.03.03'
    },
    {
      id: 'party-1',
      _id: 'party-1',
      name: '派对时尚',
      category: 'party',
      previewImage: mockPreviewImages[3],
      items: [
        { id: 'p1', name: '亮片上衣', imageUrl: mockImages[0] },
        { id: 'p2', name: '紧身裤', imageUrl: mockImages[1] },
        { id: 'p3', name: '高跟鞋', imageUrl: mockImages[2] }
      ],
      createTime: new Date('2023-03-04').getTime(),
      createTimeFormatted: '2023.03.04'
    },
    {
      id: 'sport-1',
      _id: 'sport-1',
      name: '运动健身',
      category: 'sport',
      previewImage: mockPreviewImages[4],
      items: [
        { id: 's1', name: '运动T恤', imageUrl: mockImages[3] },
        { id: 's2', name: '运动短裤', imageUrl: mockImages[4] },
        { id: 's3', name: '运动鞋', imageUrl: mockImages[0] }
      ],
      createTime: new Date('2023-03-05').getTime(),
      createTimeFormatted: '2023.03.05'
    },
    {
      id: 'seasonal-1',
      _id: 'seasonal-1',
      name: '春季出行',
      category: 'seasonal',
      previewImage: mockPreviewImages[5],
      items: [
        { id: 'se1', name: '轻薄外套', imageUrl: mockImages[1] },
        { id: 'se2', name: '长袖T恤', imageUrl: mockImages[2] },
        { id: 'se3', name: '休闲裤', imageUrl: mockImages[3] },
        { id: 'se4', name: '帆布鞋', imageUrl: mockImages[4] }
      ],
      createTime: new Date('2023-03-06').getTime(),
      createTimeFormatted: '2023.03.06'
    }
  ];
}

// 导出模块接口
module.exports = {
  getAllOutfits,
  getOutfitsByCategory,
  getClothesData,
  ensureDefaultImages,
  shouldUseCache,
  loadFromLocalCache,
  updateLocalCache,
  clearOutfitsCache,
  formatDate,
  generateMockOutfits
}; 