// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const MAX_LIMIT = 50 // 每次获取的最大记录数

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 检查必要参数
  if (!event.wardrobeId) {
    return {
      success: false,
      message: '缺少衣柜ID'
    }
  }
  
  try {
    // 首先获取用户的所有衣物数量
    const countResult = await db.collection('clothes')
      .where({
        _openid: wxContext.OPENID
      })
      .count()
    
    const total = countResult.total
    
    // 如果没有衣物，直接返回成功
    if (total === 0) {
      return {
        success: true,
        updated: 0,
        message: '没有需要更新的衣物'
      }
    }
    
    // 计算需要分几次获取
    const batchTimes = Math.ceil(total / MAX_LIMIT)
    let updatedCount = 0
    
    // 分批次更新所有衣物
    for (let i = 0; i < batchTimes; i++) {
      const clothes = await db.collection('clothes')
        .where({
          _openid: wxContext.OPENID
        })
        .skip(i * MAX_LIMIT)
        .limit(MAX_LIMIT)
        .get()
      
      // 获取这批衣物的ID
      const clothesIds = clothes.data.map(item => item._id)
      
      // 创建批量更新任务
      const updatePromises = clothesIds.map(id => {
        return db.collection('clothes').doc(id).update({
          data: {
            wardrobeId: event.wardrobeId,
            updateTime: db.serverDate()
          }
        })
      })
      
      // 执行批量更新
      const updateResults = await Promise.all(updatePromises)
      
      // 计算更新成功的数量
      updateResults.forEach(result => {
        updatedCount += result.stats.updated
      })
    }
    
    return {
      success: true,
      updated: updatedCount,
      message: `成功更新了${updatedCount}件衣物的衣柜ID`
    }
  } catch (error) {
    console.error('批量更新衣物衣柜ID失败:', error)
    return {
      success: false,
      message: '更新失败: ' + error.message
    }
  }
} 