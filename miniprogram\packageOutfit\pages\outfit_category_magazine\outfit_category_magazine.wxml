<!-- 自定义导航栏 -->
<mp-navigation-bar
  back="{{true}}"
  background="{{navBackground}}"
  color="{{navFrontColor}}"
  title="{{navTitle}}"
  ext-class="custom-nav-bar">
</mp-navigation-bar>

<!-- 杂志风格穿搭类别页面 -->
<view class="container theme-{{themeStyle}}">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner" style="border-top-color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}}; border-color: {{themeStyle === 'blackWhite' ? blackWhiteColors.lightGray : colors.golden_batter}};"></view>
    <view class="loading-text" style="color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}};">加载中...</view>
  </view>

  <!-- 主要内容区域 -->
  <block wx:if="{{!isLoading}}">
    <!-- 返回按钮，在自定义导航栏下已不需要 -->
    <!-- <view class="back-button" bindtap="goBack">
      <view class="back-icon" style="border-color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}};"></view>
      <text style="color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}};">返回</text>
    </view> -->

    <!-- 内容区域 -->
    <view class="magazine-content">
      <!-- 有搭配时显示杂志内容 -->
      <block wx:if="{{outfits.length > 0}}">


        <view class="magazine-page {{pageTurnClass}}" animation="{{animationData}}" bindtouchstart="touchStart" bindtouchend="touchEnd">
          <!-- 书脊 -->
          <view class="magazine-spine"></view>
          <!-- 右侧书页层叠效果 -->
          <view class="right-pages-stack"></view>
          <!-- 页面阴影效果 -->
          <view class="page-shadow"></view>
          <!-- 页面波纹效果 -->
          <view class="ripple-effect"></view>
          <!-- 翻页褶皱纹理 -->
          <view class="page-crease"></view>

          <block wx:if="{{currentOutfit}}">
            <!-- 杂志封面风格展示 -->
            <view wx:if="{{showCover}}" class="magazine-cover" bindtap="goToOutfitDetail">
              <!-- 添加书脊效果 -->
              <view class="magazine-spine"></view>
              <view class="cover-model-container">
                <image
                  class="cover-model"
                  src="{{currentOutfit.outfit_cover || currentOutfit.previewImage}}"
                  mode="aspectFit"
                  binderror="handleImageError"
                  data-type="preview"
                  data-index="{{currentIndex}}"
                  bindlongpress="handleLongPress"
                ></image>

                <!-- AI评分 -->
                <view class="ai-score-badge" wx:if="{{currentOutfit.aiScore}}"
                      style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.blueDark)}};">
                  <text class="ai-score-value">{{currentOutfit.aiScore}}</text>
                  <text class="ai-score-label">AI评分</text>
                </view>

                <!-- 添加搭配名称显示 -->
                <view class="outfit-name-display">
                  {{currentOutfit.name || '未命名搭配'}}
                </view>

                <!-- 长按提示 -->
                <view class="longpress-hint">长按可上传封面图</view>

                <!-- 底部信息区 -->
                <view class="cover-bottom-info">
                  <!-- 尺码信息 -->
                  <view class="size-info">
                    <text>型号：{{currentOutfit.items[0].size || 'M码'}}</text>
                    <text>品牌：{{currentOutfit.items[0].brand || '经典款'}}</text>
                    <text>尺寸：{{currentOutfit.items[0].dimensions || '(226*18cm/3)'}}</text>
                    <text>材质：{{currentOutfit.items[0].material || '针织'}}</text>
                    <text>色系：{{currentOutfit.items[0].colorFamily || '粉色系'}}</text>
                    <text>产地：{{currentOutfit.items[0].origin || '杭州'}}</text>
                  </view>
                </view>

                <!-- 页码 - 现在移到中间位置 -->
                <view class="page-number">_0{{currentIndex + 1}}_</view>
              </view>

              <!-- 提示文本 -->

            </view>

            <!-- 杂志内容详情 -->
            <view wx:else class="outfit-content">
              <!-- 添加书脊效果 -->
              <view class="magazine-spine"></view>
              <!-- 添加纸张纹理效果 -->
              <view class="paper-texture"></view>
              <!-- 添加右侧阴影效果 -->
              <view class="right-side-shadow"></view>

              <view class="content-header">
                <view class="content-title-container">
                  <view class="content-title">{{currentOutfit.name || '未命名搭配'}}</view>
                  <!-- 内联AI评分 -->
                  <view class="ai-score-inline" wx:if="{{currentOutfit.aiScore}}"
                        style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.blueDark)}};">
                    {{currentOutfit.aiScore}}
                  </view>
                </view>
                <view class="content-date">创建于 {{currentOutfit.createTime ? formatDate(currentOutfit.createTime) : '未知日期'}}</view>
              </view>

              <view class="content-body">
                <view class="outfit-description">
                  {{currentOutfit.description || '这是一套' + categoryName + '，展示了个人风格与时尚态度。'}}
                </view>

                <view class="outfit-items-section">
                  <view class="section-title">搭配组成</view>

                  <view class="outfit-items-grid" wx:if="{{currentOutfit.items && currentOutfit.items.length > 0}}">
                    <block wx:for="{{currentOutfit.items}}" wx:key="id">
                      <view class="outfit-item">
                        <view class="item-image-container">
                          <image
                            class="item-image"
                            src="{{item.imageUrl}}"
                            mode="aspectFit"
                            binderror="handleImageError"
                            data-type="item"
                            data-index="{{currentIndex}}"
                            data-item-index="{{index}}"
                          ></image>
                        </view>
                        <view class="item-name">{{item.name || '未命名'}}</view>
                      </view>
                    </block>
                  </view>

                  <view wx:else style="text-align: center; color: #999; margin-top: 20rpx;">
                    暂无衣物信息
                  </view>
                </view>
              </view>

              <view class="magazine-footer">
                {{categoryName}}杂志 • 第{{currentIndex + 1}}期 • {{currentOutfit.season || '四季'}}系列
              </view>

              <!-- 返回封面按钮 -->
              <view class="back-to-cover" bindtap="toggleCoverMode">
                返回封面
              </view>
            </view>
          </block>
        </view>

        <!-- 翻页按钮 -->
        <view class="page-buttons">
          <view class="page-button" bindtap="onPageButtonTap" data-direction="prev" wx:if="{{currentIndex > 0}}">
            <view class="button-icon prev-icon" style="border-color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}};"></view>
          </view>
          <view class="page-button" bindtap="onPageButtonTap" data-direction="next" wx:if="{{currentIndex < totalOutfits - 1}}">
            <view class="button-icon next-icon" style="border-color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}};"></view>
          </view>
        </view>

        <!-- 页码指示器 -->
        <view class="page-indicator" wx:if="{{showPageIndicator}}">
          <view class="indicator-text"
                bindlongpress="startPageDrag"
                bindtouchmove="onPageDrag"
                bindtouchend="endPageDrag"
                bindtouchcancel="endPageDrag">
            <text class="{{isDraggingPage ? 'dragging' : ''}}">{{currentIndex + 1}} / {{totalOutfits}}</text>
            <view class="page-slider" wx:if="{{isDraggingPage}}">
              <view class="slider-track"></view>
              <view class="slider-thumb" style="left: {{sliderPosition}}%;"></view>
              <view class="slider-label">{{dragPageIndex + 1}}</view>
            </view>
          </view>
        </view>

        <!-- 创建新搭配按钮 -->
        <view class="create-button" bindtap="goToCreateOutfit" style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)}};">
          <text class="plus-icon">+</text>
          <text>创建{{categoryName}}</text>
        </view>
      </block>

      <!-- 空状态展示 -->
      <view class="empty-state" wx:if="{{showEmptyState}}">
        <view class="empty-icon">👕</view>
        <view class="empty-text" style="color: {{themeStyle === 'autumn' ? '#666' : (themeStyle === 'blackWhite' ? blackWhiteColors.darkGray : pinkBlueColors.pinkDark)}};">
          还没有{{categoryName}}，快来创建吧！
        </view>
        <view class="create-button" bindtap="goToCreateOutfit" style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)}};">
          <text class="plus-icon">+</text>
          <text>创建{{categoryName}}</text>
        </view>
      </view>
    </view>
  </block>
</view>