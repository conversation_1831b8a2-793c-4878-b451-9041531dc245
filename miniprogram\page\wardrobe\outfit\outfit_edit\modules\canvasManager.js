// 画布管理模块
// 负责处理画布相关的操作，包括衣物项的添加、移动、缩放、旋转等

// 计算两点之间的距离
function getDistance(p1, p2) {
  const xDiff = p2.x - p1.x;
  const yDiff = p2.y - p1.y;
  return Math.sqrt(xDiff * xDiff + yDiff * yDiff);
}

// 计算两点构成的角度
function getAngle(p1, p2) {
  const xDiff = p2.x - p1.x;
  const yDiff = p2.y - p1.y;
  return Math.atan2(yDiff, xDiff) * 180 / Math.PI;
}

// 获取旋转后的坐标
function getRotatedPoint(point, center, rotation) {
  const radians = (rotation * Math.PI) / 180;
  const cos = Math.cos(radians);
  const sin = Math.sin(radians);

  const x = cos * (point.x - center.x) - sin * (point.y - center.y) + center.x;
  const y = sin * (point.x - center.x) + cos * (point.y - center.y) + center.y;

  return { x, y };
}

// 检查点是否在旋转的矩形内部
function isPointInRotatedRect(point, rect) {
  // 矩形中心点
  const center = {
    x: rect.x + rect.width / 2,
    y: rect.y + rect.height / 2
  };

  // 将点反向旋转，转换到矩形坐标系
  const rotatedPoint = getRotatedPoint(
    point,
    center,
    -rect.rotation || 0
  );

  // 检查点是否在未旋转的矩形内
  return (
    rotatedPoint.x >= rect.x &&
    rotatedPoint.x <= rect.x + rect.width &&
    rotatedPoint.y >= rect.y &&
    rotatedPoint.y <= rect.y + rect.height
  );
}

// 添加衣物到画布
function addClothToCanvas(canvasItems, cloth, canvasWidth, canvasHeight, nextId) {
  if (!cloth) return { items: canvasItems, nextId };

  console.log('添加衣物到画布:', cloth);

  // 计算衣物的初始位置和大小
  const MAX_WIDTH = canvasWidth * 0.6;  // 最大宽度为画布的60%
  const MAX_HEIGHT = canvasHeight * 0.6; // 最大高度为画布的60%

  // 根据图片宽高比例，确定初始化的大小
  let itemWidth, itemHeight;

  // 使用默认长宽比例，防止没有原始尺寸信息的情况
  const ratio = (cloth.width && cloth.height) ? (cloth.width / cloth.height) : 0.8;

  if (ratio > 1) {
    // 宽大于高
    itemWidth = MAX_WIDTH;
    itemHeight = itemWidth / ratio;
  } else {
    // 高大于或等于宽
    itemHeight = MAX_HEIGHT;
    itemWidth = itemHeight * ratio;
  }

  // 确保尺寸不超过最大值
  if (itemWidth > MAX_WIDTH) {
    itemWidth = MAX_WIDTH;
    itemHeight = itemWidth / ratio;
  }

  if (itemHeight > MAX_HEIGHT) {
    itemHeight = MAX_HEIGHT;
    itemWidth = itemHeight * ratio;
  }

  // 放置在画布中心位置
  const x = (canvasWidth - itemWidth) / 2;
  const y = (canvasHeight - itemHeight) / 2;

  // 确保衣物有图片URL
  let imageUrl = cloth.imageUrl;
  if (!imageUrl) {
    // 如果没有imageUrl，尝试使用其他可能的图片字段
    imageUrl = cloth.tempImageUrl || cloth.processedImageUrl || cloth.originalImageUrl || '';
    console.log('衣物没有imageUrl，使用备用图片:', imageUrl);
  }

  // 计算新项目的图层值 - 确保放在最上层
  const maxLayer = canvasItems.length > 0 ? Math.max(...canvasItems.map(item => item.layer || 0)) : -1;
  const newLayer = maxLayer + 1;

  // 创建新的画布项
  const newItem = {
    id: nextId,
    clothId: cloth._id,
    imageUrl: imageUrl,
    fileID: cloth.fileID || cloth.imageFileID,
    // 保存所有可能的抠图相关字段
    bgRemovedFileID: cloth.bgRemovedFileID,
    processedImageFileID: cloth.processedImageFileID,
    processedImageUrl: cloth.processedImageUrl,
    isProcessed: cloth.isProcessed, // 保存抠图标记
    name: cloth.name,
    type: cloth.type,
    category: cloth.category,
    categoryId: cloth.categoryId,
    x,
    y,
    width: itemWidth,
    height: itemHeight,
    rotation: 0,
    layer: newLayer,  // 使用计算出的图层值，确保在最上层
    aspectRatio: ratio  // 保存原始宽高比，确保缩放时保持比例
  };

  console.log(`创建新画布项，ID: ${nextId}, 图层: ${newLayer}, 当前最大图层: ${maxLayer}`);

  console.log('创建的新画布项:', newItem);

  // 更新画布项并返回
  const updatedItems = [...canvasItems, newItem];
  return {
    items: updatedItems,
    nextId: nextId + 1
  };
}

// 查找点击位置下的最上层画布项
function findTopItemAtPosition(items, position) {
  if (!items || items.length === 0) return null;

  // 按layer从大到小排序，先检查最上层的项
  const sortedItems = [...items].sort((a, b) => b.layer - a.layer);

  for (const item of sortedItems) {
    if (isPointInRotatedRect(position, item)) {
      return item;
    }
  }

  return null;
}

// 将项移动到最上层
function bringItemToTop(items, itemId) {
  if (!items || items.length <= 1) return items;

  // 找出当前最大的layer值
  const maxLayer = Math.max(...items.map(item => item.layer));

  // 更新指定项的layer值
  return items.map(item => {
    if (item.id === itemId) {
      return { ...item, layer: maxLayer + 1 };
    }
    return item;
  });
}

// 移动项
function moveItem(items, itemId, dx, dy) {
  return items.map(item => {
    if (item.id === itemId) {
      return {
        ...item,
        x: item.x + dx,
        y: item.y + dy
      };
    }
    return item;
  });
}

// 缩放项
function scaleItem(items, itemId, scale, center) {
  return items.map(item => {
    if (item.id === itemId) {
      // 获取原始宽高比，如果存在
      const aspectRatio = item.aspectRatio || (item.width / item.height);

      // 计算新的尺寸，保持宽高比
      const newWidth = item.width * scale;
      // 根据宽高比计算新高度，确保比例一致
      const newHeight = newWidth / aspectRatio;

      // 计算新的位置，保持缩放中心点不变
      const itemCenterX = item.x + item.width / 2;
      const itemCenterY = item.y + item.height / 2;

      // 中心点与缩放中心的向量
      const vectorX = itemCenterX - center.x;
      const vectorY = itemCenterY - center.y;

      // 缩放后的新中心点位置
      const newCenterX = center.x + vectorX * scale;
      const newCenterY = center.y + vectorY * scale;

      // 计算新的左上角坐标
      const newX = newCenterX - newWidth / 2;
      const newY = newCenterY - newHeight / 2;

      console.log(`缩放项目 ${item.id}: 宽高比 ${aspectRatio.toFixed(2)}, 新尺寸 ${newWidth.toFixed(0)}x${newHeight.toFixed(0)}`);

      return {
        ...item,
        x: newX,
        y: newY,
        width: newWidth,
        height: newHeight,
        aspectRatio: aspectRatio // 保存宽高比，确保一致性
      };
    }
    return item;
  });
}

// 旋转项
function rotateItem(items, itemId, angle) {
  return items.map(item => {
    if (item.id === itemId) {
      // 获取当前旋转角度
      const currentRotation = item.rotation || 0;

      // 计算新的旋转角度
      const newRotation = (currentRotation + angle) % 360;

      return {
        ...item,
        rotation: newRotation
      };
    }
    return item;
  });
}

// 删除项
function removeItem(items, itemId) {
  return items.filter(item => item.id !== itemId);
}

// 选中项目但不改变其图层
// 这个函数与 bringItemToTop 类似，但不会改变图层值
function selectItemWithoutLayerChange(items, itemId) {
  if (!items || items.length <= 1) return items;

  // 直接返回原始项目数组，不做任何图层变化
  return items;
}

module.exports = {
  getDistance,
  getAngle,
  isPointInRotatedRect,
  addClothToCanvas,
  findTopItemAtPosition,
  bringItemToTop,
  moveItem,
  scaleItem,
  rotateItem,
  removeItem,
  selectItemWithoutLayerChange
};