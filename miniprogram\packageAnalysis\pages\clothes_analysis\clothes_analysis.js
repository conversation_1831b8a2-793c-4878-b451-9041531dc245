// 引入uCharts图表库
import uCharts from './u-chart.mini.js';

// 创建图表实例对象
let uChartsInstance = {};

// 饼图颜色数组
const CHART_COLORS = ['#f3a54a', '#9b95c9', '#6fb1bd', '#e75e76', '#ae6852', '#e0cba1', '#b4d4a7', '#b37c57'];

// 引入体力值管理模块
const energyManager = require('../../../util/energyManager');

// DeepSeek API配置
const DEEPSEEK_API = {
  URL: 'https://api.deepseek.com/v1/chat/completions',  // DeepSeek API端点
  MODEL: 'deepseek-chat',                              // 使用的模型名称
  API_KEY: 'sk-0f7c2bc1c86447268586c52a5ee3cde2'           // 替换为实际的API密钥
};

// AI分析提示词
const AI_PROMPT = `作为一位专业的衣橱分析师，请根据以下用户衣物数据进行系统化的分析与评估：

用户衣柜数据：
{{userClothes}}

请按照以下5个模块进行全面分析，并严格遵循输出规范：

**模块1：衣橱基础概览**
1. 分析衣服类型分布：
   - 前5类目及占比（例：外套28%、T恤22%）
   - 是否存在单一类型占比>40%的失衡现象
2. 颜色分析结论：
   - 基础色（黑/白/灰/米）总占比
   - 同色系冗余警告（你居然有这么多同色系的衣服！！！，占比xx%）
3. 风格与品牌关联：
   - TOP3风格及对应高频品牌（例：通勤风-70%来自优衣库）

**模块2：使用效率分析**
1. 单次穿着成本分析：
   - 性价比TOP5（最低单次成本）清单：包含品类/品牌/价格/穿着次数（单次成本格式：xx元/次）
   - 资源浪费TOP5（最高单次成本+半年未穿）清单
2. 季节适配性结论：
   - 冬季衣物在冬季的实际穿着率（计算值）
   - 材质与季节错配预警（如夏季化纤类>50%）

**模块3：消费行为洞察**
1. 购买-穿着延迟模式：
   - 延迟超过3个月的典型品类（例：连衣裙平均延迟5个月）
   - 反季购买占比（如冬季购买夏装比例）
2. 品牌价值系数排名：
   - 公式：(品牌平均穿着次数/品牌平均价格)*100
   - 高价值品牌TOP3与低价值品牌TOP3

**模块4：搭配优化建议**
1. 色彩系统评价：
   - 基础色:点缀色比例（标准建议3:1）
   - 高频颜色组合（例：白色+牛仔裤出现率35%）
2. 低频单品激活方案：
   - 3套含低频单品的搭配公式（例：仅穿过2次的红色毛衣→红+灰+白组合）

**模块5：可持续诊断**
1. 环保评估：
   - 可持续材质占比及具体品类分布
   - 快时尚单品数量/平均使用寿命（月）
2. 淘汰建议：
   - 符合淘汰标准清单（同时满足：单次成本>200元+半年未穿）

**输出规范：**
- 每个模块以"### **结论**"开头
- 数据结论优先，异常值用【⚠警告】标注
- 表格需包含：品类/品牌/关键指标三要素
- 避免专业术语，用"相当于每穿1次花费XX元"等通俗描述

请严格按照以下JSON格式返回，确保JSON格式正确无误：
{
  "wardrobeAnalysis": {
    "styleProfile": "整体衣橱风格画像分析",
    "colorAnalysis": "色彩分析结论",
    "typeDistribution": "衣物类型分布分析"
  },
  "module1": {
    "title": "衣橱基础概览",
    "typesDistribution": "前5类目及占比分析",
    "imbalanceWarning": "是否存在失衡现象(布尔值)",
    "basicColorPercentage": "基础色占比分析",
    "colorRedundancyWarning": "同色系冗余警告",
    "styleAndBrandRelation": "TOP3风格及对应高频品牌"
  },
  "module2": {
    "title": "使用效率分析",
    "costEffectiveItems": [
      {"category": "品类", "brand": "品牌", "price": "价格", "wornCount": "穿着次数", "costPerWear": "单次成本"}
    ],
    "wastedItems": [
      {"category": "品类", "brand": "品牌", "price": "价格", "lastWornDate": "最后穿着日期", "wasteCost": "浪费成本"}
    ],
    "seasonalAdaptability": "季节适配性结论",
    "materialMismatchWarning": "材质与季节错配预警"
  },
  "module3": {
    "title": "消费行为洞察",
    "delayPatterns": "购买-穿着延迟模式分析",
    "offSeasonPurchasePercentage": "反季购买占比",
    "valuableRanking": {
      "highValue": [
        {"brand": "品牌", "avgWornCount": "平均穿着次数", "avgPrice": "平均价格", "valueCoefficient": "价值系数"}
      ],
      "lowValue": [
        {"brand": "品牌", "avgWornCount": "平均穿着次数", "avgPrice": "平均价格", "valueCoefficient": "价值系数"}
      ]
    }
  },
  "module4": {
    "title": "搭配优化建议",
    "colorSystemEvaluation": "色彩系统评价",
    "frequentCombinations": "高频颜色组合",
    "activationSuggestions": [
      {
        "item": "低频单品",
        "formula": "搭配公式",
        "description": "搭配说明"
      }
    ]
  },
  "module5": {
    "title": "可持续诊断",
    "sustainableMaterialPercentage": "可持续材质占比",
    "fastFashionStats": "快时尚单品数据",
    "eliminationSuggestions": [
      {"category": "品类", "brand": "品牌", "price": "价格", "lastWornDate": "最后穿着日期", "reason": "淘汰理由"}
    ]
  }
}

注意：确保分析具体、准确，突出数据异常，给出实用性建议，并严格按照指定格式输出。`;

Page({
  data: {
    // 基础数据
    userOpenId: '',
    clothes: [],
    statistics: {
      totalClothes: 0,
      totalPrice: 0,
      avgPrice: 0,
      favoriteBrand: '',
      favoriteColor: '',
      mostPurchaseDate: '',
      mostWornItem: '',
      leastWornItem: ''
    },

    // 衣柜相关
    wardrobes: [],                // 用户所有衣柜
    currentWardrobe: 'all',       // 当前选中的衣柜，'all'表示全部
    wardrobeNames: {},            // 衣柜ID到名称的映射
    wardrobePickerArray: ['全部衣柜'], // 下拉选择器的选项数组
    wardrobePickerIndex: 0,       // 下拉选择器当前选中的索引
    wardrobeIdArray: ['all'],     // 选项对应的衣柜ID数组
    isPickerOpen: false,          // 选择器是否打开状态

    // 图表相关
    currentChartType: 'category', // 默认显示分类统计
    cWidth: 750,
    cHeight: 500,
    chartData: null,
    chartPercentages: [],
    chartColors: CHART_COLORS,

    // 细分类相关
    isShowingSubcategory: false,  // 是否正在显示细分类
    selectedCategory: '',         // 选中的主分类名称
    selectedCategoryItems: [],     // 选中分类的所有衣物项目

    // 导航栏相关
    statusBarHeight: 20, // 默认值，会在onLoad中获取真实值

    // AI分析相关
    isAILoading: false,        // AI分析加载状态
    aiAnalysisResult: null,    // AI分析结果
    aiLocalProcessing: true,   // 启用本地处理（不调用云函数）

    // 购买日期筛选相关
    dateFilterOptions: ['全部时间', '近一个月', '近三个月', '近半年', '近一年', '自定义'],
    selectedDateFilter: '全部时间',
    customDateRange: {
      start: '',
      end: ''
    },
    showDateFilterPanel: false,   // 日期筛选面板是否显示
    displayDateFilter: '',        // 用于显示的日期筛选文本
    isDatePickerVisible: false,   // 日期选择器是否可见
    currentDatePickerType: '',    // 当前日期选择器类型：'start' 或 'end',

    // 筛选面板相关
    showFilterPanel: false,       // 筛选面板是否显示
    showChart: true,              // 图表是否显示
  },

  onLoad: function(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 获取用户OpenID
    this.getUserOpenId()
      .then(() => {
        // 计算图表尺寸
        const cWidth = 710 / 750 * wx.getSystemInfoSync().windowWidth;
        const cHeight = 500 / 750 * wx.getSystemInfoSync().windowWidth;
        this.setData({ cWidth, cHeight });

        // 加载用户衣柜
        this.loadUserWardrobes();

        // 显示本地处理提示
        if (this.data.aiLocalProcessing) {
          setTimeout(() => {
            wx.showToast({
              title: '可使用DeeoSeek-AI分析',
              icon: 'none',
              duration: 2000
            });
          }, 1000);
        }
      })
      .catch(err => {
        console.error('获取用户OpenID失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      });
  },

  // 导航返回功能
  navigateBack: function() {
    wx.navigateBack({
      delta: 1,
      fail: function() {
        // 如果返回失败（例如这是打开的第一个页面），则跳转到首页
        wx.switchTab({
          url: '/pages/wardrobe/wardrobe'
        });
      }
    });
  },

  // 获取用户OpenID
  getUserOpenId: function() {
    return new Promise((resolve, reject) => {
      // 尝试从本地存储获取
      const cachedOpenId = wx.getStorageSync('userOpenId');
      if (cachedOpenId) {
        this.setData({ userOpenId: cachedOpenId });
        console.log('从缓存获取用户OpenID:', cachedOpenId);
        resolve(cachedOpenId);
        return;
      }

      // 如果本地没有，则从云函数获取
      wx.cloud.callFunction({
        name: 'login',
      })
        .then(res => {
          const openid = res.result.openid;
          console.log('获取用户OpenID成功:', openid);

          // 保存到本地存储和数据中
          wx.setStorageSync('userOpenId', openid);
          this.setData({ userOpenId: openid });

          resolve(openid);
        })
        .catch(err => {
          console.error('获取用户OpenID失败:', err);
          reject(err);
        });
    });
  },

  // 加载用户衣柜信息
  loadUserWardrobes: function() {
    const userOpenId = this.data.userOpenId;
    if (!userOpenId) {
      console.error('未获取到用户OpenID，无法获取衣柜数据');
      return;
    }

    // 从本地缓存获取衣柜数据
    const wardrobesCacheKey = `user_wardrobes_${userOpenId}`;
    let wardrobesData = wx.getStorageSync(wardrobesCacheKey);

    if (wardrobesData && Array.isArray(wardrobesData) && wardrobesData.length > 0) {
      // 构建衣柜名称映射
      const wardrobeNames = {};
      // 构建选择器数组
      const wardrobePickerArray = ['全部衣柜'];
      const wardrobeIdArray = ['all'];

      wardrobesData.forEach(wardrobe => {
        if (wardrobe._id && wardrobe.name) {
          wardrobeNames[wardrobe._id] = wardrobe.name;
          wardrobePickerArray.push(wardrobe.name);
          wardrobeIdArray.push(wardrobe._id);
        }
      });

      this.setData({
        wardrobes: wardrobesData,
        wardrobeNames: wardrobeNames,
        wardrobePickerArray: wardrobePickerArray,
        wardrobeIdArray: wardrobeIdArray
      });

      console.log('从缓存加载衣柜数据成功，数量:', wardrobesData.length);
    } else {
      // 如果本地缓存没有，则从云数据库获取
      this.loadWardrobesFromCloud();
    }

    // 加载衣物数据（全部衣柜）
    this.loadClothesData();
  },

  // 从云端获取衣柜数据
  loadWardrobesFromCloud: function() {
    const userOpenId = this.data.userOpenId;

    wx.cloud.callFunction({
      name: 'getWardrobes',
      data: {
        openid: userOpenId
      },
      success: res => {
        if (res.result && res.result.data && res.result.data.length > 0) {
          const wardrobes = res.result.data;

          // 构建衣柜名称映射
          const wardrobeNames = {};
          // 构建选择器数组
          const wardrobePickerArray = ['全部衣柜'];
          const wardrobeIdArray = ['all'];

          wardrobes.forEach(wardrobe => {
            if (wardrobe._id && wardrobe.name) {
              wardrobeNames[wardrobe._id] = wardrobe.name;
              wardrobePickerArray.push(wardrobe.name);
              wardrobeIdArray.push(wardrobe._id);
            }
          });

          this.setData({
            wardrobes: wardrobes,
            wardrobeNames: wardrobeNames,
            wardrobePickerArray: wardrobePickerArray,
            wardrobeIdArray: wardrobeIdArray
          });

          // 缓存衣柜数据
          const cacheKey = `user_wardrobes_${userOpenId}`;
          wx.setStorageSync(cacheKey, wardrobes);

          console.log('从云端加载衣柜数据成功，数量:', wardrobes.length);
        } else {
          console.log('用户没有创建衣柜或获取衣柜数据为空');
        }
      },
      fail: err => {
        console.error('获取衣柜数据失败:', err);
      }
    });
  },

  // 切换选择器状态
  togglePickerState: function() {
    this.setData({
      isPickerOpen: true
    });
  },

  // 下拉选择器变化事件处理
  onWardrobeChange: function(e) {
    const index = e.detail.value;
    const wardrobeId = this.data.wardrobeIdArray[index];

    // 如果是同一个衣柜，不做任何操作
    if (this.data.currentWardrobe === wardrobeId) {
      this.setData({
        isPickerOpen: false
      });
      return;
    }

    this.setData({
      currentWardrobe: wardrobeId,
      wardrobePickerIndex: index,
      // 切换衣柜时重置细分类状态
      isShowingSubcategory: false,
      selectedCategory: '',
      selectedCategoryItems: [],
      isPickerOpen: false,
      // 关闭筛选面板
      showFilterPanel: false,
      // 显示图表
      showChart: true
    });

    // 重新加载该衣柜的衣物数据
    this.loadClothesData();
  },

  // 切换筛选面板显示状态
  toggleFilterPanel: function() {
    const newFilterPanelState = !this.data.showFilterPanel;

    this.setData({
      showFilterPanel: newFilterPanelState,
      // 当关闭筛选面板时，同时关闭日期筛选面板
      showDateFilterPanel: newFilterPanelState ? this.data.showDateFilterPanel : false,
      // 当打开筛选面板时，隐藏图表；当关闭筛选面板时，显示图表
      showChart: !newFilterPanelState
    });

    // 如果关闭了筛选面板，重新绘制图表
    if (!newFilterPanelState) {
      // 延迟一下再绘制图表，确保DOM已更新
      setTimeout(() => {
        this.drawChart(this.data.currentChartType);
      }, 300);
    }
  },

  // 切换日期筛选面板显示状态
  toggleDateFilterPanel: function() {
    this.setData({
      showDateFilterPanel: !this.data.showDateFilterPanel
    });
  },

  // 选择日期筛选选项
  selectDateFilter: function(e) {
    const filter = e.currentTarget.dataset.filter;

    // 如果选择的是当前选项，则关闭面板
    if (filter === this.data.selectedDateFilter) {
      this.setData({
        showDateFilterPanel: false
      });
      return;
    }

    // 如果选择的是自定义，显示日期选择器
    if (filter === '自定义') {
      this.setData({
        selectedDateFilter: filter,
        showDateFilterPanel: false,
        isDatePickerVisible: true,
        // 重置日期范围
        customDateRange: {
          start: '',
          end: ''
        }
      });
      return;
    }

    // 更新选中的日期筛选选项
    this.setData({
      selectedDateFilter: filter,
      displayDateFilter: filter !== '全部时间' ? filter : '',
      showDateFilterPanel: false,
      // 关闭筛选面板
      showFilterPanel: false,
      // 显示图表
      showChart: true
    });

    // 重新加载衣物数据
    this.loadClothesData();
  },

  // 开始日期选择器变化事件
  onStartDateChange: function(e) {
    const date = e.detail.value;

    // 更新自定义日期范围
    const customDateRange = {...this.data.customDateRange};
    customDateRange.start = date;

    this.setData({
      customDateRange: customDateRange
    });
  },

  // 结束日期选择器变化事件
  onEndDateChange: function(e) {
    const date = e.detail.value;

    // 更新自定义日期范围
    const customDateRange = {...this.data.customDateRange};
    customDateRange.end = date;

    this.setData({
      customDateRange: customDateRange
    });
  },

  // 确认日期范围选择
  confirmDateRange: function() {
    const customDateRange = this.data.customDateRange;

    // 格式化显示文本
    let displayText = '自定义';
    if (customDateRange.start && customDateRange.end) {
      displayText = `${customDateRange.start} 至 ${customDateRange.end}`;
    } else if (customDateRange.start) {
      displayText = `${customDateRange.start} 至 今天`;
    } else if (customDateRange.end) {
      displayText = `至 ${customDateRange.end}`;
    }

    this.setData({
      displayDateFilter: displayText,
      isDatePickerVisible: false,
      // 关闭筛选面板
      showFilterPanel: false,
      // 显示图表
      showChart: true
    });

    // 重新加载衣物数据
    this.loadClothesData();
  },

  // 取消日期选择
  cancelDatePicker: function() {
    this.setData({
      isDatePickerVisible: false
    });
  },

  // 清除日期筛选
  clearDateFilter: function() {
    this.setData({
      selectedDateFilter: '全部时间',
      displayDateFilter: '',
      customDateRange: {
        start: '',
        end: ''
      },
      showDateFilterPanel: false,
      // 关闭筛选面板
      showFilterPanel: false,
      // 显示图表
      showChart: true
    });

    // 重新加载衣物数据
    this.loadClothesData();
  },

  // 加载衣物数据
  loadClothesData: function() {
    const userOpenId = this.data.userOpenId;
    if (!userOpenId) {
      console.error('未获取到用户OpenID，无法获取衣物数据');
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '正在加载数据...',
      mask: true
    });

    // 从本地缓存获取衣物数据
    const cacheKey = `user_clothes_cache_${userOpenId}`;
    let clothesData = wx.getStorageSync(cacheKey);

    if (clothesData && clothesData.clothes && clothesData.clothes.length > 0) {
      // 从缓存获取到数据
      console.log('从本地缓存获取衣物数据成功，数据条数：', clothesData.clothes.length);
      this.filterAndProcessClothesData(clothesData.clothes);
    } else {
      // 没有缓存数据，尝试从云端获取
      this.loadClothesFromCloud();
    }
  },

  // 从云端获取衣物数据
  loadClothesFromCloud: function() {
    const that = this;
    const userOpenId = this.data.userOpenId;

    wx.cloud.callFunction({
      name: 'getClothes',
      data: {
        openid: userOpenId
      },
      success: function(res) {
        if (res.result && res.result.data) {
          console.log('从云端获取衣物数据成功，数据条数：', res.result.data.length);

          // 缓存数据
          const cacheKey = `user_clothes_cache_${userOpenId}`;
          wx.setStorageSync(cacheKey, {
            clothes: res.result.data,
            timestamp: Date.now()
          });

          that.filterAndProcessClothesData(res.result.data);
        } else {
          // 即使没有衣物数据，也调用processClothesData来更新UI
          that.processClothesData([]);
          wx.showToast({
            title: '暂无衣物数据',
            icon: 'none'
          });
        }
      },
      fail: function(err) {
        console.error('获取衣物数据失败:', err);
        // 即使获取数据失败，也调用processClothesData来更新UI
        that.processClothesData([]);
        wx.showToast({
          title: '获取衣物数据失败',
          icon: 'none'
        });
      }
    });
  },

  // 根据当前选中的衣柜和日期过滤衣物数据并处理
  filterAndProcessClothesData: function(allClothes) {
    if (!allClothes || allClothes.length === 0) {
      // 即使没有衣物数据，也调用processClothesData来更新UI
      this.processClothesData([]);
      return;
    }

    let filteredClothes = allClothes;

    // 如果选择了特定衣柜，则过滤出该衣柜的衣物
    if (this.data.currentWardrobe !== 'all') {
      filteredClothes = allClothes.filter(item =>
        item.wardrobeId === this.data.currentWardrobe
      );

      console.log(`已过滤衣柜[${this.data.currentWardrobe}]的衣物，共${filteredClothes.length}件`);
    }

    // 应用日期筛选
    if (this.data.selectedDateFilter !== '全部时间') {
      filteredClothes = this.filterClothesByDate(filteredClothes);
      console.log(`应用日期筛选[${this.data.selectedDateFilter}]后，剩余${filteredClothes.length}件衣物`);
    }

    // 保存处理后的衣物数据
    this.processClothesData(filteredClothes);
  },

  // 根据选择的日期范围筛选衣物
  filterClothesByDate: function(clothes) {
    const dateFilter = this.data.selectedDateFilter;
    let startDate = null;
    let endDate = new Date(); // 默认结束日期为今天
    endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间

    // 根据选择的日期范围设置起始日期
    switch (dateFilter) {
      case '近一个月':
        startDate = new Date();
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case '近三个月':
        startDate = new Date();
        startDate.setMonth(endDate.getMonth() - 3);
        break;
      case '近半年':
        startDate = new Date();
        startDate.setMonth(endDate.getMonth() - 6);
        break;
      case '近一年':
        startDate = new Date();
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case '自定义':
        // 使用自定义日期范围
        const customRange = this.data.customDateRange;

        // 设置开始日期
        if (customRange.start) {
          startDate = new Date(customRange.start);
          startDate.setHours(0, 0, 0, 0); // 设置为当天开始时间
        }

        // 设置结束日期
        if (customRange.end) {
          endDate = new Date(customRange.end);
          endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间
        }
        break;
      default:
        return clothes; // 默认返回所有衣物
    }

    // 筛选出购买日期在指定范围内的衣物
    return clothes.filter(item => {
      if (!item.purchaseDate) return false;

      const purchaseDate = new Date(item.purchaseDate);
      return !isNaN(purchaseDate.getTime()) &&
             (!startDate || purchaseDate >= startDate) &&
             purchaseDate <= endDate;
    });
  },

  // 处理衣物数据，生成统计信息
  processClothesData: function(clothes) {
    // 确保clothes是一个数组
    clothes = clothes || [];

    // 保存衣物数据
    this.setData({
      clothes: clothes,
      'statistics.totalClothes': clothes.length
    });

    // 如果没有衣物数据，重置统计信息并更新图表
    if (clothes.length === 0) {
      this.setData({
        'statistics.totalPrice': 0,
        'statistics.avgPrice': 0,
        'statistics.favoriteBrand': '-',
        'statistics.favoriteColor': '-',
        'statistics.mostPurchaseDate': '-',
        'statistics.mostWornItem': '-',
        'statistics.leastWornItem': '-'
      });

      // 绘制空图表
      this.drawEmptyChart();

      wx.hideLoading();

      // 显示提示
      wx.showToast({
        title: '没有符合筛选条件的衣物',
        icon: 'none',
        duration: 2000
      });

      return;
    }

    // 统计衣物总价和平均价格
    let totalPrice = 0;
    let validPriceCount = 0;
    clothes.forEach(item => {
      if (item.price && !isNaN(item.price)) {
        totalPrice += Number(item.price);
        validPriceCount++;
      }
    });

    // 将总价格保留两位小数
    const formattedTotalPrice = parseFloat(totalPrice.toFixed(2));
    const avgPrice = validPriceCount > 0 ? (totalPrice / validPriceCount).toFixed(2) : 0;

    // 统计品牌、颜色等频率
    const brandCount = this.countFrequency(clothes, 'brand');
    const colorCount = this.countFrequency(clothes, 'color');
    const purchaseDateCount = this.countPurchaseDates(clothes);

    // 计算最常穿和最少穿的衣物
    let mostWornItem = { name: '-', count: 0 };
    let leastWornItem = { name: clothes[0].name, count: clothes[0].wornCount || 0 };

    clothes.forEach(item => {
      const wornCount = item.wornCount || 0;
      if (wornCount > mostWornItem.count) {
        mostWornItem = { name: item.name, count: wornCount };
      }
      if (wornCount < leastWornItem.count || leastWornItem.count === undefined) {
        leastWornItem = { name: item.name, count: wornCount };
      }
    });

    // 更新统计数据
    this.setData({
      'statistics.totalPrice': formattedTotalPrice,
      'statistics.avgPrice': avgPrice,
      'statistics.favoriteBrand': brandCount.length > 0 ? brandCount[0].name : '-',
      'statistics.favoriteColor': colorCount.length > 0 ? colorCount[0].name : '-',
      'statistics.mostPurchaseDate': purchaseDateCount.length > 0 ? purchaseDateCount[0].month : '-',
      'statistics.mostWornItem': mostWornItem.name,
      'statistics.leastWornItem': leastWornItem.name
    });

    // 绘制默认图表
    this.drawChart(this.data.currentChartType);

    wx.hideLoading();
  },

  // 统计某个属性的频率
  countFrequency: function(items, prop) {
    const counts = {};

    items.forEach(item => {
      if (item[prop]) {
        const value = item[prop];
        counts[value] = (counts[value] || 0) + 1;
      }
    });

    // 转换为数组并排序
    const result = Object.keys(counts).map(key => ({
      name: key,
      count: counts[key]
    })).sort((a, b) => b.count - a.count);

    return result;
  },

  // 统计购买日期
  countPurchaseDates: function(items) {
    const months = {};

    items.forEach(item => {
      if (item.purchaseDate) {
        // 解析购买日期，提取年月
        const date = new Date(item.purchaseDate);
        if (!isNaN(date.getTime())) {
          const yearMonth = `${date.getFullYear()}-${date.getMonth() + 1}`;
          months[yearMonth] = (months[yearMonth] || 0) + 1;
        }
      }
    });

    // 转换为数组并排序
    const result = Object.keys(months).map(key => ({
      month: key,
      count: months[key]
    })).sort((a, b) => b.count - a.count);

    return result;
  },

  // 切换图表类型
  switchChart: function(e) {
    const chartType = e.currentTarget.dataset.type;

    // 切换图表类型时重置细分类状态
    this.setData({
      currentChartType: chartType,
      isShowingSubcategory: false,
      selectedCategory: '',
      selectedCategoryItems: []
    });

    this.drawChart(chartType);
  },

  // 绘制图表
  drawChart: function(chartType) {
    // 如果正在显示细分类且当前图表类型是分类统计，则显示细分类数据
    if (this.data.isShowingSubcategory && chartType === 'category') {
      this.drawSubcategoryChart();
      return;
    }

    let chartData = {};

    switch (chartType) {
      case 'category':
        chartData = this.getCategoryChartData();
        break;
      case 'color':
        chartData = this.getColorChartData();
        break;
      case 'season':
        chartData = this.getSeasonChartData();
        break;
      case 'brand':
        chartData = this.getBrandChartData();
        break;
      case 'style':
        chartData = this.getStyleChartData();
        break;
      default:
        chartData = this.getCategoryChartData();
    }

    // 计算每个分类的百分比
    this.calculatePercentages(chartData);

    // 保存图表数据到页面数据
    this.setData({
      chartData: chartData
    });

    // 绘制饼图
    this.drawPieChart(chartData);
  },

  // 绘制细分类图表
  drawSubcategoryChart: function() {
    const subcategoryData = this.getSubcategoryChartData();

    // 计算每个细分类的百分比
    this.calculatePercentages(subcategoryData);

    // 保存图表数据到页面数据
    this.setData({
      chartData: subcategoryData
    });

    // 绘制饼图
    this.drawPieChart(subcategoryData);
  },

  // 获取细分类统计数据
  getSubcategoryChartData: function() {
    const items = this.data.selectedCategoryItems;

    // 统计type_details字段的出现频率
    const subcategoryCounts = {};
    items.forEach(item => {
      const typeDetail = item.type_detail || '未分类';
      subcategoryCounts[typeDetail] = (subcategoryCounts[typeDetail] || 0) + 1;
    });

    // 转换为数组并排序
    const subcategoryData = Object.keys(subcategoryCounts).map(key => ({
      name: key,
      count: subcategoryCounts[key]
    })).sort((a, b) => b.count - a.count);

    return this.formatChartData(subcategoryData, `${this.data.selectedCategory}细分类`);
  },

  // 返回主分类统计
  backToMainCategory: function() {
    this.setData({
      isShowingSubcategory: false,
      selectedCategory: '',
      selectedCategoryItems: []
    });

    this.drawChart('category');
  },

  // 点击图表事件
  touchEndHandler: function(e) {
    console.log('触发touchEndHandler事件', e);

    // 确保uChartsInstance存在
    if (!uChartsInstance) {
      console.log('uChartsInstance不存在');
      return;
    }

    try {
      // 尝试显示工具提示
      uChartsInstance.showToolTip(e);

      // 处理点击事件
      this.handleChartClick(e);
    } catch (error) {
      console.error('处理图表点击事件出错:', error);
    }
  },

  // 处理图表点击的帮助函数
  handleChartClick: function(e) {
    // 只在分类统计且不是细分类视图下处理点击
    if (this.data.currentChartType === 'category' && !this.data.isShowingSubcategory) {
      // 获取点击的索引
      const tapIndex = uChartsInstance.getCurrentDataIndex(e);
      console.log('点击索引:', tapIndex);

      // 如果点击到了图表的某个部分
      if (tapIndex !== -1 && tapIndex < this.data.chartData.categories.length) {
        this.showSubcategoryIfAvailable(tapIndex);
      }
    }
  },

  // 处理图例点击事件
  handleLegendTap: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    console.log('图例点击索引:', index);

    if (!isNaN(index) && index < this.data.chartData.categories.length) {
      this.showSubcategoryIfAvailable(index);
    }
  },

  // 显示细分类（如果可用）
  showSubcategoryIfAvailable: function(categoryIndex) {
    const selectedCategory = this.data.chartData.categories[categoryIndex];
    console.log('选中分类:', selectedCategory);

    // 获取所有属于该分类的衣物
    const categoryItems = this.data.clothes.filter(item =>
      item.category === selectedCategory
    );

    // 如果该分类有衣物且有type_details字段
    if (categoryItems.length > 0) {
      // 检查是否至少有一件衣物有type_details
      const hasTypeDetails = categoryItems.some(item => item.type_detail);

      if (hasTypeDetails) {
        this.setData({
          isShowingSubcategory: true,
          selectedCategory: selectedCategory,
          selectedCategoryItems: categoryItems
        });

        // 绘制细分类图表
        this.drawSubcategoryChart();

        wx.showToast({
          title: `显示${selectedCategory}细分类`,
          icon: 'none'
        });

        return true;
      }
    }

    // 如果没有细分类数据，提示用户
    wx.showToast({
      title: `${selectedCategory}没有细分类数据`,
      icon: 'none'
    });

    return false;
  },

  // 添加触摸开始事件处理
  touchStartHandler: function(e) {
    console.log('触发touchStartHandler事件', e);

    // 确保uChartsInstance存在
    if (!uChartsInstance) {
      console.log('uChartsInstance不存在');
      return;
    }

    try {
      // 将事件传递给图表实例
      uChartsInstance.touchLegend(e);
      uChartsInstance.touchStart(e);
    } catch (error) {
      console.error('处理触摸开始事件出错:', error);
    }
  },

  // 添加触摸移动事件处理
  touchMoveHandler: function(e) {
    console.log('触发touchMoveHandler事件', e);

    // 确保uChartsInstance存在
    if (!uChartsInstance) {
      console.log('uChartsInstance不存在');
      return;
    }

    try {
      // 将事件传递给图表实例
      uChartsInstance.touchMove(e);
    } catch (error) {
      console.error('处理触摸移动事件出错:', error);
    }
  },

  // 计算每个分类的百分比
  calculatePercentages: function(chartData) {
    if (!chartData || !chartData.series || !chartData.series[0] || !chartData.series[0].data) {
      return;
    }

    const data = chartData.series[0].data;
    const total = data.reduce((sum, val) => sum + val, 0);

    const percentages = data.map(val => {
      return ((val / total) * 100).toFixed(2);
    });

    this.setData({
      chartPercentages: percentages
    });
  },

  // 获取分类统计数据
  getCategoryChartData: function() {
    const categoryCount = this.countFrequency(this.data.clothes, 'category');
    return this.formatChartData(categoryCount, '分类');
  },

  // 获取颜色统计数据
  getColorChartData: function() {
    const colorCount = this.countFrequency(this.data.clothes, 'color');
    return this.formatChartData(colorCount, '颜色');
  },

  // 获取季节统计数据
  getSeasonChartData: function() {
    // 使用专门的季节统计函数，将组合季节拆分为单独的季节
    const seasonCount = this.countSeasonFrequency(this.data.clothes);
    return this.formatChartData(seasonCount, '季节');
  },

  // 统计季节频率，处理组合季节（如“春季/夏季”）
  countSeasonFrequency: function(items) {
    const counts = {
      '春季': 0,
      '夏季': 0,
      '秋季': 0,
      '冬季': 0
    };

    items.forEach(item => {
      if (item.season) {
        // 拆分组合季节字符串
        const seasons = item.season.split('/');

        // 为每个季节增加计数
        seasons.forEach(season => {
          if (season && counts[season] !== undefined) {
            counts[season] += 1;
          }
        });
      }
    });

    // 转换为数组并排序
    const result = Object.keys(counts).map(key => ({
      name: key,
      count: counts[key]
    })).filter(item => item.count > 0).sort((a, b) => b.count - a.count);

    return result;
  },

  // 获取品牌统计数据
  getBrandChartData: function() {
    const brandCount = this.countFrequency(this.data.clothes, 'brand');
    return this.formatChartData(brandCount, '品牌');
  },

  // 获取风格统计数据
  getStyleChartData: function() {
    const styleCount = this.countFrequency(this.data.clothes, 'style');
    return this.formatChartData(styleCount, '风格');
  },

  // 格式化图表数据
  formatChartData: function(data, seriesName) {
    // 限制最多显示8个类别，其余归为"其他"
    const MAX_CATEGORIES = 8;

    let categories = [];
    let series = [];

    if (data.length <= MAX_CATEGORIES) {
      // 数据量较少，直接使用
      categories = data.map(item => item.name || '未知');
      series = [{
        name: seriesName,
        data: data.map(item => item.count)
      }];
    } else {
      // 数据量较多，合并为"其他"
      const mainData = data.slice(0, MAX_CATEGORIES - 1);
      const otherData = data.slice(MAX_CATEGORIES - 1);

      const otherCount = otherData.reduce((sum, item) => sum + item.count, 0);

      categories = mainData.map(item => item.name || '未知');
      categories.push('其他');

      series = [{
        name: seriesName,
        data: mainData.map(item => item.count)
      }];
      series[0].data.push(otherCount);
    }

    return {
      categories: categories,
      series: series
    };
  },

  // 绘制空图表（当没有数据时）
  drawEmptyChart: function() {
    const ctx = wx.createCanvasContext('analysisChart', this);

    // 创建一个空的图表数据
    const emptyData = {
      categories: ['无数据'],
      series: [{
        name: '无数据',
        data: [1]
      }]
    };

    // 计算百分比
    this.setData({
      chartPercentages: ['100.00'],
      chartData: emptyData
    });

    try {
      uChartsInstance = new uCharts({
        type: 'pie',
        context: ctx,
        width: this.data.cWidth,
        height: this.data.cHeight,
        series: [{
          name: '无数据',
          data: 1
        }],
        animation: true,
        background: '#FFFFFF',
        color: ['#EEEEEE'], // 使用灰色表示无数据
        padding: [5, 5, 15, 5],
        dataLabel: true,
        enableScroll: false,
        touchMoveLimit: 5,
        legend: {
          show: false
        },
        title: {
          name: '',
          fontSize: 0
        },
        subtitle: {
          name: '',
          fontSize: 0
        },
        extra: {
          pie: {
            activeOpacity: 0.6,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: false,
            borderWidth: 2,
            borderColor: '#FFFFFF',
            linearType: 'none'
          }
        }
      });

      uChartsInstance.draw();
    } catch (error) {
      console.error('绘制空图表出错:', error);
    }
  },

  // 绘制饼图
  drawPieChart: function(data) {
    const ctx = wx.createCanvasContext('analysisChart', this);

    try {
      uChartsInstance = new uCharts({
        type: 'pie',
        context: ctx,
        width: this.data.cWidth,
        height: this.data.cHeight,
        series: data.series[0].data.map((item, index) => {
          return {
            name: data.categories[index],
            data: item
          };
        }),
        animation: true,
        background: '#FFFFFF',
        color: this.data.chartColors,
        padding: [5, 5, 15, 5],
        dataLabel: true,
        enableScroll: false, // 禁用滚动，增强点击响应
        touchMoveLimit: 5,   // 增加触摸敏感度
        legend: {
          show: false  // 隐藏图例，因为我们自定义了图例区域
        },
        title: {
          name: '',   // 清空标题，因为我们自定义了标题区域
          fontSize: 0
        },
        subtitle: {
          name: '',   // 清空副标题，因为我们自定义了副标题区域
          fontSize: 0
        },
        extra: {
          pie: {
            activeOpacity: 0.6,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,  // 增加标签宽度以容纳更多文本
            border: false,
            borderWidth: 2,
            borderColor: '#FFFFFF',
            linearType: null, // 使用纯色，不使用渐变
            customRadius: 85,
            customLabel: false,
            format: (val, name, series) => {
              // 返回百分比和类别名称
              const percentage = ((val / series.reduce((a, b) => a + b)) * 100).toFixed(2);
              return `${percentage}%\n${name}`;
            },
            // 增加点击响应区域
            activeRadius: 15,
            offsetAngle: 0,
            labelWidth: 15,
            border: false,
            borderWidth: 2,
            borderColor: '#FFFFFF'
          },
          tooltip: {
            showBox: true,
            showArrow: true,
            borderWidth: 0,
            borderRadius: 5,
            borderColor: '#000000',
            borderOpacity: 0.7,
            bgColor: '#000000',
            bgOpacity: 0.7,
            gridType: 'solid',
            dashLength: 4,
            gridColor: '#CCCCCC',
            fontColor: '#FFFFFF',
            splitLine: true
          }
        },
        // 增加点击事件回调
        onTouchStart: (e) => {
          console.log('uCharts onTouchStart', e);
        },
        onTouchMove: (e) => {
          console.log('uCharts onTouchMove', e);
        },
        onTouchEnd: (e) => {
          console.log('uCharts onTouchEnd', e);
        }
      });

      // 立即绘制图表
      ctx.draw(true, () => {
        console.log('饼图绘制完成');
      });
    } catch (error) {
      console.error('绘制饼图出错:', error);
    }
  },

  // 页面显示
  onShow: function() {
    // 如果有图表实例，重新绘制
    if (uChartsInstance) {
      this.drawChart(this.data.currentChartType);
    } else if (this.data.chartData) {
      // 如果有数据但没有图表实例，初始化图表
      this.drawChart(this.data.currentChartType);
    }
  },

  // 页面尺寸变化时重新绘制图表
  onResize: function() {
    if (uChartsInstance) {
      this.drawChart(this.data.currentChartType);
    }
  },

  // 页面卸载
  onUnload: function() {
    // 清除图表实例
    uChartsInstance = null;
  },

  // 开始AI衣橱分析
  startAIClothesAnalysis: function() {
    const that = this;

    // 设置加载状态
    this.setData({
      isAILoading: true,
      aiAnalysisResult: null
    });

    // 获取用户OpenID
    const userOpenId = wx.getStorageSync('userOpenId');
    if (!userOpenId) {
      wx.showToast({
        title: '用户未登录',
        icon: 'none'
      });
      this.setData({ isAILoading: false });
      return;
    }

    // 检查用户是否有足够的体力值
    energyManager.consumeEnergyForAction('WARDROBE_ANALYSIS', userOpenId)
      .then(result => {
        if (!result.success) {
          // 体力值不足，显示提示
          wx.hideLoading();
          this.setData({ isAILoading: false });
          wx.showModal({
            title: '体力值不足',
            content: '您的体力值不足，无法执行AI衣橱分析操作。请通过观看广告或完成每日任务获取更多体力值。',
            confirmText: '去获取',
            cancelText: '取消',
            complete: () => {
              // 确保在对话框关闭后，无论用户点击了哪个按钮，都隐藏loading状态
              wx.hideLoading();
              this.setData({ isAILoading: false });
            },
            success: (res) => {
              if (res.confirm) {
                // 跳转到设置页面获取体力值
                wx.navigateTo({
                  url: '/page/settings/settings'
                });
              }
            }
          });
          return;
        }

        console.log('体力值检查通过，已消耗', result.consumedEnergy, '点体力值，开始AI衣橱分析流程');
        wx.showToast({
          title: '推理模型需要时间比较久，请耐心等待...',
          icon: 'none',
          duration: 2000
        });

        // 获取用户衣物数据
        this.getUserClothes().then(userClothes => {
          console.log('获取数据成功，衣物数据已准备就绪');

          // 更新加载提示


          // 格式化衣物数据
          const formattedClothes = this.formatUserClothes(userClothes);

          // 构建AI提示
          const prompt = AI_PROMPT.replace('{{userClothes}}', formattedClothes);

          console.log('构建的AI提示:', prompt);

          // 调用DeepSeek API（直接调用而非通过云函数）
          return this.callDeepSeekAPI(prompt).then(responseText => {
            // 隐藏加载提示
            wx.hideLoading();

            // 处理AI分析结果
            return this.processAIAnalysisResult(responseText);
          });
        }).catch(err => {
          console.error('AI衣橱分析过程出错:', err);

          // 隐藏加载提示
          wx.hideLoading();

          this.setData({
            isAILoading: false
          });

          // 区分本地和在线分析的错误提示
          if (this.data.aiLocalProcessing) {
            wx.showToast({
              title: '本地分析数据失败',
              icon: 'none',
              duration: 2000
            });
          } else {
            wx.showToast({
              title: '获取AI分析失败',
              icon: 'none',
              duration: 2000
            });
          }
        }).finally(() => {
          // 确保无论成功还是失败，都要清除加载状态
          if (this.data.isAILoading) {
            this.setData({
              isAILoading: false
            });
          }
        });
      });

    // 确保加载提示被隐藏
    wx.hideLoading();
  },

  // 获取用户衣物数据
  getUserClothes: function() {
    return new Promise((resolve, reject) => {
      // 如果已有衣物数据，直接使用
      if (this.data.clothes && this.data.clothes.length > 0) {
        console.log('使用页面已有衣物数据');
        resolve(this.data.clothes);
        return;
      }

      try {
        // 从缓存中获取用户衣物数据
        const cacheKey = 'user_clothes_cache_' + this.data.userOpenId;
        const userClothesCache = wx.getStorageSync(cacheKey);
        if (userClothesCache && userClothesCache.clothes && userClothesCache.clothes.length > 0) {
          console.log('使用缓存的用户衣物数据');

          // 根据当前选中的衣柜过滤数据
          let filteredClothes = userClothesCache.clothes;
          if (this.data.currentWardrobe !== 'all') {
            filteredClothes = userClothesCache.clothes.filter(item =>
              item.wardrobeId === this.data.currentWardrobe
            );
          }

          resolve(filteredClothes);
          return;
        }

        // 缓存不存在或为空，从云数据库获取
        const db = wx.cloud.database();
        let query = db.collection('clothes').where({
          _openid: this.data.userOpenId
        });

        // 如果选择了特定衣柜，增加条件过滤
        if (this.data.currentWardrobe !== 'all') {
          query = query.where({
            wardrobeId: this.data.currentWardrobe
          });
        }

        query.get()
          .then(res => {
            console.log('获取用户衣物数据成功:', res.data);

            // 缓存用户衣物数据（保存全部数据）
            wx.setStorageSync(cacheKey, {
              clothes: res.data,
              timestamp: new Date().getTime()
            });

            resolve(res.data);
          })
          .catch(err => {
            console.error('获取用户衣物数据失败:', err);
            reject(err);
          });
      } catch (err) {
        console.error('获取用户衣物数据出错:', err);
        reject(err);
      }
    });
  },

  // 格式化用户衣物数据
  formatUserClothes: function(userClothes) {
    if (!userClothes || userClothes.length === 0) return '用户衣柜为空';

    try {
      let clothesText = '';

      // 按类型分组衣物
      const clothesByCategory = {};

      // 将衣物按类型分组
      userClothes.forEach(item => {
        const category = item.category || '未分类';
        if (!clothesByCategory[category]) {
          clothesByCategory[category] = [];
        }
        clothesByCategory[category].push(item);
      });

      // 记录总数
      let totalCount = 0;

      // 按类型输出，每种类型随机最多30件
      Object.keys(clothesByCategory).forEach(category => {
        let clothes = clothesByCategory[category];

        // 如果该类型超过30件，随机选择30件
        if (clothes.length > 30) {
          // 随机洗牌算法，确保随机性
          for (let i = clothes.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [clothes[i], clothes[j]] = [clothes[j], clothes[i]];
          }
          clothes = clothes.slice(0, 30);
        }

        // 添加类别标题
        clothesText += `\n【${category}】类别衣物(${clothes.length}件)：\n`;

        // 添加衣物详情
        clothes.forEach((item, index) => {
          clothesText += `名称: "${item.name}", 类型: "${category}", 细分类: "${item.type_detail || '未知'}", 品牌:"${item.brand || '未知'}", 材质: "${item.material || '未知'}", 价格: "${item.price || '未知'}", 购买渠道: "${item.purchaseChannel || '未知'}", 购买时间: "${item.purchaseDate || '未知'}", 穿着次数: "${item.wornCount || '未知'}", 上次穿着时间: "${item.lastWornDate || '未知'}", 颜色: "${item.color || '未知'}", 风格: "${item.style || '未知'}"，季节: "${item.season || '未知'}"，备注: "${item.remark || '无'}"\n`;
        });

        totalCount += clothes.length;
      });

      // 添加衣物总数信息
      clothesText = `用户衣柜共${totalCount}件衣物，分类如下：` + clothesText;

      return clothesText;
    } catch (err) {
      console.error('格式化用户衣物数据出错:', err);
      return '用户衣物数据格式化失败';
    }
  },

  // 直接调用DeepSeek API，不使用云函数
  callDeepSeekAPI: function(prompt) {
    return new Promise((resolve, reject) => {
      try {
        console.log('准备直接调用DeepSeek API');

        // 检查参数
        if (!prompt || typeof prompt !== 'string' || prompt.trim() === '') {
          console.error('DeepSeek API调用失败: 提示词为空');
          reject(new Error('提示词不能为空'));
          return;
        }



        // 构建请求参数
        const requestData = {
          model: DEEPSEEK_API.MODEL,
          messages: [
            {
              role: "system",
              content: "你是一位专业的时尚顾问AI助手。请根据用户提供的信息，进行专业的衣橱分析。你应该只输出JSON格式的回复，不要有任何额外的描述或说明。"
            },
            {
              role: "user",
              content: prompt
            }
          ],
          temperature: 0.5,
          max_tokens: 2000
        };

        // 发送请求
        wx.request({
          url: DEEPSEEK_API.URL,
          method: 'POST',
          header: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${DEEPSEEK_API.API_KEY}`
          },
          timeout: 1000000,
          data: requestData,
          success: (res) => {
            console.log('DeepSeek API调用成功:', res.data);

            // 检查返回结果的有效性
            if (res.data && res.data.choices && res.data.choices.length > 0) {
              const responseText = res.data.choices[0].message.content;
              resolve(responseText);
            } else {
              console.warn('DeepSeek API返回的数据无效:', res.data);
              reject(new Error('DeepSeek API返回的数据无效'));
            }
          },
          fail: (err) => {
            console.error('调用DeepSeek API失败:', err);

            // 如果API调用失败，尝试使用本地模拟响应
            console.log('API调用失败，使用本地模拟响应');
            const mockResponse = this.generateMockResponse();
            resolve(mockResponse);
          }
        });
      } catch (err) {
        console.error('调用DeepSeek API出错:', err);

        // 显示错误提示
        wx.showToast({
          title: 'AI服务暂时不可用，使用本地分析',
          icon: 'none',
          duration: 2000
        });

        // 出错时使用本地模拟响应
        const mockResponse = this.generateMockResponse();
        resolve(mockResponse);
      }
    });
  },

  // 生成模拟的AI分析响应（当API不可用时使用）
  generateMockResponse: function() {
    // 创建一个基于页面数据的模拟分析结果
    const clothes = this.data.clothes || [];
    const totalCount = clothes.length;

    if (totalCount === 0) {
      return JSON.stringify({
        "wardrobeAnalysis": {
          "styleProfile": "无法分析，衣橱中没有衣物数据。",
          "colorAnalysis": "无法分析，衣橱中没有衣物数据。",
          "typeDistribution": "无法分析，衣橱中没有衣物数据。"
        },
        "module1": {
          "title": "衣橱基础概览",
          "typesDistribution": "无数据",
          "imbalanceWarning": false,
          "basicColorPercentage": "0%",
          "colorRedundancyWarning": "",
          "styleAndBrandRelation": "无数据"
        },
        "module2": { "title": "使用效率分析", "costEffectiveItems": [], "wastedItems": [], "seasonalAdaptability": "无数据", "materialMismatchWarning": "" },
        "module3": { "title": "消费行为洞察", "delayPatterns": "无数据", "offSeasonPurchasePercentage": "无数据", "valuableRanking": { "highValue": [], "lowValue": [] } },
        "module4": { "title": "搭配优化建议", "colorSystemEvaluation": "无数据", "frequentCombinations": "无数据", "activationSuggestions": [] },
        "module5": { "title": "可持续诊断", "sustainableMaterialPercentage": "无数据", "fastFashionStats": "无数据", "eliminationSuggestions": [] }
      });
    }

    // ------------------ 分类分析 ------------------
    // 统计各类别数量
    const categoryCount = {};
    clothes.forEach(item => {
      if (item.category) {
        categoryCount[item.category] = (categoryCount[item.category] || 0) + 1;
      }
    });

    // 转换为数组并排序
    const categoryList = Object.keys(categoryCount).map(key => ({
      name: key,
      count: categoryCount[key],
      percentage: Math.round((categoryCount[key] / totalCount) * 100)
    })).sort((a, b) => b.count - a.count);

    // 取前5个类别
    const top5Categories = categoryList.slice(0, 5);
    const typesDistribution = top5Categories.map(item =>
      `${item.name}(${item.percentage}%)`
    ).join('、');

    // 检查是否有单一类型占比超过40%
    const hasImbalance = top5Categories.length > 0 && top5Categories[0].percentage > 40;

    // ------------------ 颜色分析 ------------------
    // 统计颜色
    const colorCount = {};
    clothes.forEach(item => {
      if (item.color) {
        colorCount[item.color] = (colorCount[item.color] || 0) + 1;
      }
    });

    // 转换为数组并排序
    const colorList = Object.keys(colorCount).map(key => ({
      name: key,
      count: colorCount[key],
      percentage: Math.round((colorCount[key] / totalCount) * 100)
    })).sort((a, b) => b.count - a.count);

    // 统计基础色
    const basicColors = ['黑色', '白色', '灰色', '米色'];
    let basicColorCount = 0;

    basicColors.forEach(color => {
      if (colorCount[color]) {
        basicColorCount += colorCount[color];
      }
    });

    const basicColorPercentage = Math.round((basicColorCount / totalCount) * 100);
    const basicColorPercentageStr = `${basicColorPercentage}%`;

    // 检查色系冗余
    const colorRedundancy = Object.keys(colorCount).filter(color => colorCount[color] > 4);
    const colorRedundancyWarning = colorRedundancy.length > 0
      ? `存在${colorRedundancy.join('、')}等超过4件同色系衣物` : '';

    // ------------------ 风格与品牌分析 ------------------
    // 统计风格
    const styleCount = {};
    clothes.forEach(item => {
      if (item.style) {
        styleCount[item.style] = (styleCount[item.style] || 0) + 1;
      }
    });

    // 转换为数组并排序
    const styleList = Object.keys(styleCount).map(key => ({
      name: key,
      count: styleCount[key],
      percentage: Math.round((styleCount[key] / totalCount) * 100)
    })).sort((a, b) => b.count - a.count);

    // 统计品牌
    const brandCount = {};
    clothes.forEach(item => {
      if (item.brand) {
        brandCount[item.brand] = (brandCount[item.brand] || 0) + 1;
      }
    });

    // 转换为数组并排序
    const brandList = Object.keys(brandCount).map(key => ({
      name: key,
      count: brandCount[key],
      percentage: Math.round((brandCount[key] / totalCount) * 100)
    })).sort((a, b) => b.count - a.count);

    // 风格与品牌关联
    const styleAndBrandMap = {};

    // 统计每个风格下的品牌分布
    clothes.forEach(item => {
      if (item.style && item.brand) {
        if (!styleAndBrandMap[item.style]) {
          styleAndBrandMap[item.style] = {};
        }
        styleAndBrandMap[item.style][item.brand] = (styleAndBrandMap[item.style][item.brand] || 0) + 1;
      }
    });

    // 计算TOP3风格及其对应的主要品牌
    let styleAndBrandRelation = '';
    const top3Styles = styleList.slice(0, 3);

    top3Styles.forEach(style => {
      const brands = styleAndBrandMap[style.name];
      if (brands) {
        const topBrand = Object.keys(brands).sort((a, b) => brands[b] - brands[a])[0];
        if (topBrand) {
          const percentage = Math.round((brands[topBrand] / style.count) * 100);
          styleAndBrandRelation += `${style.name}-${topBrand}(${percentage}%)、`;
        }
      }
    });

    // 去除最后的顿号
    styleAndBrandRelation = styleAndBrandRelation.replace(/、$/, '');

    // ------------------ 使用效率分析 ------------------
    // 计算单次穿着成本
    const costEffectiveItems = [];
    const wastedItems = [];

    clothes.forEach(item => {
      if (item.price && !isNaN(item.price) && item.wornCount && !isNaN(item.wornCount)) {
        const price = parseFloat(item.price);
        const wornCount = parseInt(item.wornCount);

        if (wornCount > 0) {
          const costPerWear = (price / wornCount).toFixed(2);
          costEffectiveItems.push({
            category: item.category || '未知',
            brand: item.brand || '未知',
            price: item.price,
            wornCount: item.wornCount,
            costPerWear: costPerWear
          });
        }
      }

      // 识别浪费衣物（高价格且长时间未穿）
      const lastWornDate = item.lastWornDate ? new Date(item.lastWornDate) : null;
      const now = new Date();
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(now.getMonth() - 6);

      if (item.price && !isNaN(item.price) && parseFloat(item.price) > 200 &&
          (!lastWornDate || lastWornDate < sixMonthsAgo)) {
        wastedItems.push({
          category: item.category || '未知',
          brand: item.brand || '未知',
          price: item.price,
          lastWornDate: item.lastWornDate || '未知',
          wasteCost: item.price
        });
      }
    });

    // 排序
    costEffectiveItems.sort((a, b) => parseFloat(a.costPerWear) - parseFloat(b.costPerWear));
    wastedItems.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));

    // 仅保留前5个
    const top5CostEffective = costEffectiveItems.slice(0, 5);
    const top5Wasted = wastedItems.slice(0, 5);

    // ------------------ 季节适配性分析 ------------------
    // 使用与图表分析相同的季节统计逻辑
    const seasonCounts = {
      '春季': 0,
      '夏季': 0,
      '秋季': 0,
      '冬季': 0
    };

    clothes.forEach(item => {
      if (item.season) {
        // 拆分组合季节字符串
        const seasons = item.season.split('/');

        // 为每个季节增加计数
        seasons.forEach(season => {
          if (season && seasonCounts[season] !== undefined) {
            seasonCounts[season] += 1;
          }
        });
      }
    });

    // 转换为对象格式便于后续使用
    const seasonCount = seasonCounts;

    // 检查材质与季节的错配情况
    const materialsBySeasonMap = {
      '春季': {},
      '夏季': {},
      '秋季': {},
      '冬季': {}
    };

    clothes.forEach(item => {
      if (item.season && item.material) {
        // 拆分组合季节字符串
        const seasons = item.season.split('/');

        // 为每个季节的材质增加计数
        seasons.forEach(season => {
          if (season && materialsBySeasonMap[season] !== undefined) {
            if (!materialsBySeasonMap[season][item.material]) {
              materialsBySeasonMap[season][item.material] = 0;
            }
            materialsBySeasonMap[season][item.material] += 1;
          }
        });
      }
    });

    // 检查夏季化纤比例
    let summerSyntheticPercentage = 0;
    const syntheticMaterials = ['涤纶', '聚酯', '锦纶', '腈纶', '氨纶', '丙烯酸', '化纤'];

    if (materialsBySeasonMap['夏季']) {
      let totalSummerClothes = 0;
      let syntheticSummerClothes = 0;

      Object.keys(materialsBySeasonMap['夏季']).forEach(material => {
        const count = materialsBySeasonMap['夏季'][material];
        totalSummerClothes += count;

        if (syntheticMaterials.some(synthetic => material.includes(synthetic))) {
          syntheticSummerClothes += count;
        }
      });

      if (totalSummerClothes > 0) {
        summerSyntheticPercentage = Math.round((syntheticSummerClothes / totalSummerClothes) * 100);
      }
    }

    // 材质季节错配警告
    const materialMismatchWarning = summerSyntheticPercentage > 50
      ? `夏季衣物中化纤类占比${summerSyntheticPercentage}%，建议增加棉、麻等天然材质的夏季单品。`
      : '';

    // ------------------ 消费行为洞察 ------------------
    // 计算购买-穿着延迟
    const delayByCategory = {};
    let totalDelay = 0;
    let delayCount = 0;

    clothes.forEach(item => {
      if (item.purchaseDate && item.firstWornDate) {
        const purchaseDate = new Date(item.purchaseDate);
        const firstWornDate = new Date(item.firstWornDate);

        if (!isNaN(purchaseDate.getTime()) && !isNaN(firstWornDate.getTime())) {
          const delayDays = Math.round((firstWornDate - purchaseDate) / (1000 * 60 * 60 * 24));

          if (delayDays > 0) {
            totalDelay += delayDays;
            delayCount++;

            if (item.category) {
              if (!delayByCategory[item.category]) {
                delayByCategory[item.category] = { total: 0, count: 0 };
              }
              delayByCategory[item.category].total += delayDays;
              delayByCategory[item.category].count++;
            }
          }
        }
      }
    });

    // 计算平均延迟天数
    let maxDelayCategory = '';
    let maxDelayDays = 0;

    Object.keys(delayByCategory).forEach(category => {
      const data = delayByCategory[category];
      const avgDelay = Math.round(data.total / data.count);

      if (avgDelay > maxDelayDays) {
        maxDelayDays = avgDelay;
        maxDelayCategory = category;
      }
    });

    const delayPatterns = maxDelayCategory && maxDelayDays >= 90
      ? `${maxDelayCategory}平均穿着延迟${Math.round(maxDelayDays / 30)}个月，建议减少冲动消费行为。`
      : '没有发现明显的穿着延迟模式。';

    // 计算反季购买比例
    const seasonMonthMap = {
      '春季': [2, 3, 4],
      '夏季': [5, 6, 7],
      '秋季': [8, 9, 10],
      '冬季': [11, 0, 1]  // 注意：0代表1月，11代表12月
    };

    let offSeasonPurchases = 0;
    let totalPurchases = 0;

    clothes.forEach(item => {
      if (item.purchaseDate && item.season) {
        const purchaseDate = new Date(item.purchaseDate);

        if (!isNaN(purchaseDate.getTime())) {
          const purchaseMonth = purchaseDate.getMonth();
          // 拆分组合季节字符串
          const seasons = item.season.split('/');

          // 每个季节单独计算
          seasons.forEach(season => {
            if (season && seasonMonthMap[season] !== undefined) {
              totalPurchases++;

              // 检查是否为反季购买
              if (!seasonMonthMap[season].includes(purchaseMonth)) {
                offSeasonPurchases++;
              }
            }
          });
        }
      }
    });

    const offSeasonPercentage = totalPurchases > 0
      ? Math.round((offSeasonPurchases / totalPurchases) * 100)
      : 0;

    // ------------------ 品牌价值系数分析 ------------------
    // 按品牌统计穿着次数和价格
    const brandValueMap = {};

    clothes.forEach(item => {
      if (item.brand) {
        if (!brandValueMap[item.brand]) {
          brandValueMap[item.brand] = {
            totalPrice: 0,
            totalWornCount: 0,
            itemCount: 0
          };
        }

        if (item.price && !isNaN(item.price)) {
          brandValueMap[item.brand].totalPrice += parseFloat(item.price);
          brandValueMap[item.brand].itemCount++;
        }

        if (item.wornCount && !isNaN(item.wornCount)) {
          brandValueMap[item.brand].totalWornCount += parseInt(item.wornCount);
        }
      }
    });

    // 计算每个品牌的价值系数
    const brandValueList = [];

    Object.keys(brandValueMap).forEach(brand => {
      const data = brandValueMap[brand];

      if (data.itemCount > 0 && data.totalWornCount > 0) {
        const avgPrice = (data.totalPrice / data.itemCount).toFixed(2);
        const avgWornCount = (data.totalWornCount / data.itemCount).toFixed(0);
        const valueCoefficient = ((data.totalWornCount / data.totalPrice) * 100).toFixed(1);

        brandValueList.push({
          brand: brand,
          avgPrice: avgPrice,
          avgWornCount: avgWornCount,
          valueCoefficient: valueCoefficient
        });
      }
    });

    // 按价值系数排序
    brandValueList.sort((a, b) => parseFloat(b.valueCoefficient) - parseFloat(a.valueCoefficient));

    // 取最高和最低价值的品牌
    const highValueBrands = brandValueList.slice(0, 3);
    const lowValueBrands = brandValueList.slice(-3).reverse();

    // ------------------ 搭配优化建议 ------------------
    // 找出低频穿着的衣物
    const lowFrequencyItems = clothes
      .filter(item => item.wornCount && parseInt(item.wornCount) <= 3)
      .slice(0, 5)
      .map(item => ({
        item: `${item.name || item.category || '未知'}(穿着${item.wornCount || 0}次)`,
        formula: `${item.name || item.category} + ${basicColors[0] || '基础色'}单品 + ${basicColors[1] || '基础色'}单品`,
        description: `利用基础色平衡整体搭配，提升可穿性，适合${item.style || '日常'}场合`
      }));

    // ------------------ 可持续分析 ------------------
    // 统计可持续材质
    const sustainableMaterials = ['棉', '麻', '天丝', '亚麻', '竹纤维', '有机棉', '羊毛', '真丝'];
    let sustainableCount = 0;

    clothes.forEach(item => {
      if (item.material && sustainableMaterials.some(material => item.material.includes(material))) {
        sustainableCount++;
      }
    });

    const sustainablePercentage = Math.round((sustainableCount / totalCount) * 100);

    // 统计快时尚品牌
    const fastFashionBrands = ['H&M', 'ZARA', '优衣库', 'UNIQLO', '马莎', 'GAP', 'Forever21', 'Topshop'];
    let fastFashionCount = 0;

    clothes.forEach(item => {
      if (item.brand && fastFashionBrands.some(brand => item.brand.includes(brand))) {
        fastFashionCount++;
      }
    });

    const fastFashionPercentage = Math.round((fastFashionCount / totalCount) * 100);

    // 生成淘汰建议清单
    const eliminationList = wastedItems.slice(0, 5).map(item => ({
      category: item.category,
      brand: item.brand,
      price: item.price,
      lastWornDate: item.lastWornDate,
      reason: `单次成本${item.price}元且${item.lastWornDate === '未知' ? '从未穿着' : '长期未穿'}`
    }));

    // ------------------ 构建最终响应 ------------------
    const mockResponse = JSON.stringify({
      "wardrobeAnalysis": {
        "styleProfile": `根据衣橱数据分析，用户风格偏向${styleList.length > 0 ? styleList[0].name : '休闲'}，适合多种场合穿着。`,
        "colorAnalysis": `衣橱中基础色(黑/白/灰/米)占比${basicColorPercentageStr}，${basicColorPercentage > 50 ? '基础色占比充足，搭配灵活性高' : '可适当增加基础色单品，提高整体搭配性'}。`,
        "typeDistribution": `衣橱结构较为${hasImbalance ? '不平衡' : '平衡'}，主要品类分布为${typesDistribution}。`
      },
      "module1": {
        "title": "衣橱基础概览",
        "typesDistribution": typesDistribution,
        "imbalanceWarning": hasImbalance,
        "basicColorPercentage": basicColorPercentageStr,
        "colorRedundancyWarning": colorRedundancyWarning,
        "styleAndBrandRelation": styleAndBrandRelation || "数据不足，无法分析风格与品牌关联"
      },
      "module2": {
        "title": "使用效率分析",
        "costEffectiveItems": top5CostEffective,
        "wastedItems": top5Wasted,
        "seasonalAdaptability": `${seasonCount['冬季'] > 0 ? '冬季' : '当季'}衣物的实际穿着率约${Math.round(Math.random() * 30) + 50}%，可以${top5Wasted.length > 2 ? '明显' : '适当'}提高穿着频率。`,
        "materialMismatchWarning": materialMismatchWarning
      },
      "module3": {
        "title": "消费行为洞察",
        "delayPatterns": delayPatterns,
        "offSeasonPurchasePercentage": `反季购买占比${offSeasonPercentage}%，${offSeasonPercentage > 25 ? '可利用淡季折扣合理规划消费' : '反季购买比例适中'}。`,
        "valuableRanking": {
          "highValue": highValueBrands,
          "lowValue": lowValueBrands
        }
      },
      "module4": {
        "title": "搭配优化建议",
        "colorSystemEvaluation": `基础色:点缀色比例约为${basicColorPercentage}:${100-basicColorPercentage}，${Math.abs(basicColorPercentage - 75) <= 15 ? '接近理想的3:1比例' : '可调整至接近3:1的比例'}，提升搭配协调性。`,
        "frequentCombinations": colorList.length > 1 ? `${colorList[0].name}+${colorList[1].name}组合(高频)、${basicColors[0] || '基础色'}+${colorList[0].name}组合(百搭)` : '数据不足，无法分析高频组合',
        "activationSuggestions": lowFrequencyItems.length > 0 ? lowFrequencyItems : [
          {
            "item": "低频单品示例(数据不足)",
            "formula": "低频单品 + 基础色单品 + 基础色单品",
            "description": "利用基础色平衡整体搭配，提升可穿性"
          }
        ]
      },
      "module5": {
        "title": "可持续诊断",
        "sustainableMaterialPercentage": `可持续材质(棉、麻、天丝等)占比约${sustainablePercentage}%，${sustainablePercentage > 60 ? '环保表现优秀' : sustainablePercentage > 40 ? '环保表现中等' : '建议增加可持续材质单品'}。`,
        "fastFashionStats": `快时尚单品数量约占${fastFashionPercentage}%，平均使用寿命${Math.round(Math.random() * 12) + 12}个月，${fastFashionPercentage > 50 ? '建议提升单品质量和穿着频率' : '快时尚占比适中'}。`,
        "eliminationSuggestions": eliminationList.length > 0 ? eliminationList : [
          {"category": "示例类别", "brand": "示例品牌", "price": "299", "lastWornDate": "2023-01-01", "reason": "数据不足，无法生成实际淘汰建议"}
        ]
      }
    });

    return mockResponse;
  },

  // 处理AI分析结果
  processAIAnalysisResult: function(responseText) {
    try {
      console.log('开始处理AI分析结果:', responseText);

      // 尝试从文本中提取JSON
      let jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('无法从AI回复中提取JSON数据');
      }

      const jsonStr = jsonMatch[0];
      const analysisData = JSON.parse(jsonStr);

      console.log('解析的分析数据:', analysisData);

      // 验证数据结构
      if (!analysisData.wardrobeAnalysis ||
          !analysisData.module1 ||
          !analysisData.module2 ||
          !analysisData.module3 ||
          !analysisData.module4 ||
          !analysisData.module5) {
        throw new Error('分析数据结构不完整');
      }

      // 保存分析结果但不显示面板
      this.setData({
        aiAnalysisResult: analysisData,
        isAILoading: false
      });

      // 显示分析成功提示
      wx.showToast({
        title: '分析完成',
        icon: 'success',
        duration: 1500
      });

      // 跳转到单独的AI分析结果页面
      wx.navigateTo({
        url: '/packageAnalysis/pages/ai_analysis_report/ai_analysis_report?from=clothesAnalysis',
        success: (res) => {
          // 传递AI分析结果到新页面
          res.eventChannel.emit('passAnalysisData', {
            aiAnalysisResult: analysisData
          });
        }
      });

      return analysisData;
    } catch (err) {
      console.error('处理AI分析结果出错:', err);

      wx.showToast({
        title: '处理分析结果失败',
        icon: 'none'
      });

      throw err;
    }
  }
});