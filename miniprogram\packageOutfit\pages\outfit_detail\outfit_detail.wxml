<view class="container" style="background-color: {{themeStyle === 'autumn' ? colors.golden_batter : pinkBlueColors.pinkLight}};">
  <!-- Loading state -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner" style="border-top-color: {{colors.cowhide_cocoa}}; border-color: {{colors.golden_batter}};"></view>
    <view class="loading-text" style="color: {{colors.cowhide_cocoa}};">加载中...</view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{!isLoading && outfitData}}">
    <!-- 顶部区域 -->
    <view class="top-section">
      <view class="back-button" bindtap="goBack">
        <view class="back-icon" style="border-right-color: {{colors.cowhide_cocoa}};"></view>
        <text style="color: {{colors.cowhide_cocoa}};">返回</text>
      </view>

      <view class="outfit-title" style="color: {{colors.cowhide_cocoa}};">{{outfitData.name || '未命名搭配'}}</view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="share-button" bindtap="shareOutfit" style="background-color: {{colors.olive_harvest}}; color: {{colors.golden_batter}};">
          <text>分享</text>
        </view>
        <view class="edit-button" bindtap="editOutfit" style="background-color: {{colors.olive_harvest}}; color: {{colors.golden_batter}};">
          <text>编辑</text>
        </view>
        <view class="delete-button" bindtap="deleteOutfit" style="background-color: {{colors.spiced_wine}}; color: {{colors.golden_batter}};">
          <text>删除</text>
        </view>
      </view>
    </view>

    <!-- 搭配详情卡片 -->
    <view class="outfit-detail-card {{isSmallScreen ? 'small-screen' : ''}}" style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : pinkBlueColors.pinkMedium}};">
      <!-- 搭配预览图 -->
      <view class="outfit-preview {{isSmallScreen ? 'small-screen' : ''}}" style="height: {{imageHeight}};">
        <image
          src="{{outfitData.previewImage}}"
          mode="aspectFill"
          class="preview-image"
          style="border: 2rpx solid {{themeStyle === 'autumn' ? colors.golden_batter : pinkBlueColors.blueMedium}};"
          binderror="handleImageError"
          data-type="preview"
          bindtap="previewOutfitImage"
        ></image>

        <!-- AI评分标签 -->
        <view class="ai-score-badge" wx:if="{{outfitData.aiScore || (scoreData && scoreData.score)}}"
              style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : pinkBlueColors.blueDark}};"
              bindtap="deleteAIScore">
          <text class="ai-score-value">{{outfitData.aiScore || scoreData.score}}</text>
          <text class="ai-score-label">AI评分</text>
        </view>
      </view>

      <!-- 搭配信息 -->
      <view class="outfit-info {{isSmallScreen ? 'small-screen' : ''}}">
        <view class="info-row">
          <view class="info-label" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">创建时间：</view>
          <view class="info-value" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{outfitData.createTimeFormatted || '未知'}}</view>
        </view>

        <view class="info-row">
          <view class="info-label" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">类别：</view>
          <view class="info-value" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">
            <text wx:if="{{outfitData.category === 'daily'}}">日常穿搭</text>
            <text wx:elif="{{outfitData.category === 'work'}}">职业穿搭</text>
            <text wx:elif="{{outfitData.category === 'party'}}">派对穿搭</text>
            <text wx:elif="{{outfitData.category === 'sport'}}">运动穿搭</text>
            <text wx:elif="{{outfitData.category === 'seasonal'}}">季节穿搭</text>
            <text wx:else>{{outfitData.category || '其他'}}</text>
          </view>
        </view>

        <view class="info-row" wx:if="{{outfitData.description}}">
          <view class="info-label" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">描述：</view>
          <view class="info-value" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{outfitData.description}}</view>
        </view>

        <!-- 标签 -->
        <view class="tags-container" wx:if="{{outfitData.tags && outfitData.tags.length > 0}}">
          <view class="info-label" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">标签：</view>
          <view class="tags-list">
            <block wx:for="{{outfitData.tags}}" wx:key="*this">
              <view class="tag" style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : pinkBlueColors.blueMedium}}; color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">
                {{item}}
              </view>
            </block>
          </view>
        </view>

        <!-- AI评分按钮 -->
        <view class="ai-score-button-container">
          <button
            class="ai-score-button"
            bindtap="scoreOutfit"
            style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : pinkBlueColors.blueDark}}; color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};"
            disabled="{{isScoring}}">
            <text wx:if="{{!isScoring}}">AI穿搭评分</text>
            <text wx:else>评分中...</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 添加一个查看AI分析按钮 -->
    <view class="view-ai-score-button-container" wx:if="{{scoreData}}">
      <button
        class="view-ai-score-button"
        bindtap="closeAIScoreModal"
        style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : pinkBlueColors.blueDark}}; color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">
        查看AI穿搭分析
      </button>
    </view>

    <!-- 搭配组成部分 -->
    <view class="outfit-items-section" wx:if="{{outfitData.items && outfitData.items.length > 0}}">
      <view class="section-title" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : pinkBlueColors.pinkDark}};">搭配组成</view>

      <view class="outfit-items-grid">
        <block wx:for="{{outfitData.items}}" wx:key="id">
          <view class="outfit-item-card" style="background-color: white;">
            <image
              src="{{item.imageUrl}}"
              mode="aspectFit"
              class="item-image"
              style="border: 2rpx solid {{themeStyle === 'autumn' ? colors.golden_batter : pinkBlueColors.blueMedium}};"
              binderror="handleImageError"
              data-type="item"
              data-index="{{index}}"
              data-url="{{item.imageUrl}}"
              bindtap="previewItemImage"
            ></image>
            <view class="item-info" style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : pinkBlueColors.blueDark}};">
              <view class="item-name" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{item.name || '未命名'}}</view>
              <view class="item-type" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">
                <text wx:if="{{item.type === 'top'}}">上衣</text>
                <text wx:elif="{{item.type === 'bottom'}}">裤子</text>
                <text wx:elif="{{item.type === 'outerwear'}}">外套</text>
                <text wx:elif="{{item.type === 'shoes'}}">鞋子</text>
                <text wx:elif="{{item.type === 'accessory'}}">配饰</text>
                <text wx:else>{{item.type || item.category || '未分类'}}</text>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 相似搭配推荐 -->
    <view class="similar-outfits-section" wx:if="{{similarOutfits.length > 0}}">
      <view class="section-title" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : pinkBlueColors.pinkDark}};">相似搭配推荐</view>

      <scroll-view scroll-x="true" class="similar-outfits-scroll">
        <block wx:for="{{similarOutfits}}" wx:key="id">
          <view class="similar-outfit-card" bindtap="viewSimilarOutfit" data-id="{{item.id || item._id}}" style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : pinkBlueColors.pinkDark}};">
            <image
              src="{{item.previewImage}}"
              mode="aspectFill"
              class="similar-outfit-image"
              style="border: 2rpx solid {{themeStyle === 'autumn' ? colors.golden_batter : pinkBlueColors.blueMedium}};"
              binderror="handleImageError"
              data-type="similar"
              data-index="{{index}}"
              data-url="{{item.previewImage}}"
              catchtap="previewSimilarImage"
            ></image>
            <view class="similar-outfit-name" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{item.name || '未命名搭配'}}</view>
            <view class="similar-outfit-date" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{item.createTimeFormatted || '未知日期'}}</view>
          </view>
        </block>
      </scroll-view>
    </view>

    <!-- 无数据提示 -->
    <view class="no-data-container" wx:if="{{!isLoading && !outfitData}}">
      <view class="no-data-icon">🧥</view>
      <view class="no-data-text" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : pinkBlueColors.pinkDark}};">未找到搭配数据</view>
      <view class="no-data-subtext" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : pinkBlueColors.blueDark}};">请返回重新选择</view>
    </view>
  </view>

  <!-- 添加图片预览和保存的浮层 -->
  <view class="image-preview-container" wx:if="{{showImagePreview}}" bindtap="closeImagePreview">
    <view class="preview-content" catchtap="preventClose">
      <image
        src="{{previewImageUrl}}"
        mode="aspectFit"
        class="fullscreen-image"
      ></image>
      <view class="preview-actions">
        <button class="preview-action-btn save-btn" bindtap="saveImageToAlbum">保存图片</button>
        <button class="preview-action-btn close-btn" bindtap="closeImagePreview">关闭</button>
      </view>
    </view>
  </view>

  <!-- AI评分结果弹窗 -->
  <view class="ai-score-modal" wx:if="{{showAIScoreModal && scoreData}}" bindtap="closeAIScoreModal">
    <view class="ai-score-modal-content" catchtap="preventAIScoreModalClose">
      <view class="ai-score-modal-header">
        <text class="ai-score-modal-title" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : pinkBlueColors.pinkDark}};">AI穿搭分析</text>
        <view class="ai-score-modal-close" bindtap="closeAIScoreModal">×</view>
      </view>

      <view class="ai-score-cards-container">
        <!-- 总分卡片 -->
        <view class="ai-score-card main-score-card" style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : pinkBlueColors.pinkMedium}};">
          <view class="card-header" style="border-bottom-color: {{themeStyle === 'autumn' ? 'rgba(232, 209, 167, 0.2)' : 'rgba(255, 255, 255, 0.2)'}};">
            <text style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">总体评分</text>
          </view>
          <view class="main-score-content">
            <view class="total-score-container">
              <view class="total-score" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{scoreData.score}}</view>
              <view class="total-score-label" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">总分</view>
            </view>
            <view class="style-type-container">
              <view class="style-type-label" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">风格类型</view>
              <view class="style-type" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}}; background-color: {{themeStyle === 'autumn' ? 'rgba(232, 209, 167, 0.2)' : 'rgba(255, 255, 255, 0.2)'}};">{{scoreData.styleType}}</view>
            </view>
          </view>
        </view>

        <!-- 分项评分卡片 -->
        <view class="ai-score-card detail-score-card" style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : pinkBlueColors.pinkMedium}};">
          <view class="card-header" style="border-bottom-color: {{themeStyle === 'autumn' ? 'rgba(232, 209, 167, 0.2)' : 'rgba(255, 255, 255, 0.2)'}};">
            <text style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">分项评分</text>
          </view>
          <view class="score-details">
            <view class="score-item">
              <view class="score-item-value" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{scoreData.colorScore}}</view>
              <view class="score-item-label" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">色彩搭配</view>
            </view>
            <view class="score-item">
              <view class="score-item-value" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{scoreData.styleScore}}</view>
              <view class="score-item-label" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">风格统一</view>
            </view>
            <view class="score-item">
              <view class="score-item-value" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{scoreData.occasionScore}}</view>
              <view class="score-item-label" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">场合适配</view>
            </view>
          </view>
        </view>

        <!-- 评价卡片 -->
        <view class="ai-score-card comment-card" style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : pinkBlueColors.pinkMedium}};">
          <view class="card-header" style="border-bottom-color: {{themeStyle === 'autumn' ? 'rgba(232, 209, 167, 0.2)' : 'rgba(255, 255, 255, 0.2)'}};">
            <text style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">专业评价</text>
          </view>
          <view class="comment-content">
            <view class="comment-text" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{scoreData.comment}}</view>
          </view>
        </view>

        <!-- 建议卡片 -->
        <view class="ai-score-card suggestion-card" style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : pinkBlueColors.pinkMedium}};">
          <view class="card-header" style="border-bottom-color: {{themeStyle === 'autumn' ? 'rgba(232, 209, 167, 0.2)' : 'rgba(255, 255, 255, 0.2)'}};">
            <text style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">改进建议</text>
          </view>
          <view class="suggestion-content">
            <view class="suggestion-text" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : 'white'}};">{{scoreData.suggestion}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>