/* page/wardrobe/outfit/outfit_category/outfit_category.wxss */

/* 定义主题变量 */
page {
  /* 秋季主题变量 */
  --cowhide-cocoa: #442D1C;
  --spiced-wine: #74301C;
  --toasted-caramel: #84592B;
  --olive-harvest: #9D9167;
  --golden-batter: #E8D1A7;

  /* 粉蓝主题变量 */
  --pink-dark: #D47C99;
  --pink-medium: #EEA0B2;
  --pink-light: #F9C9D6;
  --blue-light: #CBE0F9;
  --blue-medium: #97C8E5;
  --blue-dark: #5EA0D0;

  /* 黑白主题变量 */
  --black: #000000;
  --dark-gray: #333333;
  --medium-gray: #666666;
  --light-gray: #CCCCCC;
  --white: #FFFFFF;
  --off-white: #F5F5F5;
}

/* 全局容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  padding: 8rpx;
  transition: background-color 0.3s ease;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 5rpx solid;
  border-radius: 50%;
  margin-bottom: 20rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  letter-spacing: 1rpx;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding-bottom: 40rpx;
}

/* 顶部区域 */
.top-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 0 3%;
  width: 94%;
  margin-left: auto;
  margin-right: auto;
}

/* 返回按钮 */
.back-button {
  display: flex;
  align-items: center;
  padding: 10rpx;
}

.back-icon {
  width: 0;
  height: 0;
  border-top: 15rpx solid transparent;
  border-bottom: 15rpx solid transparent;
  border-right: 24rpx solid;
  transform: rotate(180deg);
  margin-right: 10rpx;
}

/* 类别标题 */
.category-title {
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
}

/* 创建搭配按钮 */
.create-outfit-button {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.create-outfit-button:active {
  transform: scale(0.96);
  opacity: 0.9;
}

.plus-icon {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 8rpx;
}

/* 搭配列表滚动区域 */
.outfits-scroll {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  width: 100%;
}

/* 搭配卡片 */
.outfit-card {
  width: 94%;
  border-radius: 20rpx;
  padding: 15rpx;
  margin-bottom: 20rpx;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
  min-height: 280rpx;
  box-sizing: border-box;
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease, border 0.3s ease;
}

.outfit-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 黑白主题下的卡片样式 */
.theme-blackWhite .outfit-card {
  border: 1px solid var(--dark-gray);
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.15);
}

/* 搭配信息区域 */
.outfit-info {
  width: calc(100% - 145rpx);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-right: 0;
}

/* 搭配名称容器 */
.outfit-name-container {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.outfit-name {
  font-size: 28rpx;
  font-weight: 600;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1;
}

/* 内联AI评分 */
.ai-score-inline {
  font-size: 22rpx;
  font-weight: bold;
  color: white;
  min-width: 36rpx;
  height: 36rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
  padding: 0 8rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.outfit-date {
  font-size: 22rpx;
  opacity: 0.8;
  margin-bottom: 12rpx;
}

/* 衣物列表 */
.outfit-items {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  width: 100%;
  overflow: hidden;
}

.outfit-item {
  display: flex;
  align-items: center;
  width: 100%;
  overflow: hidden;
}

.item-image {
  width: 70rpx;
  height: 70rpx;
  min-width: 70rpx;
  border-radius: 10rpx;
  margin-right: 10rpx;
  object-fit: contain;
  background-color: rgba(255, 255, 255, 0.2);
}

.item-name {
  font-size: 22rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* 搭配预览图区域 */
.outfit-preview {
  width: 140rpx;
  height: 190rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  position: absolute;
  right: 10rpx;
  bottom: 15rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
  width: 100%;
  padding: 40rpx;
  box-sizing: border-box;
  text-align: center;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.create-button {
  padding: 20rpx 60rpx;
  background-color: #84592B; /* 使用秋季主题色 */
  color: #E8D1A7;
  border-radius: 40rpx;
  font-size: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.create-button:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 主题样式 */
.theme-autumn {
  background-color: var(--golden-batter) !important;
}

.theme-pinkBlue {
  background-color: var(--pink-light) !important;
}

.theme-blackWhite {
  background-color: var(--off-white) !important;
}

/* 黑白主题下的按钮样式 */
.theme-blackWhite .create-outfit-button,
.theme-blackWhite .create-button {
  background-color: var(--black);
  color: var(--white);
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 黑白主题下的图片样式 */
.theme-blackWhite .item-image,
.theme-blackWhite .preview-image {
  border: 2rpx solid var(--dark-gray) !important;
  background-color: var(--white);
}