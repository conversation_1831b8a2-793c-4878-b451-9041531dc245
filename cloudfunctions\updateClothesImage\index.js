// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 检查必要参数
  if (!event.clothingId) {
    return {
      success: false,
      message: '缺少衣物ID'
    }
  }
  
  if (!event.imageFileID) {
    return {
      success: false,
      message: '缺少图片文件ID'
    }
  }
  
  try {
    // 构建更新数据对象
    const updateData = {
      imageFileID: event.imageFileID,
      processedImageFileID: event.imageFileID,
      updateTime: db.serverDate()
    }
    
    // 执行更新操作
    const result = await db.collection('clothes').doc(event.clothingId).update({
      data: updateData
    })
    
    return {
      success: true,
      updated: result.stats.updated,
      message: '图片更新成功'
    }
  } catch (error) {
    console.error('更新衣物图片失败:', error)
    return {
      success: false,
      message: '更新失败: ' + error.message
    }
  }
} 