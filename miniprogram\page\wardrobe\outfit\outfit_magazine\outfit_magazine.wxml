<view class="container theme-{{themeStyle}}" style="background-color: #d4d3ce;" bindtouchstart="touchStart" bindtouchend="touchEnd">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner" style="border-top-color: #666666; border-color: #d4d3ce;"></view>
    <view class="loading-text" style="color: #666666;">加载中...</view>
  </view>

  <!-- 返回按钮 -->
  <view class="back-button" bindtap="goBack">
    <view class="back-icon"></view>
    <text style="color: #666666;">返回</text>
  </view>

  <!-- 主内容区域 -->
  <view class="magazine-content" wx:if="{{!isLoading}}" animation="{{pageAnimation}}">
    <!-- 封面页 -->
    <view class="magazine-page cover-page" wx:if="{{currentPage === 'cover'}}">
      <view class="magazine-spine"></view>

      <!-- AI评分总和标签 -->
      <view class="ai-score-total-badge" wx:if="{{totalAIScoreCount > 0}}">
        <text class="ai-score-total-value">{{totalAIScoreCount}}</text>
        <text class="ai-score-total-label">AI评分总分</text>
      </view>

      <view class="magazine-cover">
        <view class="magazine-header">
          <text class="magazine-name">FASHION WARDROBE</text>
          <text class="magazine-issue">Issue No.{{currentIssue}} | {{currentSeason}} {{currentYear}}</text>
        </view>

        <view class="cover-image-container">
          <!-- 封面形象图，使用推荐搭配中的第一个 -->
          <image class="cover-image"
            src="{{recommendedOutfits[0].outfit_cover || recommendedOutfits[0].previewImage || '/images/default_cover.jpg'}}"
            mode="aspectFill"></image>
          <view class="cover-overlay"></view>

          <view class="cover-title">
            <text class="cover-subtitle">你的穿搭分类指南</text>
            <text class="cover-heading">个人穿搭集</text>
            <text class="cover-description">发现你的个人风格</text>
          </view>
        </view>

        <view class="magazine-info">
          <text class="magazine-tagline">打造完美衣橱 · 展现个性风格</text>
          <view class="magazine-highlights">
            <text class="highlight-item">日常搭配指南</text>
            <text class="highlight-item">职场穿搭精选</text>
            <text class="highlight-item">季节流行趋势</text>
          </view>
        </view>
      </view>

      <view class="page-nav-hint">
        <text>向左滑动查看更多</text>
        <view class="swipe-icon"></view>
      </view>
    </view>

    <!-- 目录页 -->
    <view class="magazine-page contents-page" wx:elif="{{currentPage === 'contents'}}">
      <view class="magazine-spine"></view>
      <view class="contents-header">
        <text class="contents-title">目录 CONTENTS</text>
        <text class="contents-issue">第{{currentIssue}}期 · {{currentSeason}} {{currentYear}}</text>
      </view>

      <view class="contents-body">
        <view class="contents-section">
          <view class="section-title">穿搭分类</view>
          <view class="contents-items">
            <view class="contents-item" bindtap="navigateToPage" data-page="daily">
              <text class="item-number">01</text>
              <text class="item-title">日常穿搭</text>
              <text class="item-description">舒适休闲的日常搭配</text>
            </view>

            <view class="contents-item" bindtap="navigateToPage" data-page="work">
              <text class="item-number">02</text>
              <text class="item-title">职业穿搭</text>
              <text class="item-description">得体专业的职场造型</text>
            </view>

            <view class="contents-item" bindtap="navigateToPage" data-page="party">
              <text class="item-number">03</text>
              <text class="item-title">派对穿搭</text>
              <text class="item-description">闪耀夺目的聚会装扮</text>
            </view>

            <view class="contents-item" bindtap="navigateToPage" data-page="sport">
              <text class="item-number">04</text>
              <text class="item-title">运动穿搭</text>
              <text class="item-description">活力四溢的运动装扮</text>
            </view>

            <view class="contents-item" bindtap="navigateToPage" data-page="seasonal">
              <text class="item-number">05</text>
              <text class="item-title">季节穿搭</text>
              <text class="item-description">应季时尚的搭配选择</text>
            </view>
          </view>
        </view>

        <view class="contents-section">
          <view class="section-title">特别企划</view>
          <view class="contents-items">
            <view class="contents-item" bindtap="navigateToPage" data-page="features">
              <text class="item-number">06</text>
              <text class="item-title">编辑精选</text>
              <text class="item-description">精心挑选的搭配推荐</text>
            </view>
          </view>
        </view>
      </view>

      <view class="contents-footer">
        <text class="footer-text">FASHION WARDROBE - 专属于你的时尚指南</text>
      </view>
    </view>

    <!-- 日常穿搭页 -->
    <view class="magazine-page category-page" wx:elif="{{currentPage === 'daily'}}">
      <view class="magazine-spine"></view>
      <view class="category-header">
        <text class="category-title">日常穿搭</text>
        <text class="category-subtitle">DAILY STYLE</text>
      </view>

      <view class="category-content">
        <view class="category-intro">
          <text class="intro-text">日常穿搭强调舒适与实用，同时不失时尚感。以下是你的日常穿搭精选。</text>
        </view>

        <!-- 日常搭配展示 -->
        <view class="featured-outfit" wx:if="{{featuredOutfits.daily}}">
          <image class="featured-image" src="{{featuredOutfits.daily.outfit_cover || featuredOutfits.daily.previewImage}}" mode="aspectFit" bindtap="viewOutfitDetail" data-id="{{featuredOutfits.daily._id}}"></image>
          <view class="featured-info">
            <text class="featured-title">{{featuredOutfits.daily.name || '舒适日常'}}</text>
            <text class="featured-description">{{featuredOutfits.daily.description || '适合日常场合的舒适搭配，展现休闲时尚风格。'}}</text>
          </view>
        </view>

        <!-- 无数据展示 -->
        <view class="empty-state" wx:else>
          <text class="empty-text">你还没有添加日常穿搭</text>
          <view class="create-button" bindtap="goToCreateOutfit">
            <text>创建搭配</text>
          </view>
        </view>
      </view>

      <!-- 查看更多按钮 -->
      <view class="view-more-button" bindtap="navigateToCategoryMagazine" data-category="daily" wx:if="{{featuredOutfits.daily}}">
        <text>查看更多日常穿搭</text>
      </view>

      <view class="page-number">01</view>
    </view>

    <!-- 职业穿搭页 -->
    <view class="magazine-page category-page" wx:elif="{{currentPage === 'work'}}">
      <view class="magazine-spine"></view>
      <view class="category-header">
        <text class="category-title">职业穿搭</text>
        <text class="category-subtitle">WORK ATTIRE</text>
      </view>

      <view class="category-content">
        <view class="category-intro">
          <text class="intro-text">职业穿搭需要兼顾专业形象与个人风格。精致的剪裁与适当的色彩搭配能展现你的职场魅力。</text>
        </view>

        <!-- 职业搭配展示 -->
        <view class="featured-outfit" wx:if="{{featuredOutfits.work}}">
          <image class="featured-image" src="{{featuredOutfits.work.outfit_cover || featuredOutfits.work.previewImage}}" mode="aspectFit" bindtap="viewOutfitDetail" data-id="{{featuredOutfits.work._id}}"></image>
          <view class="featured-info">
            <text class="featured-title">{{featuredOutfits.work.name || '职场精英'}}</text>
            <text class="featured-description">{{featuredOutfits.work.description || '专业得体的职场搭配，彰显自信与能力。'}}</text>
          </view>
        </view>

        <!-- 无数据展示 -->
        <view class="empty-state" wx:else>
          <text class="empty-text">你还没有添加职业穿搭</text>
          <view class="create-button" bindtap="goToCreateOutfit">
            <text>创建搭配</text>
          </view>
        </view>
      </view>

      <!-- 查看更多按钮 -->
      <view class="view-more-button" bindtap="navigateToCategoryMagazine" data-category="work" wx:if="{{featuredOutfits.work}}">
        <text>查看更多职业穿搭</text>
      </view>

      <view class="page-number">02</view>
    </view>

    <!-- 派对穿搭页 -->
    <view class="magazine-page category-page" wx:elif="{{currentPage === 'party'}}">
      <view class="magazine-spine"></view>
      <view class="category-header">
        <text class="category-title">派对穿搭</text>
        <text class="category-subtitle">PARTY STYLE</text>
      </view>

      <view class="category-content">
        <view class="category-intro">
          <text class="intro-text">派对场合是展现个性的绝佳机会。大胆的色彩、亮眼的单品和精心的搭配将让你成为焦点。</text>
        </view>

        <!-- 派对搭配展示 -->
        <view class="featured-outfit" wx:if="{{featuredOutfits.party}}">
          <image class="featured-image" src="{{featuredOutfits.party.outfit_cover || featuredOutfits.party.previewImage}}" mode="aspectFit" bindtap="viewOutfitDetail" data-id="{{featuredOutfits.party._id}}"></image>
          <view class="featured-info">
            <text class="featured-title">{{featuredOutfits.party.name || '派对焦点'}}</text>
            <text class="featured-description">{{featuredOutfits.party.description || '闪耀夺目的派对装扮，让你成为全场焦点。'}}</text>
          </view>
        </view>

        <!-- 无数据展示 -->
        <view class="empty-state" wx:else>
          <text class="empty-text">你还没有添加派对穿搭</text>
          <view class="create-button" bindtap="goToCreateOutfit">
            <text>创建搭配</text>
          </view>
        </view>
      </view>

      <!-- 查看更多按钮 -->
      <view class="view-more-button" bindtap="navigateToCategoryMagazine" data-category="party" wx:if="{{featuredOutfits.party}}">
        <text>查看更多派对穿搭</text>
      </view>

      <view class="page-number">03</view>
    </view>

    <!-- 运动穿搭页 -->
    <view class="magazine-page category-page" wx:elif="{{currentPage === 'sport'}}">
      <view class="magazine-spine"></view>
      <view class="category-header">
        <text class="category-title">运动穿搭</text>
        <text class="category-subtitle">SPORTS WEAR</text>
      </view>

      <view class="category-content">
        <view class="category-intro">
          <text class="intro-text">运动穿搭强调功能性与舒适度，同时注重时尚感。活力的色彩与实用的设计让运动更加愉悦。</text>
        </view>

        <!-- 运动搭配展示 -->
        <view class="featured-outfit" wx:if="{{featuredOutfits.sport}}">
          <image class="featured-image" src="{{featuredOutfits.sport.outfit_cover || featuredOutfits.sport.previewImage}}" mode="aspectFit" bindtap="viewOutfitDetail" data-id="{{featuredOutfits.sport._id}}"></image>
          <view class="featured-info">
            <text class="featured-title">{{featuredOutfits.sport.name || '活力运动'}}</text>
            <text class="featured-description">{{featuredOutfits.sport.description || '充满活力的运动装扮，兼顾功能性与时尚感。'}}</text>
          </view>
        </view>

        <!-- 无数据展示 -->
        <view class="empty-state" wx:else>
          <text class="empty-text">你还没有添加运动穿搭</text>
          <view class="create-button" bindtap="goToCreateOutfit">
            <text>创建搭配</text>
          </view>
        </view>
      </view>

      <!-- 查看更多按钮 -->
      <view class="view-more-button" bindtap="navigateToCategoryMagazine" data-category="sport" wx:if="{{featuredOutfits.sport}}">
        <text>查看更多运动穿搭</text>
      </view>

      <view class="page-number">04</view>
    </view>

    <!-- 季节穿搭页 -->
    <view class="magazine-page category-page" wx:elif="{{currentPage === 'seasonal'}}">
      <view class="magazine-spine"></view>
      <view class="category-header">
        <text class="category-title">季节穿搭</text>
        <text class="category-subtitle">SEASONAL STYLE</text>
      </view>

      <view class="category-content">
        <view class="category-intro">
          <text class="intro-text">季节性穿搭随着气候变化而调整，展现当季流行元素。掌握季节穿搭技巧，让你全年都保持时尚。</text>
        </view>

        <!-- 季节搭配展示 -->
        <view class="featured-outfit" wx:if="{{featuredOutfits.seasonal}}">
          <image class="featured-image" src="{{featuredOutfits.seasonal.outfit_cover || featuredOutfits.seasonal.previewImage}}" mode="aspectFit" bindtap="viewOutfitDetail" data-id="{{featuredOutfits.seasonal._id}}"></image>
          <view class="featured-info">
            <text class="featured-title">{{featuredOutfits.seasonal.name || currentSeason + '时尚'}}</text>
            <text class="featured-description">{{featuredOutfits.seasonal.description || '应季的时尚搭配，展现当季流行元素。'}}</text>
          </view>
        </view>

        <!-- 无数据展示 -->
        <view class="empty-state" wx:else>
          <text class="empty-text">你还没有添加季节穿搭</text>
          <view class="create-button" bindtap="goToCreateOutfit">
            <text>创建搭配</text>
          </view>
        </view>
      </view>

      <!-- 查看更多按钮 -->
      <view class="view-more-button" bindtap="navigateToCategoryMagazine" data-category="seasonal" wx:if="{{featuredOutfits.seasonal}}">
        <text>查看更多季节穿搭</text>
      </view>

      <view class="page-number">05</view>
    </view>

    <!-- 特别企划页 -->
    <view class="magazine-page features-page" wx:elif="{{currentPage === 'features'}}">
      <view class="magazine-spine"></view>
      <view class="features-header">
        <text class="features-title">编辑精选</text>
        <text class="features-subtitle">EDITOR'S PICKS</text>
      </view>

      <view class="features-content">
        <view class="features-intro">
          <text class="intro-text">这里展示了我们精心挑选的穿搭搭配，希望能为你提供灵感和参考。</text>
        </view>

        <!-- 编辑精选列表 -->
        <view class="editor-picks" wx:if="{{editorPickOutfits.length > 0}}">
          <view class="editor-pick-item" wx:for="{{editorPickOutfits}}" wx:key="_id" bindtap="viewOutfitDetail" data-id="{{item._id}}">
            <image class="pick-image" src="{{item.outfit_cover || item.previewImage}}" mode="aspectFill"></image>
            <view class="pick-info">
              <text class="pick-title">{{item.name || '精选搭配'}}</text>
              <text class="pick-description">{{item.description || '精心挑选的时尚搭配，展现独特魅力。'}}</text>
            </view>
          </view>
        </view>

        <!-- 无数据展示 -->
        <view class="empty-state" wx:else>
          <text class="empty-text">暂无编辑精选内容</text>
        </view>


      </view>

      <view class="page-number">06</view>
    </view>
  </view>

  <!-- 底部目录按钮 -->
  <view class="contents-button-bottom" bindtap="toggleCategoryIndex" wx:if="{{!isLoading}}">
    <view class="contents-icon-view"></view>
    <text class="contents-text">目录</text>
  </view>

  <!-- 侧边类别索引 -->
  <view class="category-index {{showCategoryIndex ? 'show' : ''}}" wx:if="{{!isLoading}}">
    <view class="index-header">
      <text class="index-title">目录</text>
    </view>

    <view class="index-list">
      <view class="index-item {{currentPage === 'cover' ? 'active' : ''}}" bindtap="navigateToPage" data-page="cover">
        <text class="item-text">封面</text>
      </view>
      <view class="index-item {{currentPage === 'contents' ? 'active' : ''}}" bindtap="navigateToPage" data-page="contents">
        <text class="item-text">目录</text>
      </view>
      <view class="index-item {{currentPage === 'daily' ? 'active' : ''}}" bindtap="navigateToPage" data-page="daily">
        <text class="item-text">日常穿搭</text>
      </view>
      <view class="index-item {{currentPage === 'work' ? 'active' : ''}}" bindtap="navigateToPage" data-page="work">
        <text class="item-text">职业穿搭</text>
      </view>
      <view class="index-item {{currentPage === 'party' ? 'active' : ''}}" bindtap="navigateToPage" data-page="party">
        <text class="item-text">派对穿搭</text>
      </view>
      <view class="index-item {{currentPage === 'sport' ? 'active' : ''}}" bindtap="navigateToPage" data-page="sport">
        <text class="item-text">运动穿搭</text>
      </view>
      <view class="index-item {{currentPage === 'seasonal' ? 'active' : ''}}" bindtap="navigateToPage" data-page="seasonal">
        <text class="item-text">季节穿搭</text>
      </view>
      <view class="index-item {{currentPage === 'features' ? 'active' : ''}}" bindtap="navigateToPage" data-page="features">
        <text class="item-text">精选特辑</text>
      </view>
      <view class="index-item" bindtap="goToCreateOutfit">
        <text class="item-text">创建穿搭</text>
      </view>
    </view>

    <view class="index-footer">
      <view class="close-button" bindtap="toggleCategoryIndex">关闭</view>
    </view>
  </view>
</view>