// 导入模块
const outfitDetailManager = require('./modules/outfitDetailManager');
const userManager = require('./modules/userManager');
const imageManager = require('./modules/imageManager');
const limitManager = require('./modules/limitManager');
// 引入本地图片缓存模块
const localImageCache = require('./modules/localImageCache');
// 引入穿搭详情缓存管理模块
const outfitDetailCacheManager = require('./modules/outfitDetailCacheManager');
// 引入能量管理模块
const energyManager = require('../../../../util/energyManager');


Page({
  data: {
    // 定义颜色常量 - 秋季色彩方案
    colors: {
      cowhide_cocoa: '#442D1C',   // 深棕色 Cowhide Cocoa
      spiced_wine: '#74301C',     // 红棕色 Spiced Wine
      toasted_caramel: '#84592B', // 焦糖色 Toasted Caramel
      olive_harvest: '#9D9167',   // 橄榄色 Olive Harvest
      golden_batter: '#E8D1A7',   // 金黄色 Golden Batter
    },
    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',      // 深粉色
      pinkMedium: '#EEA0B2',    // 中粉色
      pinkLight: '#F9C9D6',     // 浅粉色
      blueLight: '#CBE0F9',     // 浅蓝色
      blueMedium: '#97C8E5',    // 中蓝色
      blueDark: '#5EA0D0',      // 深蓝色
    },
    // 主题风格
    themeStyle: 'autumn', // 默认秋季风格
    isLoading: true,
    userOpenId: '',

    // 搭配详情数据
    outfitId: '',
    outfitData: null,

    // 相似搭配推荐
    similarOutfits: [],

    // 屏幕信息
    isSmallScreen: false,
    isIOS: false,
    screenHeight: 0,
    screenWidth: 0,
    imageHeight: '60vh', // 默认图片高度为60vh

    // 图片预览
    showImagePreview: false,
    previewImageUrl: '',
    tempFilePath: '', // 用于保存图片的临时路径

    // AI评分相关
    isScoring: false,
    scoreData: null,
    showAIScoreModal: false // 控制AI评分弹窗显示
  },

  // URL检查定时器
  urlCheckTimer: null,

  onLoad: function(options) {
    console.log('搭配详情页面 onLoad');
    // 初始化云环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-3gi97kso9ab01185',
        traceUser: true,
      });
    }

    // 清除过期的URL缓存
    imageManager.clearExpiredURLCache();

    // 确保本地图片缓存目录存在
    localImageCache.ensureCacheDir();

    // 清除过期的本地图片缓存
    localImageCache.clearExpiredCache();

    // 清除所有图片加载失败计数，确保每次打开页面都有新的尝试机会
    imageManager.clearAllImageFailureCounts();

    // 获取设备信息和屏幕尺寸
    const systemInfo = wx.getSystemInfoSync();
    const screenHeight = systemInfo.windowHeight;
    const screenWidth = systemInfo.windowWidth;

    // 屏幕宽高比判断
    const isSmallScreen = screenWidth < 375 || screenHeight < 667;
    const screenRatio = screenHeight / screenWidth;

    // 根据屏幕比例优化图片高度
    let imageHeight = '60vh'; // 默认60vh

    // 如果是超长屏幕，稍微减小图片比例以保持整体平衡
    if (screenRatio > 2) {
      imageHeight = '55vh';
    }
    // 如果是宽屏设备，稍微增加图片比例
    else if (screenRatio < 1.5) {
      imageHeight = '65vh';
    }

    this.setData({
      isSmallScreen,
      isIOS: systemInfo.platform === 'ios',
      screenHeight,
      screenWidth,
      imageHeight,
      // 根据设备选择主题
      themeStyle: systemInfo.theme === 'dark' ? 'autumn' : 'autumn' // 预留暗色主题支持
    });

    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });
      // 应用主题样式
      this.applyThemeStyle(savedTheme);
    }

    // 获取搭配ID
    if (options && options.id) {
      this.setData({
        outfitId: options.id
      });

      // 获取用户OpenID并加载搭配详情
      this.getUserOpenIdAndLoadOutfitDetail();
    } else {
      console.error('搭配页面缺少ID参数', options);

      wx.showToast({
        title: '搭配ID不存在',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onShow: function() {
    console.log('页面显示，检查图片URL');

    // 启动定时器，定期检查临时URL是否需要刷新（每3分钟检查一次）
    this.startURLCheckTimer();

    // 如果已加载搭配数据，立即检查一次图片
    if (this.data.outfitData) {
      this.checkAndRefreshAllImages();
    }

    // 检查是否需要刷新穿搭数据
    const needRefreshOutfits = wx.getStorageSync('needRefreshOutfits');
    if (needRefreshOutfits && this.data.outfitId && this.data.userOpenId) {
      console.log('检测到其他页面修改了穿搭数据，重新加载数据');

      // 清除标记
      wx.removeStorageSync('needRefreshOutfits');

      // 清除缓存
      outfitDetailCacheManager.clearCache(this.data.outfitId, this.data.userOpenId);

      // 重新加载穿搭详情
      this.loadOutfitDetail();
    }
  },

  onHide: function() {
    // 页面隐藏时清除定时器
    this.clearURLCheckTimer();
  },

  onUnload: function() {
    // 页面卸载时清除定时器
    this.clearURLCheckTimer();
  },

  // 启动URL检查定时器
  startURLCheckTimer: function() {
    // 清除可能存在的旧定时器
    this.clearURLCheckTimer();

    // 创建新定时器，每3分钟检查一次
    this.urlCheckTimer = setInterval(() => {
      this.checkAndRefreshAllImages();
    }, 180000); // 3分钟

    console.log('已启动临时URL检查定时器');
  },

  // 清除URL检查定时器
  clearURLCheckTimer: function() {
    if (this.urlCheckTimer) {
      clearInterval(this.urlCheckTimer);
      this.urlCheckTimer = null;
      console.log('已清除临时URL检查定时器');
    }
  },

  // 检查并刷新所有图片
  checkAndRefreshAllImages: function() {
    if (!this.data.outfitData) return;

    console.log('检查并刷新搭配详情中的所有图片');

    // 重置所有图片加载失败计数，给予新的尝试机会
    imageManager.clearAllImageFailureCounts();

    // 收集所有需要处理的fileID
    const fileIDs = [];

    // 添加预览图fileID
    const previewFileID = this.data.outfitData.imageFileID ||
                        (this.data.outfitData.previewImage && this.data.outfitData.previewImage.includes('cloud://') ?
                         this.data.outfitData.previewImage : null);
    if (previewFileID) {
      fileIDs.push(previewFileID);
    }

    // 添加每个衣物项的fileID
    if (this.data.outfitData.items && Array.isArray(this.data.outfitData.items)) {
      this.data.outfitData.items.forEach(item => {
        const itemFileID = item.imageFileID ||
                          item.fileID ||
                          (item.imageUrl && item.imageUrl.includes('cloud://') ?
                           item.imageUrl : null);

        if (itemFileID) {
          fileIDs.push(itemFileID);
        }
      });
    }

    // 如果有相似搭配，也添加它们的预览图fileID
    if (this.data.similarOutfits && this.data.similarOutfits.length > 0) {
      this.data.similarOutfits.forEach(outfit => {
        const similarFileID = outfit.imageFileID ||
                             (outfit.previewImage && outfit.previewImage.includes('cloud://') ?
                              outfit.previewImage : null);

        if (similarFileID) {
          fileIDs.push(similarFileID);
        }
      });
    }

    // 使用图片缓存模块处理所有图片
    if (fileIDs.length > 0) {
      localImageCache.batchProcessImages(fileIDs)
        .then(localPathMap => {
          // 使用本地图片路径更新数据
          const outfitData = { ...this.data.outfitData };

          // 更新预览图
          if (previewFileID && localPathMap[previewFileID]) {
            outfitData.previewImage = localPathMap[previewFileID];
          }

          // 更新衣物项图片
          if (outfitData.items && Array.isArray(outfitData.items)) {
            outfitData.items.forEach((item, index) => {
              const itemFileID = item.imageFileID ||
                               item.fileID ||
                               (item.imageUrl && item.imageUrl.includes('cloud://') ?
                                item.imageUrl : null);

              if (itemFileID && localPathMap[itemFileID]) {
                outfitData.items[index].imageUrl = localPathMap[itemFileID];
              }
            });
          }

          // 更新数据
          this.setData({
            outfitData: outfitData
          });

          // 更新相似搭配图片
          if (this.data.similarOutfits && this.data.similarOutfits.length > 0) {
            const similarOutfits = [...this.data.similarOutfits];
            let needUpdate = false;

            similarOutfits.forEach((outfit, index) => {
              const similarFileID = outfit.imageFileID ||
                                  (outfit.previewImage && outfit.previewImage.includes('cloud://') ?
                                   outfit.previewImage : null);

              if (similarFileID && localPathMap[similarFileID]) {
                similarOutfits[index].previewImage = localPathMap[similarFileID];
                needUpdate = true;
              }
            });

            if (needUpdate) {
              this.setData({
                similarOutfits: similarOutfits
              });
            }
          }
        })
        .catch(err => {
          console.error('处理图片缓存失败:', err);

          // 失败时使用imageManager作为备选方案
          imageManager.checkAndRefreshAllImages(this.data.outfitData, (updatedOutfitData) => {
            this.setData({
              outfitData: updatedOutfitData
            });
          });
        });
    } else {
      // 使用imageManager检查并刷新所有图片
      imageManager.checkAndRefreshAllImages(this.data.outfitData, (updatedOutfitData) => {
        this.setData({
          outfitData: updatedOutfitData
        });
      });
    }
  },

  // 获取用户OpenID并加载搭配详情
  getUserOpenIdAndLoadOutfitDetail: function() {
    const that = this;

    // 检查outfitId是否有效
    if (!that.data.outfitId) {
      console.error('搭配ID无效，无法加载详情');
      wx.showToast({
        title: '搭配ID无效',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    wx.showLoading({
      title: '加载中...',
    });

    // 设置超时处理，确保不会一直显示加载中
    const timeoutId = setTimeout(() => {
      console.log('获取搭配详情超时');
      wx.hideLoading();

      if (that.data.isLoading) {
        // 如果仍在加载中，显示错误提示并使用模拟数据
        wx.showToast({
          title: '加载超时，使用示例数据',
          icon: 'none',
          duration: 2000
        });
        that.useSimulatedData();
      }
    }, 15000);

    // 获取用户OpenID
    userManager.getUserOpenId()
      .then(openid => {
        console.log('获取用户OpenID成功:', openid);

        that.setData({
          userOpenId: openid
        });

        // 再次检查outfitId是否有效
        if (!that.data.outfitId) {
          throw new Error('搭配ID无效');
        }

        // 使用带缓存功能的方法获取搭配详情
        return outfitDetailCacheManager.getOutfitDetail(that.data.outfitId, openid);
      })
      .then(outfitData => {
        console.log('获取搭配详情成功:', outfitData);

        // 清除超时定时器
        clearTimeout(timeoutId);

        // 更新数据
        that.setData({
          outfitData: outfitData,
          isLoading: false
        });

        // 隐藏加载提示
        wx.hideLoading();

        // 获取相似搭配推荐
        return outfitDetailCacheManager.getSimilarOutfits(outfitData, that.data.userOpenId);
      })
      .then(similarOutfits => {
        console.log('获取相似搭配成功:', similarOutfits);

        that.setData({
          similarOutfits: similarOutfits
        });

        // 启动图片URL检查
        that.startURLCheckTimer();
        // 立即检查一次图片
        that.checkAndRefreshAllImages();
      })
      .catch(err => {
        console.error('加载搭配详情失败:', err);

        // 清除超时定时器
        clearTimeout(timeoutId);

        // 隐藏加载提示
        wx.hideLoading();

        if (that.data.isLoading) {
          wx.showToast({
            title: '加载失败，使用示例数据',
            icon: 'none',
            duration: 2000
          });
          that.useSimulatedData();
        }
      });
  },

  // 加载搭配详情
  loadOutfitDetail: function() {
    const that = this;

    // 检查必要参数
    if (!that.data.outfitId || !that.data.userOpenId) {
      console.error('搭配ID或用户ID无效，无法加载详情');
      return;
    }

    wx.showLoading({
      title: '刷新中...',
    });

    // 清除所有图片加载失败计数，给予新的尝试机会
    imageManager.clearAllImageFailureCounts();

    // 使用带缓存功能的方法获取搭配详情
    outfitDetailCacheManager.getOutfitDetail(that.data.outfitId, that.data.userOpenId)
      .then(outfitData => {
        console.log('刷新搭配详情成功:', outfitData);

        // 更新数据
        that.setData({
          outfitData: outfitData,
          isLoading: false
        });

        // 获取相似搭配推荐
        return outfitDetailCacheManager.getSimilarOutfits(outfitData, that.data.userOpenId);
      })
      .then(similarOutfits => {
        console.log('刷新相似搭配成功:', similarOutfits);

        that.setData({
          similarOutfits: similarOutfits
        });

        // 立即检查一次图片
        that.checkAndRefreshAllImages();

        // 隐藏加载提示
        wx.hideLoading();
      })
      .catch(err => {
        console.error('刷新搭配详情失败:', err);

        // 隐藏加载提示
        wx.hideLoading();

        wx.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      });
  },

  // 使用模拟数据（当无法获取真实数据时）
  useSimulatedData: function(useEmptyData = false) {
    console.log('使用模拟数据，useEmptyData:', useEmptyData);

    if (useEmptyData) {
      // 如果指定使用空数据，则不显示模拟数据
      wx.showToast({
        title: '没有搭配数据',
        icon: 'none',
        duration: 2000
      });

      this.setData({
        outfitData: null,
        similarOutfits: [],
        isLoading: false
      });

      // 确保隐藏加载提示
      wx.hideLoading();

      console.log('已设置为空数据');
      return;
    }

    // 提示用户正在使用模拟数据
    wx.showToast({
      title: '使用示例数据',
      icon: 'none',
      duration: 2000
    });

    // 生成模拟搭配数据
    const mockOutfit = outfitDetailManager.generateMockOutfit(useEmptyData);
    const mockSimilarOutfits = outfitDetailManager.generateMockSimilarOutfits(useEmptyData);

    this.setData({
      outfitData: mockOutfit,
      similarOutfits: mockSimilarOutfits,
      isLoading: false
    });

    // 确保隐藏加载提示
    wx.hideLoading();

    console.log('模拟数据加载完成');
  },

  // 处理图片加载错误
  handleImageError: function(e) {
    console.log('图片加载错误:', e);

    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;
    const defaultImageUrl = imageManager.getDefaultImageUrl();

    if (type === 'preview') {
      // 更新搭配预览图
      const outfitData = this.data.outfitData;
      if (outfitData) {
        // 检查是否有原始的云存储fileID
        const originalFileID = outfitData.imageFileID ||
                              (outfitData.previewImage && outfitData.previewImage.includes('cloud://') ?
                               outfitData.previewImage : null);

        if (originalFileID) {
          // 检查该图片的失败次数
          const failureCount = imageManager.getImageFailureCount(originalFileID);
          console.log(`搭配预览图 ${originalFileID} 的失败次数: ${failureCount}`);

          // 如果失败次数超过最大重试次数，直接使用默认图片
          if (failureCount >= 3) {
            console.warn(`搭配预览图 ${originalFileID} 已达到最大重试次数，使用默认图片`);
            outfitData.previewImage = defaultImageUrl;
            this.setData({
              outfitData: outfitData
            });
            return;
          }

          // 如果有原始fileID，尝试重新获取临时URL
          console.log(`尝试重新获取搭配预览图临时URL, fileID: ${originalFileID}`);
          wx.showLoading({
            title: '重新加载图片...',
            mask: false
          });

          imageManager.refreshTempFileURL(originalFileID, (newUrl) => {
            wx.hideLoading();

            if (newUrl) {
              // 更新成功
              outfitData.previewImage = newUrl;
              this.setData({
                outfitData: outfitData
              });
              console.log('已更新搭配预览图URL');
            } else {
              // 更新失败，使用默认图片
              outfitData.previewImage = defaultImageUrl;
              this.setData({
                outfitData: outfitData
              });
              console.log('无法获取新的临时URL，已使用默认图片');

              // 显示提示
              wx.showToast({
                title: '图片加载失败',
                icon: 'none',
                duration: 1500
              });
            }
          });
        } else {
          // 没有原始fileID，直接使用默认图片
          outfitData.previewImage = defaultImageUrl;
          this.setData({
            outfitData: outfitData
          });
          console.log('已更新搭配预览图为默认图片');
        }
      }
    } else if (type === 'item' && index !== undefined) {
      // 更新衣物图片
      const outfitData = this.data.outfitData;
      if (outfitData && outfitData.items && outfitData.items[index]) {
        const item = outfitData.items[index];

        // 检查是否有原始的云存储fileID
        const originalFileID = item.imageFileID ||
                              item.fileID ||
                              (item.imageUrl && item.imageUrl.includes('cloud://') ?
                               item.imageUrl : null);

        if (originalFileID) {
          // 检查该图片的失败次数
          const failureCount = imageManager.getImageFailureCount(originalFileID);
          console.log(`衣物图片 ${originalFileID} 的失败次数: ${failureCount}`);

          // 如果失败次数超过最大重试次数，直接使用默认图片
          if (failureCount >= 3) {
            console.warn(`衣物图片 ${originalFileID} 已达到最大重试次数，使用默认图片`);
            outfitData.items[index].imageUrl = defaultImageUrl;
            this.setData({
              [`outfitData.items[${index}].imageUrl`]: defaultImageUrl
            });
            return;
          }

          // 如果有原始fileID，尝试重新获取临时URL
          console.log(`尝试重新获取衣物图片临时URL, fileID: ${originalFileID}`);

          // 对于衣物图片，不显示全屏loading，避免影响用户体验
          imageManager.refreshTempFileURL(originalFileID, (newUrl) => {
            if (newUrl) {
              // 更新成功
              outfitData.items[index].imageUrl = newUrl;
              // 使用精确的路径更新数据，提高效率
              this.setData({
                [`outfitData.items[${index}].imageUrl`]: newUrl
              });
              console.log(`已更新衣物项 ${index} 的图片URL`);
            } else {
              // 更新失败，使用默认图片
              outfitData.items[index].imageUrl = defaultImageUrl;
              // 使用精确的路径更新数据，提高效率
              this.setData({
                [`outfitData.items[${index}].imageUrl`]: defaultImageUrl
              });
              console.log(`无法获取新的临时URL，已使用默认图片`);
            }
          });
        } else {
          // 没有原始fileID，直接使用默认图片
          console.log(`更新衣物项 ${index} 的图片为默认图片`);
          outfitData.items[index].imageUrl = defaultImageUrl;
          // 使用精确的路径更新数据，提高效率
          this.setData({
            [`outfitData.items[${index}].imageUrl`]: defaultImageUrl
          });
        }
      }
    } else if (type === 'similar' && index !== undefined) {
      // 更新相似搭配图片
      const similarOutfits = this.data.similarOutfits;
      if (similarOutfits && similarOutfits[index]) {
        const outfit = similarOutfits[index];

        // 检查是否有原始的云存储fileID
        const originalFileID = outfit.imageFileID ||
                              (outfit.previewImage && outfit.previewImage.includes('cloud://') ?
                               outfit.previewImage : null);

        if (originalFileID) {
          // 检查该图片的失败次数
          const failureCount = imageManager.getImageFailureCount(originalFileID);
          console.log(`相似搭配图片 ${originalFileID} 的失败次数: ${failureCount}`);

          // 如果失败次数超过最大重试次数，直接使用默认图片
          if (failureCount >= 3) {
            console.warn(`相似搭配图片 ${originalFileID} 已达到最大重试次数，使用默认图片`);
            similarOutfits[index].previewImage = defaultImageUrl;
            this.setData({
              [`similarOutfits[${index}].previewImage`]: defaultImageUrl
            });
            return;
          }

          // 如果有原始fileID，尝试重新获取临时URL
          console.log(`尝试重新获取相似搭配图片临时URL, fileID: ${originalFileID}`);

          // 对于相似搭配图片，不显示全屏loading，避免影响用户体验
          imageManager.refreshTempFileURL(originalFileID, (newUrl) => {
            if (newUrl) {
              // 更新成功
              similarOutfits[index].previewImage = newUrl;
              // 使用精确的路径更新数据，提高效率
              this.setData({
                [`similarOutfits[${index}].previewImage`]: newUrl
              });
              console.log(`已更新相似搭配 ${index} 的图片URL`);
            } else {
              // 更新失败，使用默认图片
              similarOutfits[index].previewImage = defaultImageUrl;
              // 使用精确的路径更新数据，提高效率
              this.setData({
                [`similarOutfits[${index}].previewImage`]: defaultImageUrl
              });
              console.log(`无法获取新的临时URL，已使用默认图片`);
            }
          });
        } else {
          // 没有原始fileID，直接使用默认图片
          similarOutfits[index].previewImage = defaultImageUrl;
          // 使用精确的路径更新数据，提高效率
          this.setData({
            [`similarOutfits[${index}].previewImage`]: defaultImageUrl
          });
          console.log(`已更新相似搭配 ${index} 的图片为默认图片`);
        }
      }
    }
  },

  // 格式化日期
  formatDate: function(timestamp) {
    return outfitDetailManager.formatDate(timestamp);
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 查看相似搭配
  viewSimilarOutfit: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('查看相似搭配:', id);

    if (!id) {
      console.error('搭配ID不存在');
      return;
    }

    // 跳转到搭配详情页面
    wx.navigateTo({
      url: `../outfit_detail/outfit_detail?id=${id}`
    });
  },

  // 编辑搭配
  editOutfit: function() {
    const outfitId = this.data.outfitId;
    console.log('编辑搭配:', outfitId);

    wx.navigateTo({
      url: `../outfit_edit/outfit_edit?id=${outfitId}`
    });
  },

  // 删除搭配
  deleteOutfit: function() {
    const that = this;
    const outfitId = this.data.outfitId;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个搭配吗？此操作不可恢复。',
      success: function(res) {
        if (res.confirm) {
          console.log('用户确认删除');

          wx.showLoading({
            title: '删除中...',
          });

          const db = wx.cloud.database();

          // 先获取搭配信息
          db.collection('outfits')
            .doc(outfitId)
            .get()
            .then(res => {
              const outfit = res.data;
              const imageFileID = outfit.imageFileID || outfit.previewImage;

              // 删除数据库记录
              return db.collection('outfits')
                .doc(outfitId)
                .remove()
                .then(() => {
                  // 如果有图片文件，也删除图片
                  if (imageFileID && imageFileID.includes('cloud://')) {
                    return wx.cloud.deleteFile({
                      fileList: [imageFileID]
                    });
                  }
                  return { fileList: [] };
                })
                .then(() => {
                  // 返回搭配数据，用于后续处理
                  return outfit;
                });
            })
            .then((outfit) => {
              // 获取应用实例
              const app = getApp();

              // 获取用户openid
              let userOpenId = app.globalData.openid;

              // 如果全局状态中没有openid，尝试从本地缓存获取
              if (!userOpenId) {
                userOpenId = wx.getStorageSync('openid');
              }

              if (userOpenId) {
                // 更新用户穿搭计数（减1）
                return limitManager.updateOutfitsCount(userOpenId, -1)
                  .then(() => {
                    console.log('更新穿搭计数成功');

                    // 设置全局标记，指示限制数据需要刷新
                    app.globalData.limitDataNeedRefresh = true;
                    wx.setStorageSync('needRefreshOutfitSummary', true);
                    wx.setStorageSync('needRefreshOutfits', true);

                    return outfit;
                  })
                  .catch(err => {
                    console.error('更新穿搭计数失败:', err);
                    // 即使更新计数失败，也继续处理
                    return outfit;
                  });
              }

              return outfit;
            })
            .then(() => {
              wx.hideLoading();

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });

              // 延迟返回上一页
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            })
            .catch(err => {
              console.error('删除搭配失败:', err);

              wx.hideLoading();

              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 分享搭配
  shareOutfit: function() {
    // 在小程序中，分享功能通常通过onShareAppMessage实现
    // 这里我们可以显示一个提示，引导用户点击右上角的分享按钮
    wx.showToast({
      title: '请点击右上角分享',
      icon: 'none'
    });

    // 也可以使用微信的分享卡片API（如果小程序已开通）
    if (wx.canIUse('shareToFriend')) {
      wx.shareToFriend({
        title: `${this.data.outfitData.name || '精美穿搭'} - 衣柜橱窗`,
        path: `/page/wardrobe/outfit/outfit_detail/outfit_detail?id=${this.data.outfitData._id}`,
        imageUrl: this.data.outfitData.previewImage || '/image/outfit-icon-HL.png'
      });
    }
  },

  // 分享到朋友圈
  shareToMoments: function() {
    wx.showToast({
      title: '暂不支持分享到朋友圈',
      icon: 'none'
    });
  },

  // 添加分享功能
  onShareAppMessage: function() {
    // 如果有穿搭数据，分享该穿搭信息
    if (this.data.outfitData && this.data.outfitData._id) {
      // 获取第一件衣物的图片作为分享图（如果有）
      let imageUrl = '/image/outfit-icon-HL.png';
      if (this.data.outfitData.items && this.data.outfitData.items.length > 0 && this.data.outfitData.items[0].imageUrl) {
        imageUrl = this.data.outfitData.items[0].imageUrl;
      }

      return {
        title: `${this.data.outfitData.name || '精美穿搭'} - 衣柜橱窗`,
        path: `/page/wardrobe/outfit/outfit_detail/outfit_detail?id=${this.data.outfitData._id}`,
        imageUrl: imageUrl
      };
    } else {
      return {
        title: '时尚穿搭推荐 - 每日穿衣灵感',
        path: '/page/wardrobe/outfit/outfit',
        imageUrl: '/image/outfit-icon-HL.png'
      };
    }
  },

  // 应用主题样式
  applyThemeStyle: function(themeName) {
    console.log('应用新主题：', themeName);

    // 更新页面样式变量
    this.setData({
      themeStyle: themeName
    });

    // 保存主题设置到本地存储
    wx.setStorageSync('themeStyle', themeName);

    // 设置导航栏颜色
    if (themeName === 'autumn') {
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: '#E8D1A7', // 金黄色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });
    } else if (themeName === 'pinkBlue') {
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: '#F9C9D6', // 浅粉色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });
    }
  },

  // 预览搭配图片
  previewOutfitImage: function() {
    if (!this.data.outfitData || !this.data.outfitData.previewImage) {
      wx.showToast({
        title: '图片无法预览',
        icon: 'none'
      });
      return;
    }

    console.log('预览搭配图片:', this.data.outfitData.previewImage);

    this.setData({
      showImagePreview: true,
      previewImageUrl: this.data.outfitData.previewImage
    });

    // 预下载图片以便保存
    this.downloadImageForSaving(this.data.outfitData.previewImage);
  },

  // 预览单个衣物图片
  previewItemImage: function(e) {
    const url = e.currentTarget.dataset.url;

    if (!url) {
      wx.showToast({
        title: '图片无法预览',
        icon: 'none'
      });
      return;
    }

    console.log('预览衣物图片:', url);

    this.setData({
      showImagePreview: true,
      previewImageUrl: url
    });

    // 预下载图片以便保存
    this.downloadImageForSaving(url);
  },

  // 预览相似搭配图片
  previewSimilarImage: function(e) {
    // 阻止事件冒泡，避免触发viewSimilarOutfit
    e.stopPropagation();

    const url = e.currentTarget.dataset.url;

    if (!url) {
      wx.showToast({
        title: '图片无法预览',
        icon: 'none'
      });
      return;
    }

    console.log('预览相似搭配图片:', url);

    this.setData({
      showImagePreview: true,
      previewImageUrl: url
    });

    // 预下载图片以便保存
    this.downloadImageForSaving(url);
  },

  // 下载图片到临时路径，用于后续保存
  downloadImageForSaving: function(url) {
    wx.showLoading({
      title: '准备图片中...'
    });

    wx.downloadFile({
      url: url,
      success: (res) => {
        if (res.statusCode === 200) {
          this.setData({
            tempFilePath: res.tempFilePath
          });
          console.log('图片已下载至临时路径:', res.tempFilePath);
        } else {
          console.error('下载图片失败:', res.statusCode);
        }
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('下载图片失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '图片准备失败',
          icon: 'none'
        });
      }
    });
  },

  // 保存图片到相册
  saveImageToAlbum: function() {
    if (!this.data.tempFilePath) {
      wx.showToast({
        title: '图片未就绪',
        icon: 'none'
      });
      return;
    }

    // 先请求保存相册权限
    wx.getSetting({
      success: (res) => {
        // 如果没有相册权限
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.doSaveImage();
            },
            fail: () => {
              wx.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                confirmText: '去授权',
                cancelText: '取消',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting({
                      success: (res) => {
                        if (res.authSetting['scope.writePhotosAlbum']) {
                          this.doSaveImage();
                        }
                      }
                    });
                  }
                }
              });
            }
          });
        } else {
          // 已有权限，直接保存
          this.doSaveImage();
        }
      }
    });
  },

  // 执行保存图片操作
  doSaveImage: function() {
    wx.showLoading({
      title: '保存中...'
    });

    wx.saveImageToPhotosAlbum({
      filePath: this.data.tempFilePath,
      success: () => {
        wx.hideLoading();
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('保存图片失败:', err);
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    });
  },

  // 关闭图片预览
  closeImagePreview: function() {
    this.setData({
      showImagePreview: false,
      previewImageUrl: ''
    });
  },

  // 防止点击图片内容关闭预览
  preventClose: function(e) {
    // 阻止事件冒泡
    e.stopPropagation();
  },

  // AI穿搭评分
  scoreOutfit: function() {
    const that = this;

    // 检查是否有搭配图片
    if (!this.data.outfitData || !this.data.outfitData.previewImage) {
      wx.showToast({
        title: '无法评分，搭配图片不存在',
        icon: 'none'
      });
      return;
    }

    // 设置评分中状态
    this.setData({
      isScoring: true
    });

    // 检查并消耗能量
    energyManager.consumeEnergyForAction('OUTFIT_SCORING')
      .then(result => {
        if (!result.success) {
          // 能量不足，显示提示
          wx.showModal({
            title: '能量不足',
            content: `AI穿搭评分需要消耗${energyManager.ENERGY_COST.OUTFIT_SCORING}点能量，您的能量不足。请通过观看广告或完成每日任务获取更多能量。`,
            confirmText: '去获取',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                // 跳转到设置页面获取能量
                wx.navigateTo({
                  url: '/page/settings/settings'
                });
              }
            },
            complete: () => {
              // 无论用户点击了哪个按钮，都重置评分状态
              that.setData({
                isScoring: false
              });
            }
          });
          return Promise.reject('能量不足');
        }

        console.log('能量检查通过，已消耗', result.consumedEnergy, '点能量，开始AI穿搭评分');

        wx.showLoading({
          title: 'AI评分中...',
          mask: true
        });

        // 获取搭配的imageFileID
        const imageFileID = this.data.outfitData.imageFileID ||
                          (this.data.outfitData.previewImage && this.data.outfitData.previewImage.includes('cloud://') ?
                           this.data.outfitData.previewImage : null);

        let scoringPromise;

        // 如果有有效的云存储文件ID，先获取临时URL
        if (imageFileID && imageFileID.includes('cloud://')) {
          console.log('获取搭配图片临时URL, fileID:', imageFileID);

          // 获取临时URL
          scoringPromise = wx.cloud.getTempFileURL({
            fileList: [imageFileID],
          })
          .then(res => {
            if (res.fileList && res.fileList.length > 0 && res.fileList[0].tempFileURL) {
              const tempFileURL = res.fileList[0].tempFileURL;
              console.log('获取临时URL成功:', tempFileURL);

              // 使用临时URL进行评分
              return wx.cloud.callFunction({
                name: 'scoreOutfit',
                data: {
                  imageUrl: tempFileURL
                }
              });
            } else {
              console.error('获取临时URL失败:', res);
              // 如果获取临时URL失败，尝试使用现有的previewImage
              const fallbackUrl = this.data.outfitData.previewImage;
              console.log('使用备用URL:', fallbackUrl);

              return wx.cloud.callFunction({
                name: 'scoreOutfit',
                data: {
                  imageUrl: fallbackUrl
                }
              });
            }
          })
          .catch(err => {
            console.error('获取临时URL出错:', err);
            // 如果出错，尝试使用现有的previewImage
            const fallbackUrl = this.data.outfitData.previewImage;
            console.log('使用备用URL:', fallbackUrl);

            return wx.cloud.callFunction({
              name: 'scoreOutfit',
              data: {
                imageUrl: fallbackUrl
              }
            });
          });
        } else {
          // 如果没有有效的云存储文件ID，直接使用previewImage
          const imageUrl = this.data.outfitData.previewImage;
          console.log('没有找到有效的云存储文件ID，直接使用previewImage:', imageUrl);

          // 调用云函数进行评分
          scoringPromise = wx.cloud.callFunction({
            name: 'scoreOutfit',
            data: {
              imageUrl: imageUrl
            }
          });
        }

        return scoringPromise.then(res => {
          console.log('AI评分结果:', res);

          if (res.result && res.result.success) {
            // 更新评分数据
            const scoreData = res.result.data;
            that.setData({
              scoreData: scoreData,
              isScoring: false,
              showAIScoreModal: true // 显示AI评分弹窗
            });

            // 更新搭配数据中的AI评分
            if (that.data.outfitId && scoreData && typeof scoreData.score === 'number') {
              // 导入outfitDetailManager模块
              const outfitDetailManager = require('./modules/outfitDetailManager');

              // 保存评分到数据库
              outfitDetailManager.updateOutfitAIScore(that.data.outfitId, scoreData.score)
                .then(result => {
                  console.log('AI评分已保存到数据库:', result);

                  // 更新本地数据
                  that.setData({
                    'outfitData.aiScore': scoreData.score
                  });

                  // 设置标记，通知其他页面刷新数据
                  wx.setStorageSync('needRefreshOutfits', true);
                })
                .catch(err => {
                  console.error('保存AI评分到数据库失败:', err);
                  // 即使保存失败，也不影响用户体验，继续显示评分结果
                });
            }

            wx.hideLoading();
          } else {
            console.error('AI评分失败:', res.result ? res.result.error : '未知错误');

            // 评分失败，补偿用户能量
            return energyManager.increaseEnergy(energyManager.ENERGY_COST.OUTFIT_SCORING)
              .then(() => {
                console.log('已补偿用户能量');

                that.setData({
                  isScoring: false
                });

                wx.hideLoading();
                wx.showToast({
                  title: '评分失败，已返还能量',
                  icon: 'none'
                });
              });
          }
        });
      })
      .catch(err => {
        if (err === '能量不足') {
          // 已经处理过的错误，不需要再次处理
          return;
        }

        console.error('AI评分过程出错:', err);

        // 评分失败，补偿用户能量
        energyManager.increaseEnergy(energyManager.ENERGY_COST.OUTFIT_SCORING)
          .then(() => {
            console.log('已补偿用户能量');

            that.setData({
              isScoring: false
            });

            wx.hideLoading();
            wx.showToast({
              title: '评分失败，已返还能量',
              icon: 'none'
            });
          });
      });
  },

  // 关闭AI评分弹窗
  closeAIScoreModal: function() {
    this.setData({
      showAIScoreModal: false
    });
  },

  // 防止AI评分弹窗内容点击事件冒泡
  preventAIScoreModalClose: function(e) {
    // 阻止事件冒泡
    e.stopPropagation();
  },

  // 删除AI评分
  deleteAIScore: function() {
    const that = this;

    // 确认是否删除评分
    wx.showModal({
      title: '删除评分',
      content: '确定要删除这个搭配的AI评分吗？',
      success: function(res) {
        if (res.confirm) {
          console.log('用户确认删除AI评分');

          wx.showLoading({
            title: '删除中...',
          });

          // 检查是否有有效的搭配ID
          if (!that.data.outfitId) {
            wx.hideLoading();
            wx.showToast({
              title: '搭配ID无效',
              icon: 'none'
            });
            return;
          }

          // 导入outfitDetailManager模块
          const outfitDetailManager = require('./modules/outfitDetailManager');

          // 从数据库中删除AI评分
          outfitDetailManager.updateOutfitAIScore(that.data.outfitId, null)
            .then(result => {
              console.log('AI评分已从数据库删除:', result);

              // 更新本地数据
              that.setData({
                'outfitData.aiScore': null,
                'scoreData': null
              });

              // 设置标记，通知其他页面刷新数据
              wx.setStorageSync('needRefreshOutfits', true);

              wx.hideLoading();
              wx.showToast({
                title: '评分已删除',
                icon: 'success'
              });
            })
            .catch(err => {
              console.error('删除AI评分失败:', err);

              wx.hideLoading();
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            });
        }
      }
    });
  },
});