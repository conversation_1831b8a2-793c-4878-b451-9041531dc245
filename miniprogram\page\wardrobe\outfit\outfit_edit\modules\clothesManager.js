// 衣物管理模块
// 负责处理衣物数据的获取、过滤等操作

// 导入图片管理模块
const imageManager = require('./imageManager');

// 获取用户所有衣物
function getUserClothes(userOpenId) {
  return new Promise((resolve, reject) => {
    if (!userOpenId) {
      reject(new Error('用户ID不能为空'));
      return;
    }

    console.log('开始获取用户衣物数据，openid:', userOpenId);

    // 使用云函数获取所有衣物数据，避免20条限制
    wx.cloud.callFunction({
      name: 'getAllClothes',
      data: {
        userOpenId: userOpenId
      }
    })
    .then(res => {
      // 检查云函数返回结果
      if (!res || !res.result || !res.result.success) {
        console.error('云函数返回错误:', res);
        return Promise.reject(new Error('获取衣物数据失败'));
      }

      const clothes = res.result.data || [];
      console.log('云函数成功获取用户衣物数据，总数:', clothes.length);

      // 处理所有衣物的图片
      return imageManager.batchProcessClothesImages(clothes);
    })
    .catch(err => {
      console.error('云函数获取衣物数据失败，尝试使用小程序端获取:', err);

      // 如果云函数失败，尝试使用小程序端获取（作为备用方案）
      return fallbackGetUserClothes(userOpenId);
    })
      .then(processedClothes => {
        console.log('衣物图片处理完成');
        resolve(processedClothes);
      })
      .catch(err => {
        console.error('获取用户衣物失败:', err);
        reject(err);
      });
  });
}

// 备用方案：使用小程序端获取用户衣物
function fallbackGetUserClothes(userOpenId) {
  return new Promise((resolve, reject) => {
    console.log('使用备用方案获取用户衣物数据');

    const db = wx.cloud.database();
    const _ = db.command;
    const MAX_LIMIT = 20; // 小程序端限制为20

    // 构建查询条件 - 同时支持_openid和openid两种字段名
    let query = _.or([
      { _openid: userOpenId },
      { openid: userOpenId }
    ]);

    // 先获取数据总数
    db.collection('clothes')
      .where(query)
      .count()
      .then(res => {
        const total = res.total;
        console.log('用户衣物总数:', total);

        // 计算需要分几次获取
        const batchTimes = Math.ceil(total / MAX_LIMIT);

        // 没有数据，返回空数组
        if (batchTimes === 0) {
          resolve([]);
          return;
        }

        // 批量获取数据的任务列表
        const tasks = [];
        for (let i = 0; i < batchTimes; i++) {
          const promise = db.collection('clothes')
            .where(query)
            .skip(i * MAX_LIMIT)
            .limit(MAX_LIMIT)
            .get();
          tasks.push(promise);
        }

        // 等待所有任务完成
        return Promise.all(tasks);
      })
      .then(results => {
        // 合并查询结果
        let clothes = [];
        results.forEach(res => {
          clothes = clothes.concat(res.data);
        });

        console.log('备用方案成功获取用户衣物数据，总数:', clothes.length);

        // 处理所有衣物的图片
        return imageManager.batchProcessClothesImages(clothes);
      })
      .then(processedClothes => {
        resolve(processedClothes);
      })
      .catch(err => {
        console.error('备用方案获取衣物数据失败:', err);
        reject(err);
      });
  });
}

// 按类别过滤衣物
function filterByCategory(clothes, categoryFilter) {
  if (!clothes || clothes.length === 0) {
    return [];
  }

  console.log('过滤衣物，类别过滤条件:', categoryFilter);
  console.log('衣物数量:', clothes.length);

  // 如果没有指定类别或类别为0（全部），返回所有衣物
  if (categoryFilter === null || categoryFilter === undefined) {
    return [...clothes];
  }

  // 打印所有衣物的类别信息，便于调试
  console.log('所有衣物的类别信息:');
  clothes.forEach((item, index) => {
    if (index < 10) { // 只打印前10个，避免日志过多
      console.log(`衣物${index}: id=${item._id}, 类别=${item.category || item.categoryId || item.category_id}`);
    }
  });

  // 按类别过滤
  const filtered = clothes.filter(item => {
    // 兼容不同的类别字段命名
    const itemCategory = item.category || item.categoryId || item.category_id;

    // 将两者转换为字符串进行比较，解决类型不一致问题
    const match = String(itemCategory) === String(categoryFilter);

    // 输出调试信息
    console.log('衣物类别:', itemCategory, '过滤类别:', categoryFilter, '是否匹配:', match);

    return match;
  });

  console.log('过滤后的衣物数量:', filtered.length);
  return filtered;
}

// 统计各类别的衣物数量
function getCategoryCounts(clothes) {
  if (!clothes || clothes.length === 0) {
    return {};
  }

  const counts = {};
  clothes.forEach(item => {
    // 兼容不同的类别字段命名
    const categoryId = item.categoryId || item.category_id || item.category;

    if (categoryId !== undefined && categoryId !== null) {
      counts[categoryId] = (counts[categoryId] || 0) + 1;
    }
  });

  return counts;
}

// 获取衣物类别列表
function getCategories() {
  return new Promise((resolve, reject) => {
    // 从缓存中获取类别数据
    const cachedCategories = wx.getStorageSync('clothesCategories');
    if (cachedCategories && cachedCategories.length > 0) {
      console.log('从缓存获取衣物类别:', cachedCategories);
      resolve(cachedCategories);
      return;
    }

    // 缓存中没有，从数据库获取
    console.log('从数据库获取衣物类别');
    const db = wx.cloud.database();

    db.collection('categories')
      .where({
        type: 'clothes'
      })
      .get()
      .then(res => {
        const categories = res.data;

        // 添加"全部"类别
        const allCategories = [
          { id: 0, name: '全部', icon: '全', count: 0 }
        ].concat(categories);

        // 缓存类别数据（1小时）
        wx.setStorageSync('clothesCategories', allCategories);
        wx.setStorageSync('clothesCategoriesExpire', Date.now() + 3600000);

        console.log('成功获取衣物类别:', allCategories);
        resolve(allCategories);
      })
      .catch(err => {
        console.error('获取衣物类别失败:', err);

        // 出错时返回默认类别
        const defaultCategories = [
          { id: 0, name: '全部', icon: '全', count: 0 },
          { id: 1, name: '上衣', icon: '👕', count: 0 },
          { id: 2, name: '裤子', icon: '👖', count: 0 },
          { id: 3, name: '外套', icon: '🧥', count: 0 },
          { id: 4, name: '鞋子', icon: '👟', count: 0 },
          { id: 5, name: '配饰', icon: '👔', count: 0 }
        ];

        resolve(defaultCategories);
      });
  });
}

// 更新类别数量
function updateCategoryCounts(clothes, categories) {
  if (!categories || !clothes) {
    return categories;
  }

  console.log('更新类别数量, 衣物数量:', clothes.length, '类别数量:', categories.length);

  // 打印类别信息，便于调试
  console.log('类别信息:');
  categories.forEach(cat => {
    console.log(`类别: id=${cat.id}, name=${cat.name}, category=${cat.category}`);
  });

  // 统计各类别的衣物数量
  const categoryCounts = {};
  clothes.forEach(item => {
    const category = item.category || item.categoryId || item.category_id;
    if (category) {
      // 将类别转换为字符串作为键
      const categoryKey = String(category);
      categoryCounts[categoryKey] = (categoryCounts[categoryKey] || 0) + 1;
    }
  });

  console.log('类别计数:', categoryCounts);

  // 更新各类别的数量
  return categories.map(category => {
    if (category.id === 0) {
      // "全部"类别
      return { ...category, count: clothes.length };
    } else {
      // 其他类别 - 将category.category转换为字符串进行查找
      const categoryKey = category.category ? String(category.category) : null;
      const count = categoryKey ? (categoryCounts[categoryKey] || 0) : 0;
      console.log(`类别 ${category.name} (${categoryKey}) 的计数: ${count}`);
      return { ...category, count };
    }
  });
}

module.exports = {
  getUserClothes,
  filterByCategory,
  getCategoryCounts,
  getCategories,
  updateCategoryCounts
};