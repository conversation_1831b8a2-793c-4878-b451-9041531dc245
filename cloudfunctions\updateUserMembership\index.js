// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = event.openid || wxContext.OPENID
  
  if (!openid) {
    return {
      success: false,
      errMsg: '未能获取有效的用户标识'
    }
  }
  
  try {
    // 更新用户会员状态
    const result = await db.collection('users').where({
      _openid: openid
    }).update({
      data: {
        memberType: event.memberType || 'regular',
        memberDaysLeft: event.memberDaysLeft || 0,
        updateTime: db.serverDate()
      }
    })
    
    return {
      success: true,
      updated: result.stats.updated,
      openid: openid
    }
  } catch (err) {
    console.error('更新会员状态失败:', err)
    return {
      success: false,
      errMsg: err.message,
      openid: openid
    }
  }
}
