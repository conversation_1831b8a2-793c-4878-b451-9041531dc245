/* 会员兑换页面样式 */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
  box-sizing: border-box;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 头部样式 */
.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 兑换卡片样式 */
.redeem-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.input-container {
  display: flex;
  margin-bottom: 30rpx;
}

.redeem-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.redeem-btn {
  width: 160rpx;
  height: 80rpx;
  background-color: #e0e0e0;
  color: #999;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
}

.redeem-btn.active {
  background-color: #D4AF37;
  color: white;
}

.tips {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 兑换结果弹窗样式 */
.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.result-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.result-content {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1001;
}

.result-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.result-icon.success {
  background-color: #e8f5e9;
  color: #4caf50;
}

.result-icon.fail {
  background-color: #ffebee;
  color: #f44336;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.result-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.result-detail {
  width: 100%;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 15rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 160rpx;
  font-size: 26rpx;
  color: #666;
}

.detail-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

.result-btn {
  width: 80%;
  height: 80rpx;
  background-color: #D4AF37;
  color: white;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
  padding: 20rpx;
  margin: 10rpx;
}

/* 会员权益展示样式 */
.benefits-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 15rpx;
}

.benefit-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.benefit-item:nth-child(1) {
  background-color: rgba(144, 202, 249, 0.15);
  border-left: 6rpx solid #90CAF9;
}

.benefit-item:nth-child(2) {
  background-color: rgba(255, 213, 79, 0.15);
  border-left: 6rpx solid #FFD54F;
}

.benefit-item:nth-child(3) {
  background-color: rgba(129, 199, 132, 0.15);
  border-left: 6rpx solid #81C784;
}

.benefit-item:nth-child(4) {
  background-color: rgba(186, 104, 200, 0.15);
  border-left: 6rpx solid #BA68C8;
}

.benefit-item:nth-child(5) {
  background-color: rgba(255, 138, 128, 0.15);
  border-left: 6rpx solid #FF8A80;
}

.benefit-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.benefit-content {
  flex: 1;
}

.benefit-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.benefit-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
