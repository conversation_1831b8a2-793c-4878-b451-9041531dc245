// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const userOpenId = wxContext.OPENID
  
  // 获取请求参数
  const { rewardType, date } = event
  
  if (!rewardType) {
    return {
      success: false,
      error: '奖励类型不能为空'
    }
  }
  
  // 使用事务确保数据一致性
  const transaction = await db.startTransaction()
  
  try {
    // 1. 检查用户是否完成所有任务
    const dailyTaskRecord = await transaction.collection('dailyTasks').where({
      userOpenId: userOpenId,
      date: date || new Date().toISOString().split('T')[0]
    }).get()
    
    if (dailyTaskRecord.data.length === 0) {
      await transaction.rollback()
      return {
        success: false,
        error: '未找到每日任务记录'
      }
    }
    
    const taskData = dailyTaskRecord.data[0]
    
    // 检查是否已完成所有任务
    if (!taskData.canDrawPrize) {
      await transaction.rollback()
      return {
        success: false,
        error: '未完成所有任务，无法领取奖励'
      }
    }
    
    // 检查是否已领取奖励
    if (taskData.rewardClaimed) {
      await transaction.rollback()
      return {
        success: false,
        error: '今日奖励已领取'
      }
    }
    
    // 2. 根据奖励类型更新相应的限制
    let updateField = {}
    
    switch (rewardType) {
      case 'clothes':
        updateField = { clothesLimit: _.inc(1) }
        break
      case 'outfits':
        updateField = { outfitsLimit: _.inc(1) }
        break
      case 'storage':
        updateField = { storageLimit: _.inc(1) }
        break
      default:
        await transaction.rollback()
        return {
          success: false,
          error: '无效的奖励类型'
        }
    }
    
    // 3. 更新用户限制
    const userResult = await transaction.collection('users').where({
      _openid: userOpenId
    }).update({
      data: updateField
    })
    
    if (userResult.stats.updated === 0) {
      await transaction.rollback()
      return {
        success: false,
        error: '更新用户限制失败'
      }
    }
    
    // 4. 标记奖励已领取
    const taskResult = await transaction.collection('dailyTasks').doc(taskData._id).update({
      data: {
        rewardClaimed: true,
        rewardType: rewardType,
        rewardClaimedAt: db.serverDate()
      }
    })
    
    if (taskResult.stats.updated === 0) {
      await transaction.rollback()
      return {
        success: false,
        error: '标记奖励领取状态失败'
      }
    }
    
    // 5. 添加奖励领取记录
    await transaction.collection('rewards').add({
      data: {
        userOpenId: userOpenId,
        rewardType: rewardType,
        source: 'dailyTask',
        date: date || new Date().toISOString().split('T')[0],
        createdAt: db.serverDate()
      }
    })
    
    // 提交事务
    await transaction.commit()
    
    return {
      success: true,
      message: '奖励领取成功',
      rewardType: rewardType
    }
    
  } catch (error) {
    // 发生错误时回滚事务
    await transaction.rollback()
    console.error('领取奖励失败:', error)
    
    return {
      success: false,
      error: '领取奖励过程中发生错误',
      detail: error.message
    }
  }
} 