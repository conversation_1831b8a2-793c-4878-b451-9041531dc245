/* 全局容器 */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 30rpx;
}

/* 加载提示 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 8rpx solid rgba(255, 255, 255, 0.3);
  border-top: 8rpx solid #442D1C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 32rpx;
  color: #442D1C;
}

/* 主要内容区域 */
.main-content {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow-y: auto;
}

/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  padding-top: 25px; /* 默认状态栏高度 */
  box-sizing: border-box;
  position: relative;
  color: white;
  z-index: 100;
}

.nav-content {
  width: 100%;
  height: 90rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.back-button, .save-button {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 30rpx;
  font-size: 28rpx;
}

.back-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.page-title {
  font-size: 34rpx;
  font-weight: bold;
}

/* 搭配信息编辑区域 */
.outfit-info-editor {
  width: 95%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  margin: 20rpx 0;
  border-radius: 20rpx;
  box-sizing: border-box;
}

.outfit-name-input {
  flex: 1;
}

.outfit-name-input input {
  font-size: 32rpx;
  padding: 10rpx 0;
  color: white;
}

.outfit-category-selector {
  display: flex;
  align-items: center;
  position: relative;
  padding: 10rpx 20rpx;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10rpx;
}

.category-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.category-name {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.dropdown-icon {
  font-size: 24rpx;
}

/* 类型选择器下拉菜单 */
.category-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
  padding: 10rpx 0;
  margin-top: 10rpx;
  min-width: 180rpx;
}

.category-option {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
}

.option-icon {
  margin-right: 15rpx;
}

.option-name {
  font-size: 28rpx;
  color: #333;
}

/* 画布区域 */
.canvas-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20rpx 0;
  position: relative;
  z-index: 1;
  flex: 1;
}

.canvas-container {
  position: relative;
  background-color: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  margin-bottom: 20rpx;
  overflow: hidden;
}

/* 画布项目 */
.canvas-item {
  position: absolute;
  transform-origin: center center;
  border: 1px solid transparent;
  box-sizing: border-box;
  touch-action: none;
  cursor: move;
  transition: border-color 0.2s ease;
  overflow: visible;
}

.canvas-item.active {
  border: 2rpx dashed #ff4500;
  box-shadow: 0 0 10rpx rgba(255, 69, 0, 0.5);
  z-index: 999 !important; /* 使选中项始终显示在最上层 */
}

/* 控制按钮 */
.control-btn {
  position: absolute;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  z-index: 20;
  font-weight: bold;
  font-size: 18px;
  transition: all 0.2s ease;
}

.control-btn:active {
  transform: scale(0.9);
  opacity: 0.8;
}

.delete-btn {
  background-color: #ff4500;
  color: white;
}

.rotate-cw-btn, .rotate-ccw-btn {
  background-color: #4285f4;
  color: white;
}

.layer-up-btn, .layer-down-btn {
  background-color: #34a853;
  color: white;
}

.size-increase-btn, .size-decrease-btn {
  background-color: #fbbc05;
  color: white;
}

/* 图层信息 */
.layer-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  font-size: 20px;
  font-weight: bold;
  z-index: 15;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* 画布工具栏 */
.canvas-toolbar {
  width: 95%;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-radius: 10rpx;
  margin-top: 10rpx;
}

/* 画布提示 */
.canvas-hint {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 28rpx;
  text-align: center;
  line-height: 1.8;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 5;
}

.toolbar-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.button-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.button-text {
  font-size: 24rpx;
}

/* 衣物选择区域 */
.closet-container {
  width: 95%;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  margin-bottom: 15rpx;
  position: relative;
  z-index: 5;
  height: 240rpx;
}

/* 类别选择 */
.category-list {
  width: 100%;
  padding: 5rpx 0;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10rpx;
  margin-bottom: 2rpx;
  overflow: visible;
  z-index: 10;
  position: relative;
}

.category-list-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.save-button-container {
  padding-right: 10rpx;
  flex-shrink: 0;
}

.save-button {
  background-color: rgba(255, 255, 255, 0.3);
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.save-button:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.categories-scroll {
  white-space: nowrap;
  padding: 0 10rpx;
  flex: 1;
  overflow-x: auto;
}

.category-item {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  padding: 6rpx 12rpx;
  margin-right: 12rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  color: white;
  min-width: 70rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
}

.category-item.active {
  background-color: rgba(255, 255, 255, 0.4);
}

.category-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
  display: inline-block;
  font-weight: bold;
}

.category-name {
  font-size: 22rpx;
  display: inline-block;
  white-space: nowrap;
}

.category-count {
  font-size: 18rpx;
  opacity: 0.8;
  margin-left: 4rpx;
  display: inline-block;
}

/* 衣物列表 */
.clothes-list {
  background-color: #f5f5f5;
  padding: 10rpx 0;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 6;
  height: 190rpx;
}

.clothes-scroll {
  width: 100%;
  height: 100%;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
}

.clothes-grid {
  display: inline-flex;
  flex-wrap: nowrap;
  padding: 0 10rpx;
}

.cloth-item {
  width: 120rpx;
  min-width: 120rpx;
  height: 170rpx;
  background-color: white;
  margin: 0 10rpx;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  display: inline-flex;
  flex-direction: column;
  transition: transform 0.2s;
}

.cloth-item:first-child {
  margin-left: 15rpx;
}

.cloth-item:last-child {
  margin-right: 15rpx;
}

.cloth-item-hover {
  transform: scale(0.95);
}

.cloth-image {
  width: 100%;
  height: 120rpx;
  background-color: #f9f9f9;
  object-fit: contain;
  margin: 5rpx 0;
}

.cloth-name {
  font-size: 18rpx;
  padding: 4rpx;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333;
  background-color: #f0f0f0;
  width: 100%;
}

/* 无数据提示 */
.no-clothes-tip {
  display: inline-block;
  padding: 20rpx 30rpx;
  text-align: center;
  color: #999;
  font-size: 24rpx;
  white-space: normal;
  width: 100%;
}

.tip-detail {
  font-size: 24rpx;
  color: #bbb;
  margin-top: 10rpx;
}

/* 主题相关 */
.autumn {
  color: #442D1C;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .cloth-item {
    width: 48%;
  }
}