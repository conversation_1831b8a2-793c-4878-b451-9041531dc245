Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: '上传结果'
    },
    successCount: {
      type: Number,
      value: 0
    },
    failedCount: {
      type: Number,
      value: 0
    },
    categoryStats: {
      type: Object,
      value: {}
    },
    refundAmount: {
      type: Number,
      value: 0
    },
    isUrlUpload: {
      type: Boolean,
      value: false
    },
    failedItems: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    sortedCategories: [],
    uncategorizedCount: 0,
    itemType: '',
    itemUnit: ''
  },

  /**
   * 数据监听器
   */
  observers: {
    'categoryStats, isUrlUpload': function(categoryStats, isUrlUpload) {
      // 根据是否是URL上传调整提示文本
      const itemType = isUrlUpload ? 'URL' : '照片';
      const itemUnit = isUrlUpload ? '个' : '件';

      // 将类别按数量排序
      const sortedCategories = Object.entries(categoryStats || {})
        .sort((a, b) => b[1] - a[1])
        .map(([category, count]) => {
          return {
            name: category,
            count: count
          };
        });

      // 计算未分类数量
      const uncategorizedCount = (categoryStats && categoryStats['未分类']) || 0;

      this.setData({
        sortedCategories,
        uncategorizedCount,
        itemType,
        itemUnit
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 关闭弹窗
    onClose() {
      this.setData({
        visible: false
      });
      this.triggerEvent('close');
    },

    // 阻止冒泡
    preventBubble() {
      // 阻止点击事件冒泡
    },

    // 查看失败图片
    viewFailedImages() {
      const { failedItems, isUrlUpload } = this.data;

      if (!failedItems || failedItems.length === 0) {
        wx.showToast({
          title: '没有失败图片',
          icon: 'none'
        });
        return;
      }

      if (isUrlUpload) {
        // 如果是URL上传，显示URL列表
        wx.showModal({
          title: '失败URL列表',
          content: failedItems.join('\n'),
          showCancel: false,
          confirmText: '确定'
        });
      } else {
        // 如果是本地图片上传，使用预览图片API
        wx.previewImage({
          urls: failedItems,
          current: failedItems[0]
        });
      }
    }
  }
})
