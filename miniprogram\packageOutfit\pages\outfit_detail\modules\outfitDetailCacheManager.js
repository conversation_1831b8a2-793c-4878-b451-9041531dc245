/**
 * 穿搭详情缓存管理模块
 * 负责outfit_detail页面穿搭数据的缓存管理
 */

// 缓存相关常量
const OUTFIT_DETAIL_CACHE_KEY = 'outfit_detail_cache_';
const OUTFIT_DETAIL_CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7天缓存过期时间

/**
 * 从本地缓存加载搭配详情数据
 * @param {string} outfitId - 搭配ID
 * @param {string} userOpenId - 用户的OpenID
 * @returns {Object|null} 缓存的搭配详情数据，如果不存在或已过期则返回null
 */
function loadFromCache(outfitId, userOpenId) {
  if (!outfitId || !userOpenId) return null;
  
  try {
    // 缓存键包含用户ID和搭配ID，确保不同用户、不同搭配有不同的缓存
    const cacheKey = `${OUTFIT_DETAIL_CACHE_KEY}${userOpenId}_${outfitId}`;
    
    const cachedData = wx.getStorageSync(cacheKey);
    if (!cachedData) return null;
    
    // 检查缓存是否已过期
    const now = Date.now();
    if (now - cachedData.timestamp > OUTFIT_DETAIL_CACHE_TTL) {
      console.log('搭配详情缓存已过期');
      // 删除过期缓存
      wx.removeStorageSync(cacheKey);
      return null;
    }
    
    console.log('从本地缓存加载搭配详情数据:', outfitId);
    return cachedData.outfitData;
  } catch (err) {
    console.error('从本地缓存加载搭配详情数据失败:', err);
    return null;
  }
}

/**
 * 更新本地缓存
 * @param {string} outfitId - 搭配ID
 * @param {string} userOpenId - 用户的OpenID
 * @param {Object} outfitData - 搭配详情数据
 */
function updateCache(outfitId, userOpenId, outfitData) {
  if (!outfitId || !userOpenId || !outfitData) return;
  
  try {
    // 缓存键包含用户ID和搭配ID，确保不同用户、不同搭配有不同的缓存
    const cacheKey = `${OUTFIT_DETAIL_CACHE_KEY}${userOpenId}_${outfitId}`;
    
    const cacheData = {
      outfitData: outfitData,
      timestamp: Date.now()
    };
    
    // 存储到本地缓存
    wx.setStorageSync(cacheKey, cacheData);
    console.log('已更新搭配详情本地缓存:', outfitId);
  } catch (err) {
    console.error('更新搭配详情本地缓存失败:', err);
  }
}

/**
 * 清除搭配详情缓存
 * @param {string} outfitId - 搭配ID，如果为空则清除所有搭配详情缓存
 * @param {string} userOpenId - 用户的OpenID，如果为空则清除所有用户的缓存
 */
function clearCache(outfitId, userOpenId) {
  try {
    if (outfitId && userOpenId) {
      // 清除指定用户的指定搭配缓存
      const cacheKey = `${OUTFIT_DETAIL_CACHE_KEY}${userOpenId}_${outfitId}`;
      wx.removeStorageSync(cacheKey);
      console.log(`已清除用户 ${userOpenId} 的搭配 ${outfitId} 详情缓存`);
    } else if (userOpenId) {
      // 清除指定用户的所有搭配详情缓存
      const allKeys = wx.getStorageInfoSync().keys;
      let clearedCount = 0;
      
      allKeys.forEach(key => {
        if (key.startsWith(`${OUTFIT_DETAIL_CACHE_KEY}${userOpenId}_`)) {
          wx.removeStorageSync(key);
          clearedCount++;
        }
      });
      
      console.log(`已清除用户 ${userOpenId} 的所有搭配详情缓存，共 ${clearedCount} 项`);
    } else {
      // 清除所有与搭配详情相关的缓存
      const allKeys = wx.getStorageInfoSync().keys;
      let clearedCount = 0;
      
      allKeys.forEach(key => {
        if (key.startsWith(OUTFIT_DETAIL_CACHE_KEY)) {
          wx.removeStorageSync(key);
          clearedCount++;
        }
      });
      
      console.log(`已清除所有用户的搭配详情缓存，共 ${clearedCount} 项`);
    }
  } catch (err) {
    console.error('清除搭配详情缓存失败:', err);
  }
}

/**
 * 检查缓存是否应该被使用
 * 考虑全局缓存设置和特殊场景
 * @returns {boolean} 是否应该使用缓存
 */
function shouldUseCache() {
  // 从全局设置或页面设置中获取是否使用缓存的标志
  const app = getApp();
  // 如果全局禁用缓存，则不使用缓存
  if (app.globalData && app.globalData.disableCache === true) {
    console.log('全局设置禁用缓存，不使用搭配详情缓存');
    return false;
  }
  
  // 检查是否有强制刷新标志
  const needRefreshOutfits = wx.getStorageSync('needRefreshOutfits');
  if (needRefreshOutfits) {
    console.log('检测到强制刷新标志，不使用搭配详情缓存');
    return false;
  }
  
  return true;
}

/**
 * 获取搭配详情
 * 先尝试从缓存获取，如果缓存不存在或无效则从云端获取
 * @param {string} outfitId - 搭配ID
 * @param {string} userOpenId - 用户的OpenID
 * @returns {Promise<Object>} 搭配详情数据的Promise
 */
function getOutfitDetail(outfitId, userOpenId) {
  return new Promise((resolve, reject) => {
    if (!outfitId || !userOpenId) {
      reject(new Error('搭配ID或用户OpenID不能为空'));
      return;
    }
    
    console.log('获取搭配详情，ID:', outfitId, '用户ID:', userOpenId);
    
    // 检查是否应该使用缓存
    if (shouldUseCache()) {
      // 尝试从缓存获取数据
      const cachedOutfit = loadFromCache(outfitId, userOpenId);
      if (cachedOutfit) {
        console.log('使用本地缓存的搭配详情数据');
        resolve(cachedOutfit);
        return;
      }
    }
    
    // 缓存不存在或不应使用缓存，从云端获取
    console.log('从云端获取搭配详情数据');
    
    // 获取数据库实例
    const db = wx.cloud.database();
    
    // 查询搭配详情数据
    db.collection('outfits').doc(outfitId).get({
      success: res => {
        if (res.data) {
          console.log('从云端获取搭配详情成功:', res.data);
          
          // 更新缓存
          updateCache(outfitId, userOpenId, res.data);
          
          resolve(res.data);
        } else {
          console.error('搭配详情不存在');
          reject(new Error('搭配详情不存在'));
        }
      },
      fail: err => {
        console.error('获取搭配详情失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 获取相似搭配推荐
 * @param {Object} outfitData - 当前搭配数据
 * @param {string} userOpenId - 用户的OpenID
 * @param {number} limit - 获取的最大数量，默认为5
 * @returns {Promise<Array>} 相似搭配数据的Promise
 */
function getSimilarOutfits(outfitData, userOpenId, limit = 5) {
  return new Promise((resolve, reject) => {
    if (!outfitData || !userOpenId) {
      resolve([]);
      return;
    }
    
    // 通常相似搭配不需要缓存，因为每次可能需要最新的推荐
    // 如果未来需要缓存，可以增加类似的缓存机制
    
    // 获取数据库实例
    const db = wx.cloud.database();
    const _ = db.command;
    
    // 构建查询条件
    // 1. 类别相同
    // 2. 季节相同
    // 3. 排除当前搭配
    // 4. 仅查询当前用户的搭配
    const queryConditions = {
      _openid: userOpenId,
      _id: _.neq(outfitData._id) // 排除当前搭配
    };
    
    // 如果有类别，添加到查询条件
    if (outfitData.category) {
      queryConditions.category = outfitData.category;
    }
    
    // 如果有季节，添加到查询条件
    if (outfitData.season) {
      queryConditions.season = outfitData.season;
    }
    
    // 查询相似搭配
    db.collection('outfits')
      .where(queryConditions)
      .limit(limit)
      .get({
        success: res => {
          if (res.data && res.data.length > 0) {
            console.log('获取相似搭配成功，数量:', res.data.length);
            resolve(res.data);
          } else {
            console.log('没有找到相似搭配');
            resolve([]);
          }
        },
        fail: err => {
          console.error('获取相似搭配失败:', err);
          resolve([]); // 即使失败也返回空数组，不影响主流程
        }
      });
  });
}

// 导出模块
module.exports = {
  loadFromCache,
  updateCache,
  clearCache,
  shouldUseCache,
  getOutfitDetail,
  getSimilarOutfits
}; 