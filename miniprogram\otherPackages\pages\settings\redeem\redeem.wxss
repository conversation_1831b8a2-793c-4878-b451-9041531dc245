/* 兑换码页面样式 */
.container {
  padding: 40rpx 30rpx;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef, #f1f8ff); /* 渐变背景 */
  min-height: 100vh;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

/* 背景装饰元素 */
.container::before {
  content: '';
  position: absolute;
  top: -100rpx;
  right: -100rpx;
  width: 400rpx;
  height: 400rpx;
  background: radial-gradient(circle, rgba(123, 97, 255, 0.1) 0%, rgba(123, 97, 255, 0) 70%);
  z-index: 0;
  border-radius: 50%;
}

.container::after {
  content: '';
  position: absolute;
  bottom: -100rpx;
  left: -100rpx;
  width: 500rpx;
  height: 500rpx;
  background: radial-gradient(circle, rgba(255, 107, 129, 0.08) 0%, rgba(255, 107, 129, 0) 70%);
  z-index: 0;
  border-radius: 50%;
}

/* 颜色波点装饰 */
.container::before, .container::after {
  animation: pulse 15s infinite alternate ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.05); opacity: 0.7; }
  100% { transform: scale(1); opacity: 0.5; }
}

/* 头部样式 */
.header {
  text-align: center;
  padding: 40rpx 0;
  margin-bottom: 50rpx;
  position: relative;
  z-index: 1;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.95));
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
  padding: 40rpx 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  animation: fadeIn 0.8s ease;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg,
    rgba(123, 97, 255, 0.05) 0%,
    rgba(255, 107, 129, 0.05) 25%,
    rgba(255, 193, 107, 0.05) 50%,
    rgba(107, 213, 255, 0.05) 75%,
    rgba(123, 97, 255, 0.05) 100%);
  border-radius: 20rpx;
  z-index: -1;
}

.header::after {
  content: '';
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 100rpx;
  height: 6rpx;
  background: linear-gradient(90deg,
    rgba(123, 97, 255, 0.2),
    rgba(255, 107, 129, 0.8),
    rgba(123, 97, 255, 0.2));
  border-radius: 3rpx;
  box-shadow: 0 2rpx 10rpx rgba(255, 107, 129, 0.3);
}

/* 标签切换样式 */
.tab-container {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  padding: 6rpx;
}

.tab-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg,
    rgba(123, 97, 255, 0.03) 0%,
    rgba(255, 107, 129, 0.03) 50%,
    rgba(107, 213, 255, 0.03) 100%);
  z-index: -1;
  border-radius: 20rpx;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 28rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
  letter-spacing: 1rpx;
  border-radius: 16rpx;
  margin: 0 4rpx;
  font-weight: 500;
}

.tab:first-child {
  margin-left: 0;
}

.tab:last-child {
  margin-right: 0;
}

.tab.active {
  color: white;
  font-weight: bold;
  background: linear-gradient(135deg, #7b61ff, #ff6b81);
  box-shadow: 0 6rpx 15rpx rgba(255, 107, 129, 0.3);
  transform: translateY(-2rpx);
}

.tab:not(.active):hover {
  background-color: rgba(255, 255, 255, 0.5);
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 8rpx;
  left: 30%;
  width: 40%;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2rpx;
  animation: fadeInWidth 0.3s ease forwards;
}

@keyframes fadeInWidth {
  from { width: 0; left: 50%; opacity: 0; }
  to { width: 40%; left: 30%; opacity: 1; }
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
  position: relative;
  display: inline-block;
  background: linear-gradient(to right, #7b61ff, #ff6b81, #ffc16b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.title::before, .title::after {
  content: '\2605'; /* 星星符号 */
  font-size: 28rpx;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(to right, #7b61ff, #ff6b81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: twinkle 2s infinite alternate ease-in-out;
}

@keyframes twinkle {
  0% { opacity: 0.6; transform: translateY(-50%) scale(0.9); }
  100% { opacity: 1; transform: translateY(-50%) scale(1.1); }
}

.title::before {
  left: -50rpx;
  animation-delay: 0s;
}

.title::after {
  right: -50rpx;
  animation-delay: 0.5s;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  letter-spacing: 1rpx;
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.7);
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  display: inline-block;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-top: 10rpx;
}

/* 卡片共用样式 */
.info-card, .redeem-section, .tips-card, .history-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

/* 卡片悬浮效果 */
.info-card:hover, .redeem-section:hover, .tips-card:hover, .history-section:hover {
  transform: translateY(-5rpx);
  box-shadow: 0 20rpx 50rpx rgba(0, 0, 0, 0.12);
}

/* 为每个卡片设置不同的装饰元素和边框颜色 */
.info-card {
  border-top: 6rpx solid #7b61ff;
}

.redeem-section {
  border-top: 6rpx solid #ff6b81;
}

.tips-card {
  border-top: 6rpx solid #ffc16b;
}

.history-section {
  border-top: 6rpx solid #6bd5ff;
}

/* 卡片内装饰元素 */
.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150rpx;
  height: 150rpx;
  background: radial-gradient(circle, rgba(123, 97, 255, 0.08) 0%, rgba(123, 97, 255, 0) 70%);
  border-radius: 0 0 0 150rpx;
  z-index: -1;
}

.redeem-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150rpx;
  height: 150rpx;
  background: radial-gradient(circle, rgba(255, 107, 129, 0.08) 0%, rgba(255, 107, 129, 0) 70%);
  border-radius: 0 0 0 150rpx;
  z-index: -1;
}

.tips-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150rpx;
  height: 150rpx;
  background: radial-gradient(circle, rgba(255, 193, 107, 0.08) 0%, rgba(255, 193, 107, 0) 70%);
  border-radius: 0 0 0 150rpx;
  z-index: -1;
}

.history-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150rpx;
  height: 150rpx;
  background: radial-gradient(circle, rgba(107, 213, 255, 0.08) 0%, rgba(107, 213, 255, 0) 70%);
  border-radius: 0 0 0 150rpx;
  z-index: -1;
}

/* 卡片标题样式 */
.info-title, .tips-title, .section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  padding-bottom: 20rpx;
  position: relative;
  letter-spacing: 1.5rpx;
  display: flex;
  align-items: center;
}

/* 为每个卡片标题设置不同的下划线颜色 */
.info-title::before {
  content: '•'; /* 圆点符号 */
  color: #7b61ff;
  font-size: 40rpx;
  margin-right: 12rpx;
  line-height: 1;
}

.info-title::after {
  content: '';
  position: absolute;
  bottom: -1rpx;
  left: 0;
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to right, #7b61ff, #9b8aff);
  border-radius: 2rpx;
}

.tips-title::before {
  content: '•'; /* 圆点符号 */
  color: #ffc16b;
  font-size: 40rpx;
  margin-right: 12rpx;
  line-height: 1;
}

.tips-title::after {
  content: '';
  position: absolute;
  bottom: -1rpx;
  left: 0;
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to right, #ffc16b, #ffd591);
  border-radius: 2rpx;
}

.section-title::before {
  content: '•'; /* 圆点符号 */
  color: #6bd5ff;
  font-size: 40rpx;
  margin-right: 12rpx;
  line-height: 1;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -1rpx;
  left: 0;
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to right, #6bd5ff, #a1e5ff);
  border-radius: 2rpx;
}

/* 卡片内容样式 */
.info-content {
  padding: 16rpx 10rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  align-items: center;
  padding: 10rpx 16rpx;
  border-radius: 12rpx;
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.info-item:hover {
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
  font-size: 28rpx;
  letter-spacing: 1rpx;
  font-weight: 500;
}

.info-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
  background: linear-gradient(to right, rgba(123, 97, 255, 0.1), rgba(123, 97, 255, 0.05));
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  border: 1rpx solid rgba(123, 97, 255, 0.1);
}

/* 兑换码输入区域 */
.input-group {
  display: flex;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5rpx);
}

.input-group:focus-within {
  border-color: rgba(255, 107, 129, 0.3);
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 129, 0.15);
  transform: translateY(-2rpx);
}

.redeem-input {
  flex: 1;
  height: 100rpx;
  padding: 0 30rpx;
  font-size: 30rpx;
  letter-spacing: 1rpx;
  color: #333;
  background: transparent;
}

.paste-btn {
  width: 160rpx;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  background: linear-gradient(135deg, rgba(123, 97, 255, 0.1), rgba(123, 97, 255, 0.2));
  color: #7b61ff;
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border-left: 1rpx solid rgba(255, 255, 255, 0.8);
}

.paste-btn:active {
  background: linear-gradient(135deg, rgba(123, 97, 255, 0.15), rgba(123, 97, 255, 0.25));
  color: #6952e0;
}

.paste-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.paste-btn:active::after {
  transform: translateX(100%);
}

.redeem-btn {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background: linear-gradient(135deg, #7b61ff, #ff6b81);
  color: white;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 3rpx;
  box-shadow: 0 10rpx 25rpx rgba(255, 107, 129, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.redeem-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 5rpx 15rpx rgba(255, 107, 129, 0.2);
}

.redeem-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.redeem-btn:active::after {
  transform: translateX(100%);
}

.redeem-btn[disabled] {
  background: linear-gradient(135deg, #cccccc, #bbbbbb);
  color: #ffffff;
  box-shadow: none;
  opacity: 0.7;
}

/* 会员兑换按钮 */
.membership-btn {
  background: linear-gradient(135deg, #ffc16b, #ff9a5a);
  box-shadow: 0 10rpx 25rpx rgba(255, 193, 107, 0.25);
}

.membership-btn:active {
  box-shadow: 0 5rpx 15rpx rgba(255, 193, 107, 0.2);
}

.membership-btn[disabled] {
  background: linear-gradient(135deg, #cccccc, #bbbbbb);
  color: #ffffff;
  box-shadow: none;
  opacity: 0.7;
}

/* 会员文字样式 */
.vip-text {
  background: linear-gradient(to right, #ffc16b, #ff9a5a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 107, 0.2);
  border: 1rpx solid rgba(255, 193, 107, 0.3);
  background-color: rgba(255, 255, 255, 0.9);
}

.vip-text::before, .vip-text::after {
  content: '★'; /* 星星符号 */
  position: absolute;
  font-size: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(to right, #ffc16b, #ff9a5a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.vip-text::before {
  left: 6rpx;
}

.vip-text::after {
  right: 6rpx;
}

/* 提示卡片样式 */
.tips-content {
  padding: 16rpx 10rpx;
  position: relative;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 24rpx;
  line-height: 1.6;
  padding: 16rpx 20rpx 16rpx 40rpx;
  position: relative;
  letter-spacing: 0.5rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
}

.tip-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(5rpx);
}

.tip-item::before {
  content: '•';
  position: absolute;
  left: 16rpx;
  top: 16rpx;
  font-size: 30rpx;
  line-height: 1.6;
}

.tip-item:nth-child(1)::before {
  color: #7b61ff;
}

.tip-item:nth-child(2)::before {
  color: #ff6b81;
}

.tip-item:nth-child(3)::before {
  color: #ffc16b;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 会员奖励文字 */
.membership-reward {
  background: linear-gradient(to right, #ffc16b, #ff9a5a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  border: 1rpx solid rgba(255, 193, 107, 0.3);
  background-color: rgba(255, 255, 255, 0.9);
  display: inline-block;
  box-shadow: 0 4rpx 10rpx rgba(255, 193, 107, 0.1);
}

/* 兑换历史 */
.history-list {
  max-height: 600rpx;
  overflow-y: auto;
  padding: 10rpx 0;
}

.history-item {
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.6);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.history-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(to bottom, #6bd5ff, #a1e5ff);
  border-radius: 3rpx;
}

.history-item:nth-child(2n)::before {
  background: linear-gradient(to bottom, #ff6b81, #ff9eb5);
}

.history-item:nth-child(3n)::before {
  background: linear-gradient(to bottom, #7b61ff, #9b8aff);
}

.history-item:nth-child(4n)::before {
  background: linear-gradient(to bottom, #ffc16b, #ffd591);
}

.history-item:hover {
  transform: translateY(-3rpx) translateX(3rpx);
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.06);
  background: rgba(255, 255, 255, 0.8);
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-main {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  align-items: center;
}

.history-name {
  font-size: 30rpx;
  color: #333;
  flex: 1;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  padding-left: 16rpx;
}

.history-time {
  font-size: 24rpx;
  color: #666;
  background: rgba(255, 255, 255, 0.8);
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  letter-spacing: 0.5rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
}

.history-detail {
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 0 0 16rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.reward-text {
  display: inline-block;
  margin-right: 10rpx;
  font-weight: 500;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  color: #7b61ff;
}

.reward-text:nth-child(2n) {
  color: #ff6b81;
}

.reward-text:nth-child(3n) {
  color: #ffc16b;
}

.reward-text:nth-child(4n) {
  color: #6bd5ff;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
}

.modal-content {
  position: relative;
  width: 85%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  overflow: hidden;
  z-index: 1001;
  box-shadow: 0 30rpx 80rpx rgba(0, 0, 0, 0.25);
  animation: slideUp 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  transform: translateY(0);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

@keyframes slideUp {
  from { transform: translateY(80rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-header {
  text-align: center;
  padding: 40rpx 0;
  font-size: 38rpx;
  font-weight: bold;
  color: white;
  position: relative;
  overflow: hidden;
}

.modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 60%);
  z-index: 1;
}

.modal-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0) 100%);
  transform: translateX(-100%);
  animation: shine 2.5s infinite;
  z-index: 2;
}

@keyframes shine {
  0% { transform: translateX(-100%); }
  20%, 100% { transform: translateX(100%); }
}

.modal-header.success {
  background: linear-gradient(135deg, #7b61ff, #ff6b81);
}

.modal-header.fail {
  background: linear-gradient(135deg, #ff6b81, #ff9a5a);
}

.modal-body {
  padding: 50rpx 40rpx;
  position: relative;
}

.modal-body::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(123, 97, 255, 0.03) 0%, rgba(123, 97, 255, 0) 70%);
  border-radius: 0 0 0 200rpx;
  z-index: -1;
}

.modal-message {
  font-size: 32rpx;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
  letter-spacing: 1rpx;
  font-weight: 500;
}

.reward-info {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16rpx;
  padding: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.reward-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(to right, #7b61ff, #ff6b81, #ffc16b, #6bd5ff);
  animation: gradientShift 6s infinite linear;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.reward-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  align-items: center;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border-left: 4rpx solid transparent;
}

.reward-item:nth-child(1) {
  border-left-color: #7b61ff;
  box-shadow: 0 4rpx 12rpx rgba(123, 97, 255, 0.1);
}

.reward-item:nth-child(2) {
  border-left-color: #ff6b81;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 129, 0.1);
}

.reward-item:nth-child(3) {
  border-left-color: #ffc16b;
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 107, 0.1);
}

.reward-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateX(5rpx);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.reward-item:last-child {
  margin-bottom: 0;
}

.reward-label {
  font-size: 28rpx;
  color: #666;
  letter-spacing: 1rpx;
  font-weight: 500;
}

.reward-value {
  font-size: 28rpx;
  font-weight: bold;
  letter-spacing: 1rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.reward-value::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.reward-item:hover .reward-value::after {
  transform: translateX(100%);
}

/* 为不同类型的奖励设置不同的颜色 */
.reward-item:nth-child(1) .reward-value {
  color: #7b61ff;
}

.reward-item:nth-child(2) .reward-value {
  color: #ff6b81;
}

.reward-item:nth-child(3) .reward-value {
  color: #ffc16b;
}

.modal-footer {
  padding: 20rpx 30rpx 50rpx;
  text-align: center;
}

.modal-btn {
  background: linear-gradient(135deg, #7b61ff, #ff6b81);
  background-size: 200% 200%;
  color: white;
  width: 260rpx;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  box-shadow: 0 10rpx 25rpx rgba(255, 107, 129, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: gradientMove 3s infinite alternate;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  padding: 20rpx;
  margin: 10rpx;
}

@keyframes gradientMove {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}

.modal-btn:active {
  transform: translateY(3rpx) scale(0.98);
  box-shadow: 0 5rpx 15rpx rgba(255, 107, 129, 0.2);
}

.modal-btn::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  z-index: -1;
  background: linear-gradient(45deg, #7b61ff, #ff6b81, #ffc16b, #6bd5ff);
  background-size: 400% 400%;
  border-radius: 45rpx;
  animation: borderGlow 3s ease infinite;
  opacity: 0.7;
}

@keyframes borderGlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.modal-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.modal-btn:active::after {
  transform: translateX(100%);
}