// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 检查必要参数
  if (!event.clothingIds || !Array.isArray(event.clothingIds) || event.clothingIds.length === 0) {
    return {
      success: false,
      message: '缺少衣物ID列表'
    }
  }
  
  try {
    // 构建更新数据对象
    const updateData = {}
    
    // 只更新提供的字段
    if (event.category !== undefined) updateData.category = event.category
    if (event.type_detail !== undefined) updateData.type_detail = event.type_detail
    if (event.color !== undefined) updateData.color = event.color
    if (event.style !== undefined) updateData.style = event.style
    if (event.season !== undefined) updateData.season = event.season
    if (event.wardrobeId !== undefined) updateData.wardrobeId = event.wardrobeId
    if (event.wardrobeName !== undefined) updateData.wardrobeName = event.wardrobeName
    if (event.brand !== undefined) updateData.brand = event.brand
    if (event.material !== undefined) updateData.material = event.material
    if (event.size !== undefined) updateData.size = event.size
    if (event.storageLocation !== undefined) updateData.storageLocation = event.storageLocation
    if (event.purchaseChannel !== undefined) updateData.purchaseChannel = event.purchaseChannel
    if (event.hiden !== undefined) updateData.hiden = event.hiden
    if (event.wantToDiscard !== undefined) updateData.wantToDiscard = event.wantToDiscard
    
    // 添加更新时间
    updateData.updateTime = db.serverDate()
    
    // 如果没有任何字段需要更新，则返回错误
    if (Object.keys(updateData).length === 1) { // 只有updateTime
      return {
        success: false,
        message: '没有提供任何需要更新的字段'
      }
    }
    
    // 创建批量更新任务
    const updatePromises = event.clothingIds.map(id => {
      return db.collection('clothes').doc(id).update({
        data: updateData
      })
    })
    
    // 执行批量更新
    const updateResults = await Promise.all(updatePromises)
    
    // 计算更新成功的数量
    let updatedCount = 0
    updateResults.forEach(result => {
      updatedCount += result.stats.updated
    })
    
    return {
      success: true,
      updated: updatedCount,
      message: `成功更新了${updatedCount}件衣物`
    }
  } catch (error) {
    console.error('批量更新衣物失败:', error)
    return {
      success: false,
      message: '更新失败: ' + error.message
    }
  }
}
