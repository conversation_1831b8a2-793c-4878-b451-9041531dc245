.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  background-color: #f5f8f7;
  box-sizing: border-box;
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  background-color: #2a8c70;
  z-index: 1000;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

.nav-content {
  display: flex;
  align-items: center;
  height: 120rpx;
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.back-icon {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: bold;
}

.nav-title {
  flex: 1;
  display: flex;
  justify-content: center;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 50px;
}

/* 导航栏标题样式 */
.nav-title .section-title {
  font-size: 32rpx;
  font-weight: 900;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-title .section-subtitle {
  font-size: 24rpx;
  color: #000000;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  font-weight: 500;
  margin-top: 6rpx;
  letter-spacing: 1rpx;
}

.nav-placeholder {
  width: 60rpx;
}

/* 页面内容区域 */
.page-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  box-sizing: border-box;
}

.report-content {
  width: 100%;
  height: calc(100vh - 150rpx);
  box-sizing: border-box;
}

/* 分析部分样式 */
.analysis-section {
  margin-bottom: 50rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.18);
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  transform: translateZ(0);
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
  width: 100%;
  box-sizing: border-box;
  margin-left: 0;
  margin-right: 0;
}

/* 卡片右上角标签 */
.analysis-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.03);
  clip-path: polygon(0 0, 100% 0, 100% 100%);
}

/* 不同模块的卡片顶部标识 */
.overview-section {
  border-top: 8rpx solid #2a8c70;
  box-shadow: 0 16rpx 40rpx rgba(42, 140, 112, 0.15);
  position: relative;
}

/* 添加总体概览的堆叠效果 */
.overview-section::after {
  content: '';
  position: absolute;
  top: -8rpx;
  left: 0;
  right: 0;
  height: 8rpx;
  background-color: rgba(42, 140, 112, 0.7);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  z-index: -1;
  transform: translateY(-100%);
  box-shadow: 0 -2rpx 5rpx rgba(42, 140, 112, 0.2);
}

.module-1 {
  border-top: 8rpx solid #4285F4; /* 模块1：衣橱基础概览 */
  box-shadow: 0 16rpx 40rpx rgba(66, 133, 244, 0.15);
  position: relative;
}

/* 添加衣橱基础概览的堆叠效果 */
.module-1::after {
  content: '';
  position: absolute;
  top: -8rpx;
  left: 0;
  right: 0;
  height: 8rpx;
  background-color: rgba(66, 133, 244, 0.7);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  z-index: -1;
  transform: translateY(-100%);
  box-shadow: 0 -2rpx 5rpx rgba(66, 133, 244, 0.2);
}

.module-2 {
  border-top: 8rpx solid #EA4335; /* 模块2：使用效率分析 */
  box-shadow: 0 16rpx 40rpx rgba(234, 67, 53, 0.15);
  position: relative;
}

/* 添加明确的使用效率分析卡片堆叠效果 */
.module-2::after {
  content: '';
  position: absolute;
  top: -8rpx;
  left: 0;
  right: 0;
  height: 8rpx;
  background-color: rgba(234, 67, 53, 0.7);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  z-index: -1;
  transform: translateY(-100%);
  box-shadow: 0 -2rpx 5rpx rgba(234, 67, 53, 0.2);
}

.module-3 {
  border-top: 8rpx solid #FBBC05; /* 模块3：消费行为洞察 */
  box-shadow: 0 16rpx 40rpx rgba(251, 188, 5, 0.15);
}

.module-4 {
  border-top: 8rpx solid #34A853; /* 模块4：搭配优化建议 */
  box-shadow: 0 16rpx 40rpx rgba(52, 168, 83, 0.15);
}

.module-5 {
  border-top: 8rpx solid #8D6E63; /* 模块5：可持续诊断 */
  box-shadow: 0 16rpx 40rpx rgba(141, 110, 99, 0.15);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f4f3;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60rpx;
  height: 4rpx;
  background-color: #2a8c70;
  border-radius: 2rpx;
}

.section-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  background-color: #f8fbfa;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.analysis-item {
  margin-bottom: 24rpx;
  padding: 16rpx;
  background-color: #fafcfb;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
}

.analysis-item:hover, .analysis-item:active {
  background-color: #f0f7f4;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);
}

.item-label {
  font-size: 28rpx;
  color: #5c7d8d;
  margin-bottom: 10rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  padding-left: 10rpx;
  border-left: 4rpx solid #e7f2ee;
}

/* 警告样式 */
.item-label.warning, .item-value.warning {
  color: #e75e76;
  font-weight: 600;
}

.analysis-item .item-label.warning + .item-value.warning {
  background-color: #fff5f6;
  padding: 12rpx 16rpx;
  border-left: 4rpx solid #e75e76;
}

/* 子区域样式 */
.analysis-subsection {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  background-color: #f8fbfa;
  border-radius: 16rpx;
  padding: 20rpx 10rpx;
  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.subsection-title {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  font-weight: 600;
  color: #3a6b99;
  margin-bottom: 16rpx;
  text-align: center;
  padding-bottom: 10rpx;
  border-bottom: 1rpx dashed rgba(128, 128, 128, 0.2);
}

/* 数据表格样式 */
.data-table {
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  font-size: 20rpx;
  border: 1rpx solid #e7f2ee;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.06);
  margin-bottom: 16rpx;
  table-layout: fixed;
  box-sizing: border-box;
  margin-left: 0;
  margin-right: 0;
}

.table-header {
  background-color: #e7f2ee;
  color: #2a8c70;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
  font-size: 20rpx;
  min-height: 48rpx;
  line-height: 1;
  display: flex;
}

.table-row {
  border-bottom: 1rpx solid #f0f4f3;
  transition: background-color 0.2s ease;
  min-height: 52rpx;
  line-height: 1;
  display: flex;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover, .table-row:active {
  background-color: #f9f9f9;
}

.th, .td {
  flex: 1;
  padding: 10rpx 2rpx;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  box-sizing: border-box;
  font-size: 20rpx;
  line-height: 1.1;
}

/* 表格列宽调整 - 优化以确保内容完全显示 */
.table-header .th:nth-child(1), 
.table-row .td:nth-child(1) {
  flex: 0.8;
  padding-left: 6rpx;
}

.table-header .th:nth-child(2), 
.table-row .td:nth-child(2) {
  flex: 0.7;
}

.table-header .th:nth-child(3), 
.table-row .td:nth-child(3) {
  flex: 0.6;
}

/* 确保最后一列(单次成本)有足够空间 */
.table-header .th:last-child, 
.table-row .td:last-child {
  flex: 1.2;
  padding-right: 6rpx;
  text-align: right;
}

/* 为高亮列提供更多空间 */
.table-header .th.highlight-column, 
.table-row .td.highlight-column {
  flex: 1.2;
  font-size: 20rpx;
  overflow: visible;
  padding-right: 6rpx;
}

/* 小屏幕适配 */
@media screen and (max-width: 375px) {
  .th, .td {
    font-size: 18rpx;
    padding: 10rpx 2rpx;
  }
  
  .table-header .th.highlight-column, 
  .table-row .td.highlight-column {
    font-size: 18rpx;
  }
}

/* 单次成本高亮列样式 */
.highlight-column {
  background-color: #f8eee3;
  color: #e26432;
  font-weight: bold;
}

.th.highlight-column {
  background-color: #ffd2b3;
  color: #c24e1b;
}

/* 成本效益表格自定义样式 */
.cost-table .table-row:nth-child(1) .highlight-column {
  background-color: #e7f2ee;
  color: #2a8c70;
}

.cost-table .table-row:nth-child(2) .highlight-column {
  background-color: #ebf2e7;
  color: #4f9a6c;
}

/* 浪费表格自定义样式 */
.waste-table .table-row:nth-child(1) .highlight-column {
  background-color: #fce5e5;
  color: #d84545;
}

.waste-table .table-row:nth-child(2) .highlight-column {
  background-color: #fceaea;
  color: #d06868;
}

/* 成本解释说明样式 */
.cost-explanation, .waste-explanation, .value-explanation, .elimination-summary {
  font-size: 24rpx;
  color: #777;
  text-align: center;
  margin-top: 16rpx;
  padding: 12rpx 16rpx;
  background-color: #f8fbfa;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  position: relative;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.cost-explanation {
  border-left: 4rpx solid #89c3b0;
}

.waste-explanation {
  border-left: 4rpx solid #e78a8a;
}

.value-explanation {
  border-left: 4rpx solid #9e9e9e;
}

/* 激活卡片样式 */
.activation-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 16rpx;
}

.activation-card {
  background-color: #f8fbfa;
  border-radius: 16rpx;
  padding: 20rpx;
  border-left: 6rpx solid #89c3b0;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.activation-card:hover, .activation-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 28rpx rgba(0, 0, 0, 0.12);
}

.activation-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: #89c3b0;
  opacity: 0.1;
  border-bottom-left-radius: 50%;
}

.activation-item {
  font-size: 30rpx;
  font-weight: 600;
  color: #2a8c70;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.activation-item::before {
  content: '👕';
  margin-right: 8rpx;
  font-size: 32rpx;
}

.activation-formula {
  font-size: 26rpx;
  color: #333;
  padding: 10rpx 16rpx;
  background-color: #e7f2ee;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
  display: inline-block;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}

.activation-desc {
  font-size: 26rpx;
  color: #5c7d8d;
  line-height: 1.5;
  padding: 8rpx 0;
}

/* 淘汰建议清单样式 */
.elimination-table {
  border: 1rpx solid #fcd6d6;
}

.elimination-table .table-header {
  background-color: #fce5e5;
  color: #e26464;
}

.elimination-reason {
  flex: 1.1 !important;
  text-align: left;
  padding-left: 6rpx;
  max-width: 35%;
}

.elimination-summary {
  font-size: 24rpx;
  text-align: center;
  margin-top: 16rpx;
  padding: 12rpx;
  background-color: #fef8f8;
  border-radius: 8rpx;
  color: #c25151;
  border-left: 4rpx solid #e78a8a;
}

.title-icon {
  margin-right: 8rpx;
  display: inline-block;
  vertical-align: middle;
}

/* 价值系数表格样式 */
.value-table {
  border: 1rpx solid #e0e0e0;
}

.value-column {
  font-weight: bold;
  flex: 0.9 !important;
}

.high-value .value-column {
  color: #4CAF50;
}

.high-value .table-header .value-column {
  background-color: #e8f5e9;
}

.low-value .value-column {
  color: #FF9800;
}

.low-value .table-header .value-column {
  background-color: #fff3e0;
}

/* 模块图标样式 */
.module-icon-overview {
  background-color: #e8f5e9;
  color: #2a8c70;
}

.module-icon-1 {
  background-color: #e8f4fc;
  color: #4285F4;
}

.module-icon-2 {
  background-color: #fce9e8;
  color: #EA4335;
}

.module-icon-3 {
  background-color: #fff8e6;
  color: #FBBC05;
}

.module-icon-4 {
  background-color: #e8f5e9;
  color: #34A853;
}

.module-icon-5 {
  background-color: #f0ebe7;
  color: #8D6E63;
}

/* 卡片模块通用样式 */
.card-module {
  position: relative;
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-module::after {
  content: '';
  position: absolute;
  bottom: -4rpx;
  left: 0;
  right: 0;
  height: 10rpx;
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
  z-index: -1;
  background: inherit;
  opacity: 0.2;
  filter: blur(4rpx);
}

/* 卡片标题下方分隔线 */
.card-module .section-header {
  position: relative;
  margin-bottom: 25rpx;
}

.card-module .section-header::after {
  height: 5rpx;
  width: 80rpx;
  border-radius: 3rpx;
}

.overview-section .section-header::after { background-color: #2a8c70; }
.module-1 .section-header::after { background-color: #4285F4; }
.module-2 .section-header::after { background-color: #EA4335; }
.module-3 .section-header::after { background-color: #FBBC05; }
.module-4 .section-header::after { background-color: #34A853; }
.module-5 .section-header::after { background-color: #8D6E63; }

/* 卡片内标题样式 */
.section-header .section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2a8c70;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
} 