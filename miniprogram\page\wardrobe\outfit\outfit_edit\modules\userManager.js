// 用户管理模块
// 负责处理用户相关的操作，如获取用户openid

// 获取用户openid
function getUserOpenId() {
  return new Promise((resolve, reject) => {
    // 尝试从全局数据获取openid
    const app = getApp();
    if (app.globalData.openid) {
      console.log('从全局数据获取到openid:', app.globalData.openid);
      resolve(app.globalData.openid);
      return;
    }
    
    // 尝试从本地缓存获取openid
    const cachedOpenid = wx.getStorageSync('openid');
    if (cachedOpenid) {
      console.log('从本地缓存获取到openid:', cachedOpenid);
      
      // 更新全局数据
      if (app.globalData) {
        app.globalData.openid = cachedOpenid;
      }
      
      resolve(cachedOpenid);
      return;
    }
    
    // 如果没有缓存，则调用云函数获取
    console.log('本地没有openid，调用云函数获取');
    wx.cloud.callFunction({
      name: 'login',
      data: {},
      success: res => {
        const openid = res.result.openid;
        console.log('云函数获取到的openid:', openid);
        
        // 保存到本地缓存
        wx.setStorageSync('openid', openid);
        
        // 更新全局数据
        if (app.globalData) {
          app.globalData.openid = openid;
        }
        
        resolve(openid);
      },
      fail: err => {
        console.error('获取用户openid失败:', err);
        reject(err);
      }
    });
  });
}

// 检查用户权限（是否为VIP等）
function checkUserPermission() {
  return new Promise((resolve, reject) => {
    getUserOpenId()
      .then(openid => {
        const db = wx.cloud.database();
        return db.collection('users').where({
          _openid: openid
        }).get();
      })
      .then(res => {
        if (res.data && res.data.length > 0) {
          const user = res.data[0];
          resolve({
            isVIP: user.isVIP || false,
            permissions: user.permissions || {}
          });
        } else {
          resolve({
            isVIP: false,
            permissions: {}
          });
        }
      })
      .catch(err => {
        console.error('检查用户权限失败:', err);
        reject(err);
      });
  });
}

module.exports = {
  getUserOpenId,
  checkUserPermission
}; 