<!-- 我的衣柜页面 -->
<view class="wardrobe-container">
  <!-- 衣柜主体 -->
  <view class="wardrobe-body">
    <!-- 左侧衣柜部分(包含推拉门和抽屉) -->
    <view class="left-section">
      <!-- 上部衣柜(推拉门) -->
      <view class="upper-cabinet">
        <!-- 衣柜内部内容 - 只有当门打开时才能看到 -->
        <view class="cabinet-interior">
          <!-- 衣柜内部 - 分为左右两区 -->
          <view class="rod-area">
            <view class="left-half">
              <block wx:if="{{leftDoorOpen || rightDoorOpen}}">
                <view class="section-title">上衣区</view>
                <scroll-view scroll-y="true" class="clothes-list">
                  <block wx:for="{{topsData}}" wx:key="id">
                    <view class="clothes-item" data-item="{{item}}" data-category="tops" bindtap="handleCardClick">
                      <image class="clothes-image" src="{{item.img}}" mode="aspectFill"></image>
                      <view class="clothes-info">
                        <text class="clothes-name">{{item.name}}</text>
                        <text class="clothes-color">{{item.color}}</text>
                      </view>
                    </view>
                  </block>
                </scroll-view>
              </block>
            </view>
            <view class="right-half">
              <block wx:if="{{leftDoorOpen || rightDoorOpen}}">
                <view class="section-title">外套区</view>
                <scroll-view scroll-y="true" class="clothes-list">
                  <block wx:for="{{outerwearData}}" wx:key="id">
                    <view class="clothes-item" data-item="{{item}}" data-category="outerwear" bindtap="handleCardClick">
                      <image class="clothes-image" src="{{item.img}}" mode="aspectFill"></image>
                      <view class="clothes-info">
                        <text class="clothes-name">{{item.name}}</text>
                        <text class="clothes-color">{{item.color}}</text>
                      </view>
                    </view>
                  </block>
                </scroll-view>
              </block>
            </view>
          </view>
        </view>
        
        <!-- 左侧推拉门 -->
        <view class="left-door {{leftDoorOpen ? 'door-open' : ''}}" bindtap="toggleLeftDoor">
          <view class="door-handle right"></view>
          <view class="door-content">
            <text class="door-hint">{{leftDoorOpen ? '' : '点击打开左侧门'}}</text>
          </view>
        </view>
        
        <!-- 右侧推拉门 -->
        <view class="right-door {{rightDoorOpen ? 'door-open' : ''}}" bindtap="toggleRightDoor">
          <view class="door-handle left"></view>
          <view class="door-content">
            <text class="door-hint">{{rightDoorOpen ? '' : '点击打开右侧门'}}</text>
          </view>
        </view>
      </view>
      
      <!-- 抽屉组 -->
      <view class="drawers-section">
        <!-- 抽屉1 -->
        <view class="drawer {{openDrawer === 'drawer1' ? 'active' : ''}}" bindtap="toggleDrawer" data-drawer="drawer1">
          <view class="drawer-handle"></view>
          
          <!-- 抽屉内容 -->
          <view class="drawer-content {{openDrawer === 'drawer1' ? 'open' : ''}}">
            <scroll-view scroll-x="true" class="drawer-items">
              <block wx:for="{{pantsData}}" wx:key="id">
                <view class="drawer-item" data-item="{{item}}" data-category="pants" catchlongtap="handleCardClick">
                  <image class="drawer-item-image" src="{{item.img}}" mode="aspectFill"></image>
                  <text class="drawer-item-name">{{item.name}}</text>
                  <text class="drawer-item-color">{{item.color}}</text>
                </view>
              </block>
            </scroll-view>
          </view>
        </view>
        
        <!-- 抽屉2 -->
        <view class="drawer {{openDrawer === 'drawer2' ? 'active' : ''}}" bindtap="toggleDrawer" data-drawer="drawer2">
          <view class="drawer-handle"></view>
          
          <!-- 抽屉内容 -->
          <view class="drawer-content {{openDrawer === 'drawer2' ? 'open' : ''}}">
            <scroll-view scroll-x="true" class="drawer-items">
              <block wx:for="{{shoesData}}" wx:key="id">
                <view class="drawer-item" data-item="{{item}}" data-category="shoes" catchlongtap="handleCardClick">
                  <image class="drawer-item-image" src="{{item.img}}" mode="aspectFill"></image>
                  <text class="drawer-item-name">{{item.name}}</text>
                  <text class="drawer-item-color">{{item.color}}</text>
                </view>
              </block>
            </scroll-view>
          </view>
        </view>
        
        <!-- 抽屉3 -->
        <view class="drawer {{openDrawer === 'drawer3' ? 'active' : ''}}" bindtap="toggleDrawer" data-drawer="drawer3">
          <view class="drawer-handle"></view>
          
          <!-- 抽屉内容 -->
          <view class="drawer-content {{openDrawer === 'drawer3' ? 'open' : ''}}">
            <scroll-view scroll-x="true" class="drawer-items">
              <block wx:for="{{accessoriesData}}" wx:key="id">
                <view class="drawer-item" data-item="{{item}}" data-category="accessories" catchlongtap="handleCardClick">
                  <image class="drawer-item-image" src="{{item.img}}" mode="aspectFill"></image>
                  <text class="drawer-item-name">{{item.name}}</text>
                  <text class="drawer-item-color">{{item.color}}</text>
                </view>
              </block>
            </scroll-view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 右侧衣柜部分(玻璃推拉门) -->
    <view class="right-section">
      <!-- 玻璃推拉门 -->
      <view class="glass-door {{doorOpen ? 'door-open' : ''}}" bindtap="toggleDoor">
        <view class="upper-glass">
          <view class="glass-texture"></view>
        </view>
        <view class="middle-separator"></view>
        <view class="lower-glass">
          <view class="glass-texture"></view>
        </view>
        <view class="door-handle left"></view>
      </view>
      
      <!-- 门后内容 -->
      <view class="door-content-area">
        <block wx:if="{{doorOpen}}">
          <view class="section-title">衣物收纳区</view>
          <scroll-view scroll-y="true" class="special-clothes-list">
            <block wx:for="{{specialItems}}" wx:key="id">
              <view class="special-clothes-item" data-item="{{item}}" data-category="special" bindtap="handleCardClick">
                <image class="special-clothes-image" src="{{item.img}}" mode="aspectFill"></image>
                <text class="special-clothes-name">{{item.name}}</text>
                <text class="special-clothes-color">{{item.color}}</text>
              </view>
            </block>
          </scroll-view>
        </block>
      </view>
      
      <!-- 开关按钮 -->
      <view class="door-toggle">
        <view class="toggle-btn" catchtap="toggleDoor">
          <text class="{{doorOpen ? 'icon-right' : 'icon-left'}}"></text>
        </view>
      </view>
    </view>
    
    <!-- 衣柜底座 -->
    <view class="wardrobe-base"></view>
  </view>
  
  <!-- 底部功能按钮 -->
  <view class="action-buttons">
    <button class="add-btn" bindtap="addNewItem">添加新物品</button>
    <button class="create-btn" bindtap="createOutfit">创建搭配</button>
  </view>
  
  <!-- 操作提示 -->
  <view class="tips">
    <text>点击左右两侧衣柜门或右侧玻璃推拉门可以打开衣柜</text>
    <text>点击抽屉可以查看内部物品</text>
  </view>
</view>

<!-- 衣物详情弹窗 -->
<view class="card-detail-modal" wx:if="{{activeCard}}" bindtap="closeCardDetail">
  <view class="modal-content" catchtap="preventBubble">
    <view class="modal-header">
      <text class="modal-title">{{activeCard.name}}</text>
      <view class="close-btn" bindtap="closeCardDetail">×</view>
    </view>
    <view class="modal-body">
      <view class="item-image-container">
        <image class="item-image" src="{{activeCard.img}}" mode="aspectFit"></image>
      </view>
      <view class="item-details">
        <view class="detail-row">
          <text class="detail-label">类别</text>
          <text class="detail-value">{{categoryNames[activeCategory]}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">颜色</text>
          <text class="detail-value">{{activeCard.color}}</text>
        </view>
        <view class="detail-row" wx:if="{{activeCard.season}}">
          <text class="detail-label">适用季节</text>
          <text class="detail-value">{{activeCard.season}}</text>
        </view>
      </view>
      <view class="modal-actions">
        <button class="action-btn add-outfit">添加到搭配</button>
        <button class="action-btn edit">编辑</button>
      </view>
    </view>
  </view>
</view> 