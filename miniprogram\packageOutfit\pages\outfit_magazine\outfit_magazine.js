// page/wardrobe/outfit/outfit_magazine/outfit_magazine.js

// 引入图片管理模块
const imageManager = require('../modules/imageManager');
// 引入搭配管理模块
const outfitManager = require('../modules/outfitManager');
// 引入本地图片缓存模块
const localImageCache = require('../modules/localImageCache');

// 定义颜色主题
const THEMES = {
  AUTUMN: 'autumn',
  PINK_BLUE: 'pinkBlue',
  BLACK_WHITE: 'blackWhite'
};

// 定义季节
const SEASONS = {
  SPRING: 'spring',
  SUMMER: 'summer',
  AUTUMN: 'autumn',
  WINTER: 'winter'
};

Page({
  data: {
    // 定义颜色常量 - 秋季色彩方案
    colors: {
      cowhideCocoa: '#442D1C',   // 深棕色
      spicedWine: '#74301C',     // 红棕色
      toastedCaramel: '#84592B', // 焦糖色
      oliveHarvest: '#9D9167',   // 橄榄色
      goldenBatter: '#E8D1A7',   // 金黄色
    },
    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',      // 深粉色
      pinkMedium: '#EEA0B2',    // 中粉色
      pinkLight: '#F9C9D6',     // 浅粉色
      blueLight: '#CBE0F9',     // 浅蓝色
      blueMedium: '#97C8E5',    // 中蓝色
      blueDark: '#5EA0D0',      // 深蓝色
    },
    // 黑白色系配色
    blackWhiteColors: {
      black: '#000000',         // 纯黑
      darkGray: '#333333',      // 深灰
      mediumGray: '#666666',    // 中灰
      lightGray: '#CCCCCC',     // 浅灰
      white: '#FFFFFF',         // 纯白
      offWhite: '#F5F5F5',      // 灰白
    },
    
    // 主题风格
    themeStyle: THEMES.AUTUMN,  // 默认秋季风格
    
    // 导航栏样式
    navBackground: '#E8D1A7',   // 默认导航栏背景色
    navFrontColor: '#000000',   // 默认导航栏前景色
    navTitle: 'FASHION WARDROBE',   // 导航栏标题
    
    isLoading: true,            // 加载状态
    userOpenId: '',             // 用户OpenID
    
    // 杂志数据
    currentIssue: 1,            // 当前杂志期号
    currentSeason: SEASONS.AUTUMN, // 当前季节
    currentYear: new Date().getFullYear(), // 当前年份
    
    // 每个类别一个精选搭配
    featuredOutfits: {
      daily: null,
      work: null,
      party: null,
      sport: null,
      seasonal: null
    },
    
    // 推荐搭配列表
    recommendedOutfits: [],
    
    // 编辑推荐搭配
    editorPickOutfits: [],
    
    // 杂志页面状态
    currentPage: 'cover',      // cover, contents, category, features
    currentCategory: '',       // 当前查看的类别
    
    // 动画相关
    pageAnimation: null,       // 页面切换动画
    isPageTurning: false,      // 是否正在翻页
    
    // 用户交互相关
    touchStartX: 0,            // 触摸开始X坐标
    touchStartTime: 0,         // 触摸开始时间
    
    // 页面状态
    showCategoryIndex: false,  // 是否显示类别索引
    
    // 杂志导航
    magazinePages: [
      { id: 'cover', title: '封面', icon: 'cover-icon' },
      { id: 'contents', title: '目录', icon: 'contents-icon' },
      { id: 'daily', title: '日常穿搭', icon: 'daily-icon' },
      { id: 'work', title: '职业穿搭', icon: 'work-icon' },
      { id: 'party', title: '派对穿搭', icon: 'party-icon' },
      { id: 'sport', title: '运动穿搭', icon: 'sport-icon' },
      { id: 'seasonal', title: '季节穿搭', icon: 'seasonal-icon' },
      { id: 'features', title: '精选特辑', icon: 'features-icon' },
    ]
  },
  
  // 页面加载
  onLoad: function(options) {
    // 初始化云环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-3gi97kso9ab01185',
        traceUser: true,
      });
    }
    
    // 清除过期的URL缓存
    imageManager.clearExpiredURLCache();
    
    // 确保本地图片缓存目录存在
    localImageCache.ensureCacheDir();
    
    // 清除过期的本地图片缓存
    localImageCache.clearExpiredCache();
    
    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });
      // 应用主题样式
      this.applyThemeStyle(savedTheme);
    }
    
    // 获取当前季节
    this.determineCurrentSeason();
    
    // 获取用户OpenID
    this.getUserOpenId();
    
    // 初始化页面切换动画
    this.initPageAnimation();
    
    // 如果有传入的参数
    if (options) {
      // 如果指定了页面ID，直接跳转到对应页面
      if (options.page) {
        this.setData({
          currentPage: options.page
        });
      }
      
      // 如果指定了类别，设置当前类别
      if (options.category) {
        this.setData({
          currentCategory: options.category
        });
      }
    }
  },
  
  // 页面显示
  onShow: function() {
    console.log('杂志页面显示');
    
    // 检查是否需要强制刷新主题
    const needRefreshTheme = wx.getStorageSync('needRefreshOutfitTheme');
    if (needRefreshTheme) {
      console.log('检测到强制刷新主题标志，正在更新主题');
      wx.removeStorageSync('needRefreshOutfitTheme'); // 清除标志
      
      // 获取当前主题并应用
      const savedTheme = wx.getStorageSync('themeStyle');
      if (savedTheme) {
        console.log('强制应用主题:', savedTheme);
        // 先设置数据
        this.setData({
          themeStyle: savedTheme
        }, () => {
          // 强制应用主题
          this.applyThemeStyle(savedTheme);
        });
      }
    }
    
    // 如果已经有用户ID，加载数据
    if (this.data.userOpenId) {
      this.loadMagazineData();
    }
  },
  
  // 应用主题样式
  applyThemeStyle: function(themeStyle) {
    let navBackground, navFrontColor;
    
    switch (themeStyle) {
      case THEMES.AUTUMN:
        navBackground = this.data.colors.goldenBatter;
        navFrontColor = '#000000';
        break;
      case THEMES.PINK_BLUE:
        navBackground = this.data.pinkBlueColors.pinkLight;
        navFrontColor = '#000000';
        break;
      case THEMES.BLACK_WHITE:
        navBackground = this.data.blackWhiteColors.white;
        navFrontColor = '#000000';
        break;
      default:
        navBackground = this.data.colors.goldenBatter;
        navFrontColor = '#000000';
    }
    
    this.setData({
      navBackground: navBackground,
      navFrontColor: navFrontColor
    });
    
    // 设置导航栏样式
    wx.setNavigationBarColor({
      frontColor: navFrontColor,
      backgroundColor: navBackground,
      animation: {
        duration: 400,
        timingFunc: 'easeIn'
      }
    });
  },
  
  // 获取用户OpenID
  getUserOpenId: function() {
    // 尝试从缓存获取
    const openid = wx.getStorageSync('openid');
    if (openid) {
      this.setData({
        userOpenId: openid
      }, () => {
        this.loadMagazineData();
      });
      return;
    }
    
    // 如果缓存中没有，则从云函数获取
    wx.cloud.callFunction({
      name: 'login',
      data: {},
      success: res => {
        console.log('获取用户OpenID成功:', res.result.openid);
        const userOpenId = res.result.openid;
        
        // 保存到本地缓存
        wx.setStorageSync('openid', userOpenId);
        
        // 更新数据并加载数据
        this.setData({
          userOpenId: userOpenId
        }, () => {
          this.loadMagazineData();
        });
      },
      fail: err => {
        console.error('获取用户OpenID失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 初始化页面切换动画
  initPageAnimation: function() {
    this.pageAnimation = wx.createAnimation({
      duration: 600,
      timingFunction: 'ease',
      delay: 0
    });
  },
  
  // 加载杂志数据
  loadMagazineData: function() {
    this.setData({
      isLoading: true
    });
    
    // 并行获取各类别的数据
    Promise.all([
      this.getCategoryFeaturedOutfit('daily'),
      this.getCategoryFeaturedOutfit('work'),
      this.getCategoryFeaturedOutfit('party'),
      this.getCategoryFeaturedOutfit('sport'),
      this.getCategoryFeaturedOutfit('seasonal'),
      this.getRecommendedOutfits(),
      this.getEditorPickOutfits()
    ])
    .then(() => {
      // 所有数据加载完成
      this.setData({
        isLoading: false
      });
    })
    .catch(err => {
      console.error('加载杂志数据出错:', err);
      this.setData({
        isLoading: false
      });
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    });
  },
  
  // 获取某类别的特色搭配（取该类别的第一个搭配）
  getCategoryFeaturedOutfit: function(category) {
    return new Promise((resolve, reject) => {
      const db = wx.cloud.database();
      db.collection('outfits')
        .where({
          category: category,
          _openid: this.data.userOpenId
        })
        .orderBy('createTime', 'desc')
        .limit(1)
        .get()
        .then(res => {
          if (res.data && res.data.length > 0) {
            // 处理图片URL
            return imageManager.processOutfitsImages([res.data[0]])
              .then(processedOutfits => {
                // 设置特色搭配
                this.setData({
                  [`featuredOutfits.${category}`]: processedOutfits[0]
                });
                resolve();
              });
          } else {
            // 没有该类别的搭配
            this.setData({
              [`featuredOutfits.${category}`]: null
            });
            resolve();
          }
        })
        .catch(err => {
          console.error(`获取${category}特色搭配失败:`, err);
          this.setData({
            [`featuredOutfits.${category}`]: null
          });
          resolve(); // 即使失败也resolve，不影响其他数据加载
        });
    });
  },
  
  // 获取推荐搭配（随机获取5个）
  getRecommendedOutfits: function() {
    return new Promise((resolve, reject) => {
      const db = wx.cloud.database();
      db.collection('outfits')
        .where({
          _openid: this.data.userOpenId
        })
        .get()
        .then(res => {
          if (res.data && res.data.length > 0) {
            // 随机选择最多5个搭配
            const outfits = res.data;
            const shuffled = this.shuffleArray(outfits);
            const selected = shuffled.slice(0, Math.min(5, shuffled.length));
            
            // 处理图片URL
            return imageManager.processOutfitsImages(selected)
              .then(processedOutfits => {
                // 设置推荐搭配
                this.setData({
                  recommendedOutfits: processedOutfits
                });
                resolve();
              });
          } else {
            this.setData({
              recommendedOutfits: []
            });
            resolve();
          }
        })
        .catch(err => {
          console.error('获取推荐搭配失败:', err);
          this.setData({
            recommendedOutfits: []
          });
          resolve(); // 即使失败也resolve
        });
    });
  },
  
  // 获取编辑推荐搭配（取最新的3个）
  getEditorPickOutfits: function() {
    return new Promise((resolve, reject) => {
      const db = wx.cloud.database();
      db.collection('outfits')
        .where({
          _openid: this.data.userOpenId
        })
        .orderBy('createTime', 'desc')
        .limit(3)
        .get()
        .then(res => {
          if (res.data && res.data.length > 0) {
            // 处理图片URL
            return imageManager.processOutfitsImages(res.data)
              .then(processedOutfits => {
                // 设置编辑推荐搭配
                this.setData({
                  editorPickOutfits: processedOutfits
                });
                resolve();
              });
          } else {
            this.setData({
              editorPickOutfits: []
            });
            resolve();
          }
        })
        .catch(err => {
          console.error('获取编辑推荐搭配失败:', err);
          this.setData({
            editorPickOutfits: []
          });
          resolve(); // 即使失败也resolve
        });
    });
  },
  
  // 根据当前日期确定季节
  determineCurrentSeason: function() {
    const now = new Date();
    const month = now.getMonth() + 1; // getMonth返回0-11
    
    let season;
    if (month >= 3 && month <= 5) {
      season = SEASONS.SPRING;
    } else if (month >= 6 && month <= 8) {
      season = SEASONS.SUMMER;
    } else if (month >= 9 && month <= 11) {
      season = SEASONS.AUTUMN;
    } else {
      season = SEASONS.WINTER;
    }
    
    this.setData({
      currentSeason: season,
      currentYear: now.getFullYear()
    });
  },
  
  // 导航到特定页面
  navigateToPage: function(e) {
    // 先关闭类别索引侧边栏
    if (this.data.showCategoryIndex) {
      this.setData({
        showCategoryIndex: false
      });
    }
    
    // 延迟一下再切换页面，让侧边栏有时间关闭
    setTimeout(() => {
      // Pass the entire event object to changePage
      this.changePage(e);
    }, 100);
  },
  
  // 改变页面
  changePage: function(e, categoryId = '') {
    // If called directly from a bindtap event in WXML
    let pageId;
    if (e && e.currentTarget && e.currentTarget.dataset) {
      pageId = e.currentTarget.dataset.page;
      // If there's a category in dataset, use it
      if (e.currentTarget.dataset.category) {
        categoryId = e.currentTarget.dataset.category;
      }
    } else {
      // If called programmatically with pageId as first param
      pageId = e;
    }

    console.log('Changing to page:', pageId, 'Category:', categoryId);
    
    // Only proceed if pageId is valid
    if (!pageId) {
      console.error('No valid pageId provided to changePage');
      return;
    }
    
    // Check if we're already on this page
    if (this.data.currentPage === pageId && !categoryId) {
      console.log('Already on page:', pageId);
      return;
    }
    
    if (this.data.isPageTurning) return; // 如果正在翻页中，忽略
    
    this.setData({
      isPageTurning: true
    });
    
    // 执行翻页动画
    const animation = this.pageAnimation
      .opacity(0)
      .step();
    
    this.setData({
      pageAnimation: animation.export()
    });
    
    // 延迟更新页面内容
    setTimeout(() => {
      let newData = {
        currentPage: pageId,
        isPageTurning: false
      };
      
      // 如果提供了类别ID
      if (categoryId) {
        newData.currentCategory = categoryId;
      }
      
      // 更新页面状态
      this.setData(newData);
      
      // 执行淡入动画
      const showAnimation = this.pageAnimation
        .opacity(1)
        .step();
      
      this.setData({
        pageAnimation: showAnimation.export()
      });
      
      // 如果切换到类别页面，加载该类别的更多数据
      if (pageId === 'category' && categoryId) {
        this.loadCategoryData(categoryId);
      }
    }, 300);
  },
  
  // 加载某类别的更多数据
  loadCategoryData: function(category) {
    const db = wx.cloud.database();
    db.collection('outfits')
      .where({
        category: category,
        _openid: this.data.userOpenId
      })
      .orderBy('createTime', 'desc')
      .get()
      .then(res => {
        if (res.data && res.data.length > 0) {
          // 处理图片URL
          imageManager.processOutfitsImages(res.data)
            .then(processedOutfits => {
              // 设置类别搭配列表
              this.setData({
                categoryOutfits: processedOutfits
              });
            });
        } else {
          this.setData({
            categoryOutfits: []
          });
        }
      })
      .catch(err => {
        console.error(`加载${category}类别数据失败:`, err);
        this.setData({
          categoryOutfits: []
        });
      });
  },
  
  // 查看搭配详情
  viewOutfitDetail: function(e) {
    const outfitId = e.currentTarget.dataset.id;
    if (!outfitId) return;
    
    wx.navigateTo({
      url: `/page/wardrobe/outfit/outfit_detail/outfit_detail?id=${outfitId}`
    });
  },
  
  // 跳转到某类别的杂志页面
  navigateToCategoryMagazine: function(e) {
    const category = e.currentTarget.dataset.category;
    
    wx.navigateTo({
      url: `/page/wardrobe/outfit/outfit_category_magazine/outfit_category_magazine?category=${category}`
    });
  },
  
  // 工具方法：随机打乱数组
  shuffleArray: function(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  },
  
  // 触摸开始事件
  touchStart: function(e) {
    this.setData({
      touchStartX: e.touches[0].clientX,
      touchStartTime: new Date().getTime()
    });
  },
  
  // 触摸结束事件，实现左右滑动翻页
  touchEnd: function(e) {
    if (this.data.isPageTurning) return; // 如果正在翻页中，忽略
    
    const touchEndX = e.changedTouches[0].clientX;
    const touchEndTime = e.timeStamp;
    const deltaX = touchEndX - this.data.touchStartX;
    const deltaTime = touchEndTime - this.data.touchStartTime;
    
    // 获取当前页面的索引
    const currentPageIndex = this.data.magazinePages.findIndex(page => page.id === this.data.currentPage);
    
    // 判断是否为快速滑动（滑动距离超过50px且时间少于300ms）
    const isQuickSwipe = Math.abs(deltaX) > 50 && deltaTime < 300;
    
    // 判断是否为有效滑动（滑动距离超过屏幕宽度的20%）
    const screenWidth = wx.getSystemInfoSync().windowWidth;
    const isValidSwipe = Math.abs(deltaX) > screenWidth * 0.2;
    
    if ((isQuickSwipe || isValidSwipe) && !this.data.showCategoryIndex) {
      if (deltaX < 0) {
        // 向左滑动，翻到下一页
        if (currentPageIndex < this.data.magazinePages.length - 1) {
          // 使用新的函数签名，直接传递页面ID
          const nextPageId = this.data.magazinePages[currentPageIndex + 1].id;
          this.changePage(nextPageId);
        }
      } else if (deltaX > 0) {
        // 向右滑动，翻到上一页
        if (currentPageIndex > 0) {
          // 使用新的函数签名，直接传递页面ID
          const prevPageId = this.data.magazinePages[currentPageIndex - 1].id;
          this.changePage(prevPageId);
        }
      }
    }
  },
  
  // 显示/隐藏类别索引
  toggleCategoryIndex: function() {
    this.setData({
      showCategoryIndex: !this.data.showCategoryIndex
    });
  },
  
  // 跳转到创建搭配页面
  goToCreateOutfit: function() {
    // 先关闭类别索引侧边栏
    if (this.data.showCategoryIndex) {
      this.setData({
        showCategoryIndex: false
      });
    }
    
    // 延迟一下再跳转，让侧边栏有时间关闭
    setTimeout(() => {
      wx.navigateTo({
        url: '/page/wardrobe/outfit/outfit_create/outfit_create'
      });
    }, 100);
  },
  
  // 返回上页
  goBack: function() {
    wx.navigateBack();
  },
  
  // 处理滑动翻页
  handleSwipe: function(direction) {
    // 获取当前页面的索引
    const currentPageIndex = this.data.magazinePages.findIndex(page => page.id === this.data.currentPage);
    
    if (direction === 'left') {
      // 向左滑动，翻到下一页
      if (currentPageIndex < this.data.magazinePages.length - 1) {
        // 使用新的函数签名，直接传递页面ID
        const nextPageId = this.data.magazinePages[currentPageIndex + 1].id;
        this.changePage(nextPageId);
      }
    } else if (direction === 'right') {
      // 向右滑动，翻到上一页
      if (currentPageIndex > 0) {
        // 使用新的函数签名，直接传递页面ID
        const prevPageId = this.data.magazinePages[currentPageIndex - 1].id;
        this.changePage(prevPageId);
      }
    }
  },
}); 