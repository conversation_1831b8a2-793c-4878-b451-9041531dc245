/**
 * viewManager.js
 * 负责类别页面中衣物的视图切换管理
 */

// 视图类型常量
const VIEW_TYPES = {
  GRID: 'grid', // 网格视图（默认）
  DETAIL: 'detail' // 详细视图
};

/**
 * 初始化视图管理器
 * @param {Object} page - 页面实例
 * @returns {Object} 视图管理器实例
 */
function init(page) {
  // 确保页面数据中有视图类型
  if (!page.data.viewType) {
    page.setData({
      viewType: VIEW_TYPES.GRID
    });
  }
  
  return {
    // 获取当前视图类型
    getCurrentViewType: () => page.data.viewType,
    
    // 切换视图类型
    toggleViewType: () => {
      const currentType = page.data.viewType;
      const newType = currentType === VIEW_TYPES.GRID ? VIEW_TYPES.DETAIL : VIEW_TYPES.GRID;
      
      page.setData({
        viewType: newType
      });
      
      // 保存视图类型到缓存，以便用户下次打开时保持选择
      try {
        wx.setStorageSync('categoryViewType', newType);
      } catch (e) {
        console.error('保存视图类型失败:', e);
      }
      
      return newType;
    },
    
    // 加载保存的视图类型（从缓存）
    loadSavedViewType: () => {
      try {
        const savedViewType = wx.getStorageSync('categoryViewType');
        if (savedViewType) {
          page.setData({
            viewType: savedViewType
          });
          return savedViewType;
        }
      } catch (e) {
        console.error('读取视图类型失败:', e);
      }
      
      return page.data.viewType;
    },
    
    // 直接设置视图类型
    setViewType: (viewType) => {
      if (Object.values(VIEW_TYPES).includes(viewType)) {
        page.setData({
          viewType: viewType
        });
        
        // 保存视图类型到缓存
        try {
          wx.setStorageSync('categoryViewType', viewType);
        } catch (e) {
          console.error('保存视图类型失败:', e);
        }
        
        return viewType;
      }
      
      return page.data.viewType;
    },
    
    // 计算每次使用成本
    calculateCostPerUse: (price, wornCount) => {
      if (price && wornCount && wornCount > 0) {
        return (price / wornCount).toFixed(2);
      }
      return "0.00";
    },
    
    // 视图类型常量
    VIEW_TYPES
  };
}

module.exports = {
  init,
  VIEW_TYPES
}; 