/* page/wardrobe/category/category.wxss */

/* 自定义导航栏样式 */
.nav-right-container {
  display: flex;
  align-items: center;
  height: 100%;
  padding-right: 10rpx;
}

/* 主题切换器样式 */
.theme-switcher {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 40rpx;
  padding: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.theme-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 4rpx;
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.theme-btn.active {
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  transform: scale(1.1);
}

.theme-icon {
  font-size: 32rpx;
}

.autumn-icon {
  color: #8d7a56;
}

.pinkblue-icon {
  color: #D47C99;
}

.bw-icon {
  color: #000000;
}

.category-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 88vh; /* 使用固定高度而不是最小高度 */
  padding: 0 20rpx;
  padding-top: 10rpx; /* 为自定义导航栏留出空间 */
  padding-bottom: 120rpx; /* 为底部信息栏留出空间 */
  box-sizing: border-box;
  background-color: #f9f5f0;
  position: relative; /* 添加相对定位，作为底部栏的参考 */
  overflow: hidden; /* 防止整体内容溢出 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f2e7d5;
  border-top: 6rpx solid #8d7a56;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #8d7a56;
}

/* 类别标题 */
.category-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8d7a56;
  margin: 20rpx 0;
  text-align: center;
}

/* 类别主体内容 */
.category-body {
  width: 100%;
  overflow: hidden;
  background-color: #e9d7bd;
  border-radius: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #d5c3a5;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease; /* 添加平滑过渡效果 */
}

/* 添加滚动视图样式 */
.clothes-scroll-view {
  width: 100%;
  height: 100% !important;
  box-sizing: border-box;
}

/* 衣物网格 */
.clothes-grid {
  width: 100%;
  padding: 15rpx;
  box-sizing: border-box;
  position: relative;
}

.clothes-grid-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx; /* 使用gap属性控制间距 */
  position: relative;
}

/* 详细视图列表 */
.clothes-detail-list {
  width: 100%;
  padding: 15rpx;
  box-sizing: border-box;
  position: relative;
}

.detail-list-item {
  width: 100%;
  display: flex;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  padding: 20rpx 0;
  margin-bottom: 15rpx;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
  overflow: hidden;
}

.detail-list-item:active {
  transform: scale(0.98);
  background-color: rgba(255, 255, 255, 0.85);
}

.detail-item-left {
  width: 160rpx;
  height: 160rpx;
  position: relative;
  margin: 0 20rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.detail-item-image {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

.detail-item-right {
  flex: 1;
  padding: 10rpx 20rpx 10rpx 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.detail-item-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detail-item-info-section {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item-row {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  line-height: 1.4;
}

.detail-item-label {
  font-weight: bold;
  color: #666;
  margin-right: 10rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

.detail-item-value {
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 底部视图切换按钮 */
.view-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  border: 1px solid;
  margin-right: 16rpx;
  font-size: 24rpx;
  height: 56rpx;
}

.view-icon {
  font-size: 24rpx;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
}

/* 断舍离标记小尺寸样式 */
.discard-mark.small {
  position: absolute;
  top: 5rpx;
  right: 5rpx;
  background-color: rgba(231, 76, 60, 0.95);
  color: white;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  z-index: 3;
  font-weight: bold;
  width: auto;
  height: auto;
  max-width: 35%;
}

/* 详细视图中的断舍离样式 */
.detail-item-left {
  position: relative;
}

.detail-item-left .discard-mark.small {
  top: 5rpx;
  right: 5rpx;
  font-size: 16rpx;
  padding: 2rpx 6rpx;
  max-width: 60rpx;
  text-align: center;
  width: auto;
  height: auto;
  border-radius: 6rpx;
  left: auto;
  bottom: auto;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 断舍离标记样式 */
.discard-mark {
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: rgba(231, 76, 60, 0.9);
  color: white;
  font-size: 22rpx;
  padding: 6rpx 10rpx;
  border-radius: 0 8rpx 0 0;
  font-weight: bold;
  box-shadow: 0 -1rpx 3rpx rgba(0, 0, 0, 0.2);

}

/* 详情页中的断舍离标记样式 */
.item-image-container .discard-mark {
  bottom: auto;
  top: 0;
  left: 0;
  border-radius: 0 0 8rpx 0;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

/* 类别主体内容 */
.category-body {
  width: 100%;
  overflow: hidden;
  background-color: #e9d7bd;
  border-radius: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #d5c3a5;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease; /* 添加平滑过渡效果 */
}

/* 添加滚动视图样式 */
.clothes-scroll-view {
  width: 100%;
  height: 100% !important;
  box-sizing: border-box;
}

/* 衣物网格 */
.clothes-grid {
  width: 100%;
  padding: 15rpx;
  box-sizing: border-box;
  position: relative;
}

.clothes-grid-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx; /* 使用gap属性控制间距 */
  position: relative;
}

.grid-item {
  width: calc((100% - 20rpx) / 2); /* 宽度计算为(容器宽度-间距)/2 */
  margin: 0; /* 移除margin，使用gap控制间距 */
  background-color: white;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
  height: 340rpx;
  box-sizing: border-box;
}

.grid-item-image-container {
  width: 100%;
  height: 240rpx;
  position: relative;
  overflow: hidden;
  border-radius: 8rpx;
  background-color: #f9f9f9;
}

.grid-item-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 添加性价比标签样式 */
.cost-effectiveness-tag {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  background-color: rgba(51, 153, 255, 0.9);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(2px);
  z-index: 5;
}

/* 调整价格标签样式，避免与性价比标签冲突 */
.price-tag {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  background-color: rgba(255, 87, 51, 0.9);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(2px);
}

.grid-item-info {
  padding: 10rpx;
  width: 100%;
  text-align: center;
}

.grid-item-name {
  display: block;
  font-size: 26rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.grid-item-color {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-data-tip {
  width: 100%;
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 详情弹窗 */
.card-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 改为顶部对齐 */
  padding-top: 200rpx; /* 添加顶部填充，使弹窗下移 */
}

/* 图片卡片提示样式 */
.image-card {
  position: relative;
}

.image-card::after {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  font-size: 20rpx;
  color: #666;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  white-space: pre-wrap;
  text-align: center;
  line-height: 1.3;
  opacity: 0.8;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.modal-content {
  position: relative;
  width: 85%;
  max-height: calc(90vh - 200rpx); /* 调整最大高度，考虑到padding-top的影响 */
  background-color: #f9f9f9;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  animation: modal-enter 0.3s ease;
  z-index: 10;
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 垂直方向排列 */
}

@keyframes modal-enter {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  flex-wrap: nowrap;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.title-container {
  flex: 1;
  overflow: hidden;
  margin-right: 20rpx;
  position: relative;
}

.title-container:active::after {
  content: attr(title);
  position: absolute;
  top: 100%;
  left: 0;
  padding: 10rpx 16rpx;
  background-color: rgba(0, 0, 0, 0.75);
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
  z-index: 10;
  white-space: normal;
  max-width: 90vw;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.header-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  position: relative;
  padding-left: 25rpx;
}

.modal-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 4rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.close-btn {
  font-size: 46rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
}

.modal-body {
  padding: 20rpx;
  flex: 1; /* 让body填充剩余空间 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

.item-image-container {
  position: relative;
  width: 100%;
  height: 400rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #f1f1f1;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 12rpx;
  border: 1px solid #e0e0e0;
  cursor: pointer;
}

.item-image-container:active .item-image {
  opacity: 0.8;
}

.item-details {
  margin-top: 20rpx;
}

/* 新增卡片样式 */
.detail-card {
  background-color: #ffffff;
  border-radius: 15rpx;
  box-shadow:
    0 6rpx 16rpx rgba(0, 0, 0, 0.15),
    0 2rpx 4rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 20rpx;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
}

.detail-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  padding: 20rpx 25rpx;
  border-bottom: 1rpx solid #eaeaea;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.card-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.card-content {
  padding: 10rpx 0;
}

.detail-row {
  display: flex;
  padding: 18rpx 25rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s;
}

.detail-row:active {
  background-color: #f9f9f9;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

/* 价格显示与眼睛图标容器 */
.detail-value-with-icon {
  display: flex;
  align-items: center;
  flex: 1;
}

.price-eye-icon {
  margin-left: 10rpx;
  padding: 6rpx;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.eye-icon {
  font-size: 32rpx;
}

.remark-value {
  display: block;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.6;
  color: #555;
  font-size: 26rpx;
  padding: 10rpx 0;
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  padding: 25rpx 30rpx;
  border-top: 1rpx solid #eeeeee;
  gap: 40rpx; /* 增加按钮之间的间距 */
  position: sticky;
  bottom: 0;
  background-color: #fff;
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0; /* 防止底部操作栏被压缩 */
}

.action-btn {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  font-size: 30rpx;
  border-radius: 50rpx;
  margin: 0; /* 移除原来的margin */
  max-width: 280rpx; /* 限制最大宽度 */
  transition: opacity 0.2s, transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15);
}

.action-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.delete-btn {
  background: linear-gradient(to right, #ff5733, #ff7c55);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.edit {
  background: linear-gradient(to right, #3399ff, #66aaff);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 奖励弹窗样式 */
.reward-modal {
  /* 使用现有的card-detail-modal作为基础 */
}

.reward-content {
  max-width: 320px;
  width: 80%;
}

.reward-message {
  padding: 20rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  margin: 20rpx 0;
  font-weight: bold;
}

.reward-modal .item-image-container {
  width: 240rpx;
  height: 240rpx;
  margin: 0 auto;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.reward-modal .item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reward-modal .confirm-btn {
  background-color: #4CAF50;
  color: white;
  width: 70%;
  margin: 20rpx auto;
}

.reward-modal .confirm-btn:active {
  opacity: 0.8;
}

/* 编辑弹窗样式 */
.edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 改为顶部对齐 */
  padding-top: 200rpx; /* 与详情弹窗保持一致 */
  z-index: 1000;
}

.edit-modal-content {
  width: 85%;
  max-height: calc(85vh - 200rpx); /* 减少最大高度，考虑到padding-top的影响 */
  background-color: #f9f9f9;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.18),
              0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: modal-enter 0.3s ease;
}

.edit-modal-header {
  padding: 20rpx; /* 减少填充 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eeeeee;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  flex-shrink: 0; /* 防止头部压缩 */
}

.edit-modal-title {
  font-size: 32rpx; /* 减小字体大小 */
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.edit-modal-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.edit-modal-body {
  padding: 15rpx; /* 减少填充 */
  overflow-y: auto;
  flex: 1; /* 让body填充剩余空间 */
  background-color: #f5f5f5;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

.edit-section {
  background-color: transparent;
  margin-bottom: 24rpx;
  overflow: hidden;
  padding: 0;
  box-shadow: none;
}

.edit-row {
  margin-bottom: 12rpx; /* 减少底部边距 */
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx; /* 减少填充 */
  border-bottom: none;
  transition: all 0.2s;
  background-color: #ffffff;
  border-radius: 12rpx; /* 减少圆角半径 */
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.1), /* 减少阴影 */
    0 1rpx 3rpx rgba(0, 0, 0, 0.06);
  position: relative;
}

.edit-row:last-child {
  margin-bottom: 12rpx; /* 与其他行保持一致 */
}

.edit-row:active {
  transform: translateY(2rpx);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.08),
    0 1rpx 2rpx rgba(0, 0, 0, 0.04);
  background-color: #fdfdfd;
}

.edit-row::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 3rpx 0 0 3rpx;
  opacity: 0.8;
}

.edit-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  margin-right: 15rpx;
}

.edit-input-container {
  flex: 1;
  position: relative;
}

.edit-input {
  flex: 1;
  height: 60rpx; /* 减少高度 */
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx; /* 减少圆角半径 */
  padding: 0 12rpx; /* 减少填充 */
  font-size: 26rpx; /* 减小字体 */
  background-color: #f9f9f9;
  transition: all 0.3s;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05); /* 减少阴影 */
  -webkit-appearance: none;
  cursor: pointer;
  position: relative;
  z-index: 10;
}

.edit-input:focus {
  border-color: #3399ff;
  box-shadow: 0 0 0 2rpx rgba(51, 153, 255, 0.2);
  background-color: #ffffff;
}

/* 备注文本区域样式 */
.edit-textarea {
  flex: 1;
  min-height: 120rpx;
  max-height: 300rpx;
  width: 100%;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 16rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 1.6;
  background-color: #f9f9f9;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.edit-textarea:focus {
  border-color: #3399ff;
  box-shadow: 0 0 0 2rpx rgba(51, 153, 255, 0.2);
  background-color: #ffffff;
}

.edit-value {
  flex: 1;
  height: 60rpx; /* 减少高度 */
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx; /* 减少圆角半径 */
  padding: 0 12rpx; /* 减少填充 */
  font-size: 26rpx; /* 减小字体 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f9f9f9;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05); /* 减少阴影 */
  transition: all 0.3s;
}

.edit-value:active {
  background-color: #f0f0f0;
}

.arrow-right {
  color: #999;
  font-size: 24rpx;
}

.edit-actions {
  display: flex;
  justify-content: space-between;
  position: sticky;
  bottom: 0;
  background-color: #f5f5f5;
  padding: 15rpx 20rpx; /* 减少填充 */
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.08); /* 减少阴影 */
  flex-shrink: 0; /* 防止底部操作栏被压缩 */
}

.edit-actions .action-btn {
  flex: 1;
  margin: 0 10rpx; /* 减少边距 */
  height: 70rpx; /* 减少高度 */
  border-radius: 35rpx; /* 半径为高度的一半 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx; /* 减小字体 */
  transition: all 0.3s;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.12); /* 减少阴影 */
  text-shadow: 0 1rpx 1rpx rgba(0, 0, 0, 0.1);
}

.edit-actions .action-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.edit-actions .delete {
  background: linear-gradient(to right, #ff5733, #ff7c55);
  color: white;
  border: none;
}

.edit-actions .save {
  background: linear-gradient(to right, #3399ff, #66aaff);
  color: white;
  border: none;
}

/* 历史值气泡样式 */
.suggestion-bubbles {
  position: absolute;
  top: -8rpx;
  left: 0;
  width: 100%;
  transform: translateY(-100%);
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
  z-index: 9999;
  padding: 10rpx;
  border: 1rpx solid #e0e0e0;
  overflow: hidden;
}

.bubbles-scroll {
  width: 100%;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

.bubble-container {
  display: flex;
  flex-wrap: nowrap;
  padding: 6rpx;
  width: max-content; /* 确保容器宽度能容纳所有子元素 */
}

.suggestion-bubble {
  display: inline-block;
  margin: 6rpx;
  padding: 8rpx 20rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #333;
  white-space: nowrap;
  transition: all 0.2s;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0; /* 防止气泡被压缩 */
}

.suggestion-bubble:active {
  background-color: #e0e0e0;
  transform: scale(0.98);
}

/* 隐藏滚动条但保留滚动功能 */
.bubbles-scroll::-webkit-scrollbar {
  display: none;
}

/* 类别选择器 */
.category-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 改为顶部对齐 */
  padding-top: 200rpx; /* 与其他弹窗保持一致 */
  z-index: 1500;
}

.category-picker-content {
  width: 85%;
  background-color: #f9f9f9;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.18),
              0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: modal-enter 0.3s ease;
  position: relative;
  max-height: 80vh;
}

.category-picker-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eeeeee;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.category-picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.category-picker-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.category-options {
  padding: 40rpx 30rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background-color: #ffffff;
}

.category-option {
  width: 44%;
  height: 110rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  margin-bottom: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333;
  transition: all 0.2s;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  background-color: #ffffff;
}

/* 自定义选项的突出样式 */
.category-option.custom-option {
  width: 100%;
  height: 120rpx;
  margin-bottom: 30rpx;
  background: linear-gradient(to right, #3399ff, #66aaff);
  color: white;
  font-size: 34rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 6rpx 12rpx rgba(51, 153, 255, 0.3);
  position: relative;
  overflow: hidden;
}

/* 秋季主题下的自定义选项样式 */
.autumn .category-option.custom-option {
  background: linear-gradient(to right, #8d7a56, #a18e6b);
  box-shadow: 0 6rpx 12rpx rgba(141, 122, 86, 0.3);
}

.category-option.custom-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255,255,255,0.2), rgba(255,255,255,0));
  z-index: 1;
}

.category-option.custom-option:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 6rpx rgba(51, 153, 255, 0.2);
}

.category-option:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.category-option.selected {
  background: linear-gradient(to bottom, #e6f4ff, #d4eaff);
  color: #3399ff;
  border-color: #66aaff;
  box-shadow: 0 4rpx 8rpx rgba(51, 153, 255, 0.2);
  font-weight: bold;
}

/* 自定义选项的选中状态样式 */
.category-option.custom-option.selected {
  background: linear-gradient(to right, #2980b9, #3498db);
  color: white;
  box-shadow: 0 6rpx 12rpx rgba(41, 128, 185, 0.4);
  transform: scale(1.02);
}

/* 秋季主题下的自定义选项选中状态 */
.autumn .category-option.custom-option.selected {
  background: linear-gradient(to right, #7a6945, #8d7a56);
  box-shadow: 0 6rpx 12rpx rgba(122, 105, 69, 0.4);
}

/* 自定义类别输入框样式 */
.custom-category-input {
  margin: 20rpx 30rpx;
  padding: 10rpx;
  border-radius: 10rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border: 2rpx solid #e0e0e0;
}

.custom-category-input .edit-input {
  height: 70rpx;
  width: 100%;
  font-size: 28rpx;
  color: #333;
  padding: 0 20rpx;
  border-radius: 8rpx;
}

.category-picker-actions {
  display: flex;
  border-top: 1rpx solid #eeeeee;
}

.category-picker-actions .action-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: all 0.2s;
}

.category-picker-actions .cancel {
  background-color: #f5f5f5;
  color: #666;
}

.category-picker-actions .confirm {
  background: linear-gradient(to right, #3399ff, #66aaff);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

/* 日期选择器 */
.date-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1500;
}

.date-picker-content {
  width: 85%;
  background-color: #f9f9f9;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.18),
              0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: modal-enter 0.3s ease;
  position: relative;
  max-height: 80vh;
}

.date-picker-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eeeeee;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.date-picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.date-picker-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.date-picker-body {
  padding: 36rpx 24rpx;
  display: flex;
  justify-content: center;
  background-color: #ffffff;
}

.date-picker-current {
  font-size: 36rpx;
  color: #333;
  padding: 16rpx 30rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  background-color: #ffffff;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.date-picker-actions {
  display: flex;
  border-top: 1rpx solid #eeeeee;
}

.date-picker-actions .action-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: all 0.2s;
}

.date-picker-actions .cancel {
  background-color: #f5f5f5;
  color: #666;
}

.date-picker-actions .confirm {
  background: linear-gradient(to right, #3399ff, #66aaff);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 季节选择器 */
.season-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1500;
}

/* 清除季节按钮样式 */
.clear-season-btn {
  padding: 16rpx 0;
  margin: 16rpx 30rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  border-bottom: 1rpx dashed #eee;
}

.clear-season-btn:active {
  color: #3399ff;
}

.season-picker-content {
  width: 85%;
  background-color: #f9f9f9;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.18),
              0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: modal-enter 0.3s ease;
  position: relative;
  max-height: 80vh;
}

.season-picker-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eeeeee;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.season-picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.season-picker-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.season-options {
  padding: 30rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background-color: #ffffff;
}

.season-option {
  width: 44%;
  height: 90rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  margin-bottom: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: #333;
  transition: all 0.2s;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  background-color: #ffffff;
}

.season-option icon {
  margin-right: 8rpx;
}

.season-option text {
  margin-left: 5rpx;
}

.season-option.selected {
  background: linear-gradient(to bottom, #e6f7ed, #d4edda);
  color: #07c160;
  border-color: #07c160;
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);
  transform: scale(1.02);
  font-weight: bold;
}

.season-option:not(.selected) {
  background-color: #ffffff;
}

.season-option:active:not(.selected) {
  transform: scale(0.96);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.season-picker-actions {
  display: flex;
  border-top: 1rpx solid #eeeeee;
}

.season-picker-actions .action-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: all 0.2s;
}

.season-picker-actions .cancel {
  background-color: #f5f5f5;
  color: #666;
}

.season-picker-actions .confirm {
  background: linear-gradient(to right, #3399ff, #66aaff);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

/* 筛选按钮区域 */
.filter-buttons-container {
  width: 100%;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  background-color: #f2e7d5;
  border-radius: 16rpx;
  border: 2rpx solid #d5c3a5;
  white-space: nowrap;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1);
  flex-shrink: 0; /* 防止容器被压缩 */
}

.filter-buttons {
  display: inline-flex;
  padding: 0 20rpx;
}

.filter-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  padding: 0 30rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 2rpx solid #8d7a56;
  color: #8d7a56;
  background-color: transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.05);
}

.filter-button.active {
  background-color: #8d7a56;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.filter-button:last-child {
  margin-right: 0;
}

/* 详情页中的分类显示相关样式 */
.detail-categories {
  margin-top: 20rpx;
  margin-bottom: 10rpx;
}

/* 分类区域样式 */
.category-section {
  background-color: #ffffff;
  border-radius: 15rpx;
  box-shadow:
    0 6rpx 16rpx rgba(0, 0, 0, 0.15),
    0 2rpx 4rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 20rpx;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
}

.category-section:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

/* 分类标题 */
.category-section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  padding: 20rpx 25rpx;
  border-bottom: 1rpx solid #eaeaea;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.category-section-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.category-content {
  padding: 15rpx;
}

/* 分类标签样式 */
.category-tags {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx;
}

.category-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  margin: 6rpx;
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  border: 1rpx solid #e0e0e0;
  transition: all 0.2s;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.category-tag:active {
  background-color: #e0e0e0;
  transform: scale(0.96);
}

.category-tag.active {
  background: linear-gradient(to bottom, #e6f4ff, #d4eaff);
  color: #3399ff;
  border-color: #66aaff;
  box-shadow: 0 4rpx 8rpx rgba(51, 153, 255, 0.2);
  font-weight: 500;
}

/* 分类物品网格 */
.category-items-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.category-grid-item {
  width: calc(33.33% - 16rpx);
  margin: 8rpx;
  background-color: white;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
  border: 1rpx solid #f1f1f1;
  transition: transform 0.2s;
}

.category-grid-item:active {
  transform: scale(0.98);
}

.category-item-image-container {
  position: relative;
  width: 100%;
  height: 160rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.category-item-image {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

.category-item-info {
  width: 100%;
  padding: 6rpx 0;
  text-align: center;
}

.category-item-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #8d7a56;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.category-item-color {
  font-size: 22rpx;
  color: #a18e6b;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  margin-top: 2rpx;
}

/* 分类标签样式 */
.section-label {
  position: absolute;
  top: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(161, 142, 107, 0.9);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  z-index: 5;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 季节标签 */
.season-label {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 6rpx 16rpx;
  font-size: 22rpx;
  background-color: rgba(51, 153, 255, 0.9);
  color: white;
  border-radius: 15rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  z-index: 5;
}

.spring-label {
  background-color: rgba(139, 195, 74, 0.9);
}

.summer-label {
  background-color: rgba(3, 169, 244, 0.9);
}

.autumn-label {
  background-color: rgba(255, 152, 0, 0.9);
}

.winter-label {
  background-color: rgba(156, 39, 176, 0.9);
}

/* 分类列表样式 */
.category-list {
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin-bottom: 20rpx;
}

.category-list-item {
  display: flex;
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s;
}

.category-list-item:active {
  background-color: #f9f9f9;
}

.category-list-item:last-child {
  border-bottom: none;
}

.category-list-icon {
  width: 40rpx;
  font-size: 32rpx;
  color: #8d7a56;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;
}

.category-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.category-list-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.category-list-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.category-list-count {
  font-size: 24rpx;
  color: #8d7a56;
  font-weight: bold;
  background-color: rgba(161, 142, 107, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 30rpx;
  margin-left: 10rpx;
}

/* 添加加载更多样式 */
.loading-more {
  width: 100%;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
}

.loading-spinner-small {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f2e7d5;
  border-top: 4rpx solid #8d7a56;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

.loading-more-text {
  font-size: 24rpx;
  color: #8d7a56;
}

/* 修改底部信息栏样式，使其固定在屏幕底部 */
.category-bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 30rpx;
  border-top: 1rpx solid;
  box-sizing: border-box;
  z-index: 99;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 选择模式工具栏 */
.selection-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 30rpx;
  border-top: 1rpx solid;
  box-sizing: border-box;
  z-index: 99;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.selection-count {
  display: flex;
  align-items: center;
}

.selection-actions {
  display: flex;
  align-items: center;
}

.selection-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: #ffffff;
  border: 1px solid #ddd;
  font-size: 24rpx;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.selection-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.selection-btn.select-all-btn {
  padding-left: 16rpx;
}

/* 工具栏中的复选框 */
.toolbar-checkbox {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15), inset 0 0 4rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.toolbar-checkbox.selected {
  background: linear-gradient(145deg, #06b058, #07c160);
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.4), inset 0 0 4rpx rgba(255, 255, 255, 0.3);
  border: 1rpx solid rgba(7, 193, 96, 0.3);
}

.selection-btn.edit {
  background-color: #07c160;
  color: #ffffff;
  border: 1px solid #07c160;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.selection-btn.edit:active {
  background-color: #06b054;
}

.selection-btn.delete {
  background-color: #e74c3c;
  color: #ffffff;
  border: 1px solid #e74c3c;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(231, 76, 60, 0.3);
}

.selection-btn.delete:active {
  background-color: #c0392b;
}

.selection-btn.cancel {
  background-color: #f2f2f2;
  color: #333;
  border: 1px solid #ddd;
}

/* 选中状态样式 - 参考季节选择器的实现 */
.grid-item.selected {
  border: 3rpx solid #07c160;
  background: linear-gradient(to bottom, rgba(230, 247, 237, 0.5), rgba(212, 237, 218, 0.5));
}

.grid-item-image-container {
  position: relative;
}

.detail-list-item.selected {
  border-left: 8rpx solid #07c160;
  background: linear-gradient(to right, rgba(230, 247, 237, 0.5), rgba(212, 237, 218, 0.5));
}

/* 选择复选框 - 美化版 */
.selection-checkbox {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  padding: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2), inset 0 0 4rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.selection-checkbox icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.grid-item.selected .selection-checkbox,
.detail-list-item.selected .selection-checkbox {
  background: linear-gradient(145deg, #06b058, #07c160);
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.4), inset 0 0 6rpx rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  border: 1rpx solid rgba(7, 193, 96, 0.3);
}

.grid-item.selected .selection-checkbox icon,
.detail-list-item.selected .selection-checkbox icon {
  transform: scale(1.1);
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.2));
}

.clothes-count {
  flex: 1;
}

.count-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-right: 8rpx;
}

.count-label {
  font-size: 26rpx;
  color: #666;
}

.season-filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  border: 1px solid;
  font-size: 24rpx;
  height: 56rpx;
}

/* 滑动相关样式 */
.category-body {
  width: 100%;
  overflow: hidden;
  background-color: #e9d7bd;
  border-radius: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #d5c3a5;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease; /* 添加平滑过渡效果 */
}

/* 滑动提示样式 */
.swipe-hint {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 30rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  z-index: 500;
  animation: pulse-fade 3s forwards;
  pointer-events: none;
  text-align: center;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
  max-width: 80%;
  line-height: 1.5;
}

@keyframes pulse-fade {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
  10% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  70% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
}

/* 向右滑动指示动画 */
.swipe-right-animation {
  animation: swipe-right 0.3s ease;
}

/* 向左滑动指示动画 */
.swipe-left-animation {
  animation: swipe-left 0.3s ease;
}

@keyframes swipe-right {
  0% { transform: translateX(-10%); opacity: 0.7; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes swipe-left {
  0% { transform: translateX(10%); opacity: 0.7; }
  100% { transform: translateX(0); opacity: 1; }
}

/* 存储箱按钮样式 */
.store-btn {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  padding: 10rpx 20rpx;
  background: linear-gradient(to right, #f8f8f8, #e6e6e6);
  border-radius: 30rpx;
  cursor: pointer;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1rpx solid #e0e0e0;
  position: relative;
  overflow: hidden;
}

.store-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255,255,255,0.2), rgba(255,255,255,0));
  z-index: 1;
}

.store-btn:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.store-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  position: relative;
  z-index: 2;
}

.store-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #444;
  letter-spacing: 1rpx;
  text-shadow: 0 1rpx 0 rgba(255, 255, 255, 0.8);
  display: inline-block;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

/* VIP功能样式 */
.vip-feature {
  background: linear-gradient(to right, rgba(212, 175, 55, 0.05), rgba(212, 175, 55, 0.15));
  border: 1rpx solid rgba(212, 175, 55, 0.3);
  box-shadow: 0 6rpx 16rpx rgba(212, 175, 55, 0.15);
  overflow: hidden;
  position: relative;
}

.vip-feature::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg,
    rgba(212, 175, 55, 0) 0%,
    rgba(212, 175, 55, 0.05) 25%,
    rgba(212, 175, 55, 0.1) 50%,
    rgba(212, 175, 55, 0.05) 75%,
    rgba(212, 175, 55, 0) 100%);
  background-size: 200% 200%;
  animation: vipGradientMove 3s infinite linear;
  z-index: 1;
}

@keyframes vipGradientMove {
  0% { background-position: 0% 0%; }
  100% { background-position: 200% 200%; }
}

.vip-feature:active {
  background: linear-gradient(to right, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.25));
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(212, 175, 55, 0.1);
}

.vip-feature .store-text {
  background: linear-gradient(to right, #74301C, #aa8a2b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: none;
  font-weight: 600;
}

.vip-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background: linear-gradient(135deg, #d4af37, #aa8a2b);
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 6rpx 14rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.4), 0 0 4rpx rgba(255, 255, 255, 0.8) inset;
  letter-spacing: 1rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  animation: vipBadgePulse 2s infinite;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  z-index: 2;
  transform-origin: center;
}

@keyframes vipBadgePulse {
  0% { transform: scale(1); box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.4), 0 0 4rpx rgba(255, 255, 255, 0.8) inset; }
  50% { transform: scale(1.08); box-shadow: 0 6rpx 18rpx rgba(212, 175, 55, 0.6), 0 0 8rpx rgba(255, 255, 255, 0.9) inset; }
  100% { transform: scale(1); box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.4), 0 0 4rpx rgba(255, 255, 255, 0.8) inset; }
}

/* 不同主题样式 */
.autumn .store-btn {
  background: linear-gradient(to right, #f2e7d5, #e9d7bd);
  border: 1rpx solid #d5c3a5;
}

.autumn .store-text {
  color: #8d7a56;
}

/* 根据存入/取出状态的不同样式 */
.store-btn[bindtap*="retrieveClothing"] {
  background: linear-gradient(to right, #e3f0ff, #d6e7ff);
  border-color: #b8d4ff;
}

.store-btn[bindtap*="retrieveClothing"] .store-text {
  color: #4a7dbd;
}

.autumn .store-btn[bindtap*="retrieveClothing"] {
  background: linear-gradient(to right, #e0d9c2, #d5c9a8);
  border-color: #c4b48d;
}

.autumn .store-btn[bindtap*="retrieveClothing"] .store-text {
  color: #7a6945;
}

/* 衣柜选择器滚动区域样式 */
.wardrobe-options-scroll {
  max-height: 60vh;
  width: 100%;
  background-color: #ffffff;
}

/* 排序按钮样式 */
.sort-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  border: 1px solid;
  margin-right: 16rpx;
  font-size: 24rpx;
  height: 56rpx;
}

.sort-icon {
  margin-right: 6rpx;
  font-size: 26rpx;
}

/* 排序弹窗样式 */
.sort-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sort-picker-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.sort-picker-header {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.sort-picker-title {
  font-size: 32rpx;
  font-weight: 500;
}

.sort-picker-body {
  padding: 20rpx 0;
  max-height: 600rpx;
  overflow-y: auto;
}

.sort-option {
  padding: 24rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.sort-option.active {
  background-color: #f5f5f5;
  color: #e75480;
}

.sort-direction {
  display: flex;
  align-items: center;
}

.sort-direction-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.sort-direction-btn.active {
  background-color: #e75480;
  color: white;
}

/* 添加季节图标样式 */
.season-icon {
  font-size: 26rpx;
  margin-right: 6rpx;
}

/* 待办事项部分样式 */
.edit-section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 15px 0 10px 0;
  color: #333;
  padding-left: 10px;
  border-left: 3px solid #8d7a56;
}

.todos-container {
  margin: 0 10px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 10px;
}

.todo-list {
  max-height: 200px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.todo-item.completed .todo-title {
  text-decoration: line-through;
  color: #999;
}

.todo-checkbox {
  margin-right: 10px;
  flex-shrink: 0;
}

.todo-content {
  flex: 1;
  overflow: hidden;
}

.todo-title {
  font-size: 14px;
  margin-bottom: 4px;
  color: #333;
  word-break: break-all;
}

.todo-date {
  font-size: 12px;
  color: #666;
}

.todo-actions {
  margin-left: 10px;
}

.todo-delete {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  font-size: 16px;
  color: #ff5252;
  font-weight: bold;
}

.todo-add-btn {
  text-align: center;
  padding: 12px;
  color: #8d7a56;
  background-color: #f6f6f6;
  border-radius: 6px;
  margin-top: 10px;
  font-size: 14px;
  font-weight: bold;
}

/* 待办事项弹窗样式 */
.todo-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.todo-modal-content {
  width: 85%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.todo-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.todo-modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.todo-modal-body {
  padding: 15px;
}

.todo-form-row {
  margin-bottom: 15px;
}

.todo-form-label {
  font-size: 14px;
  color: #555;
  margin-bottom: 6px;
}

.todo-form-input {
  width: 100%;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0 10px;
  box-sizing: border-box;
  font-size: 14px;
  -webkit-appearance: none;
  cursor: pointer;
  position: relative;
  z-index: 1001;
}

.todo-date-picker {
  width: 100%;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0 10px;
  box-sizing: border-box;
  font-size: 14px;
  line-height: 40px;
  color: #333;
}

.todo-modal-actions {
  display: flex;
  border-top: 1px solid #eee;
}

.todo-modal-actions .action-btn {
  flex: 1;
  height: 46px;
  line-height: 46px;
  text-align: center;
  font-size: 16px;
  border-radius: 0;
}

.todo-modal-actions .cancel {
  background-color: #f5f5f5;
  color: #666;
  border-right: 1px solid #eee;
}

.todo-modal-actions .confirm {
  background-color: #8d7a56;
  color: #fff;
}

/* 详情页待办事项样式 */
.detail-todos-list {
  margin: 0;
  padding: 0;
}

.detail-todo-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-todo-item:last-child {
  border-bottom: none;
}

.detail-todo-status {
  margin-right: 10px;
  margin-top: 2px;
}

.detail-todo-content {
  flex: 1;
}

.detail-todo-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.detail-todo-item.completed .detail-todo-title {
  text-decoration: line-through;
  color: #999;
}

.detail-todo-date {
  font-size: 12px;
  color: #666;
}

/* 增强iOS上输入框的触摸反馈 */
.edit-input:active,
.todo-form-input:active {
  opacity: 0.8;
}

/* 确保iOS上弹窗中的输入框可以正常交互 */
.edit-modal-content input {
  -webkit-appearance: none;
  z-index: 9999; /* 确保高于其他元素 */
}

/* 消除iOS上input的默认内阴影 */
input {
  -webkit-appearance: none;
  box-shadow: none !important;
}

.detail-card.image-card {
  position: relative;
  margin-bottom: 30rpx;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.detail-card.image-card:active {
  transform: scale(0.98);
}

/* 修改覆盖层，使其在移动端也能显示提示 */
.item-image-container:after {
  content: "点击放大，长按更换图片";
  position: absolute;
  top: 0;  /* 从底部改为顶部 */
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  text-align: center;
  padding: 8rpx 0;
  font-size: 24rpx;
  opacity: 1; /* 始终显示提示文字 */
}

/* 去掉hover效果，因为移动设备没有hover */
.detail-card.image-card:hover .item-image-container:after {
  opacity: 1;
}

/* 批量编辑弹窗样式 */
.batch-edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 改为顶部对齐 */
  padding-top: 200rpx; /* 与其他弹窗保持一致 */
  z-index: 1000;
}

.batch-edit-modal-content {
  width: 90%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

.batch-edit-modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f8f8;
}

.batch-edit-modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.batch-edit-modal-body {
  padding: 30rpx;
  overflow-y: auto;
  max-height: 70vh;
}

.batch-edit-tip {
  font-size: 26rpx;
  color: #ff9800;
  background-color: #fff9e6;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #ff9800;
}

.batch-edit-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  background-color: #f8f8f8;
}

/* 黑白主题样式 */
.theme-blackWhite {
  background-color: #f5f5f5 !important;
}

/* 黑白主题下的卡片样式 */
.theme-blackWhite .grid-item {
  border: 1px solid #e0e0e0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

.theme-blackWhite .detail-card {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
}

.theme-blackWhite .card-title {
  color: #000000;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.theme-blackWhite .detail-label {
  color: #666666;
}

.theme-blackWhite .detail-value {
  color: #000000;
}

.theme-blackWhite .price-tag {
  background-color: #000000;
}

.theme-blackWhite .cost-effectiveness-tag {
  background-color: #333333;
}