// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 获取用户openid，优先使用传入的，否则使用上下文中的
  const userOpenId = event.userOpenId || wxContext.OPENID
  
  // 获取需要更新的数据
  const {taskId, step } = event
  
  // 参数验证
  if (!userOpenId) {
    return {
      success: false,
      error: '无法获取用户ID'
    }
  }
  
  // 至少需要一个更新字段
  if (taskId === undefined && step === undefined) {
    return {
      success: false,
      error: '更新数据不能为空'
    }
  }
  
  try {
    // 准备更新数据
    const updateData = {
      updateTime: db.serverDate()
    }
    
    // 查询用户任务进度
    const tasksCollection = db.collection('tasks')
    let userTasks = await tasksCollection.where({
      userOpenId: userOpenId,
      taskId: taskId
    }).get()
    
    // 如果用户没有任务记录，创建一个新的
    if (!userTasks.data || userTasks.data.length === 0) {
      // 补充默认值
      if (totalProgress === undefined) {
        updateData.totalProgress = 1
      }
      
      if (currentProgress === undefined) {
        updateData.currentProgress = 0
      }
      
      // 确保有taskId
      if (!updateData.taskId) {
        updateData.taskId = `task${updateData.totalProgress || 1}`
      }
      
      // 添加其他必要信息
      updateData.userOpenId = userOpenId
      updateData.createTime = db.serverDate()
      updateData.hasDone = []
      
      // 创建新记录
      const result = await tasksCollection.add({
        data: updateData
      })
      
      return {
        success: true,
        data: {
          ...updateData,
          _id: result._id
        }
      }
    }
    
    // 更新现有任务记录
    const _id = userTasks.data[0]._id
    const currentTask = userTasks.data[0]
    if (currentTask.hasDone === undefined){
      currentTask.hasDone = []
    }
    if (currentTask.hasDone.indexOf(step) !== -1) {
      return {
        success: true,
        data: {
          ...userTasks.data[0]
        }
      }
    }else{
    
      currentTask.hasDone.push(step)
      updateData.hasDone = currentTask.hasDone
      updateData.currentProgress = currentTask.hasDone.length
      const result = await tasksCollection.doc(_id).update({
        data: updateData
      })
    

    
    // 返回完整的任务数据
    const updatedTask = await tasksCollection.doc(_id).get()
    
    return {
      success: true,
      data: updatedTask.data
    }}
  } catch (error) {
    console.error('更新用户任务进度失败:', error)
    
    return {
      success: false,
      error: error.message || '更新任务进度失败'
    }
  }
} 