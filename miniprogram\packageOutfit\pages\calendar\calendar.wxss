/* page/wardrobe/calendar/calendar.wxss */

/* 主题变量 */
page {
  --toasted-caramel: #84592B;
  --spiced-wine: #74301C;
  --olive-harvest: #9D9167;
  --golden-batter: #EAD8A1;
  --cowhide-cocoa: #442D1C;
  --warm-sand: #D2B48C;

  --pink-dark: #D47C99;
  --pink-medium: #EEA0B2;
  --pink-light: #F9C9D6;
  --blue-light: #CBE0F9;
  --blue-medium: #97C8E5;
  --blue-dark: #5EA0D0;

  --black: #000000;
  --dark-gray: #333333;
  --medium-gray: #666666;
  --light-gray: #CCCCCC;
  --off-white: #F5F5F5;
  --white: #FFFFFF;
}

/* 状态栏占位符 */
.status-bar-placeholder {
  width: 100%;
  height: 40rpx;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  padding: 20rpx 0 30rpx 0;
  background-color: #f5f5f5;
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  box-sizing: border-box;
  position: relative;
}

/* 顶部区域 */
.top-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 50rpx;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}

/* 左侧区域 */
.left-section {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

/* 中间区域 */
.center-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 右侧区域 */
.right-section {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.back-btn {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-right: 20rpx;
}

.back-icon {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  text-align: right;
}

.today-btn {
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  font-weight: 500;
  transition: all 0.3s ease;
}

.today-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 主题适配 */
.autumn .today-btn {
  background-color: rgba(232, 209, 167, 0.8);
  color: var(--cowhide-cocoa);
}

.pinkBlue .today-btn {
  background-color: rgba(249, 201, 214, 0.8);
  color: var(--pink-dark);
}

.blackWhite .today-btn {
  background-color: rgba(204, 204, 204, 0.8);
  color: var(--black);
}

/* 日历头部 */
.calendar-header {
  padding: 30rpx 20rpx;
  color: white;
  border-radius: 0 0 20rpx 20rpx;
}

.month-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
}

.current-month {
  font-size: 36rpx;
  font-weight: bold;
}

.arrow {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  font-size: 30rpx;
}

/* 星期栏 */
.weekdays {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.weekday {
  width: 14.28%;
  text-align: center;
  font-size: 28rpx;
  color: white;
}

/* 日历主体 */
.calendar-body {
  padding: 20rpx;
  background-color: white;
  border-radius: 20rpx;
  margin: 20rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  flex: 1;
  min-height: 45vh;
  z-index: 1;
}

.days-grid {
  display: flex;
  flex-wrap: wrap;
}

.day-cell {
  width: 14.28%;
  height: 110rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 10rpx;
}

.day-number {
  width: 70rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.empty {
  background-color: transparent;
}

.today {
  background-color: rgba(132, 89, 43, 0.1);
  border-radius: 10rpx;
}

.today-number {
  background-color: var(--toasted-caramel);
  color: white;
  font-weight: bold;
}

.pinkBlue .today-number {
  background-color: var(--pink-dark);
}

.selected {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 10rpx;
}

.has-outfit .day-number {
  font-weight: bold;
}

.outfit-marker {
  position: absolute;
  bottom: 10rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: var(--toasted-caramel);
}

.pinkBlue .outfit-marker {
  background-color: var(--pink-dark);
}

/* 穿搭详情区域 */
.outfit-detail-section {
  padding: 0 30rpx 30rpx;
  z-index: 1;
  position: relative;
  background-color: transparent;
}

.date-header {
  font-size: 30rpx;
  margin-bottom: 15rpx;
  font-weight: bold;
  color: #333;
}

.outfit-detail-card {
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.outfit-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.outfit-detail-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.outfit-detail-type {
  font-size: 24rpx;
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
  color: white;
}

.outfit-content-layout {
  display: flex;
  justify-content: center;
}

.outfit-detail-image-container {
  width: 100%;
  text-align: center;
}

.outfit-detail-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: 10rpx;
}

/* 无穿搭提示 */
.no-outfit-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 30rpx;
  color: #999;
  font-size: 30rpx;
  z-index: 1;
  position: relative;
  background-color: transparent;
}

.add-outfit-btn {
  margin-top: 30rpx;
  background-color: var(--toasted-caramel);
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 15rpx 40rpx;
}

.pinkBlue .add-outfit-btn {
  background-color: var(--pink-dark);
}

/* 主题适配 */
.autumn .outfit-marker {
  background-color: var(--toasted-caramel);
}

.pinkBlue .outfit-marker {
  background-color: var(--pink-dark);
}

.blackWhite .outfit-marker {
  background-color: var(--black);
}

.autumn .today-number {
  background-color: var(--toasted-caramel);
}

.pinkBlue .today-number {
  background-color: var(--pink-dark);
}

.blackWhite .today-number {
  background-color: var(--black);
}

/* 操作按钮 */
.outfit-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  padding: 0 15rpx;
  flex-wrap: wrap;
  gap: 20rpx;
}

.button-container {
  flex: 1;
  display: flex;
  justify-content: center;
  min-width: 28%;
  max-width: 32%;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 26rpx;
  border-radius: 40rpx;
  margin: 0;
  padding: 0;
  border: none;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.detail-btn {
  background-color: #f5f5f5;
  color: #333;
}

.select-btn {
  background-color: var(--toasted-caramel);
  color: white;
}

.copy-btn {
  background-color: var(--olive-harvest);
  color: white;
}

/* 主题适配 */
.autumn .select-btn {
  background-color: var(--toasted-caramel);
}

.autumn .copy-btn {
  background-color: var(--olive-harvest);
  color: white;
}

.pinkBlue .select-btn {
  background-color: var(--pink-dark);
}

.pinkBlue .copy-btn {
  background-color: var(--blue-medium);
  color: white;
}

.blackWhite .select-btn {
  background-color: var(--black);
}

.blackWhite .copy-btn {
  background-color: var(--medium-gray);
  color: white;
}