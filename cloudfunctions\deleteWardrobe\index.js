// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const MAX_LIMIT = 50 // 每次获取的最大记录数

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 检查必要参数
  if (!event.wardrobeId) {
    return {
      success: false,
      message: '缺少要删除的衣柜ID'
    }
  }

  if (!event.targetWardrobeId) {
    return {
      success: false,
      message: '缺少目标衣柜ID'
    }
  }

  // 确保要删除的衣柜和目标衣柜不是同一个
  if (event.wardrobeId === event.targetWardrobeId) {
    return {
      success: false,
      message: '目标衣柜不能与要删除的衣柜相同'
    }
  }
  
  try {
    // 验证衣柜是否属于当前用户
    const wardrobeRes = await db.collection('wardrobes').doc(event.wardrobeId).get()
    const wardrobe = wardrobeRes.data
    
    if (wardrobe.owner !== wxContext.OPENID) {
      return {
        success: false,
        message: '无权删除该衣柜'
      }
    }

    // 验证目标衣柜是否属于当前用户
    const targetWardrobeRes = await db.collection('wardrobes').doc(event.targetWardrobeId).get()
    const targetWardrobe = targetWardrobeRes.data
    
    if (targetWardrobe.owner !== wxContext.OPENID) {
      return {
        success: false,
        message: '目标衣柜不属于当前用户'
      }
    }
    
    // 第一步：将要删除衣柜中的衣物转移到目标衣柜
    
    // 获取要删除衣柜中的衣物数量
    const countResult = await db.collection('clothes')
      .where({
        _openid: wxContext.OPENID,
        wardrobeId: event.wardrobeId
      })
      .count()
    
    const total = countResult.total
    let transferredCount = 0
    
    // 如果有衣物需要转移
    if (total > 0) {
      // 计算需要分几次获取
      const batchTimes = Math.ceil(total / MAX_LIMIT)
      
      // 分批次转移所有衣物
      for (let i = 0; i < batchTimes; i++) {
        // 获取当前批次的衣物
        const clothes = await db.collection('clothes')
          .where({
            _openid: wxContext.OPENID,
            wardrobeId: event.wardrobeId
          })
          .skip(i * MAX_LIMIT)
          .limit(MAX_LIMIT)
          .get()
        
        // 获取这批衣物的ID
        const clothesIds = clothes.data.map(item => item._id)
        
        // 创建批量更新任务
        const updatePromises = clothesIds.map(id => {
          return db.collection('clothes').doc(id).update({
            data: {
              wardrobeId: event.targetWardrobeId,
              wardrobeName: targetWardrobe.name,
              updateTime: db.serverDate()
            }
          })
        })
        
        // 执行批量更新
        const updateResults = await Promise.all(updatePromises)
        
        // 计算更新成功的数量
        updateResults.forEach(result => {
          transferredCount += result.stats.updated
        })
      }
    }
    
    // 第二步：删除衣柜布局数据（如果有）
    try {
      const layoutRes = await db.collection('wardrobeLayouts')
        .where({
          wardrobeId: event.wardrobeId,
          owner: wxContext.OPENID
        })
        .get()
      
      if (layoutRes.data && layoutRes.data.length > 0) {
        const layoutId = layoutRes.data[0]._id
        await db.collection('wardrobeLayouts').doc(layoutId).remove()
      }
    } catch (layoutErr) {
      console.error('删除衣柜布局数据失败:', layoutErr)
      // 继续执行，不中断流程
    }
    
    // 第三步：删除衣柜记录
    await db.collection('wardrobes').doc(event.wardrobeId).remove()
    
    return {
      success: true,
      transferred: transferredCount,
      message: `成功删除衣柜，并将${transferredCount}件衣物转移到目标衣柜`
    }
  } catch (error) {
    console.error('删除衣柜失败:', error)
    return {
      success: false,
      message: '删除失败: ' + error.message
    }
  }
} 