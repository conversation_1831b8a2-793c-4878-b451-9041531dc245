/**
 * 勋章管理模块
 * 负责勋章数据的获取、缓存和处理
 */

// 缓存相关常量
const MEDALS_CACHE_KEY = 'user_medals_cache';
const MEDALS_CACHE_EXPIRATION_KEY = 'user_medals_cache_expiration';
const MEDALS_CACHE_DURATION = 24 * 60 * 60 * 1000; // 缓存有效期，默认1天

/**
 * 获取用户勋章数据
 * @param {String} userOpenId - 用户OpenID
 * @param {Boolean} useCacheIfAvailable - 是否优先使用缓存
 * @returns {Promise<Object>} 包含勋章数据的Promise
 */
function getUserMedals(userOpenId, useCacheIfAvailable = true) {
  return new Promise((resolve, reject) => {
    // 如果可以使用缓存，且缓存存在并未过期，则使用缓存
    if (useCacheIfAvailable) {
      const cachedData = wx.getStorageSync(MEDALS_CACHE_KEY);
      const cacheExpiration = wx.getStorageSync(MEDALS_CACHE_EXPIRATION_KEY);
      const now = Date.now();

      if (cachedData && cacheExpiration && now < cacheExpiration) {
        console.log('使用缓存的勋章数据');
        resolve(cachedData);
        return;
      }
    }

    // 缓存不可用或已过期，从云函数获取
    console.log('从云函数获取勋章数据');
    wx.cloud.callFunction({
      name: 'getMedals',
      data: {
        userOpenId: userOpenId
      }
    })
      .then(res => {
        if (res.result && res.result.success) {
          const medalsData = res.result.data || [];
          const earnedCount = res.result.earnedCount || 0;
          
          // 更新缓存
          const cacheData = {
            medals: medalsData,
            earnedCount: earnedCount
          };
          
          wx.setStorageSync(MEDALS_CACHE_KEY, cacheData);
          wx.setStorageSync(MEDALS_CACHE_EXPIRATION_KEY, Date.now() + MEDALS_CACHE_DURATION);
          
          resolve(cacheData);
        } else {
          console.error('获取勋章数据失败:', res.result ? res.result.error : '未知错误');
          reject(new Error(res.result ? res.result.error : '获取勋章数据失败'));
        }
      })
      .catch(err => {
        console.error('调用获取勋章云函数失败:', err);
        reject(err);
      });
  });
}

/**
 * 清除勋章缓存
 */
function clearMedalsCache() {
  wx.removeStorageSync(MEDALS_CACHE_KEY);
  wx.removeStorageSync(MEDALS_CACHE_EXPIRATION_KEY);
  console.log('勋章缓存已清除');
}

/**
 * 下载勋章图片到本地
 * @param {Array} medals - 勋章数据数组
 * @returns {Promise<Object>} 包含勋章ID到本地图片路径映射的Promise
 */
function downloadMedalImages(medals) {
  return new Promise((resolve, reject) => {
    if (!medals || !Array.isArray(medals) || medals.length === 0) {
      resolve({});
      return;
    }
    
    // 获取缓存的图片路径
    const MEDAL_IMAGES_CACHE_KEY = 'medal_images_cache';
    const cachedImages = wx.getStorageSync(MEDAL_IMAGES_CACHE_KEY) || {};
    
    // 需要下载的图片列表
    const downloadList = [];
    
    // 检查哪些图片需要下载
    medals.forEach(medal => {
      if (medal.imageFileID && !cachedImages[medal.imageFileID]) {
        downloadList.push({
          fileID: medal.imageFileID,
          medalId: medal._id
        });
      }
    });
    
    if (downloadList.length === 0) {
      // 所有图片都已缓存
      resolve(cachedImages);
      return;
    }
    
    // 创建下载任务
    const downloadPromises = downloadList.map(item => {
      return new Promise((resolveDownload, rejectDownload) => {
        wx.cloud.downloadFile({
          fileID: item.fileID,
          success: res => {
            // 下载成功，保存到缓存
            cachedImages[item.fileID] = res.tempFilePath;
            resolveDownload({
              fileID: item.fileID,
              tempFilePath: res.tempFilePath
            });
          },
          fail: err => {
            console.error(`下载勋章图片失败 ${item.fileID}:`, err);
            rejectDownload(err);
          }
        });
      });
    });
    
    // 等待所有下载任务完成
    Promise.all(downloadPromises)
      .then(results => {
        // 更新缓存
        wx.setStorageSync(MEDAL_IMAGES_CACHE_KEY, cachedImages);
        resolve(cachedImages);
      })
      .catch(err => {
        console.error('下载勋章图片失败:', err);
        // 即使部分下载失败，仍返回已缓存的图片
        resolve(cachedImages);
      });
  });
}

module.exports = {
  getUserMedals,
  clearMedalsCache,
  downloadMedalImages
};
