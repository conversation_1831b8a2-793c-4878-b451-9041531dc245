// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 初始模板衣物数据
const initialClothesTemplates = [
  {
    name: '基础白T恤',
    type: 'top',
    category: 'T恤',
    color: '#FFFFFF',
    season: ['春季', '夏季', '秋季'],
    tags: ['基础款', '百搭'],
    imageUrl: 'cloud://prod-0gws2lgj0f0e077e.7072-prod-0gws2lgj0f0e077e-1320088/templates/white_tshirt.png',
    thumbnailUrl: 'cloud://prod-0gws2lgj0f0e077e.7072-prod-0gws2lgj0f0e077e-1320088/templates/white_tshirt_thumb.png',
    createTime: new Date(),
    updateTime: new Date(),
    isTemplate: true
  },
  {
    name: '蓝色牛仔裤',
    type: 'bottom',
    category: '牛仔裤',
    color: '#1E90FF',
    season: ['春季', '秋季', '冬季'],
    tags: ['经典', '百搭'],
    imageUrl: 'cloud://prod-0gws2lgj0f0e077e.7072-prod-0gws2lgj0f0e077e-1320088/templates/blue_jeans.png',
    thumbnailUrl: 'cloud://prod-0gws2lgj0f0e077e.7072-prod-0gws2lgj0f0e077e-1320088/templates/blue_jeans_thumb.png',
    createTime: new Date(),
    updateTime: new Date(),
    isTemplate: true
  },
  {
    name: '黑色连帽衫',
    type: 'top',
    category: '卫衣',
    color: '#000000',
    season: ['春季', '秋季', '冬季'],
    tags: ['休闲', '保暖'],
    imageUrl: 'cloud://prod-0gws2lgj0f0e077e.7072-prod-0gws2lgj0f0e077e-1320088/templates/black_hoodie.png',
    thumbnailUrl: 'cloud://prod-0gws2lgj0f0e077e.7072-prod-0gws2lgj0f0e077e-1320088/templates/black_hoodie_thumb.png',
    createTime: new Date(),
    updateTime: new Date(),
    isTemplate: true
  },
  {
    name: '灰色休闲鞋',
    type: 'shoes',
    category: '休闲鞋',
    color: '#808080',
    season: ['春季', '夏季', '秋季', '冬季'],
    tags: ['百搭', '舒适'],
    imageUrl: 'cloud://prod-0gws2lgj0f0e077e.7072-prod-0gws2lgj0f0e077e-1320088/templates/grey_shoes.png',
    thumbnailUrl: 'cloud://prod-0gws2lgj0f0e077e.7072-prod-0gws2lgj0f0e077e-1320088/templates/grey_shoes_thumb.png',
    createTime: new Date(),
    updateTime: new Date(),
    isTemplate: true
  }
];

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  // 优先使用传入的用户ID，如果没有则使用当前调用者的OpenID
  const userOpenId = event.userOpenId || wxContext.OPENID
  
  if (!userOpenId) {
    return {
      success: false,
      error: 'openid_missing',
      message: '未能获取有效的用户ID'
    }
  }
  
  try {
    console.log(`开始为用户添加初始衣物模板，OpenID: ${userOpenId}`)
    
    // 检查用户是否已有衣物
    const clothesCollection = db.collection('clothes')
    const clothesResult = await clothesCollection.where({
      _openid: userOpenId
    }).count()
    
    // 如果用户已经有衣物，则不添加模板
    if (clothesResult.total > 0) {
      console.log(`用户已有 ${clothesResult.total} 件衣物，无需添加模板`)
      return {
        success: true,
        message: '用户已有衣物，跳过添加模板',
        addedCount: 0
      }
    }
    
    // 准备向用户添加模板衣物
    const clothesToAdd = initialClothesTemplates.map(item => {
      return {
        ...item,
        _openid: userOpenId,  // 设置所有者为当前用户
      }
    })
    
    // 一次性添加所有模板衣物
    const addPromises = clothesToAdd.map(item => {
      return clothesCollection.add({
        data: item
      })
    })
    
    const addResults = await Promise.all(addPromises)
    
    console.log(`成功为用户添加 ${addResults.length} 件初始衣物模板`)
    
    return {
      success: true,
      message: '成功添加初始衣物模板',
      addedCount: addResults.length,
      addedIds: addResults.map(res => res._id)
    }
    
  } catch (err) {
    console.error('添加初始衣物模板失败:', err)
    return {
      success: false,
      error: 'db_error',
      message: '数据库操作失败',
      detail: err.message
    }
  }
} 