// 会员兑换页面
const app = getApp()
const utils = require('../../../util/util')

Page({
  data: {
    redeemCode: '',         // 兑换码输入
    isRedeeming: false,     // 是否正在兑换
    showResult: false,      // 是否显示兑换结果
    redeemResult: null,     // 兑换结果
  },
  
  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '会员兑换'
    });
  },
  
  // 处理输入变化
  onInputChange: function(e) {
    this.setData({
      redeemCode: e.detail.value
    });
  },
  
  // 兑换会员码
  redeemCode: function() {
    const code = this.data.redeemCode.trim();
    
    // 验证输入
    if (!code) {
      wx.showToast({
        title: '请输入兑换码',
        icon: 'none'
      });
      return;
    }
    
    // 设置兑换中状态
    this.setData({
      isRedeeming: true
    });
    
    // 显示加载提示
    wx.showLoading({
      title: '兑换中...',
      mask: true
    });
    
    // 调用云函数验证兑换码
    wx.cloud.callFunction({
      name: 'redeemMembershipCode',
      data: {
        code: code
      },
      success: res => {
        console.log('会员兑换结果:', res.result);
        
        // 隐藏loading
        wx.hideLoading();
        
        // 设置兑换结果
        this.setData({
          redeemResult: res.result,
          showResult: true,
          isRedeeming: false
        });
        
        // 如果兑换成功，清空兑换码输入
        if (res.result && res.result.success) {
          this.setData({
            redeemCode: ''
          });
          
          // 刷新用户信息
          wx.removeStorageSync('userInfoCache');
          wx.removeStorageSync('userInfoCacheExpiration');
        }
      },
      fail: err => {
        console.error('调用云函数失败:', err);
        
        // 隐藏loading
        wx.hideLoading();
        
        // 设置兑换结果为失败
        this.setData({
          redeemResult: {
            success: false,
            message: '网络错误，请稍后再试'
          },
          showResult: true,
          isRedeeming: false
        });
      }
    });
  },
  
  // 隐藏兑换结果
  hideResult: function() {
    this.setData({
      showResult: false
    });
  },
  
  // 格式化日期
  formatDate: function(dateStr) {
    if (!dateStr) return '';
    
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  }
})
