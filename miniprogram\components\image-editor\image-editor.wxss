/* 图片编辑组件样式 */
.image-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
}

.modal-content {
  position: relative;
  width: 95%;
  max-width: 700rpx;
  max-height: 95%;
  background-color: #ffffff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 标题栏 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  border-radius: 50%;
  background-color: #f5f5f5;
}

/* 编辑区域 */
.edit-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15rpx;
  overflow: hidden;
  min-height: 0;
}

.canvas-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
  min-height: 450rpx;
  max-height: 600rpx;
  position: relative;
}

.debug-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  z-index: 1;
}

.canvas-status {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10rpx;
  border-radius: 5rpx;
  font-size: 24rpx;
  z-index: 3;
}

.edit-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  background-color: transparent;
  z-index: 2;
}

/* 工具栏 */
.toolbar {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  max-height: 280rpx;
  overflow-y: auto;
  flex-shrink: 0;
}

.tool-row {
  display: flex;
  align-items: center;
  gap: 30rpx;
  flex-wrap: wrap;
  min-height: 80rpx;
}

.tool-group {
  display: flex;
  align-items: center;
  gap: 15rpx;
  flex-wrap: nowrap;
  min-height: 80rpx;
  flex-shrink: 0;
}

.tool-label {
  font-size: 26rpx;
  color: #666;
  min-width: 60rpx;
  flex-shrink: 0;
}

.rotate-buttons {
  display: flex;
  gap: 10rpx;
}

.rotate-buttons .tool-btn {
  width: 80rpx;
  height: 80rpx;
  flex-direction: column;
  transition: all 0.2s ease;
}

.rotate-buttons .tool-btn:hover {
  transform: scale(1.05);
  background-color: #e6f3ff;
}

.rotate-buttons .icon {
  font-size: 24rpx;
}

.rotate-buttons .btn-label {
  font-size: 20rpx;
  margin-top: 4rpx;
  line-height: 1;
}

/* 镜像按钮 */
.flip-buttons {
  display: flex;
  gap: 10rpx;
}

.flip-buttons .tool-btn {
  width: 80rpx;
  height: 80rpx;
  flex-direction: column;
  transition: all 0.2s ease;
}

.flip-buttons .tool-btn:hover {
  transform: scale(1.05);
}

.flip-buttons .tool-btn.active {
  background-color: #007aff;
  color: #ffffff;
  border-color: #007aff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.flip-buttons .btn-label {
  font-size: 20rpx;
  margin-top: 4rpx;
  line-height: 1;
}

.flip-buttons .icon {
  font-size: 24rpx;
}

/* 历史按钮 */
.history-buttons {
  display: flex;
  gap: 10rpx;
}

.history-buttons .history-btn {
  width: 80rpx;
  height: 80rpx;
  flex-direction: column;
  transition: all 0.2s ease;
}

.history-buttons .history-btn:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #eee;
  opacity: 0.6;
}

.history-buttons .history-btn:not(:disabled):hover {
  transform: scale(1.05);
  background-color: #e6f3ff;
}

.history-buttons .btn-label {
  font-size: 20rpx;
  margin-top: 4rpx;
  line-height: 1;
}

.history-buttons .icon {
  font-size: 24rpx;
}

.tool-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 24rpx;
  color: #333;
  padding: 0;
  margin: 0;
}

.tool-btn:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #eee;
}

.tool-btn.active {
  background-color: #007aff;
  color: #ffffff;
  border-color: #007aff;
}

.icon {
  font-size: 28rpx;
}

/* 画笔控制 */
.brush-controls {
  display: flex;
  align-items: center;
  gap: 15rpx;
  flex: 1;
  max-width: 400rpx;
}

.brush-size-control {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex: 1;
}

.size-label {
  font-size: 24rpx;
  color: #666;
}

/* 自定义画笔大小滑块 */
.brush-slider-container {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex: 1;
  min-width: 200rpx;
}

.brush-slider-track {
  position: relative;
  flex: 1;
  height: 6rpx;
  background-color: #e0e0e0;
  border-radius: 3rpx;
  cursor: pointer;
  padding: 15rpx 0;
  margin: -15rpx 0;
}

.brush-slider-progress {
  position: absolute;
  top: 15rpx;
  left: 0;
  height: 6rpx;
  background-color: #007aff;
  border-radius: 3rpx;
  transition: width 0.1s ease;
}

.brush-slider-thumb {
  position: absolute;
  top: 18rpx;
  width: 20rpx;
  height: 20rpx;
  background-color: #007aff;
  border: 2rpx solid #ffffff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.3);
  transition: left 0.1s ease;
}

.brush-slider-thumb:active {
  transform: translate(-50%, -50%) scale(1.2);
}

.brush-slider-track.touching .brush-slider-thumb {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.4);
}

.brush-slider-track.touching .brush-slider-progress {
  background-color: #0056d6;
}

.brush-size-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50rpx;
  height: 50rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  border: 1rpx solid #ddd;
}

.brush-preview-circle {
  background-color: rgba(0, 122, 255, 0.6);
  border-radius: 50%;
  min-width: 10rpx;
  min-height: 10rpx;
  max-width: 45rpx;
  max-height: 45rpx;
}

.brush-size-value {
  font-size: 24rpx;
  color: #333;
  min-width: 40rpx;
  text-align: center;
  font-weight: bold;
}

/* 底部按钮 */
.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  border: none;
  margin: 0;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #007aff;
  color: #ffffff;
}
