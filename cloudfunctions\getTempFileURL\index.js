// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 云函数入口函数
exports.main = async (event) => {
  try {
    // 支持两种参数格式：fileIdList 或 fileList
    const fileList = event.fileList || event.fileIdList || [];
    
    // 检查文件列表是否为空
    if (!fileList || fileList.length === 0) {
      console.error('文件列表为空');
      return {
        success: false,
        error: '文件列表为空',
        fileList: []
      };
    }
    
    console.log(`开始获取 ${fileList.length} 个文件的临时URL`);
    
    // 由于云函数有100个文件的限制，需要分批处理
    const MAX_BATCH_SIZE = 50;
    const batches = [];
    
    // 将文件列表分成多个批次
    for (let i = 0; i < fileList.length; i += MAX_BATCH_SIZE) {
      const batch = fileList.slice(i, i + MAX_BATCH_SIZE);
      batches.push(batch);
    }
    
    console.log(`分成 ${batches.length} 批处理`);
    
    // 处理每个批次
    const results = [];
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`处理第 ${i+1}/${batches.length} 批，包含 ${batch.length} 个文件`);
      
      try {
        const result = await cloud.getTempFileURL({
          fileList: batch,
        });
        
        if (result && result.fileList) {
          results.push(...result.fileList);
        }
      } catch (err) {
        console.error(`处理第 ${i+1} 批时出错:`, err);
      }
    }
    
    console.log(`成功获取 ${results.length}/${fileList.length} 个文件的临时URL`);
    
    return {
      success: true,
      fileList: results
    };
  } catch (err) {
    console.error('获取临时文件URL失败:', err);
    return {
      success: false,
      error: err.message || '获取临时文件URL失败',
      fileList: []
    };
  }
}
