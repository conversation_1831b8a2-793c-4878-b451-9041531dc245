// 初始化勋章数据脚本
// 用于在管理员页面中初始化勋章数据

/**
 * 初始化勋章数据
 * 创建基础勋章并上传勋章图片到云存储
 */
function initMedalsData() {
  wx.showLoading({
    title: '初始化勋章数据',
    mask: true
  });

  // 默认勋章数据
  const defaultMedals = [
    {
      _id: 'de2bd33968045d830055fd414381224f', // 内测勋章固定ID
      name: '内测用户',
      description: '感谢您参与内测，为衣橱管理提供宝贵意见！',
      category: 'welcome',
      isActive: true,
      order: 0,
      imageName: 'medal_beta_tester.png'
    },
    {
      name: '初来乍到',
      description: '欢迎加入衣橱管理，开始你的时尚之旅！',
      category: 'welcome',
      isActive: true,
      order: 1,
      imageName: '初来乍到.jpg'
    },
    {
      name: '衣橱老手',
      description: '添加了100件衣物，构建你的数字衣橱。',
      category: 'wardrobe',
      isActive: true,
      order: 2,
      imageName: '衣服.jpg'
    },
    {
      name: '搭配达人',
      description: '创建了10套搭配，展现你的时尚品味。',
      category: 'outfit',
      isActive: true,
      order: 3,
      imageName: '穿搭.jpg'
    },
    {
      name: 'AI探索者',
      description: '使用AI功能创建了第一套搭配，拥抱科技与时尚的结合。',
      category: 'ai',
      isActive: true,
      order: 4,
      imageName: 'AI探索者.jpg'
    },
    {
      name: '最欧养猫人',
      description: '全部用户中前20名率先集齐了第一套主题卡片。',
      category: ' ',
      isActive: true,
      order: 5,
      imageName: '最欧养猫人.jpg'
    },
    {
      name: '非酋养猫人',
      description: '飞扑王',
      category: 'login',
      isActive: true,
      order: 6,
      imageName: '非酋养猫人.jpg'
    }
  ];

  // 上传勋章图片到云存储
  const uploadPromises = defaultMedals.map(medal => {
    return uploadMedalImage(medal.imageName);
  });

  // 等待所有图片上传完成
  Promise.all(uploadPromises)
    .then(fileIDs => {
      console.log('所有勋章图片上传成功:', fileIDs);

      // 将fileID添加到勋章数据中
      const medalsWithImages = defaultMedals.map((medal, index) => {
        return {
          ...medal,
          imageFileID: fileIDs[index],
          createTime: new Date()
        };
      });

      // 将勋章数据保存到数据库
      return saveMedalsToDatabase(medalsWithImages);
    })
    .then(() => {
      wx.hideLoading();
      wx.showToast({
        title: '勋章初始化成功',
        icon: 'success'
      });
    })
    .catch(err => {
      console.error('初始化勋章数据失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '初始化失败',
        icon: 'none'
      });
    });
}

/**
 * 上传勋章图片到云存储
 * @param {String} imageName - 图片文件名
 * @returns {Promise<String>} 包含fileID的Promise
 */
function uploadMedalImage(imageName) {
  return new Promise((resolve, reject) => {
    // 获取小程序基础路径
    const fileManager = wx.getFileSystemManager();
    // 使用临时文件作为中转
    // 将文件后缀改为.svg
    const svgName = imageName.replace(/\.png$/, '.svg');
    const tempFilePath = `${wx.env.USER_DATA_PATH}/${svgName}`;

    try {
      // 使用预定义的图标数据
      // 根据图片名称生成不同颜色的图标
      const colorMap = {
        'medal_beta_tester.png': '#FFD700', // 金色内测勋章
        'medal_welcome.png': '#FF9800',
        'medal_first_cloth.png': '#4CAF50',
        'medal_first_outfit.png': '#2196F3',
        'medal_organizer.png': '#9C27B0',
        'medal_fashion_blogger.png': '#E91E63',
        'medal_declutter.png': '#795548',
        'medal_seasonal_storage.png': '#607D8B',
        'medal_login_streak.png': '#FFC107',
        'medal_ai_explorer.png': '#00BCD4',
        'medal_wardrobe_analyst.png': '#3F51B5',
        '内测.png': '#FFD700', // 兼容原来的文件名
        '初来乍到.jpg': '#FF9800',
        '衣服.jpg': '#4CAF50',
        '穿搭.jpg': '#2196F3',
        'AI探索者.jpg': '#00BCD4',
        '最欧养猫人.jpg': '#9C27B0',
        '非酒养猫人.jpg': '#795548'
      };

      // 生成一个默认图标的base64数据
      // 这里使用一个简单的彩色圆形SVG
      const color = colorMap[imageName] || '#FF9800';
      const letter = imageName.split('_')[1] ? imageName.split('_')[1][0].toUpperCase() : 'M';

      // 创建SVG图标
      const svgContent = `
        <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
          <circle cx="100" cy="100" r="80" fill="${color}" stroke="white" stroke-width="10"/>
          <text x="100" y="100" font-family="Arial" font-size="80" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle">${letter}</text>
        </svg>
      `;

      // 将SVG直接写入文件
      fileManager.writeFileSync(
        tempFilePath,
        svgContent,
        'utf8'
      );

      // 不需要转换为base64，直接使用SVG文件

      // 云存储路径 - 保持原始的png后缀以保持兼容性
      const cloudPath = `medals/${imageName}`;

      // 上传图片到云存储
      wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: tempFilePath,
        success: res => {
          console.log('上传勋章图片成功:', res.fileID);
          // 清理临时文件
          try {
            fileManager.unlinkSync(tempFilePath);
          } catch (e) {
            console.error('清理临时文件失败:', e);
          }
          resolve(res.fileID);
        },
        fail: err => {
          console.error('上传勋章图片失败:', err);
          reject(err);
        }
      });
    } catch (err) {
      console.error('创建临时图片失败:', err);
      reject(err);
    }
  });
}

/**
 * 将勋章数据保存到数据库
 * @param {Array} medals - 勋章数据数组
 * @returns {Promise} 保存结果Promise
 */
function saveMedalsToDatabase(medals) {
  const db = wx.cloud.database();
  const _ = db.command;

  // 先检查medals集合是否存在数据
  return db.collection('medals')
    .count()
    .then(res => {
      if (res.total > 0) {
        // 如果已有数据，先清空集合
        return db.collection('medals')
          .where({
            _id: _.exists(true)
          })
          .remove();
      }
      return Promise.resolve();
    })
    .then(() => {
      // 批量添加勋章数据
      const addPromises = medals.map(medal => {
        // 如果勋章有自定义ID，使用doc方法指定ID
        if (medal._id) {
          const medalId = medal._id;
          // 删除_id字段，因为不能在data中包含_id
          const { _id, ...medalData } = medal;
          return db.collection('medals').doc(medalId).set({
            data: medalData
          });
        } else {
          // 没有自定义ID，使用普通的add方法
          return db.collection('medals').add({
            data: medal
          });
        }
      });

      return Promise.all(addPromises);
    });
}

module.exports = {
  initMedalsData
};
