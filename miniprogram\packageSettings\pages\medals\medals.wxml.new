<view class="page {{themeStyle ? 'page-' + themeStyle : ''}}">
  <view class="page-header">
    <view class="page-title" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}};">我的勋章</view>
  </view>

  <view class="medals-container">
    <!-- 已获得的勋章 -->
    <view class="medals-section">
      <view class="medals-section-title" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}};">
        已获得的勋章 ({{earnedMedals.length}})
      </view>
      <view class="medals-grid">
        <block wx:if="{{earnedMedals.length > 0}}">
          <view class="medal-item" wx:for="{{earnedMedals}}" wx:key="_id" bindtap="viewMedalDetail" data-id="{{item._id}}">
            <view class="medal-image-container">
              <image
                class="medal-image"
                src="{{medalImages[item.imageFileID] || '/image/placeholder.png'}}"
                mode="aspectFit"
                catchtap="viewMedalImage"
                data-id="{{item._id}}"
              ></image>
            </view>
            <view class="medal-name" style="color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}};">{{item.name}}</view>
            <view class="medal-date" style="color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.mediumGray)}};">
              {{item.earnedTime || '未知日期'}}
            </view>
          </view>
        </block>
        <view class="no-medals-message" wx:if="{{earnedMedals.length === 0}}" style="color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.mediumGray)}};">
          您还没有获得任何勋章，继续努力吧！
        </view>
      </view>
    </view>

    <!-- 未获得的勋章 -->
    <view class="medals-section">
      <view class="medals-section-title" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}};">
        未获得的勋章 ({{unearnedMedals.length}})
      </view>
      <view class="medals-grid">
        <block wx:if="{{unearnedMedals.length > 0}}">
          <view class="medal-item medal-item-unearned" wx:for="{{unearnedMedals}}" wx:key="_id" bindtap="viewMedalDetail" data-id="{{item._id}}">
            <view class="medal-image-container">
              <image
                class="medal-image medal-image-unearned"
                src="{{medalImages[item.imageFileID] || '/image/placeholder.png'}}"
                mode="aspectFit"
                catchtap="viewMedalImage"
                data-id="{{item._id}}"
              ></image>
            </view>
            <view class="medal-name" style="color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}};">{{item.name}}</view>
            <view class="medal-hint" style="color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.mediumGray)}};">
              继续努力
            </view>
          </view>
        </block>
        <view class="no-medals-message" wx:if="{{unearnedMedals.length === 0}}" style="color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.mediumGray)}};">
          恭喜您已获得所有勋章！
        </view>
      </view>
    </view>
  </view>

  <!-- 勋章详情弹窗 -->
  <view class="medal-detail-modal {{showMedalDetail ? 'visible' : 'hidden'}}" bindtap="closeMedalDetail">
    <view class="medal-detail-container" catchtap="stopPropagation" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.95)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.97)' : 'white')}};">
      <!-- 标题 -->
      <view class="medal-detail-header">
        <view class="medal-detail-title">
          <text class="medal-title-text">{{currentMedal.name}}</text>
        </view>
      </view>

      <!-- 勋章图片 -->
      <view class="medal-detail-image-container">
        <image
          class="medal-detail-image {{!currentMedal.earned ? 'medal-detail-image-unearned' : ''}}"
          src="{{medalImages[currentMedal.imageFileID] || '/image/placeholder.png'}}"
          mode="aspectFit"
          catchtap="viewMedalImage"
        ></image>
        <view class="medal-image-hint">点击图片可放大查看</view>
      </view>

      <!-- 勋章信息卡片 -->
      <view class="medal-detail-info-card">
        <!-- 勋章描述 -->
        <view class="medal-description-container">
          "{{currentMedal.description}}"
        </view>
        
        <!-- 未获得提示 -->
        <view class="medal-hint-container" wx:if="{{!currentMedal.earned}}">
          继续努力，完成相应任务即可获得此勋章！
        </view>
        
        <!-- 勋章编号和获得时间 -->
        <view class="medal-number-container" wx:if="{{currentMedal.earned && currentMedal.globalNumber}}">
          <view class="medal-global-number">
            您是获此勋章的{{currentMedal.globalNumber}}号用户
          </view>
          <view class="medal-earned-time">
            获得时间: {{currentMedal.earnedTime || '未知日期'}}
          </view>
        </view>
      </view>

      <!-- 关闭按钮 -->
      <view class="medal-detail-close-button" catchtap="closeMedalDetail">×</view>
    </view>
  </view>
</view>
