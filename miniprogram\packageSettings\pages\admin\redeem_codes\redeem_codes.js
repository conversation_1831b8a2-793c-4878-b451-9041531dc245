// 兑换码管理页面
const app = getApp()
const utils = require('../../../../util/util')

Page({
  data: {
    isAdmin: false,         // 是否是管理员
    userOpenId: '',         // 用户OpenID
    codeList: [],           // 兑换码列表
    redeemHistory: [],      // 兑换历史
    showCreateModal: false, // 是否显示创建兑换码弹窗
    showCodeDetail: false,  // 是否显示兑换码详情弹窗
    selectedCode: null,     // 当前选中的兑换码
    showBatchModal: false,  // 是否显示批量生成弹窗
    batchResults: [],       // 批量生成的结果

    // 创建兑换码的表单数据
    formData: {
      name: '',              // 兑换码名称
      description: '',       // 兑换码描述
      rewardType: 'clothes', // 奖励类型: clothes, outfits, both
      rewardAmount: 5,       // 奖励数量(当rewardType为both时不使用)
      clothesRewardAmount: 5, // 衣物奖励数量(仅当rewardType为both时使用)
      outfitsRewardAmount: 2, // 搭配奖励数量(仅当rewardType为both时使用)
      isOneTime: true,       // 是否为一次性兑换码
      expirationDays: 30     // 有效期天数
    },

    // 批量生成兑换码的表单数据
    batchFormData: {
      prefix: '',            // 兑换码前缀
      count: 10,             // 生成数量
      nameTemplate: '扩容兑换码-%d', // 名称模板，%d会被替换为序号
      rewardType: 'clothes', // 奖励类型
      rewardAmount: 5,       // 奖励数量
      clothesRewardAmount: 5, // 衣物奖励数量
      outfitsRewardAmount: 2, // 搭配奖励数量
      isOneTime: true,       // 是否为一次性兑换码
      expirationDays: 30     // 有效期天数
    },

    // 会员兑换码的表单数据
    membershipFormData: {
      name: '',              // 兑换码名称
      description: '',       // 兑换码描述
      membershipType: 'monthly', // 会员类型: weekly, monthly, quarterly, yearly
      isOneTime: true,       // 是否为一次性兑换码
      expirationDays: 30     // 有效期天数
    },

    // 批量生成会员兑换码的表单数据
    batchMembershipFormData: {
      prefix: '',            // 兑换码前缀
      count: 10,             // 生成数量
      nameTemplate: '会员兑换码-%d', // 名称模板，%d会被替换为序号
      membershipType: 'monthly', // 会员类型: weekly, monthly, quarterly, yearly
      isOneTime: true,       // 是否为一次性兑换码
      expirationDays: 30     // 有效期天数
    },

    // 批量会员兑换码生成结果
    batchMembershipResults: [],

    // 批量会员兑换码弹窗显示状态
    showBatchMembershipModal: false,

    // 是否正在批量生成会员兑换码
    isBatchGeneratingMembership: false,

    // 页面相关
    isGenerating: false,     // 是否正在生成兑换码
    isBatchGenerating: false, // 是否正在批量生成兑换码
    isGeneratingMembership: false, // 是否正在生成会员兑换码
    showMembershipModal: false, // 是否显示会员兑换码弹窗
    isLoading: true,         // 是否正在加载数据
  },

  onLoad: function() {
    // 获取用户信息，判断是否是管理员
    this.getUserInfo();
  },

  // 获取用户信息
  getUserInfo: function() {
    const that = this;
    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    app.getUserOpenId(function(err, openid) {
      if (err) {
        wx.hideLoading();
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
        return;
      }

      // 保存用户OpenID
      that.setData({
        userOpenId: openid
      });

      // 检查用户是否是管理员
      that.checkAdminPermission(openid);
    });
  },

  // 检查用户是否具有管理员权限
  checkAdminPermission: function(openid) {
    const that = this;
    const db = wx.cloud.database();

    db.collection('adminUsers')
      .where({
        _openid: openid
      })
      .get()
      .then(res => {
        wx.hideLoading();

        if (res.data && res.data.length > 0) {
          // 是管理员，加载兑换码列表
          that.setData({
            isAdmin: true,
            isLoading: false
          });
          that.loadRedeemCodes();
          that.loadRedeemHistory();
        } else {
          // 不是管理员，显示无权限提示
          that.setData({
            isAdmin: false,
            isLoading: false
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('检查管理员权限失败:', err);
        wx.showToast({
          title: '权限验证失败',
          icon: 'none'
        });
      });
  },

  // 加载兑换码列表
  loadRedeemCodes: function() {
    const that = this;
    const db = wx.cloud.database();

    db.collection('redemptionCodes')
      .orderBy('createTime', 'desc')
      .get()
      .then(res => {
        that.setData({
          codeList: res.data
        });
      })
      .catch(err => {
        console.error('加载兑换码列表失败:', err);
        wx.showToast({
          title: '加载兑换码失败',
          icon: 'none'
        });
      });
  },

  // 加载兑换历史
  loadRedeemHistory: function() {
    const that = this;
    const db = wx.cloud.database();

    db.collection('redemptionHistory')
      .orderBy('redeemedAt', 'desc')
      .limit(50) // 最多显示50条历史记录
      .get()
      .then(res => {
        that.setData({
          redeemHistory: res.data
        });
      })
      .catch(err => {
        console.error('加载兑换历史失败:', err);
      });
  },

  // 打开创建兑换码弹窗
  showCreateCodeModal: function() {
    // 重置表单数据
    this.setData({
      formData: {
        name: '',
        description: '',
        rewardType: 'clothes',
        rewardAmount: 5,
        clothesRewardAmount: 5,
        outfitsRewardAmount: 2,
        isOneTime: true,
        expirationDays: 30
      },
      showCreateModal: true
    });
  },

  // 打开创建会员兑换码弹窗
  showCreateMembershipModal: function() {
    // 重置表单数据
    this.setData({
      membershipFormData: {
        name: '',
        description: '',
        membershipType: 'monthly',
        isOneTime: true,
        expirationDays: 30
      },
      showMembershipModal: true
    });
  },

  // 关闭会员兑换码弹窗
  closeMembershipModal: function() {
    this.setData({
      showMembershipModal: false
    });
  },

  // 打开批量生成会员兑换码弹窗
  showBatchMembershipModal: function() {
    // 重置表单数据和生成结果
    this.setData({
      batchMembershipFormData: {
        prefix: '',
        count: 10,
        nameTemplate: '会员兑换码-%d',
        membershipType: 'monthly',
        isOneTime: true,
        expirationDays: 30
      },
      batchMembershipResults: [],
      showBatchMembershipModal: true
    });
  },

  // 关闭批量会员兑换码弹窗
  closeBatchMembershipModal: function() {
    this.setData({
      showBatchMembershipModal: false
    });
  },

  // 关闭创建兑换码弹窗
  closeCreateModal: function() {
    this.setData({
      showCreateModal: false
    });
  },

  // 处理表单输入变化
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    const newFormData = { ...this.data.formData };

    if (field === 'rewardAmount' || field === 'clothesRewardAmount' || field === 'outfitsRewardAmount' || field === 'expirationDays') {
      // 数字类型字段，转为数字
      newFormData[field] = parseInt(value) || 0;
    } else if (field === 'isOneTime') {
      // 布尔类型字段
      newFormData[field] = e.detail.value;
    } else {
      // 字符串类型字段
      newFormData[field] = value;
    }

    this.setData({
      formData: newFormData
    });
  },

  // 处理奖励类型变化
  onRewardTypeChange: function(e) {
    const newFormData = { ...this.data.formData };
    newFormData.rewardType = e.detail.value;
    this.setData({
      formData: newFormData
    });
  },

  // 处理一次性兑换码切换
  onOneTimeChange: function(e) {
    const newFormData = { ...this.data.formData };
    newFormData.isOneTime = e.detail.value;
    this.setData({
      formData: newFormData
    });
  },

  // 切换会员兑换码类型
  onMembershipOneTimeChange: function(e) {
    this.setData({
      'membershipFormData.isOneTime': e.detail.value
    });
  },

  // 切换会员类型
  onMembershipTypeChange: function(e) {
    this.setData({
      'membershipFormData.membershipType': e.detail.value
    });
  },

  // 会员兑换码表单输入变化
  onMembershipInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    this.setData({
      [`membershipFormData.${field}`]: value
    });
  },

  // 生成随机兑换码
  generateRandomCode: function() {
    // 生成8位随机字母数字组合
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < 8; i++) {
      const randomIndex = Math.floor(Math.random() * chars.length);
      code += chars[randomIndex];
    }

    // 插入分隔符，如：ABCD-1234
    code = code.slice(0, 4) + '-' + code.slice(4);

    return code;
  },

  // 创建新兑换码
  createRedeemCode: function() {
    const formData = this.data.formData;

    // 表单验证
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入兑换码名称',
        icon: 'none'
      });
      return;
    }

    if (formData.rewardType === 'clothes' || formData.rewardType === 'outfits') {
      if (formData.rewardAmount <= 0) {
        wx.showToast({
          title: '奖励数量必须大于0',
          icon: 'none'
        });
        return;
      }
    } else if (formData.rewardType === 'both') {
      if (formData.clothesRewardAmount <= 0 || formData.outfitsRewardAmount <= 0) {
        wx.showToast({
          title: '奖励数量必须大于0',
          icon: 'none'
        });
        return;
      }
    }

    if (formData.expirationDays <= 0) {
      wx.showToast({
        title: '有效期必须大于0天',
        icon: 'none'
      });
      return;
    }

    // 设置为生成中状态
    this.setData({
      isGenerating: true
    });

    // 显示加载提示
    wx.showLoading({
      title: '生成中...',
      mask: true
    });

    // 生成兑换码
    const code = this.generateRandomCode();

    // 生成过期时间
    const now = new Date();
    const expirationDate = new Date(now.getTime() + formData.expirationDays * 24 * 60 * 60 * 1000);

    const db = wx.cloud.database();

    // 检查兑换码是否已存在
    db.collection('redemptionCodes')
      .where({
        code: code
      })
      .count()
      .then(res => {
        if (res.total > 0) {
          // 兑换码已存在，重新生成
          wx.hideLoading();
          this.setData({
            isGenerating: false
          });
          this.createRedeemCode();
          return;
        }

        // 构建兑换码数据
        const codeData = {
          code: code,
          name: formData.name,
          description: formData.description || '',
          rewardType: formData.rewardType,
          rewardAmount: formData.rewardAmount,
          clothesRewardAmount: formData.clothesRewardAmount,
          outfitsRewardAmount: formData.outfitsRewardAmount,
          isOneTime: formData.isOneTime,
          isActive: true,
          usedCount: 0,
          createTime: db.serverDate(),
          expirationDate: expirationDate,
          createdBy: this.data.userOpenId
        };

        // 保存到数据库
        db.collection('redemptionCodes')
          .add({
            data: codeData
          })
          .then(res => {
            // 隐藏loading
            wx.hideLoading();

            // 关闭弹窗
            this.setData({
              isGenerating: false,
              showCreateModal: false
            });

            // 显示成功提示
            wx.showToast({
              title: '兑换码创建成功',
              icon: 'success'
            });

            // 刷新兑换码列表
            this.loadRedeemCodes();

            // 复制兑换码到剪贴板
            wx.setClipboardData({
              data: code,
              success: function() {
                wx.showToast({
                  title: '兑换码已复制',
                  icon: 'success'
                });
              }
            });
          })
          .catch(err => {
            // 隐藏loading
            wx.hideLoading();

            // 恢复状态
            this.setData({
              isGenerating: false
            });

            console.error('创建兑换码失败:', err);
            wx.showToast({
              title: '创建失败，请重试',
              icon: 'none'
            });
          });
      })
      .catch(err => {
        wx.hideLoading();
        this.setData({
          isGenerating: false
        });
        console.error('检查兑换码失败:', err);
        wx.showToast({
          title: '创建失败，请重试',
          icon: 'none'
        });
      });
  },

  // 显示兑换码详情
  showCodeDetailModal: function(e) {
    const codeId = e.currentTarget.dataset.id;
    const code = this.data.codeList.find(item => item._id === codeId);

    if (code) {
      this.setData({
        selectedCode: code,
        showCodeDetail: true
      });
    }
  },

  // 关闭兑换码详情弹窗
  closeDetailModal: function() {
    this.setData({
      showCodeDetail: false,
      selectedCode: null
    });
  },

  // 复制兑换码
  copyCode: function(e) {
    const code = e.currentTarget.dataset.code;

    wx.setClipboardData({
      data: code,
      success: function() {
        wx.showToast({
          title: '兑换码已复制',
          icon: 'success'
        });
      }
    });
  },

  // 启用/禁用兑换码
  toggleCodeStatus: function(e) {
    const codeId = e.currentTarget.dataset.id;
    const codeIndex = this.data.codeList.findIndex(item => item._id === codeId);

    if (codeIndex === -1) {
      return;
    }

    const code = this.data.codeList[codeIndex];
    const newStatus = !code.isActive;

    wx.showLoading({
      title: newStatus ? '启用中...' : '禁用中...',
      mask: true
    });

    const db = wx.cloud.database();

    db.collection('redemptionCodes')
      .doc(codeId)
      .update({
        data: {
          isActive: newStatus
        }
      })
      .then(() => {
        wx.hideLoading();

        // 更新本地数据
        const newCodeList = [...this.data.codeList];
        newCodeList[codeIndex].isActive = newStatus;

        this.setData({
          codeList: newCodeList
        });

        // 如果当前正在查看该兑换码的详情，也更新详情数据
        if (this.data.selectedCode && this.data.selectedCode._id === codeId) {
          const newSelectedCode = { ...this.data.selectedCode, isActive: newStatus };
          this.setData({
            selectedCode: newSelectedCode
          });
        }

        wx.showToast({
          title: newStatus ? '已启用' : '已禁用',
          icon: 'success'
        });
      })
      .catch(err => {
        wx.hideLoading();
        console.error('更新兑换码状态失败:', err);
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      });
  },

  // 刷新数据
  onPullDownRefresh: function() {
    if (this.data.isAdmin) {
      // 重新加载数据
      this.loadRedeemCodes();
      this.loadRedeemHistory();
    }

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 格式化日期时间
  formatDateTime: function(dateStr) {
    return utils.formatTime(new Date(dateStr));
  },

  // 打开批量生成兑换码弹窗
  showBatchCreateModal: function() {
    // 重置表单数据
    this.setData({
      batchFormData: {
        prefix: '',
        count: 10,
        nameTemplate: '扩容兑换码-%d',
        rewardType: 'clothes',
        rewardAmount: 5,
        clothesRewardAmount: 5,
        outfitsRewardAmount: 2,
        isOneTime: true,
        expirationDays: 30
      },
      showBatchModal: true,
      batchResults: []
    });
  },

  // 关闭批量生成兑换码弹窗
  closeBatchModal: function() {
    this.setData({
      showBatchModal: false
    });
  },

  // 处理批量生成表单输入变化
  onBatchInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    const newBatchFormData = { ...this.data.batchFormData };

    if (field === 'count' || field === 'rewardAmount' || field === 'clothesRewardAmount' || field === 'outfitsRewardAmount' || field === 'expirationDays') {
      // 数字类型字段，转为数字
      newBatchFormData[field] = parseInt(value) || 0;
    } else {
      // 字符串类型字段
      newBatchFormData[field] = value;
    }

    this.setData({
      batchFormData: newBatchFormData
    });
  },

  // 处理批量生成奖励类型变化
  onBatchRewardTypeChange: function(e) {
    const newBatchFormData = { ...this.data.batchFormData };
    newBatchFormData.rewardType = e.detail.value;
    this.setData({
      batchFormData: newBatchFormData
    });
  },

  // 处理批量生成一次性兑换码切换
  onBatchOneTimeChange: function(e) {
    const newBatchFormData = { ...this.data.batchFormData };
    newBatchFormData.isOneTime = e.detail.value;
    this.setData({
      batchFormData: newBatchFormData
    });
  },

  // 处理批量会员兑换码表单输入变化
  onBatchMembershipInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    const newBatchMembershipFormData = { ...this.data.batchMembershipFormData };

    // 如果是数字类型的字段，转换为数字
    if (field === 'count' || field === 'expirationDays') {
      newBatchMembershipFormData[field] = parseInt(value) || 0;
    } else {
      newBatchMembershipFormData[field] = value;
    }

    this.setData({
      batchMembershipFormData: newBatchMembershipFormData
    });
  },

  // 处理批量会员类型变化
  onBatchMembershipTypeChange: function(e) {
    const newBatchMembershipFormData = { ...this.data.batchMembershipFormData };
    newBatchMembershipFormData.membershipType = e.detail.value;
    this.setData({
      batchMembershipFormData: newBatchMembershipFormData
    });
  },

  // 处理批量会员兑换码一次性切换
  onBatchMembershipOneTimeChange: function(e) {
    const newBatchMembershipFormData = { ...this.data.batchMembershipFormData };
    newBatchMembershipFormData.isOneTime = e.detail.value;
    this.setData({
      batchMembershipFormData: newBatchMembershipFormData
    });
  },

  // 生成带前缀的随机兑换码
  generateRandomCodeWithPrefix: function(prefix = '') {
    // 生成8位随机字母数字组合
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = prefix;

    // 确保前缀后面有一个分隔符
    if (prefix && !prefix.endsWith('-')) {
      code += '-';
    }

    // 生成随机部分
    for (let i = 0; i < 8; i++) {
      const randomIndex = Math.floor(Math.random() * chars.length);
      code += chars[randomIndex];
    }

    // 如果没有前缀，插入分隔符，如：ABCD-1234
    if (!prefix) {
      code = code.slice(0, 4) + '-' + code.slice(4);
    }

    return code;
  },

  // 创建会员兑换码
  createMembershipCode: function() {
    const formData = this.data.membershipFormData;

    // 表单验证
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入兑换码名称',
        icon: 'none'
      });
      return;
    }

    if (formData.expirationDays <= 0) {
      wx.showToast({
        title: '有效期必须大于0',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '生成中...',
      mask: true
    });

    // 标记正在生成
    this.setData({
      isGeneratingMembership: true
    });

    // 生成随机兑换码
    const code = this.generateRandomCode();

    // 计算过期时间
    const now = new Date();
    const expirationDate = new Date(now.getTime() + formData.expirationDays * 24 * 60 * 60 * 1000);

    const db = wx.cloud.database();

    // 检查兑换码是否已存在
    db.collection('redemptionCodes')
      .where({
        code: code
      })
      .count()
      .then(res => {
        if (res.total > 0) {
          // 兑换码已存在，重新生成
          wx.hideLoading();
          this.setData({
            isGeneratingMembership: false
          });
          this.createMembershipCode();
          return;
        }

        // 构建兑换码数据
        const codeData = {
          code: code,
          name: formData.name,
          description: formData.description || '',
          rewardType: 'membership',
          membershipType: formData.membershipType,
          isOneTime: formData.isOneTime,
          isActive: true,
          usedCount: 0,
          createTime: db.serverDate(),
          expirationDate: expirationDate,
          createdBy: this.data.userOpenId
        };

        // 将兑换码保存到数据库
        db.collection('redemptionCodes')
          .add({
            data: codeData
          })
          .then(res => {
            wx.hideLoading();

            // 重置生成状态
            this.setData({
              isGeneratingMembership: false
            });

            // 显示成功提示
            wx.showToast({
              title: '会员兑换码创建成功',
              icon: 'success'
            });

            // 关闭弹窗并刷新列表
            this.setData({
              showMembershipModal: false
            });

            this.loadRedeemCodes();
          })
          .catch(err => {
            wx.hideLoading();

            // 重置生成状态
            this.setData({
              isGeneratingMembership: false
            });

            console.error('创建会员兑换码失败:', err);

            wx.showToast({
              title: '创建失败，请重试',
              icon: 'none'
            });
          });
      })
      .catch(err => {
        wx.hideLoading();

        // 重置生成状态
        this.setData({
          isGeneratingMembership: false
        });

        console.error('检查兑换码失败:', err);

        wx.showToast({
          title: '创建失败，请重试',
          icon: 'none'
        });
      });
  },

  // 批量生成兑换码
  batchCreateRedeemCodes: function() {
    const batchFormData = this.data.batchFormData;

    // 表单验证
    if (!batchFormData.nameTemplate.trim()) {
      wx.showToast({
        title: '请输入名称模板',
        icon: 'none'
      });
      return;
    }

    if (batchFormData.count <= 0 || batchFormData.count > 100) {
      wx.showToast({
        title: '生成数量必须在1-100之间',
        icon: 'none'
      });
      return;
    }

    if (batchFormData.rewardType === 'clothes' || batchFormData.rewardType === 'outfits') {
      if (batchFormData.rewardAmount <= 0) {
        wx.showToast({
          title: '奖励数量必须大于0',
          icon: 'none'
        });
        return;
      }
    } else if (batchFormData.rewardType === 'both') {
      if (batchFormData.clothesRewardAmount <= 0 || batchFormData.outfitsRewardAmount <= 0) {
        wx.showToast({
          title: '奖励数量必须大于0',
          icon: 'none'
        });
        return;
      }
    }

    if (batchFormData.expirationDays <= 0) {
      wx.showToast({
        title: '有效期天数必须大于0',
        icon: 'none'
      });
      return;
    }

    // 设置生成中状态
    this.setData({
      isBatchGenerating: true
    });

    // 开始批量生成
    this.batchGenerateCodes();
  },

  // 执行批量生成处理
  batchGenerateCodes: async function() {
    const batchFormData = this.data.batchFormData;
    const count = batchFormData.count;
    const codes = [];
    const db = wx.cloud.database();
    const _ = db.command;
    const now = new Date();

    try {
      // 显示加载提示
      wx.showLoading({
        title: '正在生成...',
        mask: true
      });

      // 计算过期时间
      const expirationDate = new Date(now.getTime() + batchFormData.expirationDays * 24 * 60 * 60 * 1000);

      // 生成所有兑换码数据
      const batchCodes = [];
      const generatedResults = [];

      for (let i = 1; i <= count; i++) {
        // 生成兑换码
        const code = this.generateRandomCodeWithPrefix(batchFormData.prefix);

        // 创建兑换码名称（替换模板中的%d为序号）
        const name = batchFormData.nameTemplate.replace('%d', i);

        // 创建兑换码数据
        const codeData = {
          code: code,
          name: name,
          description: `批量生成的兑换码(${i}/${count})`,
          isActive: true,
          isOneTime: batchFormData.isOneTime,
          createTime: now,
          expirationDate: expirationDate,
        };

        // 设置奖励类型和数量
        if (batchFormData.rewardType === 'clothes') {
          codeData.rewardType = 'clothes';
          codeData.rewardAmount = batchFormData.rewardAmount;
        } else if (batchFormData.rewardType === 'outfits') {
          codeData.rewardType = 'outfits';
          codeData.rewardAmount = batchFormData.rewardAmount;
        } else {
          codeData.rewardType = 'both';
          codeData.clothesRewardAmount = batchFormData.clothesRewardAmount;
          codeData.outfitsRewardAmount = batchFormData.outfitsRewardAmount;
        }

        batchCodes.push(codeData);
        generatedResults.push({
          code: code,
          name: name
        });
      }

      // 分批添加到数据库（每次最多插入20条记录）
      const batchSize = 20;
      for (let i = 0; i < batchCodes.length; i += batchSize) {
        const batch = batchCodes.slice(i, i + batchSize);
        await Promise.all(batch.map(item => db.collection('redemptionCodes').add({
          data: item
        })));
      }

      // 隐藏加载提示
      wx.hideLoading();

      // 更新生成结果
      this.setData({
        batchResults: generatedResults,
        isBatchGenerating: false
      });

      // 添加复制全部结果的按钮
      this.setData({
        showBatchResults: true
      });

      // 重新加载兑换码列表
      this.loadRedeemCodes();

      // 显示成功提示
      wx.showToast({
        title: `成功生成${count}个兑换码`,
        icon: 'success'
      });

    } catch (error) {
      console.error('批量生成兑换码失败:', error);
      wx.hideLoading();

      this.setData({
        isBatchGenerating: false
      });

      wx.showToast({
        title: '生成失败，请重试',
        icon: 'none'
      });
    }
  },

  // 复制所有生成的兑换码
  copyAllCodes: function() {
    const codes = this.data.batchResults.map(item => `${item.name}: ${item.code}`).join('\n');

    wx.setClipboardData({
      data: codes,
      success: () => {
        wx.showToast({
          title: '已复制全部兑换码',
          icon: 'success'
        });
      }
    });
  },

  // 复制所有生成的会员兑换码
  copyAllMembershipCodes: function() {
    const codes = this.data.batchMembershipResults.map(item => `${item.name}: ${item.code}`).join('\n');

    wx.setClipboardData({
      data: codes,
      success: () => {
        wx.showToast({
          title: '已复制全部会员兑换码',
          icon: 'success'
        });
      }
    });
  },

  // 批量生成会员兑换码
  batchCreateMembershipCodes: function() {
    const batchMembershipFormData = this.data.batchMembershipFormData;

    // 表单验证
    if (!batchMembershipFormData.nameTemplate.trim()) {
      wx.showToast({
        title: '请输入名称模板',
        icon: 'none'
      });
      return;
    }

    if (batchMembershipFormData.count <= 0 || batchMembershipFormData.count > 100) {
      wx.showToast({
        title: '生成数量必须在1-100之间',
        icon: 'none'
      });
      return;
    }

    if (batchMembershipFormData.expirationDays <= 0) {
      wx.showToast({
        title: '有效期天数必须大于0',
        icon: 'none'
      });
      return;
    }

    // 设置生成中状态
    this.setData({
      isBatchGeneratingMembership: true
    });

    // 开始批量生成
    this.batchGenerateMembershipCodes();
  },

  // 执行批量生成会员兑换码处理
  batchGenerateMembershipCodes: async function() {
    const batchMembershipFormData = this.data.batchMembershipFormData;
    const count = batchMembershipFormData.count;
    const db = wx.cloud.database();
    const now = new Date();

    try {
      // 显示加载提示
      wx.showLoading({
        title: '正在生成...',
        mask: true
      });

      // 计算过期时间
      const expirationDate = new Date(now.getTime() + batchMembershipFormData.expirationDays * 24 * 60 * 60 * 1000);

      // 生成所有兑换码数据
      const batchCodes = [];
      const generatedResults = [];

      for (let i = 1; i <= count; i++) {
        // 生成兑换码
        const code = this.generateRandomCodeWithPrefix(batchMembershipFormData.prefix);

        // 创建兑换码名称（替换模板中的%d为序号）
        const name = batchMembershipFormData.nameTemplate.replace('%d', i);

        // 创建兑换码数据
        const codeData = {
          code: code,
          name: name,
          description: `批量生成的会员兑换码(${i}/${count})`,
          rewardType: 'membership',
          membershipType: batchMembershipFormData.membershipType,
          isActive: true,
          isOneTime: batchMembershipFormData.isOneTime,
          usedCount: 0,
          createTime: now,
          expirationDate: expirationDate,
          createdBy: this.data.userOpenId
        };

        batchCodes.push(codeData);
        generatedResults.push({
          code: code,
          name: name
        });
      }

      // 分批添加到数据库（每次最多插入20条记录）
      const batchSize = 20;
      for (let i = 0; i < batchCodes.length; i += batchSize) {
        const batch = batchCodes.slice(i, i + batchSize);
        await Promise.all(batch.map(item => db.collection('redemptionCodes').add({
          data: item
        })));
      }

      // 隐藏加载提示
      wx.hideLoading();

      // 更新生成结果
      this.setData({
        batchMembershipResults: generatedResults,
        isBatchGeneratingMembership: false
      });

      // 重新加载兑换码列表
      this.loadRedeemCodes();

      // 显示成功提示
      wx.showToast({
        title: `成功生成${count}个会员兑换码`,
        icon: 'success'
      });

    } catch (error) {
      console.error('批量生成会员兑换码失败:', error);
      wx.hideLoading();

      this.setData({
        isBatchGeneratingMembership: false
      });

      wx.showToast({
        title: '生成失败，请重试',
        icon: 'none'
      });
    }
  },
});