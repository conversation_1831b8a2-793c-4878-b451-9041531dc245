<!-- 图片编辑组件 -->
<view class="image-editor-modal" wx:if="{{show}}" catchtouchmove="preventTouchMove">
  <view class="modal-mask" bindtap="onCancel"></view>
  <view class="modal-content">
    <!-- 标题栏 -->
    <view class="modal-header">
      <text class="modal-title">编辑图片</text>
      <view class="close-btn" bindtap="onCancel">×</view>
    </view>

    <!-- 编辑区域 -->
    <view class="edit-area">
      <!-- 画布容器 -->
      <view class="canvas-container">
        <!-- 显示原图片作为背景，用于调试 -->
        <image wx:if="{{imageUrl}}" src="{{imageUrl}}" class="debug-image" mode="aspectFit"></image>
        <view class="canvas-status">
          画布: {{canvas ? '已初始化' : '未初始化'}} | 
          历史: {{historyIndex + 1}}/{{history.length}}
        </view>
        <canvas
          type="2d"
          id="editCanvas"
          class="edit-canvas"
          bindtouchstart="onCanvasTouchStart"
          bindtouchmove="onCanvasTouchMove"
          bindtouchend="onCanvasTouchEnd"
        ></canvas>
      </view>

      <!-- 工具栏 -->
      <view class="toolbar">
        <!-- 第一行：旋转和镜像工具 -->
        <view class="tool-row">
          <view class="tool-group">
            <text class="tool-label">旋转</text>
            <view class="rotate-buttons">
              <button class="tool-btn" bindtap="rotateLeft">
                <text class="icon">↺</text>
                <text class="btn-label">左转</text>
              </button>
              <button class="tool-btn" bindtap="rotateRight">
                <text class="icon">↻</text>
                <text class="btn-label">右转</text>
              </button>
            </view>
          </view>

          <view class="tool-group">
            <text class="tool-label">镜像</text>
            <view class="flip-buttons">
              <button class="tool-btn {{flipHorizontal ? 'active' : ''}}" bindtap="flipHorizontal">
                <text class="icon">↔</text>
                <text class="btn-label">水平</text>
              </button>
              <button class="tool-btn {{flipVertical ? 'active' : ''}}" bindtap="flipVertical">
                <text class="icon">↕</text>
                <text class="btn-label">垂直</text>
              </button>
            </view>
          </view>
        </view>

        <!-- 第二行：涂抹工具 -->
        <view class="tool-row">
          <view class="tool-group">
            <text class="tool-label">涂抹</text>
            <view class="brush-controls">
              <button class="tool-btn {{brushMode ? 'active' : ''}}" bindtap="toggleBrushMode">
                <text class="icon">🖌️</text>
              </button>
              <view class="brush-size-control" wx:if="{{brushMode}}">
                <text class="size-label">大小</text>
                <view class="brush-slider-container">
                  <view class="brush-slider-track {{sliderTouching ? 'touching' : ''}}" bindtouchstart="onSliderTouchStart" bindtouchmove="onSliderTouchMove" bindtouchend="onSliderTouchEnd">
                    <view class="brush-slider-progress" style="width: {{(brushSize - 5) / 45 * 100}}%"></view>
                    <view class="brush-slider-thumb" style="left: {{(brushSize - 5) / 45 * 100}}%"></view>
                  </view>
                  <view class="brush-size-preview">
                    <view class="brush-preview-circle" style="width: {{brushSize}}rpx; height: {{brushSize}}rpx;"></view>
                  </view>
                  <text class="brush-size-value">{{brushSize}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 第三行：历史和重置工具 -->
        <view class="tool-row">
          <view class="tool-group">
            <text class="tool-label">历史</text>
            <view class="history-buttons">
              <button class="tool-btn history-btn" bindtap="undo" disabled="{{!canUndo}}">
                <text class="icon">↶</text>
                <text class="btn-label">撤销</text>
              </button>
              <button class="tool-btn history-btn" bindtap="redo" disabled="{{!canRedo}}">
                <text class="icon">↷</text>
                <text class="btn-label">重做</text>
              </button>
            </view>
          </view>

          <view class="tool-group">
            <text class="tool-label">重置</text>
            <button class="tool-btn" bindtap="reset">
              <text class="icon">🔄</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="onCancel">取消</button>
      <button class="confirm-btn" bindtap="onConfirm">保存</button>
    </view>
  </view>
</view>
