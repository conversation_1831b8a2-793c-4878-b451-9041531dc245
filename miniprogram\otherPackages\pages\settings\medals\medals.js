// page/settings/medals/medals.js
const medalManager = require('../modules/medalManager');

Page({
  data: {
    theme: 'light', // WeUI的主题，light或dark
    themeStyle: 'autumn', // 默认为秋季风格，可选值：'autumn'、'pinkBlue'或'blackWhite'
    refreshing: false, // 下拉刷新状态
    // 秋季色系色彩配置
    colors: {
      cowhide_cocoa: '#442D1C',   // 深棕色 Cowhide Cocoa
      spiced_wine: '#74301C',     // 红棕色 Spiced Wine
      toasted_caramel: '#84592B', // 焦糖色 Toasted Caramel
      olive_harvest: '#9D9167',   // 橄榄色 Olive Harvest
      golden_batter: '#E8D1A7',   // 金黄色 Golden Batter
    },
    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',       // 深粉色
      pinkMedium: '#EEA0B2',     // 中粉色
      pinkLight: '#F9C9D6',      // 浅粉色
      blueLight: '#CBE0F9',      // 浅蓝色
      blueMedium: '#97C8E5',     // 中蓝色
      blueDark: '#5EA0D0',       // 深蓝色
    },
    // 黑白色系配色
    blackWhiteColors: {
      black: '#000000',          // 纯黑
      darkGray: '#333333',      // 深灰
      mediumGray: '#666666',    // 中灰
      lightGray: '#CCCCCC',     // 浅灰
      white: '#FFFFFF',         // 纯白
      offWhite: '#dee3ec',      // 灰白
    },
    // 页面样式
    pageStyle: {
      backgroundColor: '',
      backgroundImage: '',
      titleColor: '',
      cellBackgroundColor: '',
      footerColor: '',
      decorationColors: []
    },
    // 勋章数据
    medals: [],
    earnedMedals: [],
    unearnedMedals: [],
    // 勋章图片缓存
    medalImages: {},
    // 加载状态
    isLoading: true,
    // 用户OpenID
    userOpenId: '',
    // 当前查看的勋章详情
    currentMedal: null,
    showMedalDetail: false,
    // 是否隐藏未获得勋章
    hideUnearnedMedals: false
  },

  onLoad: function(options) {
    console.log('勋章页面加载');

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '我的勋章'
    });

    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });

      // 应用导航栏样式
      this.applyThemeStyle(savedTheme);
    } else {
      // 默认应用秋季主题
      this.applyThemeStyle('autumn');
    }

    // 获取保存的未获得勋章显示设置
    const hideUnearnedMedals = wx.getStorageSync('hideUnearnedMedals');
    if (hideUnearnedMedals !== '') {
      this.setData({
        hideUnearnedMedals: hideUnearnedMedals
      });
      console.log('从缓存加载未获得勋章显示设置:', hideUnearnedMedals);
    }

    // 获取用户OpenID
    this.getUserOpenId();
  },

  onShow: function() {
    // 检查并授予内测勋章
    this.checkBetaTesterMedal();
  },

  // 检查并授予内测勋章
  checkBetaTesterMedal: function() {
    if (!this.data.userOpenId) {
      console.log('用户OpenID不可用，无法检查内测勋章');
      return;
    }

    console.log('检查内测勋章资格');

    wx.cloud.callFunction({
      name: 'checkBetaTesterMedal',
      data: {
        userOpenId: this.data.userOpenId
      }
    })
      .then(res => {
        console.log('检查内测勋章结果:', res.result);

        if (res.result && res.result.success) {
          // 如果成功授予勋章，刷新勋章数据
          if (res.result.awarded) {
            console.log('成功授予内测勋章，刷新勋章数据');

            // 显示获得勋章提示
            wx.showToast({
              title: '获得内测勋章',
              icon: 'success',
              duration: 2000
            });

            // 刷新勋章数据
            this.refreshMedals();

            // 设置全局标记，通知设置页面刷新勋章数据
            const app = getApp();
            if (app && app.globalData) {
              app.globalData.medalsNeedRefresh = true;
            }
          }
        } else if (res.result && !res.result.success) {
          console.error('检查内测勋章失败:', res.result.error);
        }
      })
      .catch(err => {
        console.error('调用检查内测勋章云函数失败:', err);
      });
  },

  // 获取用户OpenID
  getUserOpenId: function() {
    const app = getApp();

    // 尝试从本地缓存获取openid
    const cachedOpenid = wx.getStorageSync('openid');
    if (cachedOpenid) {
      console.log('从缓存获取到openid:', cachedOpenid);
      this.setData({
        userOpenId: cachedOpenid
      });

      // 获取勋章数据
      this.getMedalsData();
      return;
    }

    // 如果本地没有缓存，调用获取openid的方法
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
        return;
      }

      console.log('成功获取用户openid:', openid);
      this.setData({
        userOpenId: openid
      });

      // 获取勋章数据
      this.getMedalsData();
    });
  },

  // 获取勋章数据
  getMedalsData: function(forceRefresh = false) {
    if (!this.data.userOpenId) {
      console.error('用户OpenID不可用，无法获取勋章数据');
      return;
    }

    // 显示加载提示
    this.setData({
      isLoading: true
    });

    // 获取勋章数据
    medalManager.getUserMedals(this.data.userOpenId, !forceRefresh)
      .then(data => {
        console.log('获取勋章数据成功:', data);

        // 处理勋章数据，格式化日期
        const processedMedals = data.medals.map(medal => {
          // 处理earnedTime日期格式
          if (medal.earnedTime) {
            // 如果是字符串日期，只保留日期部分
            if (typeof medal.earnedTime === 'string') {
              // 如果包含时间部分，只保留日期部分
              if (medal.earnedTime.includes('T')) {
                medal.earnedTime = medal.earnedTime.split('T')[0];
              }
            }
            // 如果是对象并且有$date字段（云函数返回的时间格式）
            else if (medal.earnedTime.$date) {
              medal.earnedTime = new Date(medal.earnedTime.$date).toISOString().split('T')[0];
            }
            // 如果是日期对象
            else if (medal.earnedTime instanceof Date) {
              medal.earnedTime = medal.earnedTime.toISOString().split('T')[0];
            }
            // 如果是数字时间戳
            else if (typeof medal.earnedTime === 'number') {
              medal.earnedTime = new Date(medal.earnedTime).toISOString().split('T')[0];
            }
          }
          return medal;
        });

        // 分离已获得和未获得的勋章
        const earnedMedals = processedMedals.filter(medal => medal.earned);
        const unearnedMedals = processedMedals.filter(medal => !medal.earned);

        this.setData({
          medals: processedMedals,
          earnedMedals: earnedMedals,
          unearnedMedals: unearnedMedals,
          isLoading: false
        });

        // 下载勋章图片
        this.downloadMedalImages();
      })
      .catch(err => {
        console.error('获取勋章数据失败:', err);
        wx.showToast({
          title: '获取勋章失败',
          icon: 'none'
        });

        this.setData({
          isLoading: false
        });
      });
  },

  // 下载勋章图片
  downloadMedalImages: function() {
    medalManager.downloadMedalImages(this.data.medals)
      .then(medalImages => {
        this.setData({
          medalImages: medalImages
        });
      })
      .catch(err => {
        console.error('下载勋章图片失败:', err);
      });
  },

  // 刷新勋章数据
  refreshMedals: function() {
    this.getMedalsData(true);
  },

  // 处理下拉刷新事件
  onRefresh: function() {
    // 设置刷新状态
    this.setData({
      refreshing: true
    });

    // 显示加载提示
    wx.showLoading({
      title: '勋章刷新中...',
      mask: true
    });

    // 刷新勋章数据
    this.refreshMedals();

    // 模拟网络请求延迟，确保用户能看到刷新动画
    setTimeout(() => {
      // 关闭加载提示
      wx.hideLoading();

      // 结束刷新状态
      this.setData({
        refreshing: false
      });

      // 显示刷新成功提示
      wx.showToast({
        title: '勋章刷新成功',
        icon: 'success',
        duration: 1500
      });
    }, 1000);
  },

  // 查看勋章详情
  viewMedalDetail: function(e) {
    const medalId = e.currentTarget.dataset.id;
    const medal = this.data.medals.find(m => m._id === medalId);

    if (medal) {
      // 如果是已获得的勋章，获取用户勋章详情
      if (medal.earned && medal.userMedalId) {
        wx.cloud.database().collection('user_medals')
          .doc(medal.userMedalId)
          .get()
          .then(res => {
            if (res.data) {
              // 合并用户勋章数据和勋章定义数据
              const userMedalData = res.data;

              // 处理earnedTime日期格式
              let earnedTime = userMedalData.earnedTime;
              if (earnedTime) {
                // 如果是字符串日期，直接使用
                if (typeof earnedTime === 'string') {
                  // 如果包含时间部分，只保留日期部分
                  if (earnedTime.includes('T')) {
                    earnedTime = earnedTime.split('T')[0];
                  }
                }
                // 如果是对象并且有$date字段（云函数返回的时间格式）
                else if (earnedTime.$date) {
                  earnedTime = new Date(earnedTime.$date).toISOString().split('T')[0];
                }
                // 如果是日期对象
                else if (earnedTime instanceof Date) {
                  earnedTime = earnedTime.toISOString().split('T')[0];
                }
                // 如果是数字时间戳
                else if (typeof earnedTime === 'number') {
                  earnedTime = new Date(earnedTime).toISOString().split('T')[0];
                }
              }

              const fullMedal = {
                ...medal,
                globalNumber: userMedalData.globalNumber,
                earnedTime: earnedTime || medal.earnedTime
              };

              this.setData({
                currentMedal: fullMedal,
                showMedalDetail: true
              });
            } else {
              this.setData({
                currentMedal: medal,
                showMedalDetail: true
              });
            }
          })
          .catch(err => {
            console.error('获取用户勋章详情失败:', err);
            this.setData({
              currentMedal: medal,
              showMedalDetail: true
            });
          });
      } else {
        this.setData({
          currentMedal: medal,
          showMedalDetail: true
        });
      }
    }
  },

  // 关闭勋章详情
  closeMedalDetail: function() {
    this.setData({
      showMedalDetail: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    e.stopPropagation();
  },

  // 查看勋章图片
  viewMedalImage: function(e) {
    // 阻止事件冒泡，避免触发关闭弹窗
    e.stopPropagation();

    // 获取勋章数据
    let medal;

    // 如果有传入的medalId，使用它来获取勋章
    if (e.currentTarget.dataset.id) {
      const medalId = e.currentTarget.dataset.id;
      medal = this.data.medals.find(m => m._id === medalId);
    } else {
      // 否则使用当前选中的勋章
      medal = this.data.currentMedal;
    }

    if (!medal || !medal.imageFileID) {
      console.log('勋章图片不可用');
      return;
    }

    // 获取图片路径
    const imagePath = this.data.medalImages[medal.imageFileID] || '';
    if (!imagePath) {
      console.log('勋章图片路径不可用');
      return;
    }

    // 使用微信预览图片API查看图片
    wx.previewImage({
      current: imagePath, // 当前显示图片的链接
      urls: [imagePath], // 需要预览的图片链接列表
      success: () => {
        console.log('打开勋章图片预览成功');
      },
      fail: (err) => {
        console.error('打开勋章图片预览失败:', err);
        wx.showToast({
          title: '图片预览失败',
          icon: 'none'
        });
      }
    });
  },

  // 应用主题样式
  applyThemeStyle: function(themeName) {
    // 更新页面样式
    let pageStyle = {};

    if (themeName === 'autumn') {
      // 设置秋季主题样式
      pageStyle = {
        backgroundColor: this.data.colors.golden_batter,
        backgroundImage: 'none',
        titleColor: this.data.colors.cowhide_cocoa,
        cellBackgroundColor: 'rgba(255, 255, 255, 0.7)',
        footerColor: this.data.colors.cowhide_cocoa,
        decorationColors: [
          this.data.colors.olive_harvest,
          this.data.colors.spiced_wine,
          this.data.colors.toasted_caramel
        ]
      };

      // 设置秋季主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.colors.golden_batter, // 金黄色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });
    } else if (themeName === 'pinkBlue') {
      // 设置粉蓝主题样式
      pageStyle = {
        backgroundColor: this.data.pinkBlueColors.pinkLight,
        backgroundImage: `linear-gradient(to bottom, white, ${this.data.pinkBlueColors.pinkLight})`,
        titleColor: this.data.pinkBlueColors.pinkDark,
        cellBackgroundColor: 'rgba(255, 255, 255, 0.9)',
        footerColor: this.data.pinkBlueColors.blueDark,
        decorationColors: [
          this.data.pinkBlueColors.blueMedium,
          this.data.pinkBlueColors.pinkMedium,
          this.data.pinkBlueColors.blueLight
        ]
      };

      // 设置粉蓝主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.pinkBlueColors.pinkLight, // 浅粉色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });
    } else if (themeName === 'blackWhite') {
      // 设置黑白主题样式
      pageStyle = {
        backgroundColor: this.data.blackWhiteColors.offWhite,
        backgroundImage: 'none',
        titleColor: this.data.blackWhiteColors.black,
        cellBackgroundColor: this.data.blackWhiteColors.white,
        footerColor: this.data.blackWhiteColors.darkGray,
        decorationColors: [
          this.data.blackWhiteColors.darkGray,
          this.data.blackWhiteColors.mediumGray,
          this.data.blackWhiteColors.lightGray
        ]
      };

      // 设置黑白主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.blackWhiteColors.white, // 白色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });
    }

    // 更新页面样式
    this.setData({
      pageStyle: pageStyle
    });
  },

  // 切换未获得勋章的显示状态
  toggleUnearnedMedals: function() {
    const newValue = !this.data.hideUnearnedMedals;

    // 更新状态
    this.setData({
      hideUnearnedMedals: newValue
    });

    // 保存到本地缓存
    wx.setStorageSync('hideUnearnedMedals', newValue);
    console.log('保存未获得勋章显示设置到缓存:', newValue);

    // 显示提示
    wx.showToast({
      title: newValue ? '已隐藏未获得勋章' : '已显示全部勋章',
      icon: 'none',
      duration: 1500
    });
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '我的勋章收藏',
      path: '/page/settings/medals/medals',
      imageUrl: '/image/share.png'
    };
  }
});
