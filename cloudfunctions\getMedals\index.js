// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const userOpenId = event.userOpenId || wxContext.OPENID

  try {
    // 获取所有可用勋章定义
    const medalsResult = await db.collection('medals')
      .where({
        isActive: true
      })
      .get()

    // 获取用户已获得的勋章
    const userMedalsResult = await db.collection('user_medals')
      .where({
        userOpenId: userOpenId
      })
      .get()

    // 合并勋章数据，标记用户已获得的勋章
    const allMedals = medalsResult.data || []
    const userMedals = userMedalsResult.data || []

    // 创建用户勋章ID映射，方便快速查找
    const userMedalMap = {}
    userMedals.forEach(medal => {
      userMedalMap[medal.medalId] = medal
    })

    // 合并数据，添加用户获得状态
    const mergedMedals = allMedals.map(medal => {
      const userMedal = userMedalMap[medal._id]
      return {
        ...medal,
        earned: !!userMedal,
        earnedTime: userMedal ? userMedal.earnedTime : null,
        userMedalId: userMedal ? userMedal._id : null,
        globalNumber: userMedal ? userMedal.globalNumber : null
      }
    })

    return {
      success: true,
      data: mergedMedals,
      earnedCount: userMedals.length
    }
  } catch (error) {
    console.error('获取勋章数据失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
