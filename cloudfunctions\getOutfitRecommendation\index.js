// 云函数入口文件
const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 获取调用参数
    const { 
      weatherData,    // 当前天气数据 (向后兼容)
      lunarData,      // 农历和八卦数据
      userClothes,    // 用户的衣柜数据，可选
      fengShuiPrompt  // 是否使用风水推演
    } = event
    
    // 验证必要参数
    if (!weatherData && !lunarData) {
      return {
        success: false,
        error: '缺少天气数据或农历数据参数'
      }
    }
    
    // DeepSeek API密钥
    const apiKey = '***********************************'
    
    // 构建提示词
    let prompt = ''
    
    // 根据是否使用风水推演选择不同的提示词
    if (fengShuiPrompt && lunarData) {
      // 风水穿搭提示词
      prompt = `
作为一位拥有几十年经验的古法风水大师(Divination Specialist, 简称DS)，请根据以下农历和八卦信息为用户推荐今日穿搭：
- 公历日期: ${lunarData.date || '未知'}
- 农历日期: ${lunarData.lunarDate || '未知'}
- 星期: ${lunarData.weekday || '未知'}
- 生肖: ${lunarData.animal || '未知'}
- 节气: ${lunarData.term || (lunarData.nextTerm ? lunarData.nextTerm.name + '将至' : '未知')}
- 八卦: ${lunarData.bagua ? lunarData.bagua.name + '卦(' + lunarData.bagua.attribute + ')' : '未知'}

请以JSON格式返回推荐的穿搭和风水学原理（注意JSON对象外不要有任何其他文字说明）：
{
  "fengShuiPrinciple": "五十字左右的风水学原理，解释本次推荐如何根据节气和八卦选择穿搭以调和五行、改善运势",
  "recommendation": [
    {
      "category": "上衣",
      "type": "衬衫",
      "color": "白色",
      "style": "简约",
      "element": "金", 
      "benefit": "提升人缘"
    },
    {
      "category": "裤子",
      "type": "休闲裤",
      "color": "藏青色",
      "style": "商务",
      "element": "水",
      "benefit": "增强智慧"
    },
    {
      "category": "外套",
      "type": "西装",
      "color": "灰色",
      "style": "正式",
      "element": "金",
      "benefit": "增强财运"
    }
  ]
}

在进行推荐时，请考虑以下五行相生相克原理:
- 金生水，水生木，木生火，火生土，土生金（相生关系）
- 金克木，木克土，土克水，水克火，火克金（相克关系）

颜色与五行的对应关系:
- 金: 白色、金色、银色、金属色
- 木: 绿色、青色、棕色
- 水: 蓝色、黑色、灰色
- 火: 红色、紫色、粉色
- 土: 黄色、棕色、米色

请根据八卦和节气分析用户当前需要补足或控制的五行，从而推荐合适的颜色和风格。
`
    } else {
      // 原来基于天气的提示词
      prompt = `
作为AI时尚顾问，请为用户根据以下天气情况推荐今日穿搭：
- 城市: ${weatherData.city || '未知'}
- 温度: ${weatherData.temperature || '未知'}
- 天气情况: ${weatherData.condition || '未知'}
- 湿度: ${weatherData.humidity || '未知'}
- 风速: ${weatherData.windSpeed || '未知'}

请以JSON格式返回推荐的穿搭（注意不要有任何其他文字说明，只返回JSON对象）：
- 推荐配合至少3件衣物，包括上衣、下装和外套（如果温度适合的话）
- 每件衣物的属性应包括：类别(category)、颜色(color)、风格(style)

推荐的格式如下：
{
  "recommendation": [
    {
      "category": "上衣",
      "type": "T恤",
      "color": "白色",
      "style": "简约"
    },
    {
      "category": "裤子",
      "type": "牛仔裤",
      "color": "蓝色",
      "style": "修身"
    },
    {
      "category": "外套",
      "type": "夹克",
      "color": "黑色",
      "style": "休闲"
    }
  ],
  "description": "今天天气适合穿简约风格的搭配，建议穿白色T恤搭配蓝色牛仔裤，外套选择黑色休闲夹克。"
}
`
    }

    // 如果有用户衣柜数据，添加到提示词中
    if (userClothes && userClothes.length > 0) {
      prompt += `\n用户的衣柜包含以下衣物：\n`
      
      userClothes.forEach((item, index) => {
        prompt += `${index + 1}. ID: ${item._id}, 类别: ${item.category}, 类型: ${item.clothingType || '未知'}, 颜色: ${item.color || '未知'}, 风格: ${item.style || '未知'}\n`
      })
      
      if (fengShuiPrompt) {
        prompt += `\n请从这些衣物中选择合适的搭配，考虑五行相生相克，帮助用户调和运势。在推荐中添加衣物ID以便我们找到对应衣物。`
      } else {
        prompt += `\n请从这些衣物中选择合适的搭配，在推荐中添加衣物ID以便我们找到对应衣物。`
      }
    }
    
    // 调用DeepSeek API
    const response = await axios({
      method: 'POST',
      url: 'https://api.deepseek.com/chat/completions',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      data: {
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: fengShuiPrompt ? 
              'You are a traditional Chinese Feng Shui master with decades of experience. Respond only with JSON format as specified in the request. Do not include any other text outside the JSON object.' :
              'You are a fashion consultant AI. Respond only with JSON format as specified in the request. Do not include any other text outside the JSON object.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        stream: false
      }
    })
    
    // 解析响应
    if (response.data && response.data.choices && response.data.choices[0] && response.data.choices[0].message) {
      const content = response.data.choices[0].message.content
      
      // 尝试从内容中提取JSON
      try {
        // 如果内容中包含了额外的描述文字，尝试只提取JSON部分
        const jsonMatch = content.match(/\{[\s\S]*\}/)
        const jsonStr = jsonMatch ? jsonMatch[0] : content
        
        const recommendationData = JSON.parse(jsonStr)
        
        // 如果有用户衣柜数据，尝试匹配推荐的衣物ID
        if (userClothes && userClothes.length > 0 && recommendationData.recommendation) {
          // 查找衣柜中匹配的衣物
          const matchedOutfit = []
          
          // 处理每个推荐的衣物
          for (const item of recommendationData.recommendation) {
            // 尝试找到匹配的衣物
            let match = null
            
            // 如果推荐中包含ID，直接通过ID匹配
            if (item.itemID) {
              match = userClothes.find(cloth => cloth._id === item.itemID)
            }
            
            // 如果没有通过ID匹配到，尝试通过属性匹配
            if (!match) {
              match = userClothes.find(cloth => 
                cloth.category === item.category && 
                (!item.color || cloth.color === item.color) &&
                (!item.style || cloth.style === item.style)
              )
            }
            
            // 如果找到匹配的衣物，添加到结果中
            if (match) {
              // 添加风水元素和增益信息
              if (fengShuiPrompt && item.element) {
                match.element = item.element
                match.benefit = item.benefit
              }
              matchedOutfit.push(match)
            }
          }
          
          // 如果找到了匹配的衣物，返回这些衣物的信息
          if (matchedOutfit.length > 0) {
            return {
              success: true,
              recommendation: recommendationData,
              matchedOutfit: matchedOutfit,
              fengShuiPrinciple: fengShuiPrompt ? recommendationData.fengShuiPrinciple : null
            }
          }
        }
        
        // 如果没有匹配到衣物或没有衣柜数据，只返回推荐信息
        return {
          success: true,
          recommendation: recommendationData,
          matchedOutfit: [],
          fengShuiPrinciple: fengShuiPrompt ? recommendationData.fengShuiPrinciple : null
        }
      } catch (error) {
        console.error('解析DeepSeek返回的JSON失败:', error)
        return {
          success: false,
          error: '解析推荐数据失败',
          content: content
        }
      }
    } else {
      return {
        success: false,
        error: 'DeepSeek API返回异常',
        response: response.data
      }
    }
  } catch (error) {
    console.error('获取穿搭推荐失败:', error)
    return {
      success: false,
      error: error.message,
      stack: error.stack
    }
  }
}
