/**
 * 图片管理模块
 * 负责图片处理、临时URL缓存和错误处理
 */

// 导入本地图片缓存模块
const localImageCache = require('./localImageCache');

/**
 * 获取默认预览图URL
 * @returns {string} 默认预览图URL
 */
function getDefaultPreviewImageUrl() {
  return localImageCache.getDefaultPreviewImageUrl();
}

/**
 * 获取默认衣物图片URL
 * @returns {string} 默认衣物图片URL
 */
function getDefaultItemImageUrl() {
  return localImageCache.getDefaultItemImageUrl();
}

/**
 * 验证图像URL是否有效
 * @param {string} url - 图像URL
 * @returns {boolean} URL是否有效
 */
function isValidImageUrl(url) {
  if (!url) return false;
  
  // 如果是本地路径、相对路径或有效URL格式，认为是有效的
  if (url.startsWith('/') || 
      url.startsWith('http://') || 
      url.startsWith('https://') || 
      url.startsWith('wxfile://') ||
      url.startsWith('cloud://')) {
    return true;
  }
  
  return false;
}

/**
 * 从缓存获取URL
 * @param {string} fileID - 文件ID
 * @returns {string|null} 缓存的临时URL，如果不存在或已过期则返回null
 */
function getURLFromCache(fileID) {
  if (!fileID) return null;
  
  // 直接使用本地图片缓存模块获取
  const localPath = localImageCache.getLocalCachedImage(fileID);
  if (localPath) {
    return localPath;
  }
  
  // 如果本地文件系统没有缓存，则从Storage缓存获取
  try {
    const cacheKey = `url_cache_${fileID}`;
    const cachedData = wx.getStorageSync(cacheKey);
    
    if (!cachedData) return null;
    
    // 检查URL是否已过期或即将过期（有效期10分钟，提前1分钟刷新）
    const now = Date.now();
    if (now - cachedData.timestamp > 540000) { // 9分钟 = 10分钟 - 1分钟安全边界
      console.log(`缓存的URL已过期或即将过期: ${fileID}`);
      // 删除过期缓存
      wx.removeStorageSync(cacheKey);
      return null;
    }
    
    return cachedData.url;
  } catch (err) {
    console.error('获取缓存URL出错:', err);
    return null;
  }
}

/**
 * 更新URL缓存
 * @param {string} fileID - 文件ID
 * @param {string} url - 临时URL
 */
function updateURLCache(fileID, url) {
  if (!fileID || !url) return;
  
  try {
    // 先尝试下载并存储到本地文件系统
    localImageCache.downloadImageToCache(fileID, url)
      .then(localPath => {
        console.log(`图片已缓存到本地文件系统: ${fileID}, 路径: ${localPath}`);
      })
      .catch(err => {
        console.warn(`图片缓存到本地文件系统失败: ${fileID}`, err);
        
        // 如果本地文件系统缓存失败，则使用原来的内存缓存方式
        // 存入本地缓存
        const cacheKey = `url_cache_${fileID}`;
        const cacheData = {
          url: url,
          timestamp: Date.now()
        };
        
        wx.setStorageSync(cacheKey, cacheData);
      });
  } catch (err) {
    console.error('更新缓存URL出错:', err);
  }
}

/**
 * 刷新临时文件URL
 * @param {string} fileID - 文件ID
 * @param {Function} callback - 回调函数，参数为新的临时URL或null
 * @param {number} retryCount - 重试次数
 */
function refreshTempFileURL(fileID, callback, retryCount = 0) {
  if (!fileID || !fileID.includes('cloud://')) {
    console.error('无效的fileID:', fileID);
    callback(null);
    return;
  }
  
  // 首先尝试从本地文件系统缓存获取
  const localPath = localImageCache.getLocalCachedImage(fileID);
  if (localPath) {
    console.log(`使用本地文件系统缓存的图片: ${fileID}`);
    callback(localPath);
    return;
  }
  
  // 尝试从URL缓存获取
  const cachedUrl = getURLFromCache(fileID);
  if (cachedUrl) {
    console.log(`使用缓存的临时URL: ${fileID}`);
    callback(cachedUrl);
    return;
  }
  
  console.log('正在刷新临时文件URL, fileID:', fileID);
  
  // 调用云函数获取临时URL
  wx.cloud.getTempFileURL({
    fileList: [fileID],
    success: res => {
      console.log('获取临时URL成功:', res);
      if (res.fileList && res.fileList.length > 0 && res.fileList[0].tempFileURL) {
        const newUrl = res.fileList[0].tempFileURL;
        
        // 更新缓存（优先下载到本地文件系统）
        updateURLCache(fileID, newUrl);
        
        // 尝试下载到本地文件系统
        localImageCache.downloadImageToCache(fileID, newUrl)
          .then(localPath => {
            // 返回本地路径
            callback(localPath);
          })
          .catch(() => {
            // 如果下载失败，返回临时URL
            callback(newUrl);
          });
      } else {
        console.error('获取临时URL成功，但返回结果无效');
        // 如果重试次数小于2，则进行重试
        if (retryCount < 2) {
          console.log(`尝试第${retryCount + 1}次重试获取临时URL`);
          setTimeout(() => {
            refreshTempFileURL(fileID, callback, retryCount + 1);
          }, 1000); // 延迟1秒后重试
        } else {
          // 返回默认图片
          callback(getDefaultPreviewImageUrl());
        }
      }
    },
    fail: err => {
      console.error('获取临时URL失败:', err);
      // 如果重试次数小于2，则进行重试
      if (retryCount < 2) {
        console.log(`尝试第${retryCount + 1}次重试获取临时URL`);
        setTimeout(() => {
          refreshTempFileURL(fileID, callback, retryCount + 1);
        }, 1000); // 延迟1秒后重试
      } else {
        // 返回默认图片
        callback(getDefaultPreviewImageUrl());
      }
    }
  });
}

/**
 * 批量获取临时文件URL
 * @param {Array} fileIDs - 文件ID数组
 * @returns {Promise<Object>} 文件ID到临时URL的映射
 */
function getTempFileURLs(fileIDs) {
  return new Promise((resolve, reject) => {
    if (!fileIDs || !Array.isArray(fileIDs) || fileIDs.length === 0) {
      resolve({});
      return;
    }
    
    // 过滤掉无效的fileID
    const validFileIDs = fileIDs.filter(fileID => 
      fileID && typeof fileID === 'string' && fileID.includes('cloud://')
    );
    
    if (validFileIDs.length === 0) {
      console.warn('没有有效的云存储fileID');
      resolve({});
      return;
    }
    
    // 使用本地图片缓存模块批量处理图片
    localImageCache.batchProcessImages(validFileIDs)
      .then(fileIDToPath => {
        console.log(`批量处理图片完成，共处理 ${Object.keys(fileIDToPath).length} 张图片`);
        resolve(fileIDToPath);
      })
      .catch(err => {
        console.error('批量处理图片失败:', err);
        
        // 如果失败，尝试使用旧方法
        // 首先检查缓存中是否有这些fileID的临时URL
        const fileIDToURL = {};
        const uncachedFileIDs = [];
        
        validFileIDs.forEach(fileID => {
          const cachedUrl = getURLFromCache(fileID);
          if (cachedUrl) {
            fileIDToURL[fileID] = cachedUrl;
          } else {
            uncachedFileIDs.push(fileID);
          }
        });
        
        // 如果所有fileID都有缓存，直接返回
        if (uncachedFileIDs.length === 0) {
          resolve(fileIDToURL);
          return;
        }
        
        // 获取临时URL
        wx.cloud.getTempFileURL({
          fileList: uncachedFileIDs,
          success: res => {
            if (res.fileList && res.fileList.length > 0) {
              res.fileList.forEach(file => {
                if (file.fileID && file.tempFileURL) {
                  fileIDToURL[file.fileID] = file.tempFileURL;
                  // 更新缓存
                  updateURLCache(file.fileID, file.tempFileURL);
                }
              });
            }
            resolve(fileIDToURL);
          },
          fail: err => {
            console.error('批量获取临时URL失败:', err);
            // 即使失败，也返回已有的缓存结果
            resolve(fileIDToURL);
          }
        });
      });
  });
}

/**
 * 处理搭配预览图URL
 * @param {Object} outfit - 搭配数据
 * @returns {Promise<Object>} 处理后的搭配数据
 */
function processOutfitPreviewImage(outfit) {
  return new Promise((resolve, reject) => {
    if (!outfit) {
      reject(new Error('搭配数据为空'));
      return;
    }
    
    // 检查预览图
    const previewFileID = outfit.imageFileID || 
                         (outfit.previewImage && outfit.previewImage.includes('cloud://') ? 
                          outfit.previewImage : null);
    
    if (!previewFileID) {
      // 如果没有有效的fileID，使用默认图片
      outfit.previewImage = outfit.previewImage || getDefaultPreviewImageUrl();
      resolve(outfit);
      return;
    }
    
    // 从缓存获取或刷新临时URL
    refreshTempFileURL(previewFileID, (newUrl) => {
      if (newUrl) {
        outfit.previewImage = newUrl;
      } else {
        // 如果获取失败，使用默认图片
        outfit.previewImage = getDefaultPreviewImageUrl();
      }
      resolve(outfit);
    });
  });
}

/**
 * 处理搭配中的所有图片URL
 * @param {Object} outfit - 搭配数据
 * @returns {Promise<Object>} 处理后的搭配数据
 */
function processOutfitImages(outfit) {
  return new Promise((resolve, reject) => {
    if (!outfit) {
      reject(new Error('搭配数据为空'));
      return;
    }
    
    // 首先处理预览图
    processOutfitPreviewImage(outfit)
      .then(outfitWithPreview => {
        // 如果没有衣物项或衣物项不是数组，提前返回
        if (!outfitWithPreview.items || !Array.isArray(outfitWithPreview.items) || outfitWithPreview.items.length === 0) {
          resolve(outfitWithPreview);
          return;
        }
        
        // 收集所有需要处理的fileID
        const fileIDs = [];
        const fileIDToItemMap = {}; // 记录fileID对应的衣物项位置
        
        outfitWithPreview.items.forEach((item, index) => {
          // 检查有效的fileID（优先使用抠图后的图片）
          const fileID = item.processedImageFileID || item.imageFileID || 
                       (item.imageUrl && item.imageUrl.includes('cloud://') ? 
                        item.imageUrl : null);
          
          if (fileID) {
            fileIDs.push(fileID);
            fileIDToItemMap[fileID] = index;
          }
        });
        
        // 如果没有需要处理的fileID，提前返回
        if (fileIDs.length === 0) {
          // 确保所有衣物项都有有效的imageUrl
          outfitWithPreview.items.forEach(item => {
            if (!item.imageUrl || !isValidImageUrl(item.imageUrl)) {
              item.imageUrl = getDefaultItemImageUrl();
            }
          });
          
          resolve(outfitWithPreview);
          return;
        }
        
        // 批量获取临时URL
        getTempFileURLs(fileIDs)
          .then(fileIDToURL => {
            // 更新每个衣物项的imageUrl
            Object.entries(fileIDToURL).forEach(([fileID, url]) => {
              const itemIndex = fileIDToItemMap[fileID];
              if (itemIndex !== undefined && outfitWithPreview.items[itemIndex]) {
                outfitWithPreview.items[itemIndex].imageUrl = url;
              }
            });
            
            // 确保所有衣物项都有有效的imageUrl
            outfitWithPreview.items.forEach(item => {
              if (!item.imageUrl || !isValidImageUrl(item.imageUrl)) {
                item.imageUrl = getDefaultItemImageUrl();
              }
            });
            
            resolve(outfitWithPreview);
          })
          .catch(err => {
            console.error('处理搭配衣物图片失败:', err);
            
            // 即使失败，也确保所有衣物项都有有效的imageUrl
            outfitWithPreview.items.forEach(item => {
              if (!item.imageUrl || !isValidImageUrl(item.imageUrl)) {
                item.imageUrl = getDefaultItemImageUrl();
              }
            });
            
            resolve(outfitWithPreview);
          });
      })
      .catch(err => {
        console.error('处理搭配预览图失败:', err);
        // 如果处理预览图失败，仍然尝试返回原始搭配数据
        resolve(outfit);
      });
  });
}

/**
 * 处理多个搭配的所有图片URL
 * @param {Array} outfits - 搭配数据数组
 * @returns {Promise<Array>} 处理后的搭配数据数组
 */
function processOutfitsImages(outfits) {
  return new Promise((resolve, reject) => {
    if (!outfits || !Array.isArray(outfits)) {
      reject(new Error('搭配数据数组无效'));
      return;
    }
    
    // 如果没有搭配，返回空数组
    if (outfits.length === 0) {
      resolve([]);
      return;
    }
    
    console.log(`开始处理 ${outfits.length} 个搭配的图片`);
    
    // 并行处理所有搭配的图片
    const processPromises = outfits.map(outfit => processOutfitImages(outfit));
    
    Promise.all(processPromises)
      .then(processedOutfits => {
        console.log(`处理完成 ${processedOutfits.length} 个搭配的图片`);
        resolve(processedOutfits);
      })
      .catch(err => {
        console.error('处理搭配图片失败:', err);
        // 如果失败，返回原始搭配数据
        resolve(outfits);
      });
  });
}

/**
 * 清除过期的URL缓存
 */
function clearExpiredURLCache() {
  console.log('开始清除过期的URL缓存');
  
  try {
    // 清除本地文件系统中的过期缓存
    localImageCache.clearExpiredCache();
    
    // 清除Storage中的过期URL缓存
    const allKeys = wx.getStorageInfoSync().keys;
    const now = Date.now();
    let clearedCount = 0;
    
    allKeys.forEach(key => {
      if (key.startsWith('url_cache_')) {
        // 获取缓存数据
        const cachedData = wx.getStorageSync(key);
        
        // 检查是否过期（10分钟过期）
        if (cachedData && cachedData.timestamp && (now - cachedData.timestamp > 600000)) {
          wx.removeStorageSync(key);
          clearedCount++;
        }
      }
    });
    
    console.log(`清除了 ${clearedCount} 个过期的URL缓存`);
  } catch (err) {
    console.error('清除过期URL缓存出错:', err);
  }
}

// 导出模块接口
module.exports = {
  getDefaultPreviewImageUrl,
  getDefaultItemImageUrl,
  isValidImageUrl,
  getURLFromCache,
  updateURLCache,
  refreshTempFileURL,
  getTempFileURLs,
  processOutfitPreviewImage,
  processOutfitImages,
  processOutfitsImages,
  clearExpiredURLCache
}; 