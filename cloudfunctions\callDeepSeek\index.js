// 云函数入口文件
const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 获取调用参数
    const {
      prompt,             // 用户提示词（必需）
      systemPrompt,       // 系统提示词（可选）
      temperature = 0.9,  // 温度参数（可选，默认0.7）
      maxTokens = 2000,   // 最大token数（可选，默认2000）
      model = 'deepseek-chat' // 模型名称（可选，默认'deepseek-chat'）
    } = event
    
    // 验证必要参数
    if (!prompt) {
      return {
        success: false,
        error: '缺少必要参数prompt'
      }
    }
    
    // DeepSeek API密钥
    const apiKey = '***********************************'
    
    // 构建默认系统提示词
    //const defaultSystemPrompt = systemPrompt || 
      //'You are a helpful AI assistant. Respond concisely and accurately to the user\'s request.'

    const defaultSystemPrompt = '你是一个穿搭经验丰富的穿搭博主，请帮用户进行衣服穿搭（如果两次是同样的衣服，尽可能做出其他搭配）'
    
    // 调用DeepSeek API
    const response = await axios({
      method: 'POST',
      url: 'https://api.deepseek.com/chat/completions',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      data: {
        model: model,
        messages: [
          {
            role: 'system',
            content: defaultSystemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: parseFloat(temperature),
        max_tokens: parseInt(maxTokens),
        stream: false
      }
    })
    
    // 返回API响应
    return {
      success: true,
      ...response.data
    }
    
  } catch (error) {
    // 处理错误
    console.error('调用DeepSeek API出错:', error)
    
    // 返回友好的错误信息
    return {
      success: false,
      error: error.message || '调用DeepSeek API失败',
      errorCode: error.code || 'UNKNOWN_ERROR',
      // 如果有API返回的错误信息，也一并返回
      apiError: error.response ? error.response.data : null
    }
  }
} 