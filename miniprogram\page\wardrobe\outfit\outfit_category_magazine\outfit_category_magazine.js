// page/wardrobe/outfit/outfit_category_magazine/outfit_category_magazine.js

// 引入图片管理模块
const imageManager = require('../outfit_category/modules/imageManager');
// 引入搭配管理模块
const outfitManager = require('../outfit_category/modules/outfitManager');
// 引入本地图片缓存模块
const localImageCache = require('../outfit_category/modules/localImageCache');

// 定义颜色主题
const THEMES = {
  AUTUMN: 'autumn',
  PINK_BLUE: 'pinkBlue',
  BLACK_WHITE: 'blackWhite'
};

Page({
  data: {
    // 定义颜色常量 - 秋季色彩方案
    colors: {
      cowhide_cocoa: '#442D1C',   // 深棕色
      spiced_wine: '#74301C',     // 红棕色
      toasted_caramel: '#84592B', // 焦糖色
      olive_harvest: '#9D9167',   // 橄榄色
      golden_batter: '#E8D1A7',   // 金黄色
    },
    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',      // 深粉色
      pinkMedium: '#EEA0B2',    // 中粉色
      pinkLight: '#F9C9D6',     // 浅粉色
      blueLight: '#CBE0F9',     // 浅蓝色
      blueMedium: '#97C8E5',    // 中蓝色
      blueDark: '#5EA0D0',      // 深蓝色
    },
    // 黑白色系配色
    blackWhiteColors: {
      black: '#000000',         // 纯黑
      darkGray: '#333333',      // 深灰
      mediumGray: '#666666',    // 中灰
      lightGray: '#CCCCCC',     // 浅灰
      white: '#FFFFFF',         // 纯白
      offWhite: '#F5F5F5',      // 灰白
    },

    // 主题风格
    themeStyle: THEMES.AUTUMN,  // 默认秋季风格

    // 导航栏样式
    navBackground: '#d4d3ce',   // 默认导航栏背景色（统一使用灰色）
    navFrontColor: '#000000',   // 默认导航栏前景色
    navTitle: '个人穿搭集',   // 导航栏标题

    isLoading: true,            // 加载状态
    userOpenId: '',             // 用户OpenID

    // 类别信息
    category: '',               // 类别ID
    categoryName: '',           // 类别名称
    outfits: [],                // 所有搭配列表

    // 杂志翻页相关
    currentIndex: 0,            // 当前显示的搭配索引
    currentOutfit: null,        // 当前显示的搭配
    totalOutfits: 0,            // 搭配总数

    // 翻页动画
    animation: null,            // 动画实例
    animating: false,           // 是否正在动画中
    pageTurnClass: '',          // 翻页效果类名

    // 触摸事件相关
    touchStartX: 0,             // 触摸开始X坐标
    touchEndX: 0,               // 触摸结束X坐标

    // 页面交互相关
    showPageIndicator: true,    // 是否显示页码指示器
    showEmptyState: false,      // 是否显示空状态

    // 图片缓存控制
    imageRefreshNeeded: false,  // 标记图片是否需要刷新

    // 新增翻页状态控制
    isPageTurning: false,        // 是否正在翻页
    touchStartTime: 0,          // 触摸开始时间

    // 封面模式控制
    showCover: true,            // 默认显示封面

    // 页码拖动相关
    isDraggingPage: false,      // 是否正在拖动页码
    dragPageIndex: 0,           // 拖动时的页码索引
    sliderPosition: 0,          // 滑块位置百分比

    // 分享相关
    showShareMenu: false,       // 是否显示分享菜单
    shareImagePaths: {          // 分享图片路径
      cover: '',
      items: ''
    },
    isGeneratingImages: false,  // 是否正在生成图片
  },

  // URL检查定时器
  urlCheckTimer: null,

  // 页面加载
  onLoad: function(options) {
    // 初始化云环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-3gi97kso9ab01185',
        traceUser: true,
      });
    }

    // 初始化动画实例
    this.initAnimation();

    // 清除过期的URL缓存
    imageManager.clearExpiredURLCache();

    // 确保本地图片缓存目录存在
    localImageCache.ensureCacheDir();

    // 清除过期的本地图片缓存
    localImageCache.clearExpiredCache();

    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });
      // 应用主题样式
      this.applyThemeStyle(savedTheme);
    }

    // 获取类别参数
    if (options && options.category) {
      const category = options.category;

      // 设置类别名称
      const categoryNames = {
        'daily': '日常穿搭',
        'work': '职业穿搭',
        'party': '派对穿搭',
        'sport': '运动穿搭',
        'seasonal': '季节穿搭',
        'custom': '自定义穿搭'
      };

      const categoryName = categoryNames[category] || '穿搭集';
      const navTitle = `${categoryName}集`;

      this.setData({
        category: category,
        categoryName: categoryName,
        navTitle: navTitle
      });

      // 获取用户OpenID
      this.getUserOpenId();
    } else {
      wx.showToast({
        title: '缺少类别参数',
        icon: 'none'
      });

      // 延迟返回
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }

    // 初始化页面数据
    this.setData({
      isPageTurning: false,
      pageTurnClass: '',
      // ... 其他现有的初始化数据
    });

    // 加载数据
    this.loadOutfits();

    // 首次进入页面，提示长按上传封面功能
    wx.showToast({
      title: '长按封面可上传图片',
      icon: 'none',
      duration: 2000
    });
  },

  // 页面显示
  onShow: function() {
    console.log('页面显示，尝试获取最新数据');

    // 如果已经有类别信息，尝试获取最新数据
    if (this.data.category && this.data.userOpenId) {
      // 启动定时器，定期检查图片是否需要刷新（每5分钟检查一次）
      this.startURLCheckTimer();
    }
  },

  // 页面隐藏
  onHide: function() {
    // 页面隐藏时清除定时器
    this.clearURLCheckTimer();
  },

  // 页面卸载
  onUnload: function() {
    // 页面卸载时清除定时器
    this.clearURLCheckTimer();
  },

  // 初始化动画实例
  initAnimation: function() {
    this.animation = wx.createAnimation({
      duration: 300,
      timingFunction: 'ease',
      delay: 0
    });
  },

  // 应用主题样式
  applyThemeStyle: function(themeStyle) {
    let navBackground, navFrontColor;

    // 统一使用灰色导航栏背景
    navBackground = '#d4d3ce';
    navFrontColor = '#000000';

    // 保留主题切换逻辑，但不再改变导航栏颜色
    switch (themeStyle) {
      case THEMES.AUTUMN:
        // 使用灰色导航栏
        break;
      case THEMES.PINK_BLUE:
        // 使用灰色导航栏
        break;
      case THEMES.BLACK_WHITE:
        // 使用灰色导航栏
        break;
      default:
        // 使用灰色导航栏
    }

    this.setData({
      navBackground: navBackground,
      navFrontColor: navFrontColor
    });
  },

  // 启动URL检查定时器
  startURLCheckTimer: function() {
    // 清除可能存在的旧定时器
    this.clearURLCheckTimer();

    // 创建新定时器，每5分钟检查一次
    this.urlCheckTimer = setInterval(() => {
      this.refreshImages();
    }, 300000); // 5分钟

    console.log('已启动图片检查定时器');
  },

  // 清除URL检查定时器
  clearURLCheckTimer: function() {
    if (this.urlCheckTimer) {
      clearInterval(this.urlCheckTimer);
      this.urlCheckTimer = null;
      console.log('已清除图片检查定时器');
    }
  },

  // 刷新所有图片
  refreshImages: function() {
    console.log('开始刷新图片');

    if (!this.data.outfits || this.data.outfits.length === 0) {
      console.log('没有搭配数据，不需要刷新图片');
      return;
    }

    this.setData({
      imageRefreshNeeded: true
    }, () => {
      // 使用imageManager处理所有搭配的图片
      imageManager.processOutfitsImages(this.data.outfits)
        .then(processedOutfits => {
          console.log('图片刷新完成，已处理', processedOutfits.length, '个搭配');

          // 更新数据
          this.setData({
            outfits: processedOutfits,
            imageRefreshNeeded: false
          });

          // 更新当前显示的搭配
          if (processedOutfits.length > 0) {
            this.updateCurrentOutfit();
          }
        })
        .catch(err => {
          console.error('刷新图片出错:', err);
          this.setData({
            imageRefreshNeeded: false
          });
        });
    });
  },

  // 获取用户OpenID
  getUserOpenId: function() {
    // 尝试从缓存获取
    try {
      const userOpenId = wx.getStorageSync('userOpenId');
      if (userOpenId) {
        this.setData({
          userOpenId: userOpenId
        }, () => {
          // 加载搭配数据
          this.loadOutfits();
        });
        return;
      }
    } catch (e) {
      console.error('从缓存获取OpenID失败:', e);
    }

    // 如果缓存中没有，则从云函数获取
    wx.cloud.callFunction({
      name: 'login',
      data: {},
      success: res => {
        console.log('获取用户OpenID成功:', res.result.openid);
        const userOpenId = res.result.openid;

        // 保存到本地缓存
        wx.setStorageSync('userOpenId', userOpenId);

        // 更新数据并加载搭配
        this.setData({
          userOpenId: userOpenId
        }, () => {
          // 加载搭配数据
          this.loadOutfits();
        });
      },
      fail: err => {
        console.error('获取用户OpenID失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载搭配数据
  loadOutfits: function() {
    this.setData({
      isLoading: true
    });

    // 查询数据库获取该类别的搭配
    const db = wx.cloud.database();
    const _ = db.command;
    const outfitsCollection = db.collection('outfits');

    // 构建查询条件
    const query = {
      category: this.data.category
    };

    // 如果有用户OpenID，添加到查询条件
    if (this.data.userOpenId) {
      query._openid = this.data.userOpenId;
    }

    console.log('从云数据库查询搭配，查询条件:', query);

    outfitsCollection.where(query)
      .orderBy('createTime', 'desc')
      .get()
      .then(res => {
        console.log('获取搭配成功:', res.data);

        // 处理搭配数据
        let outfits = res.data || [];

        // 如果没有搭配数据，显示空状态
        if (outfits.length === 0) {
          console.log('当前类别没有搭配数据');

          this.setData({
            outfits: [],
            totalOutfits: 0,
            isLoading: false,
            showEmptyState: true
          });

          // 隐藏加载提示
          wx.hideLoading();
          return;
        }

        console.log('获取到', outfits.length, '条搭配数据');

        // 格式化搭配数据，确保有必要的字段
        outfits = outfits.map(outfit => {
          // 确保outfit有id字段
          if (!outfit.id && outfit._id) {
            outfit.id = outfit._id;
          }

          // 确保outfit有类型字段
          if (!outfit.type && outfit.category) {
            outfit.type = outfit.category;
          } else if (!outfit.type && !outfit.category) {
            // 默认类型为日常
            outfit.type = 'daily';
            outfit.category = 'daily';
          }

          return outfit;
        });

        // 检查是否需要获取完整的衣物数据
        const needFetchClothes = outfits.some(outfit =>
          outfit.items && Array.isArray(outfit.items) &&
          outfit.items.some(item => item.clothingId && (!item.name || !item.imageUrl || !item.imageFileID))
        );

        if (needFetchClothes) {
          console.log('需要获取衣物详细数据');

          // 收集所有需要获取的衣物ID
          const allClothingIds = [];
          outfits.forEach(outfit => {
            if (outfit.items && Array.isArray(outfit.items)) {
              outfit.items.forEach(item => {
                if (item && item.clothingId && (!item.name || !item.imageUrl || !item.imageFileID)) {
                  allClothingIds.push(item.clothingId);
                }
              });
            }
          });

          if (allClothingIds.length > 0) {
            console.log('准备获取', allClothingIds.length, '个衣物的详细数据');

            // 获取衣物数据
            this.getClothesData(allClothingIds)
              .then(clothesData => {
                console.log('获取到', clothesData.length, '个衣物详细数据');

                // 更新每个搭配的衣物项数据
                outfits.forEach(outfit => {
                  if (outfit.items && Array.isArray(outfit.items)) {
                    outfit.items = outfit.items.map(item => {
                      if (item && item.clothingId) {
                        const clothingData = clothesData.find(c => c && c._id === item.clothingId);
                        if (clothingData) {
                          // 优先使用抠图后的图片 (processedImageFileID)
                          const imageFileID = clothingData.processedImageFileID || clothingData.imageFileID || clothingData.imageUrl || null;

                          // 合并衣物数据
                          return {
                            ...item,
                            name: item.name || clothingData.name,
                            type: item.type || clothingData.type || clothingData.category,
                            category: item.category || clothingData.category,
                            // 优先使用抠图后的图片URL或文件ID
                            imageUrl: item.imageUrl || clothingData.processedImageUrl || clothingData.imageUrl || clothingData.processedImageFileID,
                            // 保存原始fileID和抠图后的fileID用于后续刷新
                            imageFileID: imageFileID,
                            processedImageFileID: clothingData.processedImageFileID || null,
                            originalImageFileID: clothingData.imageFileID || null,
                            originalClothing: clothingData
                          };
                        }
                      }
                      return item;
                    });
                  }
                });

                // 处理所有搭配的图片URL
                return imageManager.processOutfitsImages(outfits);
              })
              .then(processedOutfits => {
                console.log('图片处理完成，更新UI');

                // 确保每个搭配都有预览图
                processedOutfits = this.ensurePreviewImages(processedOutfits);

                this.setData({
                  outfits: processedOutfits,
                  totalOutfits: processedOutfits.length,
                  isLoading: false,
                  showEmptyState: false
                }, () => {
                  // 如果有搭配数据，更新当前显示的搭配
                  if (processedOutfits.length > 0) {
                    this.updateCurrentOutfit();
                  }
                });

                // 隐藏加载提示
                wx.hideLoading();
              })
              .catch(err => {
                console.error('处理搭配衣物详细数据出错:', err);

                // 即使出错，也尝试处理图片并显示搭配数据
                imageManager.processOutfitsImages(outfits)
                  .then(processedOutfits => {
                    // 确保每个搭配都有预览图
                    processedOutfits = this.ensurePreviewImages(processedOutfits);

                    this.setData({
                      outfits: processedOutfits,
                      totalOutfits: processedOutfits.length,
                      isLoading: false,
                      showEmptyState: false
                    }, () => {
                      // 如果有搭配数据，更新当前显示的搭配
                      if (processedOutfits.length > 0) {
                        this.updateCurrentOutfit();
                      }
                    });

                    // 隐藏加载提示
                    wx.hideLoading();
                  })
                  .catch(imgErr => {
                    console.error('处理搭配图片出错:', imgErr);

                    // 确保每个搭配都有预览图
                    outfits = this.ensurePreviewImages(outfits);

                    this.setData({
                      outfits: outfits,
                      totalOutfits: outfits.length,
                      isLoading: false,
                      showEmptyState: outfits.length === 0
                    }, () => {
                      // 如果有搭配数据，更新当前显示的搭配
                      if (outfits.length > 0) {
                        this.updateCurrentOutfit();
                      }
                    });

                    wx.hideLoading();
                  });
              });
          } else {
            // 即使没有需要获取的衣物ID，也需要处理图片
            imageManager.processOutfitsImages(outfits)
              .then(processedOutfits => {
                console.log('图片处理完成，更新UI');

                // 确保每个搭配都有预览图
                processedOutfits = this.ensurePreviewImages(processedOutfits);

                this.setData({
                  outfits: processedOutfits,
                  totalOutfits: processedOutfits.length,
                  isLoading: false,
                  showEmptyState: false
                }, () => {
                  // 如果有搭配数据，更新当前显示的搭配
                  if (processedOutfits.length > 0) {
                    this.updateCurrentOutfit();
                  }
                });

                // 隐藏加载提示
                wx.hideLoading();
              })
              .catch(err => {
                console.error('处理搭配图片出错:', err);

                // 确保每个搭配都有预览图
                outfits = this.ensurePreviewImages(outfits);

                this.setData({
                  outfits: outfits,
                  totalOutfits: outfits.length,
                  isLoading: false,
                  showEmptyState: outfits.length === 0
                }, () => {
                  // 如果有搭配数据，更新当前显示的搭配
                  if (outfits.length > 0) {
                    this.updateCurrentOutfit();
                  }
                });

                wx.hideLoading();
              });
          }
        } else {
          // 即使没有需要获取的衣物数据，也需要处理图片
          imageManager.processOutfitsImages(outfits)
            .then(processedOutfits => {
              console.log('图片处理完成，更新UI');

              // 确保每个搭配都有预览图
              processedOutfits = this.ensurePreviewImages(processedOutfits);

              this.setData({
                outfits: processedOutfits,
                totalOutfits: processedOutfits.length,
                isLoading: false,
                showEmptyState: false
              }, () => {
                // 如果有搭配数据，更新当前显示的搭配
                if (processedOutfits.length > 0) {
                  this.updateCurrentOutfit();
                }
              });

              // 隐藏加载提示
              wx.hideLoading();
            })
            .catch(err => {
              console.error('处理搭配图片出错:', err);

              // 确保每个搭配都有预览图
              outfits = this.ensurePreviewImages(outfits);

              this.setData({
                outfits: outfits,
                totalOutfits: outfits.length,
                isLoading: false,
                showEmptyState: outfits.length === 0
              }, () => {
                // 如果有搭配数据，更新当前显示的搭配
                if (outfits.length > 0) {
                  this.updateCurrentOutfit();
                }
              });

              wx.hideLoading();
            });
        }
      })
      .catch(err => {
        console.error('加载搭配数据失败:', err);
        this.setData({
          isLoading: false,
          showEmptyState: true
        });

        wx.showToast({
          title: '加载搭配失败',
          icon: 'none'
        });

        wx.hideLoading();
      });
  },

  // 获取衣物数据
  getClothesData: function(clothingIds) {
    return new Promise((resolve, reject) => {
      if (!clothingIds || !Array.isArray(clothingIds) || clothingIds.length === 0) {
        resolve([]);
        return;
      }

      console.log('获取衣物数据，ID数量:', clothingIds.length);

      const db = wx.cloud.database();
      const _ = db.command;

      // 查询条件：衣物ID在指定数组中
      db.collection('clothes')
        .where({
          _id: _.in(clothingIds)
        })
        .get()
        .then(res => {
          console.log('获取衣物数据成功:', res.data);
          resolve(res.data || []);
        })
        .catch(err => {
          console.error('获取衣物数据失败:', err);
          resolve([]);
        });
    });
  },

  // 确保每个搭配都有预览图
  ensurePreviewImages: function(outfits) {
    if (!outfits || !Array.isArray(outfits)) {
      return [];
    }

    return outfits.map(outfit => {
      // 如果有outfit_cover属性，优先使用它作为预览图
      if (outfit.outfit_cover) {
        // 确保同时设置previewImage以保持兼容性
        if (!outfit.previewImage) {
          outfit.previewImage = outfit.outfit_cover;
        }
        return outfit;
      }

      // 如果没有预览图，尝试使用第一个衣物的图片作为预览图
      if (!outfit.previewImage && outfit.items && outfit.items.length > 0 && outfit.items[0].imageUrl) {
        outfit.previewImage = outfit.items[0].imageUrl;
      }

      // 如果仍然没有预览图，使用默认图片
      if (!outfit.previewImage) {
        outfit.previewImage = imageManager.getDefaultPreviewImageUrl();
      }

      return outfit;
    });
  },

  // 更新当前显示的搭配
  updateCurrentOutfit: function() {
    if(this.data.outfits && this.data.outfits.length > 0) {
      const currentOutfit = this.data.outfits[this.data.currentIndex];
      this.setData({
        currentOutfit: currentOutfit
      });
    }
  },

  // 跳转到创建搭配页面
  goToCreateOutfit: function() {
    wx.navigateTo({
      url: `/page/wardrobe/outfit/outfit_create/outfit_create?category=${this.data.category}`
    });
  },

  // 查看搭配详情
  viewOutfitDetail: function(e) {
    const outfitId = e.currentTarget.dataset.id;

    wx.navigateTo({
      url: `/page/wardrobe/outfit/outfit_detail/outfit_detail?id=${outfitId}`
    });
  },

  // 处理图片加载错误
  handleImageError: function(e) {
    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;
    const itemIndex = e.currentTarget.dataset.itemIndex;

    console.log('图片加载失败:', type, index, itemIndex);

    if (type === 'preview') {
      // 预览图加载失败，设置默认图片
      const updatedOutfits = [...this.data.outfits];
      const defaultPreviewUrl = imageManager.getDefaultPreviewImageUrl();

      // 清除outfit_cover和previewImage属性
      updatedOutfits[index].outfit_cover = null;
      updatedOutfits[index].previewImage = defaultPreviewUrl;

      this.setData({
        outfits: updatedOutfits
      });

      if (index === this.data.currentIndex) {
        this.setData({
          'currentOutfit.outfit_cover': null,
          'currentOutfit.previewImage': defaultPreviewUrl
        });
      }
    } else if (type === 'item') {
      // 衣物图片加载失败，设置默认图片
      const updatedOutfits = [...this.data.outfits];
      if (updatedOutfits[index] && updatedOutfits[index].items && updatedOutfits[index].items[itemIndex]) {
        updatedOutfits[index].items[itemIndex].imageUrl = imageManager.getDefaultItemImageUrl();
      }

      this.setData({
        outfits: updatedOutfits
      });

      if (index === this.data.currentIndex) {
        this.setData({
          [`currentOutfit.items[${itemIndex}].imageUrl`]: imageManager.getDefaultItemImageUrl()
        });
      }
    }
  },

  // 切换封面模式和内容模式
  toggleCoverMode: function() {
    if (this.data.isAnimating) return;

    this.setData({
      isAnimating: true,
      pageTurnClass: 'page-turn-left'
    });

    // 延迟切换，让翻页动画有时间播放
    setTimeout(() => {
      this.setData({
        showCover: !this.data.showCover,
        pageTurnClass: '',
        isAnimating: false
      });

      // 播放翻页声音
      this.playPageTurnSound();
    }, 300);
  },

  // 跳转到搭配详情页
  goToOutfitDetail: function() {
    if (!this.data.currentOutfit) return;

    wx.navigateTo({
      url: `/page/wardrobe/outfit/outfit_detail/outfit_detail?id=${this.data.currentOutfit._id || this.data.currentOutfit.id}`
    });
  },

  // 处理翻页按钮点击
  onPageButtonTap: function(e) {
    const direction = e.currentTarget.dataset.direction;

    // 避免动画期间重复触发
    if (this.data.isAnimating) {
      return;
    }

    // 设置动画标志
    this.setData({
      isAnimating: true
    });

    if (direction === 'prev' && this.data.currentIndex > 0) {
      // 播放翻页声音
      this.playPageTurnSound();

      // 添加向右翻页的动画类 - 修正为right
      this.setData({
        pageTurnClass: 'page-turn-right'
      });

      // 延迟切换到前一个搭配
      setTimeout(() => {
        const newIndex = this.data.currentIndex - 1;
        this.setData({
          currentIndex: newIndex,
          currentOutfit: this.data.outfits[newIndex],
          pageTurnClass: '',
          isAnimating: false,
          showCover: true // 切换搭配时恢复为封面模式
        });
      }, 600); // 动画持续时间

    } else if (direction === 'next' && this.data.currentIndex < this.data.totalOutfits - 1) {
      // 播放翻页声音
      this.playPageTurnSound();

      // 添加向左翻页的动画类 - 修正为left
      this.setData({
        pageTurnClass: 'page-turn-left'
      });

      // 延迟切换到下一个搭配
      setTimeout(() => {
        const newIndex = this.data.currentIndex + 1;
        this.setData({
          currentIndex: newIndex,
          currentOutfit: this.data.outfits[newIndex],
          pageTurnClass: '',
          isAnimating: false,
          showCover: true // 切换搭配时恢复为封面模式
        });
      }, 600); // 动画持续时间
    }
  },

  // 播放翻页声音
  playPageTurnSound: function() {
    const app = getApp();
    // 检查是否有全局音效资源配置
    if (app && app.globalData && app.globalData.audioResources && app.globalData.audioResources.pageTurn) {
      // 使用微信的音频API播放翻页声音
      const audioContext = wx.createInnerAudioContext();
      audioContext.src = app.globalData.audioResources.pageTurn; // 使用全局配置的音效路径
      audioContext.play();
      console.log('播放翻页音效:', app.globalData.audioResources.pageTurn);
    } else {
      console.warn('未找到翻页音效资源');
    }
  },

  // 触摸开始时记录位置
  touchStart: function(e) {
    if (this.data.isAnimating) return;

    // 记录开始触摸的X坐标
    this.startX = e.touches[0].pageX;
    this.touchStartTime = Date.now();
  },

  // 触摸结束时判断是否需要翻页
  touchEnd: function(e) {
    if (this.data.isAnimating || !this.startX) return;

    const endX = e.changedTouches[0].pageX;
    const distanceX = endX - this.startX;
    const touchEndTime = Date.now();
    const touchDuration = touchEndTime - this.touchStartTime;

    // 计算滑动速度
    const velocity = Math.abs(distanceX) / touchDuration;

    // 滑动距离大于20px或速度足够快时触发翻页
    if (Math.abs(distanceX) > 20 || velocity > 0.25) {
      // 向左滑动，显示下一页
      if (distanceX < 0 && this.data.currentIndex < this.data.totalOutfits - 1) {
        this.onPageButtonTap({
          currentTarget: {
            dataset: { direction: 'next' }
          }
        });
      }
      // 向右滑动，显示上一页
      else if (distanceX > 0 && this.data.currentIndex > 0) {
        this.onPageButtonTap({
          currentTarget: {
            dataset: { direction: 'prev' }
          }
        });
      }
    }

    // 重置触摸位置
    this.startX = null;
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 格式化日期
  formatDate: function(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  /**
   * 处理长按事件，上传封面图
   */
  handleLongPress: function() {
    const that = this;
    wx.showActionSheet({
      itemList: ['上传封面图'],
      success: function(res) {
        if (res.tapIndex === 0) {
          // 上传封面图
          wx.chooseImage({
            count: 1,
            sizeType: ['compressed'],
            sourceType: ['album', 'camera'],
            success: function(res) {
              const tempFilePath = res.tempFilePaths[0];
              const currentOutfitId = that.data.currentOutfit._id;

              // 显示加载提示
              wx.showLoading({
                title: '正在上传...',
                mask: true
              });

              // 直接上传图片到服务器或云存储
              that.uploadCoverImage(tempFilePath, currentOutfitId);
            }
          });
        }
      }
    });
  },

  /**
   * 上传封面图到服务器或云存储
   */
  uploadCoverImage: function(tempFilePath, outfitId) {
    const that = this;
    // 判断是否使用云开发
    if (wx.cloud) {
      const cloudPath = `outfit_covers/${outfitId}_${new Date().getTime()}.jpg`;

      wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: tempFilePath,
        success: res => {
          // 获取图片的云存储地址
          const fileID = res.fileID;
          // 更新搭配的封面图
          that.updateOutfitCoverImage(outfitId, fileID);
        },
        fail: e => {
          wx.hideLoading();
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          });
          console.error('[上传封面] 失败：', e);
        }
      });
    } else {
      // 非云开发环境，使用普通上传API
      // 此处根据实际后端接口实现
      wx.uploadFile({
        url: 'YOUR_UPLOAD_API_URL', // 替换为实际的上传接口
        filePath: tempFilePath,
        name: 'file',
        formData: {
          'outfitId': outfitId
        },
        success: function(res) {
          const data = JSON.parse(res.data);
          if (data.code === 0) {
            // 上传成功，更新搭配的封面图
            that.updateOutfitCoverImage(outfitId, data.url);
          } else {
            wx.hideLoading();
            wx.showToast({
              title: data.msg || '上传失败',
              icon: 'none'
            });
          }
        },
        fail: function() {
          wx.hideLoading();
          wx.showToast({
            title: '网络异常，请重试',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 更新搭配的封面图
   */
  updateOutfitCoverImage: function(outfitId, imageUrl) {
    const that = this;
    // 判断是否使用云开发
    if (wx.cloud) {
      const db = wx.cloud.database();
      db.collection('outfits').doc(outfitId).update({
        data: {
          previewImage: imageUrl,  // 保留原有的previewImage更新，保持向后兼容
          outfit_cover: imageUrl,  // 新增outfit_cover属性
          updateTime: db.serverDate()
        },
        success: function() {
          wx.hideLoading();
          wx.showToast({
            title: '封面更新成功',
            icon: 'success'
          });

          // 更新本地数据
          const currentIndex = that.data.currentIndex;
          const outfits = that.data.outfits;
          outfits[currentIndex].previewImage = imageUrl;
          outfits[currentIndex].outfit_cover = imageUrl;  // 同时更新outfit_cover属性
          that.setData({
            outfits: outfits,
            currentOutfit: outfits[currentIndex]
          });
        },
        fail: function(err) {
          wx.hideLoading();
          wx.showToast({
            title: '封面更新失败',
            icon: 'none'
          });
          console.error('[更新封面] 失败：', err);
        }
      });
    } else {
      // 非云开发环境，使用普通API更新
      // 此处根据实际后端接口实现
      wx.request({
        url: 'YOUR_UPDATE_API_URL', // 替换为实际的更新接口
        method: 'POST',
        data: {
          outfitId: outfitId,
          previewImage: imageUrl,
          outfit_cover: imageUrl  // 同时更新outfit_cover属性
        },
        success: function(res) {
          const data = res.data;
          if (data.code === 0) {
            wx.hideLoading();
            wx.showToast({
              title: '封面更新成功',
              icon: 'success'
            });

            // 更新本地数据
            const currentIndex = that.data.currentIndex;
            const outfits = that.data.outfits;
            outfits[currentIndex].previewImage = imageUrl;
            outfits[currentIndex].outfit_cover = imageUrl;  // 同时更新outfit_cover属性
            that.setData({
              outfits: outfits,
              currentOutfit: outfits[currentIndex]
            });
          } else {
            wx.hideLoading();
            wx.showToast({
              title: data.msg || '封面更新失败',
              icon: 'none'
            });
          }
        },
        fail: function() {
          wx.hideLoading();
          wx.showToast({
            title: '网络异常，请重试',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 开始页码拖动 - 点击页码指示器时触发
   */
  startPageDrag: function(e) {
    if (this.data.totalOutfits <= 1) return; // 只有一个搭配时不需要拖动功能

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 初始化拖动状态
    this.setData({
      isDraggingPage: true,
      dragPageIndex: this.data.currentIndex,
      sliderPosition: (this.data.currentIndex / (this.data.totalOutfits - 1)) * 100
    });

    // 记录触摸起始位置和索引
    if (e.touches && e.touches.length > 0) {
      this.dragStartX = e.touches[0].clientX;
    } else {
      // 如果没有触摸信息，使用屏幕中心点
      const systemInfo = wx.getSystemInfoSync();
      this.dragStartX = systemInfo.windowWidth / 2;
    }
    this.dragStartIndex = this.data.currentIndex;

    // 显示提示
    wx.showToast({
      title: '拖动滑块快速定位',
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 滑块触摸开始 - 直接触摸滑块时触发
   */
  onThumbTouchStart: function(e) {
    // 阻止事件冒泡
    e.stopPropagation();

    // 如果已经在拖动中，不需要重新初始化
    if (this.data.isDraggingPage) return;

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 初始化拖动状态
    this.setData({
      isDraggingPage: true,
      dragPageIndex: this.data.currentIndex,
      sliderPosition: (this.data.currentIndex / (this.data.totalOutfits - 1)) * 100
    });

    // 记录触摸起始位置和索引
    this.dragStartX = e.touches[0].clientX;
    this.dragStartIndex = this.data.currentIndex;

    // 标记为滑块拖动模式
    this.isThumbDragging = true;
  },

  /**
   * 滑动条触摸开始 - 触摸整个滑动条区域时触发
   */
  onSliderTouchStart: function(e) {
    // 如果已经在拖动中，不需要重新初始化
    if (this.data.isDraggingPage) return;

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();

    // 计算滑动条的宽度和位置
    const sliderWidth = 600 * systemInfo.windowWidth / 750;
    const sliderLeft = (systemInfo.windowWidth - sliderWidth) / 2;

    // 计算触摸点在滑动条上的相对位置
    const touchX = e.touches[0].clientX;
    const relativeX = touchX - sliderLeft;
    const positionRatio = Math.max(0, Math.min(1, relativeX / sliderWidth));

    // 计算对应的索引
    const totalSteps = this.data.totalOutfits - 1;
    const newIndex = Math.round(positionRatio * totalSteps);

    // 计算滑块位置
    const sliderPosition = (newIndex / totalSteps) * 100;

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 更新状态
    this.setData({
      isDraggingPage: true,
      dragPageIndex: newIndex,
      sliderPosition: sliderPosition
    });

    // 记录触摸起始位置和索引
    this.dragStartX = touchX;
    this.dragStartIndex = newIndex;
  },

  /**
   * 滑动轨道点击 - 点击滑动轨道时直接跳转到对应位置
   */
  onSliderTrackTap: function(e) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();

    // 计算滑动条的宽度和位置
    const sliderWidth = 600 * systemInfo.windowWidth / 750;
    const sliderLeft = (systemInfo.windowWidth - sliderWidth) / 2;

    // 计算触摸点在滑动条上的相对位置
    const touchX = e.changedTouches[0].clientX;
    const relativeX = touchX - sliderLeft;
    const positionRatio = Math.max(0, Math.min(1, relativeX / sliderWidth));

    // 计算对应的索引
    const totalSteps = this.data.totalOutfits - 1;
    const newIndex = Math.round(positionRatio * totalSteps);

    // 如果索引没有变化，不做任何操作
    if (newIndex === this.data.currentIndex) return;

    // 添加振动反馈
    wx.vibrateShort({
      type: 'medium'
    });

    // 设置动画标志
    this.setData({
      isAnimating: true,
      // 根据方向设置翻页动画
      pageTurnClass: newIndex > this.data.currentIndex ? 'page-turn-left' : 'page-turn-right'
    });

    // 播放翻页声音
    this.playPageTurnSound();

    // 延迟切换到目标搭配
    setTimeout(() => {
      this.setData({
        currentIndex: newIndex,
        currentOutfit: this.data.outfits[newIndex],
        pageTurnClass: '',
        isAnimating: false,
        showCover: true, // 切换搭配时恢复为封面模式
        isDraggingPage: false // 结束拖动状态
      });

      // 显示跳转提示
      wx.showToast({
        title: `已跳转到第${newIndex + 1}套搭配`,
        icon: 'none',
        duration: 1500
      });
    }, 400);
  },

  /**
   * 页码拖动中
   */
  onPageDrag: function(e) {
    if (!this.data.isDraggingPage) return;

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();

    // 计算滑动条的宽度和位置
    const sliderWidth = 600 * systemInfo.windowWidth / 750;
    const sliderLeft = (systemInfo.windowWidth - sliderWidth) / 2;

    // 获取当前触摸位置
    const touchX = e.touches[0].clientX;

    // 如果是滑块拖动模式，使用相对移动距离
    if (this.isThumbDragging) {
      // 计算拖动距离
      const moveX = touchX - this.dragStartX;

      // 计算滑动比例
      const moveRatio = moveX / sliderWidth;

      // 计算新的索引位置
      const totalSteps = this.data.totalOutfits - 1;
      let newIndex;

      if (totalSteps <= 0) {
        newIndex = 0;
      } else {
        // 计算拖动后的位置比例
        const positionRatio = this.dragStartIndex / totalSteps + moveRatio;
        // 将比例转换为索引
        newIndex = Math.round(positionRatio * totalSteps);
        // 限制索引范围
        newIndex = Math.max(0, Math.min(totalSteps, newIndex));
      }

      // 计算滑块位置百分比
      const sliderPosition = totalSteps <= 0 ? 50 : (newIndex / totalSteps) * 100;

      // 更新数据
      this.setData({
        dragPageIndex: newIndex,
        sliderPosition: sliderPosition
      });
    } else {
      // 直接根据触摸位置计算滑块位置
      // 计算触摸点在滑动条上的相对位置
      const relativeX = touchX - sliderLeft;
      const positionRatio = Math.max(0, Math.min(1, relativeX / sliderWidth));

      // 计算对应的索引
      const totalSteps = this.data.totalOutfits - 1;
      const newIndex = Math.round(positionRatio * totalSteps);

      // 计算滑块位置
      const sliderPosition = (newIndex / totalSteps) * 100;

      // 更新状态
      this.setData({
        dragPageIndex: newIndex,
        sliderPosition: sliderPosition
      });
    }

    // 添加触觉反馈，但只在索引变化时
    if (this.lastDragIndex !== this.data.dragPageIndex) {
      wx.vibrateShort({
        type: 'light'
      });
      this.lastDragIndex = this.data.dragPageIndex;
    }

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
  },

  /**
   * 结束页码拖动
   */
  endPageDrag: function(e) {
    if (!this.data.isDraggingPage) return;

    // 获取最终选择的索引
    const targetIndex = this.data.dragPageIndex;

    // 重置拖动索引记录和滑块拖动模式
    this.lastDragIndex = null;
    this.isThumbDragging = false;

    // 如果索引发生变化，跳转到对应搭配
    if (targetIndex !== this.data.currentIndex) {
      // 添加振动反馈，表示确认选择
      wx.vibrateShort({
        type: 'medium'
      });

      // 设置动画标志
      this.setData({
        isAnimating: true,
        // 根据方向设置翻页动画
        pageTurnClass: targetIndex > this.data.currentIndex ? 'page-turn-left' : 'page-turn-right'
      });

      // 播放翻页声音
      this.playPageTurnSound();

      // 先隐藏进度条，再切换页面
      this.setData({
        isDraggingPage: false
      });

      // 延迟切换到目标搭配
      setTimeout(() => {
        this.setData({
          currentIndex: targetIndex,
          currentOutfit: this.data.outfits[targetIndex],
          pageTurnClass: '',
          isAnimating: false,
          showCover: true // 切换搭配时恢复为封面模式
        });

        // 显示跳转提示
        wx.showToast({
          title: `已跳转到第${targetIndex + 1}套搭配`,
          icon: 'none',
          duration: 1500
        });
      }, 400); // 动画持续时间
    } else {
      // 索引未变化，直接结束拖动状态
      this.setData({
        isDraggingPage: false
      });
    }

    // 阻止事件冒泡
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  },

  // 显示分享菜单
  shareOutfit: function() {
    if (!this.data.currentOutfit) {
      wx.showToast({
        title: '无搭配可分享',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showShareMenu: true
    });
  },

  // 隐藏分享菜单
  hideShareMenu: function() {
    this.setData({
      showShareMenu: false
    });
  },

  // 防止冒泡
  preventBubble: function(e) {
    e.stopPropagation();
  },

  // 防止滑动穿透
  preventTouchMove: function(e) {
    return false;
  },

  // 生成并保存分享图片
  generateAndSaveImages: function() {
    if (this.data.isGeneratingImages) {
      return;
    }

    this.setData({
      isGeneratingImages: true
    });

    wx.showLoading({
      title: '生成图片中...',
      mask: true
    });

    // 首先绘制封面图片
    this.drawCoverImage()
      .then(coverPath => {
        console.log('封面图片生成成功:', coverPath);

        // 然后绘制衣物布局图片
        return this.drawItemsImage()
          .then(itemsPath => {
            console.log('衣物布局图片生成成功:', itemsPath);

            // 保存两个图片的路径
            this.setData({
              'shareImagePaths.cover': coverPath,
              'shareImagePaths.items': itemsPath,
              isGeneratingImages: false
            });

            wx.hideLoading();

            // 保存图片到相册
            this.saveImagesToAlbum();
          });
      })
      .catch(err => {
        console.error('生成分享图片失败:', err);
        this.setData({
          isGeneratingImages: false
        });
        wx.hideLoading();
        wx.showToast({
          title: '生成图片失败',
          icon: 'none'
        });
      });
  },

  // 绘制封面图片
  drawCoverImage: function() {
    return new Promise((resolve, reject) => {
      const outfit = this.data.currentOutfit;
      if (!outfit) {
        reject(new Error('无搭配数据'));
        return;
      }

      // 获取封面图片地址
      const coverImage = outfit.outfit_cover || outfit.previewImage;
      if (!coverImage) {
        reject(new Error('搭配无封面图片'));
        return;
      }

      // 下载图片到本地
      wx.getImageInfo({
        src: coverImage,
        success: imgInfo => {
          // 获取Canvas上下文
          const ctx = wx.createCanvasContext('shareCanvas');
          
          // 设置画布背景 - 使用灰白色背景
          ctx.setFillStyle('#f5f5f5');
          ctx.fillRect(0, 0, 750, 1000);

          // 绘制底部桌面反光阴影效果
          const gradient = ctx.createLinearGradient(375, 975, 375, 1000);
          gradient.addColorStop(0, 'rgba(0, 0, 0, 0.12)');
          gradient.addColorStop(1, 'rgba(0, 0, 0, 0.03)');
          ctx.setFillStyle(gradient);
          ctx.fillRect(0, 975, 750, 25);

          // 绘制书本底部阴影
          ctx.beginPath();
          ctx.moveTo(35, 950);
          ctx.lineTo(95, 990);
          ctx.lineTo(715, 990);
          ctx.lineTo(715, 950);
          ctx.closePath();
          ctx.setFillStyle('rgba(0, 0, 0, 0.15)');
          ctx.fill();

          // 书本右侧阴影效果
          const rightShadowGradient = ctx.createLinearGradient(700, 0, 750, 0);
          rightShadowGradient.addColorStop(0, 'rgba(0, 0, 0, 0.2)');
          rightShadowGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
          ctx.setFillStyle(rightShadowGradient);
          ctx.fillRect(700, 50, 50, 900);

          // 绘制书脊效果
          const spineGradient = ctx.createLinearGradient(30, 0, 50, 0);
          spineGradient.addColorStop(0, '#262626');
          spineGradient.addColorStop(1, '#404040');
          ctx.beginPath();
          ctx.moveTo(50, 50);
          ctx.lineTo(30, 70);
          ctx.lineTo(30, 970);
          ctx.lineTo(50, 950);
          ctx.closePath();
          ctx.setFillStyle(spineGradient);
          ctx.fill();

          // 书脊右侧亮边
          ctx.beginPath();
          ctx.moveTo(50, 50);
          ctx.lineTo(52, 52);
          ctx.lineTo(52, 948);
          ctx.lineTo(50, 950);
          ctx.closePath();
          ctx.setFillStyle('rgba(255, 255, 255, 0.3)');
          ctx.fill();

          // 绘制书脊文字
          ctx.save();
          ctx.translate(40, 500);
          ctx.rotate(-Math.PI/2);
          ctx.setFillStyle('#ffffff');
          ctx.setFontSize(22);
          ctx.setTextAlign('center');
          ctx.setTextBaseline('middle');
          ctx.setShadow(1, 1, 2, 'rgba(0, 0, 0, 0.3)');
          ctx.fillText(this.data.categoryName + '精选', 0, 0);
          ctx.restore();
          ctx.setShadow(0, 0, 0, 'transparent');

          // 绘制杂志主体
          ctx.setShadow(5, 5, 10, 'rgba(0, 0, 0, 0.25)');
          ctx.setFillStyle('#ffffff');
          ctx.fillRect(50, 50, 650, 900);
          ctx.setShadow(0, 0, 0, 'transparent');
          
          // 添加纸张纹理效果
          ctx.setStrokeStyle('rgba(0, 0, 0, 0.01)');
          ctx.setLineWidth(0.5);
          for (let i = 0; i < 40; i++) {
            const y = 50 + i * 22.5;
            ctx.beginPath();
            ctx.moveTo(50, y);
            ctx.lineTo(700, y);
            ctx.stroke();
          }
          
          // 计算图片绘制区域，保持宽高比
          const imageRatio = imgInfo.width / imgInfo.height;
          let drawWidth = 600;
          let drawHeight = drawWidth / imageRatio;
          
          // 限制图片高度，确保不超过杂志尺寸
          if (drawHeight > 750) {
            drawHeight = 750;
            drawWidth = drawHeight * imageRatio;
          }
          
          // 计算居中位置
          const startX = (750 - drawWidth) / 2;
          const startY = 100;
          
          // 绘制封面图片
          ctx.drawImage(imgInfo.path, startX, startY, drawWidth, drawHeight);
          
          // 在图片上方添加灰色半透明遮罩，用于创建文字对比
          ctx.setGlobalAlpha(0.1);
          ctx.setFillStyle('#000000');
          ctx.fillRect(startX, startY, drawWidth, drawHeight);
          ctx.setGlobalAlpha(1.0);
          
          // 绘制主标题 - 垂直方向右侧排版
          const titleX = 650; // 右侧位置
          const titleY = 200; // 从上往下约1/5的位置开始
          const lineSpacing = 50; // 行间距
          
          // 绘制标题
          ctx.save();
          
          // 逐字垂直排列标题
          ctx.setFillStyle('#000000');
          ctx.setFontSize(50);
          ctx.setTextAlign('center');
          
          // 分解类别名称为字符数组
          const categoryChars = this.data.categoryName.split('');
          
          // 绘制垂直标题
          for (let i = 0; i < categoryChars.length; i++) {
            ctx.fillText(categoryChars[i], titleX, titleY + i * lineSpacing);
          }
          
          // 添加"个人穿搭集"四个字
          const collectionText = ["个", "人", "穿", "搭", "集"];
          const startPosY = titleY + (categoryChars.length + 1) * lineSpacing;
          
          for (let i = 0; i < collectionText.length; i++) {
            ctx.fillText(collectionText[i], titleX, startPosY + i * lineSpacing);
          }
          
          // 添加分隔线
          ctx.beginPath();
          ctx.moveTo(titleX, startPosY + collectionText.length * lineSpacing + 20);
          ctx.lineTo(titleX, startPosY + collectionText.length * lineSpacing + 100);
          ctx.setStrokeStyle('#000000');
          ctx.setLineWidth(1);
          ctx.stroke();
          
          // 添加英文标题
          ctx.setFontSize(18);
          const englishTitle = "FOR URBAN WOMEN";
          ctx.save();
          ctx.translate(titleX - 15, startPosY + collectionText.length * lineSpacing + 60);
          ctx.rotate(Math.PI/2);
          ctx.fillText(englishTitle, 0, 0);
          ctx.restore();
          
          // 根据搭配类别设置副标题文本
          let subTitle = '';
          switch(this.data.category) {
            case 'daily':
              subTitle = '生活日常';
              break;
            case 'work':
              subTitle = '职场通勤';
              break;
            case 'party':
              subTitle = '派对盛装';
              break;
            case 'seasonal':
              subTitle = '时令精选';
              break;
            case 'custom':
              subTitle = '个性定制';
              break;
            default:
              subTitle = '风格专辑';
          }
          
          // 添加类别特定的副标题
          ctx.setFontSize(22);
          ctx.save();
          ctx.translate(titleX + 15, startPosY + collectionText.length * lineSpacing + 60);
          ctx.rotate(Math.PI/2);
          ctx.fillText(subTitle, 0, 0);
          ctx.restore();
          
          ctx.restore();
          
          // 底部区域信息
          // 添加搭配名称
          ctx.setFillStyle('#000000');
          ctx.setFontSize(38);
          ctx.setTextAlign('center');
          ctx.fillText(outfit.name || '我的搭配', 375, startY + drawHeight + 60);
          
          // 添加底部描述文本 - 仿照示例中的灰色小文本
          ctx.setFillStyle('#666666');
          ctx.setFontSize(18);
          ctx.setTextAlign('left');
          
          const descLines = [
            '每天的穿搭都是彰显生活态度'
          ];
          
          const descX = 100;
          const descY = startY + drawHeight + 140;
          const descLineHeight = 30;
          
          descLines.forEach((line, index) => {
            ctx.fillText(line, descX, descY + index * descLineHeight);
          });
          
          // 添加小程序信息
          ctx.setFillStyle('#999999');
          ctx.setFontSize(18);
          ctx.setTextAlign('center');
          ctx.fillText('来自「小程序·拯救衣橱」', 375, 940);
          
          // 绘制到Canvas
          ctx.draw(false, () => {
            // 延迟确保Canvas已经绘制完成
            setTimeout(() => {
              // 导出Canvas为图片
              wx.canvasToTempFilePath({
                canvasId: 'shareCanvas',
                success: res => {
                  resolve(res.tempFilePath);
                },
                fail: err => {
                  console.error('导出封面Canvas失败:', err);
                  reject(err);
                }
              });
            }, 500);
          });
        },
        fail: err => {
          console.error('获取封面图片信息失败:', err);
          reject(err);
        }
      });
    });
  },

  // 绘制衣物布局图片
  drawItemsImage: function() {
    return new Promise((resolve, reject) => {
      const outfit = this.data.currentOutfit;
      if (!outfit || !outfit.items || outfit.items.length === 0) {
        reject(new Error('搭配无衣物数据'));
        return;
      }

      // 准备下载所有衣物图片
      const items = outfit.items;
      const downloadPromises = items.map(item => {
        return new Promise((itemResolve, itemReject) => {
          wx.getImageInfo({
            src: item.imageUrl,
            success: res => itemResolve({ ...item, localPath: res.path, width: res.width, height: res.height }),
            fail: err => {
              console.error('下载衣物图片失败:', err);
              itemReject(err);
            }
          });
        });
      });

      // 等待所有图片下载完成
      Promise.all(downloadPromises)
        .then(itemsWithLocalPaths => {
          // 获取Canvas上下文
          const ctx = wx.createCanvasContext('shareItemsCanvas');

          // 设置画布背景 - 匹配封面的背景色
          // 修改为3:4的比例 (750 x 1000)
          ctx.setFillStyle('#d8d8d8');
          ctx.fillRect(0, 0, 750, 1000);

          // 绘制底部桌面反光阴影效果
          const gradient = ctx.createLinearGradient(375, 975, 375, 1000);
          gradient.addColorStop(0, 'rgba(0, 0, 0, 0.12)');
          gradient.addColorStop(1, 'rgba(0, 0, 0, 0.03)');
          ctx.setFillStyle(gradient);
          ctx.fillRect(0, 975, 750, 25);

          // 绘制书本底部最外层阴影
          ctx.beginPath();
          ctx.moveTo(35, 950);
          ctx.lineTo(95, 990);
          ctx.lineTo(715, 990);
          ctx.lineTo(715, 950);
          ctx.closePath();
          ctx.setFillStyle('rgba(0, 0, 0, 0.12)');
          ctx.fill();

          // 书本底部内层阴影
          ctx.beginPath();
          ctx.moveTo(50, 950);
          ctx.lineTo(100, 980);
          ctx.lineTo(700, 980);
          ctx.lineTo(700, 950);
          ctx.closePath();
          ctx.setFillStyle('rgba(0, 0, 0, 0.25)');
          ctx.fill();

          // 书本右侧外层阴影效果 - 渐变
          const rightShadowGradient = ctx.createLinearGradient(700, 0, 750, 0);
          rightShadowGradient.addColorStop(0, 'rgba(0, 0, 0, 0.25)');
          rightShadowGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
          ctx.setFillStyle(rightShadowGradient);
          ctx.fillRect(700, 50, 50, 900);

          // 书本右侧内层阴影
          ctx.beginPath();
          ctx.moveTo(700, 50);
          ctx.lineTo(730, 80);
          ctx.lineTo(730, 980);
          ctx.lineTo(700, 950);
          ctx.closePath();
          ctx.setFillStyle('rgba(0, 0, 0, 0.2)');
          ctx.fill();

          // 书本左侧轻微阴影
          const leftShadowGradient = ctx.createLinearGradient(0, 0, 50, 0);
          leftShadowGradient.addColorStop(0, 'rgba(0, 0, 0, 0)');
          leftShadowGradient.addColorStop(1, 'rgba(0, 0, 0, 0.08)');
          ctx.setFillStyle(leftShadowGradient);
          ctx.fillRect(0, 50, 50, 900);

          // 书脊效果 - 渐变色
          const spineGradient = ctx.createLinearGradient(30, 0, 50, 0);
          spineGradient.addColorStop(0, '#262626');
          spineGradient.addColorStop(1, '#404040');
          ctx.beginPath();
          ctx.moveTo(50, 50);
          ctx.lineTo(30, 70);
          ctx.lineTo(30, 970);
          ctx.lineTo(50, 950);
          ctx.closePath();
          ctx.setFillStyle(spineGradient);
          ctx.fill();

          // 书脊右侧亮边
          ctx.beginPath();
          ctx.moveTo(50, 50);
          ctx.lineTo(52, 52);
          ctx.lineTo(52, 948);
          ctx.lineTo(50, 950);
          ctx.closePath();
          ctx.setFillStyle('rgba(255, 255, 255, 0.3)');
          ctx.fill();

          // 绘制书脊文字 - 确保垂直居中显示
          ctx.save();
          // 旋转正确的角度并定位到书脊中央
          ctx.translate(40, 500);
          ctx.rotate(-Math.PI/2);
          ctx.setFillStyle('#ffffff');
          ctx.setFontSize(22);
          ctx.setTextAlign('center');
          ctx.setTextBaseline('middle');
          // 添加文字阴影，增强立体感
          ctx.setShadow(1, 1, 2, 'rgba(0, 0, 0, 0.3)');
          ctx.fillText('搭配组成详情', 0, 0);
          ctx.restore();
          ctx.setShadow(0, 0, 0, 'transparent');

          // 内页效果 - 多层次阴影
          ctx.setShadow(8, 8, 15, 'rgba(0, 0, 0, 0.3)');
          ctx.setFillStyle('#f0f0f0');
          ctx.fillRect(55, 55, 640, 890);

          ctx.setShadow(3, 3, 5, 'rgba(0, 0, 0, 0.2)');
          ctx.setFillStyle('#ffffff');
          ctx.fillRect(50, 50, 650, 900);
          ctx.setShadow(0, 0, 0, 'transparent');

          // 添加内页纸张纹理 - 更加精细的效果
          ctx.setStrokeStyle('rgba(0, 0, 0, 0.01)');
          ctx.setLineWidth(0.5);
          for (let i = 0; i < 40; i++) {
            const y = 50 + i * 22.5;
            ctx.beginPath();
            ctx.moveTo(50, y);
            ctx.lineTo(700, y);
            ctx.stroke();
          }

          // 添加微妙的水平纹理
          ctx.setStrokeStyle('rgba(0, 0, 0, 0.005)');
          for (let i = 0; i < 30; i++) {
            const x = 50 + i * 21.67;
            ctx.beginPath();
            ctx.moveTo(x, 50);
            ctx.lineTo(x, 950);
            ctx.stroke();
          }

          // 添加页面边缘效果
          ctx.beginPath();
          ctx.moveTo(50, 50);
          ctx.bezierCurveTo(60, 55, 690, 55, 700, 50);
          ctx.lineTo(700, 950);
          ctx.bezierCurveTo(690, 945, 60, 945, 50, 950);
          ctx.closePath();
          ctx.setStrokeStyle('rgba(0, 0, 0, 0.08)');
          ctx.stroke();

          // 内页边缘光泽效果 - 顶部
          const topGlossGradient = ctx.createLinearGradient(375, 50, 375, 70);
          topGlossGradient.addColorStop(0, 'rgba(255, 255, 255, 0.15)');
          topGlossGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
          ctx.setFillStyle(topGlossGradient);
          ctx.fillRect(50, 50, 650, 20);

          // 内页边缘光泽效果 - 左侧
          const leftGlossGradient = ctx.createLinearGradient(50, 500, 70, 500);
          leftGlossGradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
          leftGlossGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
          ctx.setFillStyle(leftGlossGradient);
          ctx.fillRect(50, 50, 20, 900);

          // 绘制标题
          ctx.setFillStyle('#333');
          ctx.setFontSize(42);
          ctx.setTextAlign('center');
          ctx.fillText(`${outfit.name || '未命名搭配'} - 搭配组成`, 375, 100);

          // 绘制标题装饰
          ctx.beginPath();
          ctx.setLineWidth(3);
          ctx.moveTo(200, 120);
          ctx.lineTo(550, 120);
          ctx.setStrokeStyle('#333');
          ctx.stroke();

          // 绘制装饰图案
          ctx.beginPath();
          ctx.arc(375, 135, 8, 0, Math.PI * 2);
          ctx.setFillStyle('#333');
          ctx.fill();

          // 绘制细小装饰线条
          ctx.beginPath();
          ctx.setLineWidth(1);
          ctx.moveTo(250, 140);
          ctx.lineTo(500, 140);
          ctx.setStrokeStyle('rgba(0, 0, 0, 0.2)');
          ctx.stroke();

          // 计算每个衣物的绘制位置
          const itemCount = itemsWithLocalPaths.length;
          const itemsPerRow = itemCount > 4 ? 3 : 2;
          const rows = Math.ceil(itemCount / itemsPerRow);
          const itemWidth = 180;
          const itemHeight = 180;
          const padding = 30;
          const startY = 180;

          // 绘制每个衣物
          itemsWithLocalPaths.forEach((item, index) => {
            const row = Math.floor(index / itemsPerRow);
            const col = index % itemsPerRow;

            // 计算绘制位置
            const x = (750 - (itemWidth * itemsPerRow + padding * (itemsPerRow - 1))) / 2 + col * (itemWidth + padding);
            const y = startY + row * (itemHeight + 140);

            // 绘制项目背景 - 卡片效果，多层次阴影
            ctx.setShadow(6, 6, 10, 'rgba(0, 0, 0, 0.15)');
            ctx.setFillStyle('#f5f5f5');
            ctx.fillRect(x - 12, y - 12, itemWidth + 24, itemHeight + 104);

            ctx.setShadow(3, 3, 5, 'rgba(0, 0, 0, 0.1)');
            ctx.setFillStyle('#f9f9f9');
            ctx.fillRect(x - 10, y - 10, itemWidth + 20, itemHeight + 100);
            ctx.setShadow(0, 0, 0, 'transparent');

            // 计算图片绘制区域，保持宽高比
            const imageRatio = item.width / item.height;
            let drawWidth = itemWidth;
            let drawHeight = drawWidth / imageRatio;

            // 如果高度过大，则按照高度缩放
            if (drawHeight > itemHeight) {
              drawHeight = itemHeight;
              drawWidth = drawHeight * imageRatio;
            }

            // 计算居中位置
            const imageX = x + (itemWidth - drawWidth) / 2;
            const imageY = y;

            // 绘制衣物图片边框 - 多层次效果
            ctx.setShadow(2, 2, 3, 'rgba(0, 0, 0, 0.1)');
            ctx.setFillStyle('#ffffff');
            ctx.fillRect(imageX - 6, imageY - 6, drawWidth + 12, drawHeight + 12);
            ctx.setShadow(0, 0, 0, 'transparent');

            // 绘制衣物图片细边框
            ctx.beginPath();
            ctx.rect(imageX - 5, imageY - 5, drawWidth + 10, drawHeight + 10);
            ctx.setStrokeStyle('rgba(0, 0, 0, 0.08)');
            ctx.setLineWidth(1);
            ctx.stroke();

            // 绘制衣物图片
            ctx.drawImage(item.localPath, imageX, imageY, drawWidth, drawHeight);

            // 图片边框线
            ctx.beginPath();
            ctx.rect(imageX, imageY, drawWidth, drawHeight);
            ctx.setStrokeStyle('rgba(0, 0, 0, 0.05)');
            ctx.setLineWidth(0.5);
            ctx.stroke();

            // 卡片内部装饰线
            ctx.beginPath();
            ctx.moveTo(x, y + itemHeight + 15);
            ctx.lineTo(x + itemWidth, y + itemHeight + 15);
            ctx.setStrokeStyle('rgba(0, 0, 0, 0.05)');
            ctx.setLineWidth(1);
            ctx.stroke();

            // 绘制衣物名称
            ctx.setFontSize(24);
            ctx.setTextAlign('center');
            ctx.setFillStyle('#333');
            ctx.fillText(item.name || '未命名', x + itemWidth / 2, y + itemHeight + 40);

            // 绘制衣物类型
            ctx.setFontSize(20);
            ctx.setFillStyle('#666');
            ctx.fillText(item.category || '未分类', x + itemWidth / 2, y + itemHeight + 70);
          });

          // 添加页脚分割线
          ctx.beginPath();
          ctx.moveTo(150, 880);
          ctx.lineTo(600, 880);
          ctx.setStrokeStyle('rgba(0, 0, 0, 0.1)');
          ctx.setLineWidth(1);
          ctx.stroke();

          // 根据搭配类别设置副标题文本
          let seriesText = '';
          switch(this.data.category) {
            case 'daily':
              seriesText = '轻松日常系列';
              break;
            case 'work':
              seriesText = '职场精英系列';
              break;
            case 'party':
              seriesText = '派对华彩系列';
              break;
            case 'seasonal':
              seriesText = '应季新品系列';
              break;
            case 'custom':
              seriesText = '个性定制系列';
              break;
            default:
              seriesText = '风尚系列';
          }

          // 添加页脚
          ctx.setFillStyle('#333');
          ctx.setFontSize(22);
          ctx.setTextAlign('center');
          ctx.fillText(`${this.data.categoryName}集 · 第${this.data.currentIndex + 1}期 · ${seriesText}`, 375, 910);

          // 添加小程序信息
          ctx.setFillStyle('#666');
          ctx.setFontSize(18);
          ctx.setTextAlign('center');
          ctx.fillText('来自「小程序·拯救衣橱」', 375, 940);

          // 绘制到Canvas
          ctx.draw(false, () => {
            // 延迟一下，确保Canvas已经绘制完成
            setTimeout(() => {
              // 导出Canvas为图片
              wx.canvasToTempFilePath({
                canvasId: 'shareItemsCanvas',
                success: res => {
                  resolve(res.tempFilePath);
                },
                fail: err => {
                  console.error('导出衣物布局Canvas失败:', err);
                  reject(err);
                }
              });
            }, 500);
          });
        })
        .catch(err => {
          console.error('处理衣物图片失败:', err);
          reject(err);
        });
    });
  },

  // 保存图片到相册
  saveImagesToAlbum: function() {
    const { cover, items } = this.data.shareImagePaths;

    if (!cover || !items) {
      wx.showToast({
        title: '图片未生成',
        icon: 'none'
      });
      return;
    }

    // 获取保存到相册的权限
    wx.getSetting({
      success: res => {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.doSaveImagesToAlbum(cover, items);
            },
            fail: () => {
              wx.showModal({
                title: '保存失败',
                content: '请授权保存图片到相册的权限',
                confirmText: '去设置',
                success: modalRes => {
                  if (modalRes.confirm) {
                    wx.openSetting();
                  }
                }
              });
            }
          });
        } else {
          this.doSaveImagesToAlbum(cover, items);
        }
      }
    });
  },

  // 执行保存图片的操作
  doSaveImagesToAlbum: function(coverPath, itemsPath) {
    wx.showLoading({
      title: '保存图片中...',
      mask: true
    });

    // 保存封面图片
    wx.saveImageToPhotosAlbum({
      filePath: coverPath,
      success: () => {
        // 保存衣物布局图片
        wx.saveImageToPhotosAlbum({
          filePath: itemsPath,
          success: () => {
            wx.hideLoading();
            wx.showToast({
              title: '已保存到相册',
              icon: 'success'
            });

            // 隐藏分享菜单
            this.hideShareMenu();
          },
          fail: err => {
            console.error('保存衣物布局图片失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '保存图片失败',
              icon: 'none'
            });
          }
        });
      },
      fail: err => {
        console.error('保存封面图片失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '保存图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 分享到微信
  shareToWechat: function() {
    wx.showToast({
      title: '请点击右上角分享',
      icon: 'none',
      duration: 2000
    });
  },

  // 微信小程序的分享接口
  onShareAppMessage: function() {
    const outfit = this.data.currentOutfit;
    if (!outfit) {
      return {
        title: `${this.data.categoryName}个人穿搭集 - 衣橱精选`,
        path: '/page/wardrobe/outfit/outfit_category_magazine/outfit_category_magazine?category=' + this.data.category,
        imageUrl: '/image/outfit-icon-HL.png'
      };
    }

    // 构建分享内容
    return {
      title: `${outfit.name || '精美穿搭'} - ${this.data.categoryName}个人穿搭集`,
      path: `/page/wardrobe/outfit/outfit_detail/outfit_detail?id=${outfit._id}`,
      imageUrl: outfit.outfit_cover || outfit.previewImage || '/image/outfit-icon-HL.png'
    };
  }
});
