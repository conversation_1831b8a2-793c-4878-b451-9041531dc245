const limitManager = require('../../wardrobe/common/limitManager');
const initMedals = require('./initMedals');

Page({
  data: {
    themeStyle: 'autumn',
    // 秋季色系
    colors: {
      cowhide_cocoa: '#442D1C',   // 深棕色 Cowhide Cocoa
      spiced_wine: '#74301C',     // 红棕色 Spiced Wine
      toasted_caramel: '#84592B', // 焦糖色 Toasted Caramel
      olive_harvest: '#9D9167',   // 橄榄色 Olive Harvest
      golden_batter: '#E8D1A7',   // 金黄色 Golden Batter
    },
    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',       // 深粉色
      pinkMedium: '#EEA0B2',     // 中粉色
      pinkLight: '#F9C9D6',      // 浅粉色
      blueLight: '#CBE0F9',      // 浅蓝色
      blueMedium: '#97C8E5',     // 中蓝色
      blueDark: '#5EA0D0',       // 深蓝色
    },
    // 页面样式
    pageStyle: {
      backgroundColor: '',
      backgroundImage: '',
      titleColor: '',
      cellBackgroundColor: '',
      footerColor: ''
    },
    // 查询用户信息
    queryOpenid: '',
    queryResult: null,
    queryError: '',
    matchedUsers: [],      // 模糊匹配的用户列表

    // 修改用户限制
    modifyOpenid: '',
    matchedUsersForModify: [], // 用于修改的模糊匹配用户列表
    selectedUserForModify: null, // 当前选中的要修改的用户
    clothesLimitIncrement: 5,  // 默认增加5件衣服
    outfitsLimitIncrement: 1,  // 默认增加1套搭配
    modifyResult: '',
    modifyError: '',

    // 功能区切换
    activeTab: 'query',  // 'query', 'modify', 'insert', 或 'medals'

    // 数据插入相关
    insertCount: 1,      // 随机插入数量
    targetOpenid: '',    // 指定插入的openid
    insertResult: '',    // 插入结果
    insertError: '',     // 插入错误信息
    matchedUsersForInsert: [], // 用于插入的模糊匹配用户列表
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '管理员控制台'
    });

    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });

      // 应用主题样式
      this.applyThemeStyle(savedTheme);
    } else {
      // 默认应用秋季主题
      this.applyThemeStyle('autumn');
    }
  },

  // 应用主题样式
  applyThemeStyle: function(themeName) {
    // 更新页面样式
    let pageStyle = {};

    if (themeName === 'autumn') {
      // 设置秋季主题样式
      pageStyle = {
        backgroundColor: this.data.colors.golden_batter,
        backgroundImage: 'none',
        titleColor: this.data.colors.cowhide_cocoa,
        cellBackgroundColor: 'rgba(255, 255, 255, 0.7)',
        footerColor: this.data.colors.cowhide_cocoa
      };

      // 设置秋季主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.colors.golden_batter, // 金黄色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });
    } else if (themeName === 'pinkBlue') {
      // 设置粉蓝主题样式
      pageStyle = {
        backgroundColor: this.data.pinkBlueColors.pinkLight,
        backgroundImage: `linear-gradient(to bottom, white, ${this.data.pinkBlueColors.pinkLight})`,
        titleColor: this.data.pinkBlueColors.pinkDark,
        cellBackgroundColor: 'rgba(255, 255, 255, 0.9)',
        footerColor: this.data.pinkBlueColors.blueDark
      };

      // 设置粉蓝主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.pinkBlueColors.pinkLight, // 浅粉色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });
    }

    // 更新页面样式
    this.setData({
      pageStyle: pageStyle
    });
  },

  // 切换功能标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab,
      // 切换时清空之前的查询/修改结果
      queryResult: null,
      queryError: '',
      modifyResult: '',
      modifyError: '',
      matchedUsers: [],
      matchedUsersForModify: [],
      selectedUserForModify: null
    });
  },

  // 处理查询输入框变化
  onQueryInput: function(e) {
    this.setData({
      queryOpenid: e.detail.value
    });

    // 输入长度大于5时，尝试模糊匹配
    if (e.detail.value.length >= 5) {
      this.fuzzyMatchOpenid(e.detail.value, 'query');
    } else {
      // 清空匹配结果
      this.setData({
        matchedUsers: []
      });
    }
  },

  // 处理修改输入框变化
  onModifyInput: function(e) {
    this.setData({
      modifyOpenid: e.detail.value
    });

    // 输入长度大于5时，尝试模糊匹配
    if (e.detail.value.length >= 5) {
      this.fuzzyMatchOpenid(e.detail.value, 'modify');
    } else {
      // 清空匹配结果
      this.setData({
        matchedUsersForModify: []
      });
    }
  },

  // 模糊匹配openid
  fuzzyMatchOpenid: function(partialOpenid, mode) {
    // 这个函数需要调用云函数进行模糊查询
    wx.cloud.callFunction({
      name: 'userSearch',
      data: {
        partialOpenid: partialOpenid
      },
      success: res => {
        console.log('模糊匹配结果:', res.result);
        // 根据模式设置不同的数据
        if (mode === 'query') {
          this.setData({
            matchedUsers: res.result || []
          });
        } else if (mode === 'modify') {
          this.setData({
            matchedUsersForModify: res.result || []
          });
        } else if (mode === 'insert') {
          this.setData({
            matchedUsersForInsert: res.result || []
          });
        }
      },
      fail: err => {
        console.error('模糊匹配失败:', err);
        wx.showToast({
          title: '匹配用户失败',
          icon: 'none'
        });
      }
    });
  },

  // 从匹配列表中选择一个用户（查询模式）
  selectUser: function(e) {
    const openid = e.currentTarget.dataset.openid;
    this.setData({
      queryOpenid: openid,
      matchedUsers: [] // 清空匹配列表
    });

    // 自动执行查询
    this.queryUserLimits();
  },

  // 从匹配列表中选择一个用户（修改模式）
  selectUserForModify: function(e) {
    const openid = e.currentTarget.dataset.openid;
    const userInfo = this.data.matchedUsersForModify.find(u => u._openid === openid);

    this.setData({
      modifyOpenid: openid,
      selectedUserForModify: userInfo,
      matchedUsersForModify: [] // 清空匹配列表
    });
  },

  // 处理衣服限制增加值输入变化
  onClothesLimitInput: function(e) {
    const value = parseInt(e.detail.value);
    this.setData({
      clothesLimitIncrement: isNaN(value) ? 0 : value
    });
  },

  // 处理搭配限制增加值输入变化
  onOutfitsLimitInput: function(e) {
    const value = parseInt(e.detail.value);
    this.setData({
      outfitsLimitIncrement: isNaN(value) ? 0 : value
    });
  },

  // 查询用户限制信息
  queryUserLimits: function() {
    const openid = this.data.queryOpenid;
    if (!openid) {
      this.setData({
        queryError: '请输入用户OpenID'
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '查询中...',
      mask: true
    });

    // 清空之前的结果
    this.setData({
      queryResult: null,
      queryError: ''
    });

    // 调用limitManager获取用户限制信息
    limitManager.getUserLimits(openid)
      .then(limits => {
        wx.hideLoading();
        console.log('查询结果:', limits);
        this.setData({
          queryResult: limits
        });
      })
      .catch(err => {
        wx.hideLoading();
        console.error('查询失败:', err);
        this.setData({
          queryError: err.message || '查询失败'
        });
      });
  },

  // 增加用户的衣服限制
  increaseClothesLimit: function() {
    this.modifyUserLimit('clothes');
  },

  // 增加用户的搭配限制
  increaseOutfitsLimit: function() {
    this.modifyUserLimit('outfits');
  },

  // 修改用户限制
  modifyUserLimit: function(type) {
    const openid = this.data.modifyOpenid;
    if (!openid) {
      this.setData({
        modifyError: '请选择要修改的用户'
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '修改中...',
      mask: true
    });

    // 清空之前的结果
    this.setData({
      modifyResult: '',
      modifyError: ''
    });

    let promise;
    if (type === 'clothes') {
      const increment = this.data.clothesLimitIncrement;
      if (increment <= 0) {
        wx.hideLoading();
        this.setData({
          modifyError: '增加值必须大于0'
        });
        return;
      }
      promise = limitManager.increaseClothesLimit(openid, increment);
    } else {
      const increment = this.data.outfitsLimitIncrement;
      if (increment <= 0) {
        wx.hideLoading();
        this.setData({
          modifyError: '增加值必须大于0'
        });
        return;
      }
      promise = limitManager.increaseOutfitsLimit(openid, increment);
    }

    promise
      .then(result => {
        wx.hideLoading();
        console.log('修改结果:', result);
        this.setData({
          modifyResult: `已成功增加用户${type === 'clothes' ? '衣服' : '搭配'}上限`,
          selectedUserForModify: result
        });
        wx.showToast({
          title: '修改成功',
          icon: 'success'
        });
      })
      .catch(err => {
        wx.hideLoading();
        console.error('修改失败:', err);
        this.setData({
          modifyError: err.message || '修改失败'
        });
      });
  },

  // 处理随机插入数量输入
  onInsertCountInput: function(e) {
    const value = parseInt(e.detail.value);
    this.setData({
      insertCount: isNaN(value) ? 1 : value
    });
  },

  // 处理指定插入openid输入
  onTargetOpenidInput: function(e) {
    this.setData({
      targetOpenid: e.detail.value
    });

    // 输入长度大于5时，尝试模糊匹配
    if (e.detail.value.length >= 5) {
      this.fuzzyMatchOpenid(e.detail.value, 'insert');
    } else {
      this.setData({
        matchedUsersForInsert: []
      });
    }
  },

  // 从匹配列表中选择一个用户（插入模式）
  selectUserForInsert: function(e) {
    const openid = e.currentTarget.dataset.openid;
    this.setData({
      targetOpenid: openid,
      matchedUsersForInsert: [] // 清空匹配列表
    });
  },

  // 随机插入数据
  randomInsert: function() {
    const count = this.data.insertCount;
    if (count <= 0) {
      this.setData({
        insertError: '插入数量必须大于0'
      });
      return;
    }

    wx.showLoading({
      title: '插入中...',
      mask: true
    });

    // 调用云函数进行随机插入
    wx.cloud.callFunction({
      name: 'randomInsertClothes',
      data: {
        count: count
      },
      success: res => {
        wx.hideLoading();
        console.log('随机插入结果:', res.result);
        this.setData({
          insertResult: `成功插入${res.result.successCount}条数据`,
          insertError: ''
        });
        wx.showToast({
          title: '插入成功',
          icon: 'success'
        });
      },
      fail: err => {
        wx.hideLoading();
        console.error('随机插入失败:', err);
        this.setData({
          insertError: err.message || '插入失败',
          insertResult: ''
        });
      }
    });
  },

  // 指定插入数据
  targetInsert: function() {
    const openid = this.data.targetOpenid;
    if (!openid) {
      this.setData({
        insertError: '请选择要插入的用户'
      });
      return;
    }

    wx.showLoading({
      title: '插入中...',
      mask: true
    });

    // 调用云函数进行指定插入
    wx.cloud.callFunction({
      name: 'targetInsertClothes',
      data: {
        openid: openid
      },
      success: res => {
        wx.hideLoading();
        console.log('指定插入结果:', res.result);
        this.setData({
          insertResult: `成功向用户${openid}插入数据`,
          insertError: ''
        });
        wx.showToast({
          title: '插入成功',
          icon: 'success'
        });
      },
      fail: err => {
        wx.hideLoading();
        console.error('指定插入失败:', err);
        this.setData({
          insertError: err.message || '插入失败',
          insertResult: ''
        });
      }
    });
  },

  // 初始化勋章数据
  initMedalsData: function() {
    initMedals.initMedalsData();
  }
});