<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-content">
      <view class="nav-back" bindtap="navigateBack">
        <text class="back-icon">←</text>
      </view>
      <view class="nav-title">
        <view class="title-container">
          <view class="section-title">AI衣橱分析报告</view>
         
        </view>
      </view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- 正文内容容器 -->
  <view class="page-content" style="margin-top: {{statusBarHeight + 200}}rpx;">
    <scroll-view class="report-content" scroll-y="true" enhanced="true" bounces="true" show-scrollbar="{{false}}">
      <!-- 总体概览 -->
      <view class="analysis-section overview-section card-module">
        <view class="section-header">
          <view class="section-icon module-icon-overview">🔍</view>
          <view class="section-title">总体概览</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">风格画像：</view>
          <view class="item-value">{{aiAnalysisResult.wardrobeAnalysis.styleProfile}}</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">色彩分析：</view>
          <view class="item-value">{{aiAnalysisResult.wardrobeAnalysis.colorAnalysis}}</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">类型分布：</view>
          <view class="item-value">{{aiAnalysisResult.wardrobeAnalysis.typeDistribution}}</view>
        </view>
      </view>
      
      <!-- 模块1：衣橱基础概览 -->
      <view class="analysis-section module-1 card-module">
        <view class="section-header">
          <view class="section-icon module-icon-1">📊</view>
          <view class="section-title">{{aiAnalysisResult.module1.title}}</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">类型分布：</view>
          <view class="item-value">{{aiAnalysisResult.module1.typesDistribution}}</view>
        </view>
        
        <view class="analysis-item" wx:if="{{aiAnalysisResult.module1.imbalanceWarning}}">
          <view class="item-label warning">【⚠警告】失衡现象：</view>
          <view class="item-value warning">存在单一类型占比过高现象</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">基础色占比：</view>
          <view class="item-value">{{aiAnalysisResult.module1.basicColorPercentage}}</view>
        </view>
        
        <view class="analysis-item" wx:if="{{aiAnalysisResult.module1.colorRedundancyWarning}}">
          <view class="item-label warning">【⚠警告】色系冗余：</view>
          <view class="item-value">{{aiAnalysisResult.module1.colorRedundancyWarning}}</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">风格与品牌关联：</view>
          <view class="item-value">{{aiAnalysisResult.module1.styleAndBrandRelation}}</view>
        </view>
      </view>
      
      <!-- 模块2：使用效率分析 -->
      <view class="analysis-section module-2 card-module">
        <view class="section-header">
          <view class="section-icon module-icon-2">⚡</view>
          <view class="section-title">{{aiAnalysisResult.module2.title}}</view>
        </view>
        
        <view class="analysis-subsection">
          <view class="subsection-title">
            <text class="title-icon">💰</text>
            <text>性价比TOP5</text>
          </view>
          <view class="data-table cost-table">
            <view class="table-header">
              <view class="th">品类</view>
              <view class="th">品牌</view>
              <view class="th">价格</view>
              <view class="th highlight-column">单次成本</view>
            </view>
            
            <view class="table-row" wx:for="{{aiAnalysisResult.module2.costEffectiveItems}}" wx:key="index">
              <view class="td">{{item.category}}</view>
              <view class="td">{{item.brand}}</view>
              <view class="td">{{item.price}}</view>
              <view class="td highlight-column">{{item.costPerWear}}</view>
            </view>
          </view>
          <view class="cost-explanation">单次成本 = 购买价格 ÷ 穿着次数（低值更划算）</view>
        </view>
        
        <view class="analysis-subsection">
          <view class="subsection-title">
            <text class="title-icon">📉</text>
            <text>资源浪费</text>
          </view>
          <view class="data-table waste-table">
            <view class="table-header">
              <view class="th">品类</view>
              <view class="th">品牌</view>
              <view class="th">价格</view>
              <view class="th highlight-column">浪费成本</view>
            </view>
            
            <view class="table-row" wx:for="{{aiAnalysisResult.module2.wastedItems}}" wx:key="index">
              <view class="td">{{item.category}}</view>
              <view class="td">{{item.brand}}</view>
              <view class="td">{{item.price}}</view>
              <view class="td highlight-column">{{item.wasteCost}}</view>
            </view>
          </view>
          <view class="waste-explanation">浪费成本 = 高价衣物长期未穿的投资损失</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">季节适配性：</view>
          <view class="item-value">{{aiAnalysisResult.module2.seasonalAdaptability}}</view>
        </view>
        
        <view class="analysis-item" wx:if="{{aiAnalysisResult.module2.materialMismatchWarning}}">
          <view class="item-label warning">【⚠警告】材质季节错配：</view>
          <view class="item-value">{{aiAnalysisResult.module2.materialMismatchWarning}}</view>
        </view>
      </view>
      
      <!-- 模块3：消费行为洞察 -->
      <view class="analysis-section module-3 card-module">
        <view class="section-header">
          <view class="section-icon module-icon-3">🔮</view>
          <view class="section-title">{{aiAnalysisResult.module3.title}}</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">购买-穿着延迟模式：</view>
          <view class="item-value">{{aiAnalysisResult.module3.delayPatterns}}</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">反季购买占比：</view>
          <view class="item-value">{{aiAnalysisResult.module3.offSeasonPurchasePercentage}}</view>
        </view>
        
        <view class="analysis-subsection">
          <view class="subsection-title">
            <text class="title-icon">⭐</text>
            <text>价值品牌</text>
          </view>
          <view class="data-table value-table high-value">
            <view class="table-header">
              <view class="th">品牌</view>
              <view class="th">价格</view>
              <view class="th value-column">价值系数</view>
            </view>
            
            <view class="table-row" wx:for="{{aiAnalysisResult.module3.valuableRanking.highValue}}" wx:key="index">
              <view class="td">{{item.brand}}</view>
              <view class="td">{{item.avgPrice}}元</view>
              <view class="td value-column">{{item.valueCoefficient}}</view>
            </view>
          </view>
        </view>
        
        <view class="analysis-subsection">
          <view class="subsection-title">
            <text class="title-icon">⚠️</text>
            <text>低价值品牌</text>
          </view>
          <view class="data-table value-table low-value">
            <view class="table-header">
              <view class="th">品牌</view>
              <view class="th">价格</view>
              <view class="th value-column">价值系数</view>
            </view>
            
            <view class="table-row" wx:for="{{aiAnalysisResult.module3.valuableRanking.lowValue}}" wx:key="index">
              <view class="td">{{item.brand}}</view>
              <view class="td">{{item.avgPrice}}元</view>
              <view class="td value-column">{{item.valueCoefficient}}</view>
            </view>
          </view>
          <view class="value-explanation">价值系数 = 穿着次数÷价格×100</view>
        </view>
      </view>
      
      <!-- 模块4：搭配优化建议 -->
      <view class="analysis-section module-4 card-module">
        <view class="section-header">
          <view class="section-icon module-icon-4">👔</view>
          <view class="section-title">{{aiAnalysisResult.module4.title}}</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">色彩系统评价：</view>
          <view class="item-value">{{aiAnalysisResult.module4.colorSystemEvaluation}}</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">高频颜色组合：</view>
          <view class="item-value">{{aiAnalysisResult.module4.frequentCombinations}}</view>
        </view>
        
        <view class="analysis-subsection">
          <view class="subsection-title">
            <text class="title-icon">✨</text>
            <text>低频单品激活方案</text>
          </view>
          
          <view class="activation-cards">
            <view class="activation-card" wx:for="{{aiAnalysisResult.module4.activationSuggestions}}" wx:key="index">
              <view class="activation-item">{{item.item}}</view>
              <view class="activation-formula">{{item.formula}}</view>
              <view class="activation-desc">{{item.description}}</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 模块5：可持续诊断 -->
      <view class="analysis-section module-5 card-module">
        <view class="section-header">
          <view class="section-icon module-icon-5">🌱</view>
          <view class="section-title">{{aiAnalysisResult.module5.title}}</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">可持续材质占比：</view>
          <view class="item-value">{{aiAnalysisResult.module5.sustainableMaterialPercentage}}</view>
        </view>
        
        <view class="analysis-item">
          <view class="item-label">快时尚单品统计：</view>
          <view class="item-value">{{aiAnalysisResult.module5.fastFashionStats}}</view>
        </view>
        
        <view class="analysis-subsection">
          <view class="subsection-title">
            <text class="title-icon">🗑️</text>
            <text>淘汰建议</text>
          </view>
          <view class="data-table elimination-table">
            <view class="table-header">
              <view class="th">品类</view>
              <view class="th">品牌</view>
              <view class="th">价格</view>
              <view class="th elimination-reason">淘汰理由</view>
            </view>
            
            <view class="table-row" wx:for="{{aiAnalysisResult.module5.eliminationSuggestions}}" wx:key="index">
              <view class="td">{{item.category}}</view>
              <view class="td">{{item.brand}}</view>
              <view class="td">{{item.price}}元</view>
              <view class="td elimination-reason">{{item.reason}}</view>
            </view>
          </view>
          <view class="elimination-summary">
            建议将以上衣物进行二次循环利用
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view> 