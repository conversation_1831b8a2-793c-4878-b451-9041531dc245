// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { medalId } = event

  // 验证参数
  if (!medalId) {
    return {
      success: false,
      error: '缺少勋章ID参数'
    }
  }

  try {
    // 为每个勋章类型创建唯一的计数器ID
    const counterId = `medal_number_${medalId}`

    // 获取当前勋章类型的计数器
    const counterResult = await db.collection('system_counters')
      .doc(counterId)
      .get()
      .catch(err => {
        // 如果计数器不存在，创建一个新的
        if (err.errCode === -1) {
          return db.collection('system_counters')
            .add({
              data: {
                _id: counterId,
                medalId: medalId,
                count: 0,
                lastUpdated: db.serverDate()
              }
            })
            .then(() => {
              return { data: { count: 0 } }
            })
        }
        throw err
      })

    // 获取当前计数
    const currentCount = counterResult.data ? counterResult.data.count : 0

    // 增加计数并更新
    const newCount = currentCount + 1
    await db.collection('system_counters')
      .doc(counterId)
      .update({
        data: {
          count: newCount,
          lastUpdated: db.serverDate()
        }
      })

    return {
      success: true,
      medalId: medalId,
      globalNumber: newCount
    }
  } catch (error) {
    console.error('获取勋章全局编号失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
