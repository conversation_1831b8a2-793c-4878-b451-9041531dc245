/**
 * 用户限制管理模块
 * 负责检查和管理用户的衣服和穿搭数量限制
 */

/**
 * 获取用户衣物数量限制信息
 * @param {String} userOpenId - 用户OpenID
 * @returns {Promise<Object>} 包含限制信息的Promise
 */
function getUserLimits(userOpenId) {
  return new Promise((resolve, reject) => {
    if (!userOpenId) {
      reject(new Error('未获取到用户ID，无法获取限制信息'));
      return;
    }

    const db = wx.cloud.database();
    
    // 查询用户的限制信息
    db.collection('users')
      .where({
        _openid: userOpenId
      })
      .get()
      .then(res => {
        // 如果找到用户记录
        if (res.data && res.data.length > 0) {
          const userInfo = res.data[0];
          // 返回限制信息，如果不存在则使用默认值
          resolve({
            clothesLimit: userInfo.clothesLimit || 30, // 默认衣物上限为100件
            outfitsLimit: userInfo.outfitsLimit || 10,  // 默认穿搭上限为50套
            clothesCount: userInfo.clothesCount || 0,   // 当前衣物数量
            outfitsCount: userInfo.outfitsCount || 0    // 当前穿搭数量
          });
        } else {
          // 如果用户记录不存在，创建一个新记录并返回默认限制
          createUserLimits(userOpenId)
            .then(defaultLimits => {
              resolve(defaultLimits);
            })
            .catch(err => {
              console.error('创建用户限制失败:', err);
              // 即使创建失败，也返回默认限制
              resolve({
                clothesLimit: 30,
                outfitsLimit: 10,
                clothesCount: 0,
                outfitsCount: 0
              });
            });
        }
      })
      .catch(err => {
        console.error('获取用户限制信息失败:', err);
        reject(err);
      });
  });
}

/**
 * 创建用户限制信息
 * @param {String} userOpenId - 用户OpenID
 * @returns {Promise<Object>} 包含默认限制信息的Promise
 */
function createUserLimits(userOpenId) {
  return new Promise((resolve, reject) => {
    const db = wx.cloud.database();
    
    // 默认限制值
    const defaultLimits = {
      clothesLimit: 30,   // 默认衣物上限为100件
      outfitsLimit: 10,    // 默认穿搭上限为50套
      clothesCount: 0,     // 当前衣物数量
      outfitsCount: 0     // 当前穿搭数量
    };
    
    // 添加到数据库
    db.collection('users').add({
      data: defaultLimits
    })
      .then(() => {
        console.log('已创建用户限制记录');
        resolve(defaultLimits);
      })
      .catch(err => {
        console.error('创建用户限制记录失败:', err);
        reject(err);
      });
  });
}

/**
 * 检查用户衣物数量是否达到限制
 * @param {String} userOpenId - 用户OpenID
 * @returns {Promise<Boolean>} 如果未达到限制返回true，否则返回false
 */
function checkClothesLimit(userOpenId) {
  return new Promise((resolve, reject) => {
    getUserLimits(userOpenId)
      .then(limits => {
        if (limits.clothesCount < limits.clothesLimit) {
          resolve(true); // 未达到限制
        } else {
          resolve(false); // 已达到限制
        }
      })
      .catch(err => {
        console.error('检查衣物限制失败:', err);
        reject(err);
      });
  });
}

/**
 * 检查用户穿搭数量是否达到限制
 * @param {String} userOpenId - 用户OpenID
 * @returns {Promise<Boolean>} 如果未达到限制返回true，否则返回false
 */
function checkOutfitsLimit(userOpenId) {
  return new Promise((resolve, reject) => {
    getUserLimits(userOpenId)
      .then(limits => {
        if (limits.outfitsCount < limits.outfitsLimit) {
          resolve(true); // 未达到限制
        } else {
          resolve(false); // 已达到限制
        }
      })
      .catch(err => {
        console.error('检查穿搭限制失败:', err);
        reject(err);
      });
  });
}

/**
 * 更新用户衣物计数
 * @param {String} userOpenId - 用户OpenID
 * @param {Number} increment - 增加的数量，可以为负数
 * @returns {Promise<Object>} 包含更新后限制信息的Promise
 */
function updateClothesCount(userOpenId, increment = 1) {
  return new Promise((resolve, reject) => {
    const db = wx.cloud.database();
    
    // 获取当前限制信息
    getUserLimits(userOpenId)
      .then(limits => {
        // 更新计数
        const newCount = Math.max(0, limits.clothesCount + increment);
        
        // 查找用户记录
        db.collection('users')
          .where({
            _openid: userOpenId
          })
          .update({
            data: {
              clothesCount: newCount
            }
          })
          .then(() => {
            // 返回更新后的限制信息
            const updatedLimits = {
              ...limits,
              clothesCount: newCount
            };
            resolve(updatedLimits);
          })
          .catch(err => {
            console.error('更新衣物计数失败:', err);
            reject(err);
          });
      })
      .catch(err => {
        console.error('获取限制信息失败:', err);
        reject(err);
      });
  });
}

/**
 * 更新用户穿搭计数
 * @param {String} userOpenId - 用户OpenID
 * @param {Number} increment - 增加的数量，可以为负数
 * @returns {Promise<Object>} 包含更新后限制信息的Promise
 */
function updateOutfitsCount(userOpenId, increment = 1) {
  return new Promise((resolve, reject) => {
    const db = wx.cloud.database();
    
    // 获取当前限制信息
    getUserLimits(userOpenId)
      .then(limits => {
        // 更新计数
        const newCount = Math.max(0, limits.outfitsCount + increment);
        
        // 查找用户记录
        db.collection('users')
          .where({
            _openid: userOpenId
          })
          .update({
            data: {
              outfitsCount: newCount
            }
          })
          .then(() => {
            // 返回更新后的限制信息
            const updatedLimits = {
              ...limits,
              outfitsCount: newCount
            };
            resolve(updatedLimits);
          })
          .catch(err => {
            console.error('更新穿搭计数失败:', err);
            reject(err);
          });
      })
      .catch(err => {
        console.error('获取限制信息失败:', err);
        reject(err);
      });
  });
}

module.exports = {
  getUserLimits,
  checkClothesLimit,
  checkOutfitsLimit,
  updateClothesCount,
  updateOutfitsCount
}; 