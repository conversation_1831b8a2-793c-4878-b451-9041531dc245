<view class="container">
  <!-- 加载中显示 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-icon"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 非管理员显示 -->
  <view class="no-permission" wx:elif="{{!isAdmin}}">
    <icon type="warn" size="64"></icon>
    <view class="no-permission-text">您没有权限访问此页面</view>
  </view>

  <!-- 管理员界面 -->
  <block wx:else>
    <!-- 页面头部 -->
    <view class="header">
      <view class="title">兑换码管理</view>
      <view class="header-buttons">
        <view class="add-btn" bindtap="showBatchCreateModal">批量生成</view>
        <view class="add-btn" bindtap="showCreateCodeModal">新建兑换码</view>
        <view class="add-btn membership-btn" bindtap="showCreateMembershipModal">会员兑换码</view>
        <view class="add-btn membership-btn" bindtap="showBatchMembershipModal">批量会员兑换码</view>
      </view>
    </view>

    <!-- 兑换码列表 -->
    <view class="section">
      <view class="section-title">兑换码列表</view>
      <view class="code-list">
        <view wx:if="{{codeList.length === 0}}" class="empty-tip">暂无兑换码</view>

        <view class="code-item" wx:for="{{codeList}}" wx:key="_id">
          <view class="code-item-main" bindtap="showCodeDetailModal" data-id="{{item._id}}">
            <view class="code-name">{{item.name}}</view>
            <view class="code-value">{{item.code}}</view>
          </view>

          <view class="code-item-actions">
            <view class="action-btn copy-btn" catchtap="copyCode" data-code="{{item.code}}">复制</view>
            <view class="action-btn {{item.isActive ? 'disable-btn' : 'enable-btn'}}" catchtap="toggleCodeStatus" data-id="{{item._id}}">
              {{item.isActive ? '禁用' : '启用'}}
            </view>
          </view>

          <view class="code-item-info">
            <text class="code-tag {{item.isActive ? 'active-tag' : 'inactive-tag'}}">{{item.isActive ? '已启用' : '已禁用'}}</text>
            <text class="code-tag {{item.isOneTime ? 'onetime-tag' : 'multi-tag'}}">{{item.isOneTime ? '一次性' : '多次使用'}}</text>
            <text class="code-time">创建于: {{formatDateTime(item.createTime)}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 兑换历史 -->
    <view class="section">
      <view class="section-title">兑换记录</view>
      <view class="history-list">
        <view wx:if="{{redeemHistory.length === 0}}" class="empty-tip">暂无兑换记录</view>

        <view class="history-item" wx:for="{{redeemHistory}}" wx:key="index">
          <view class="history-main">
            <view class="history-info">
              <view class="history-code">{{item.codeValue}}</view>
              <view class="history-name">{{item.codeName}}</view>
            </view>
            <view class="history-time">{{formatDateTime(item.redeemedAt)}}</view>
          </view>

          <view class="history-detail">
            <text class="history-user">用户: {{item.userId}}</text>

            <block wx:if="{{item.rewardType === 'clothes'}}">
              <text class="history-reward">奖励: +{{item.rewardAmount}} 衣物容量</text>
            </block>
            <block wx:elif="{{item.rewardType === 'outfits'}}">
              <text class="history-reward">奖励: +{{item.rewardAmount}} 搭配容量</text>
            </block>
            <block wx:elif="{{item.rewardType === 'both'}}">
              <text class="history-reward">奖励: +{{item.clothesRewardAmount}} 衣物容量, +{{item.outfitsRewardAmount}} 搭配容量</text>
            </block>
            <block wx:elif="{{item.rewardType === 'membership'}}">
              <text class="history-reward">奖励: {{item.membershipType === 'weekly' ? '周会员' : (item.membershipType === 'monthly' ? '月度会员' : (item.membershipType === 'quarterly' ? '季度会员' : '年度会员'))}} ({{item.membershipDays}}天)</text>
            </block>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 创建兑换码弹窗 -->
  <view class="modal" wx:if="{{showCreateModal}}">
    <view class="modal-mask" bindtap="closeCreateModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>创建新兑换码</text>
        <view class="close-btn" bindtap="closeCreateModal">×</view>
      </view>

      <view class="modal-body">
        <view class="form-item">
          <view class="form-label">名称</view>
          <input class="form-input" placeholder="输入兑换码名称" value="{{formData.name}}" bindinput="onInputChange" data-field="name" />
        </view>

        <view class="form-item">
          <view class="form-label">描述</view>
          <textarea class="form-textarea" placeholder="输入兑换码描述(可选)" value="{{formData.description}}" bindinput="onInputChange" data-field="description"></textarea>
        </view>

        <view class="form-item">
          <view class="form-label">奖励类型</view>
          <radio-group class="form-radio-group" bindchange="onRewardTypeChange">
            <label class="radio-label">
              <radio value="clothes" checked="{{formData.rewardType === 'clothes'}}" />衣物容量
            </label>
            <label class="radio-label">
              <radio value="outfits" checked="{{formData.rewardType === 'outfits'}}" />搭配容量
            </label>
            <label class="radio-label">
              <radio value="both" checked="{{formData.rewardType === 'both'}}" />两者都有
            </label>
          </radio-group>
        </view>

        <!-- 奖励数量输入 -->
        <block wx:if="{{formData.rewardType === 'clothes' || formData.rewardType === 'outfits'}}">
          <view class="form-item">
            <view class="form-label">奖励数量</view>
            <input class="form-input" type="number" placeholder="输入奖励数量" value="{{formData.rewardAmount}}" bindinput="onInputChange" data-field="rewardAmount" />
          </view>
        </block>

        <block wx:else>
          <view class="form-item">
            <view class="form-label">衣物容量奖励</view>
            <input class="form-input" type="number" placeholder="输入衣物容量奖励数量" value="{{formData.clothesRewardAmount}}" bindinput="onInputChange" data-field="clothesRewardAmount" />
          </view>

          <view class="form-item">
            <view class="form-label">搭配容量奖励</view>
            <input class="form-input" type="number" placeholder="输入搭配容量奖励数量" value="{{formData.outfitsRewardAmount}}" bindinput="onInputChange" data-field="outfitsRewardAmount" />
          </view>
        </block>

        <view class="form-item">
          <view class="form-label">兑换码类型</view>
          <switch checked="{{formData.isOneTime}}" bindchange="onOneTimeChange" color="#74301C" />
          <text class="switch-text">{{formData.isOneTime ? '一次性兑换码(仅限一名用户使用)' : '多次使用兑换码(每名用户限用一次)'}}</text>
        </view>

        <view class="form-item">
          <view class="form-label">有效期(天)</view>
          <input class="form-input" type="number" placeholder="输入有效期天数" value="{{formData.expirationDays}}" bindinput="onInputChange" data-field="expirationDays" />
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="closeCreateModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="createRedeemCode" disabled="{{isGenerating}}">
          {{isGenerating ? '生成中...' : '创建'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 兑换码详情弹窗 -->
  <view class="modal" wx:if="{{showCodeDetail}}">
    <view class="modal-mask" bindtap="closeDetailModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>兑换码详情</text>
        <view class="close-btn" bindtap="closeDetailModal">×</view>
      </view>

      <view class="modal-body">
        <view class="detail-row">
          <view class="detail-label">兑换码:</view>
          <view class="detail-value code-value">
            {{selectedCode.code}}
            <view class="copy-icon" catchtap="copyCode" data-code="{{selectedCode.code}}">复制</view>
          </view>
        </view>

        <view class="detail-row">
          <view class="detail-label">名称:</view>
          <view class="detail-value">{{selectedCode.name}}</view>
        </view>

        <view class="detail-row" wx:if="{{selectedCode.description}}">
          <view class="detail-label">描述:</view>
          <view class="detail-value">{{selectedCode.description}}</view>
        </view>

        <view class="detail-row">
          <view class="detail-label">奖励类型:</view>
          <view class="detail-value">
            <block wx:if="{{selectedCode.rewardType === 'clothes'}}">衣物容量</block>
            <block wx:elif="{{selectedCode.rewardType === 'outfits'}}">搭配容量</block>
            <block wx:elif="{{selectedCode.rewardType === 'both'}}">衣物和搭配容量</block>
            <block wx:elif="{{selectedCode.rewardType === 'membership'}}">会员权益</block>
          </view>
        </view>

        <view class="detail-row" wx:if="{{selectedCode.rewardType === 'clothes' || selectedCode.rewardType === 'outfits'}}">
          <view class="detail-label">奖励数量:</view>
          <view class="detail-value">{{selectedCode.rewardAmount}}</view>
        </view>

        <view wx:if="{{selectedCode.rewardType === 'both'}}">
          <view class="detail-row">
            <view class="detail-label">衣物容量奖励:</view>
            <view class="detail-value">{{selectedCode.clothesRewardAmount}}</view>
          </view>

          <view class="detail-row">
            <view class="detail-label">搭配容量奖励:</view>
            <view class="detail-value">{{selectedCode.outfitsRewardAmount}}</view>
          </view>
        </view>

        <view wx:if="{{selectedCode.rewardType === 'membership'}}">
          <view class="detail-row">
            <view class="detail-label">会员类型:</view>
            <view class="detail-value">
              {{selectedCode.membershipType === 'weekly' ? '周会员' :
                (selectedCode.membershipType === 'monthly' ? '月度会员' :
                (selectedCode.membershipType === 'quarterly' ? '季度会员' : '年度会员'))}}
            </view>
          </view>
        </view>

        <view class="detail-row">
          <view class="detail-label">兑换码类型:</view>
          <view class="detail-value">{{selectedCode.isOneTime ? '一次性兑换码' : '多次使用兑换码'}}</view>
        </view>

        <view class="detail-row">
          <view class="detail-label">状态:</view>
          <view class="detail-value">
            <text class="status-tag {{selectedCode.isActive ? 'active-status' : 'inactive-status'}}">
              {{selectedCode.isActive ? '已启用' : '已禁用'}}
            </text>
          </view>
        </view>

        <view class="detail-row">
          <view class="detail-label">创建时间:</view>
          <view class="detail-value">{{formatDateTime(selectedCode.createTime)}}</view>
        </view>

        <view class="detail-row">
          <view class="detail-label">过期时间:</view>
          <view class="detail-value">{{formatDateTime(selectedCode.expirationDate)}}</view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn confirm-btn" bindtap="closeDetailModal">关闭</button>
      </view>
    </view>
  </view>

  <!-- 批量生成兑换码弹窗 -->
  <view class="modal" wx:if="{{showBatchModal}}">
    <view class="modal-mask" bindtap="closeBatchModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>批量生成兑换码</text>
        <view class="close-btn" bindtap="closeBatchModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 如果有生成结果，显示结果 -->
        <block wx:if="{{batchResults.length > 0}}">
          <view class="section-title">生成结果</view>
          <view class="batch-result">
            <view class="batch-result-item" wx:for="{{batchResults}}" wx:key="index">
              <text>{{item.name}}</text>
              <text class="result-code">{{item.code}}</text>
            </view>
          </view>
          <view class="copy-all-btn" bindtap="copyAllCodes">复制全部兑换码</view>
        </block>

        <!-- 如果没有生成结果，显示表单 -->
        <block wx:else>
          <view class="form-item">
            <view class="form-label">兑换码前缀</view>
            <input class="form-input" placeholder="输入兑换码前缀(可选)" value="{{batchFormData.prefix}}" bindinput="onBatchInputChange" data-field="prefix" />
          </view>

          <view class="form-item">
            <view class="form-label">生成数量</view>
            <input class="form-input" type="number" placeholder="输入生成数量" value="{{batchFormData.count}}" bindinput="onBatchInputChange" data-field="count" />
          </view>

          <view class="form-item">
            <view class="form-label">名称模板</view>
            <input class="form-input" placeholder="例如: 扩容兑换码-%d" value="{{batchFormData.nameTemplate}}" bindinput="onBatchInputChange" data-field="nameTemplate" />
            <view class="form-hint">%d将被替换为序号</view>
          </view>

          <view class="form-item">
            <view class="form-label">奖励类型</view>
            <radio-group class="form-radio-group" bindchange="onBatchRewardTypeChange">
              <label class="radio-label">
                <radio value="clothes" checked="{{batchFormData.rewardType === 'clothes'}}" />衣物容量
              </label>
              <label class="radio-label">
                <radio value="outfits" checked="{{batchFormData.rewardType === 'outfits'}}" />搭配容量
              </label>
              <label class="radio-label">
                <radio value="both" checked="{{batchFormData.rewardType === 'both'}}" />两者都有
              </label>
            </radio-group>
          </view>

          <!-- 奖励数量输入 -->
          <block wx:if="{{batchFormData.rewardType === 'clothes' || batchFormData.rewardType === 'outfits'}}">
            <view class="form-item">
              <view class="form-label">奖励数量</view>
              <input class="form-input" type="number" placeholder="输入奖励数量" value="{{batchFormData.rewardAmount}}" bindinput="onBatchInputChange" data-field="rewardAmount" />
            </view>
          </block>

          <block wx:else>
            <view class="form-item">
              <view class="form-label">衣物容量奖励</view>
              <input class="form-input" type="number" placeholder="输入衣物容量奖励数量" value="{{batchFormData.clothesRewardAmount}}" bindinput="onBatchInputChange" data-field="clothesRewardAmount" />
            </view>

            <view class="form-item">
              <view class="form-label">搭配容量奖励</view>
              <input class="form-input" type="number" placeholder="输入搭配容量奖励数量" value="{{batchFormData.outfitsRewardAmount}}" bindinput="onBatchInputChange" data-field="outfitsRewardAmount" />
            </view>
          </block>

          <view class="form-item">
            <view class="form-label">兑换码类型</view>
            <switch checked="{{batchFormData.isOneTime}}" bindchange="onBatchOneTimeChange" color="#74301C" />
            <text class="switch-text">{{batchFormData.isOneTime ? '一次性兑换码(仅限一名用户使用)' : '多次使用兑换码(每名用户限用一次)'}}</text>
          </view>

          <view class="form-item">
            <view class="form-label">有效期(天)</view>
            <input class="form-input" type="number" placeholder="输入有效期天数" value="{{batchFormData.expirationDays}}" bindinput="onBatchInputChange" data-field="expirationDays" />
          </view>
        </block>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="closeBatchModal">{{batchResults.length > 0 ? '关闭' : '取消'}}</button>
        <button wx:if="{{batchResults.length === 0}}" class="modal-btn confirm-btn" bindtap="batchCreateRedeemCodes" disabled="{{isBatchGenerating}}">
          {{isBatchGenerating ? '生成中...' : '批量生成'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 批量会员兑换码弹窗 -->
  <view class="modal" wx:if="{{showBatchMembershipModal}}">
    <view class="modal-mask" bindtap="closeBatchMembershipModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>批量生成会员兑换码</text>
        <view class="close-btn" bindtap="closeBatchMembershipModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 如果有生成结果，显示结果 -->
        <block wx:if="{{batchMembershipResults.length > 0}}">
          <view class="section-title">生成结果</view>
          <view class="batch-result">
            <view class="batch-result-item" wx:for="{{batchMembershipResults}}" wx:key="index">
              <text>{{item.name}}</text>
              <text class="result-code">{{item.code}}</text>
            </view>
          </view>
          <view class="copy-all-btn" bindtap="copyAllMembershipCodes">复制全部兑换码</view>
        </block>

        <!-- 如果没有生成结果，显示表单 -->
        <block wx:else>
          <view class="form-item">
            <view class="form-label">兑换码前缀</view>
            <input class="form-input" placeholder="输入兑换码前缀(可选)" value="{{batchMembershipFormData.prefix}}" bindinput="onBatchMembershipInputChange" data-field="prefix" />
          </view>

          <view class="form-item">
            <view class="form-label">生成数量</view>
            <input class="form-input" type="number" placeholder="输入生成数量" value="{{batchMembershipFormData.count}}" bindinput="onBatchMembershipInputChange" data-field="count" />
          </view>

          <view class="form-item">
            <view class="form-label">名称模板</view>
            <input class="form-input" placeholder="例如: 会员兑换码-%d" value="{{batchMembershipFormData.nameTemplate}}" bindinput="onBatchMembershipInputChange" data-field="nameTemplate" />
            <view class="form-hint">%d将被替换为序号</view>
          </view>

          <view class="form-item">
            <view class="form-label">会员类型</view>
            <radio-group class="form-radio-group" bindchange="onBatchMembershipTypeChange">
              <label class="radio-label">
                <radio value="weekly" checked="{{batchMembershipFormData.membershipType === 'weekly'}}" />周会员 (7天)
              </label>
              <label class="radio-label">
                <radio value="monthly" checked="{{batchMembershipFormData.membershipType === 'monthly'}}" />月度会员 (30天)
              </label>
              <label class="radio-label">
                <radio value="quarterly" checked="{{batchMembershipFormData.membershipType === 'quarterly'}}" />季度会员 (90天)
              </label>
              <label class="radio-label">
                <radio value="yearly" checked="{{batchMembershipFormData.membershipType === 'yearly'}}" />年度会员 (365天)
              </label>
            </radio-group>
          </view>

          <view class="form-item">
            <view class="form-label">兑换码类型</view>
            <switch checked="{{batchMembershipFormData.isOneTime}}" bindchange="onBatchMembershipOneTimeChange" color="#74301C" />
            <text class="switch-text">{{batchMembershipFormData.isOneTime ? '一次性兑换码(仅限一名用户使用)' : '多次使用兑换码(每名用户限用一次)'}}</text>
          </view>

          <view class="form-item">
            <view class="form-label">有效期(天)</view>
            <input class="form-input" type="number" placeholder="输入有效期天数" value="{{batchMembershipFormData.expirationDays}}" bindinput="onBatchMembershipInputChange" data-field="expirationDays" />
          </view>
        </block>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="closeBatchMembershipModal">{{batchMembershipResults.length > 0 ? '关闭' : '取消'}}</button>
        <button wx:if="{{batchMembershipResults.length === 0}}" class="modal-btn confirm-btn" bindtap="batchCreateMembershipCodes" disabled="{{isBatchGeneratingMembership}}">
          {{isBatchGeneratingMembership ? '生成中...' : '批量生成'}}
        </button>
      </view>
    </view>
  </view>
</view>

  <!-- 会员兑换码弹窗 -->
  <view class="modal" wx:if="{{showMembershipModal}}">
    <view class="modal-mask" bindtap="closeMembershipModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>创建会员兑换码</text>
        <view class="close-btn" bindtap="closeMembershipModal">×</view>
      </view>

      <view class="modal-body">
        <view class="form-item">
          <view class="form-label">名称</view>
          <input class="form-input" placeholder="输入兑换码名称" value="{{membershipFormData.name}}" bindinput="onMembershipInputChange" data-field="name" />
        </view>

        <view class="form-item">
          <view class="form-label">描述</view>
          <textarea class="form-textarea" placeholder="输入兑换码描述(可选)" value="{{membershipFormData.description}}" bindinput="onMembershipInputChange" data-field="description"></textarea>
        </view>

        <view class="form-item">
          <view class="form-label">会员类型</view>
          <radio-group class="form-radio-group" bindchange="onMembershipTypeChange">
            <label class="radio-label">
              <radio value="weekly" checked="{{membershipFormData.membershipType === 'weekly'}}" />周会员 (7天)
            </label>
            <label class="radio-label">
              <radio value="monthly" checked="{{membershipFormData.membershipType === 'monthly'}}" />月度会员 (30天)
            </label>
            <label class="radio-label">
              <radio value="quarterly" checked="{{membershipFormData.membershipType === 'quarterly'}}" />季度会员 (90天)
            </label>
            <label class="radio-label">
              <radio value="yearly" checked="{{membershipFormData.membershipType === 'yearly'}}" />年度会员 (365天)
            </label>
          </radio-group>
        </view>

        <view class="form-item">
          <view class="form-label">兑换码类型</view>
          <switch checked="{{membershipFormData.isOneTime}}" bindchange="onMembershipOneTimeChange" color="#74301C" />
          <text class="switch-text">{{membershipFormData.isOneTime ? '一次性兑换码(仅限一名用户使用)' : '多次使用兑换码(每名用户限用一次)'}}</text>
        </view>

        <view class="form-item">
          <view class="form-label">有效期(天)</view>
          <input class="form-input" type="number" placeholder="输入有效期天数" value="{{membershipFormData.expirationDays}}" bindinput="onMembershipInputChange" data-field="expirationDays" />
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="closeMembershipModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="createMembershipCode" disabled="{{isGeneratingMembership}}">
          {{isGeneratingMembership ? '生成中...' : '创建'}}
        </button>
      </view>
    </view>
  </view>