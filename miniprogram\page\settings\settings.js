// page/settings/settings.js
//const colors = require('../../../util/colors');
const limitManager = require('../wardrobe/common/limitManager');
const taskHelper = require('../wardrobe/common/taskHelper');
const medalManager = require('./modules/medalManager');
const energyManager = require('../../util/energyManager');
Page({
  data: {
    theme: 'light', // WeUI的主题，light或dark
    // 风格切换设置
    themeStyle: 'autumn', // 默认为秋季风格，可选值：'autumn'、'pinkBlue'或'blackWhite'
    // 主题选择器弹窗
    showThemeSelectorModal: false,
    // 当前广告类型，用于跟踪正在显示的广告
    currentAdType: '', // 'energy' 或 'lottery' 或 ''
    // 视频广告相关
    videoAdData: {
      adUnitId: 'adunit-cd49aa9807d05471', // 广告单元ID
      watchCount: 0, // 当天观看次数
      maxWatchCount: 5, // 每日最大观看次数
      lastWatchDate: '' // 上次观看日期
    },
    // 广告抽奖相关
    adLotteryData: {
      adUnitId: 'adunit-6a0558739219a08b', // 广告单元ID
      lotteryCount: 0, // 当天抽奖次数
      maxLotteryCount: 2, // 每日最大抽奖次数
      lastLotteryDate: '' // 上次抽奖日期
    },
    // 每日签到相关
    checkInData: {
      isLoading: true, // 默认为加载中状态
      todayCheckedIn: false,
      consecutiveDays: 0,
      last7Days: [],
      nextRewards: {
        energy: 10,
        storage: 0
      }
    },
    // 签到缓存配置
    checkInCacheConfig: {
      cacheKey: 'userCheckInCache',
      expirationKey: 'userCheckInCacheExpiration',
      cacheDuration: 24 * 60 * 60 * 1000, // 缓存有效期，默认为当天结束
      dailyResetFlag: 'checkInResetFlag' // 标记是否已重置今日签到
    },
    // 签到奖励卡片可见性
    checkInRewardVisible: false,
    // 签到奖励数据
    checkInRewardData: {
      title: '',
      energy: 0,
      storage: 0,
      isVIP: false
    },
    // 请求锁，防止重复请求
    isCheckInRequestInProgress: false,
    // 使用秋季色系色彩配置
    colors: {
      cowhide_cocoa: '#442D1C',   // 深棕色 Cowhide Cocoa
      spiced_wine: '#74301C',     // 红棕色 Spiced Wine
      toasted_caramel: '#84592B', // 焦糖色 Toasted Caramel
      olive_harvest: '#9D9167',   // 橄榄色 Olive Harvest
      golden_batter: '#E8D1A7',   // 金黄色 Golden Batter
    },
    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',       // 深粉色
      pinkMedium: '#EEA0B2',     // 中粉色
      pinkLight: '#F9C9D6',      // 浅粉色
      blueLight: '#CBE0F9',      // 浅蓝色
      blueMedium: '#97C8E5',     // 中蓝色
      blueDark: '#5EA0D0',       // 深蓝色
    },
    // 黑白色系配色
    blackWhiteColors: {
      black: '#000000',          // 纯黑
      darkGray: '#333333',      // 深灰
      mediumGray: '#666666',    // 中灰
      lightGray: '#CCCCCC',     // 浅灰
      white: '#FFFFFF',         // 纯白
      offWhite: '#dee3ec',      // 灰白
    },
    // 页面样式
    pageStyle: {
      backgroundColor: '',
      backgroundImage: '',
      titleColor: '',
      cellBackgroundColor: '',
      footerColor: '',
      decorationColors: []
    },
    // 用户信息
    userInfo: {
      nickName: '加载中...',
      avatarUrl: '/image/default-avatar.png',
      title: '初级养猫人',
      catEnergy: 50, // 猫咪体力值，默认值为50
      memberType: 'normal', // 会员类型，默认为普通会员
      memberExpireDate: null, // 会员过期时间
      memberDaysLeft: 0 // 会员剩余天数
    },
    // 新用户赠送会员提示标志
    showNewUserVipTip: false,
    // 猫咪图片相关
    catImages: {
      happyCat: '', // 开心猫咪图片临时文件路径 (体力值 > 200)
      normalCat: '', // 普通猫咪图片临时文件路径 (50 <= 体力值 <= 200)
      tireCat: '', // 疑惑猫咪图片临时文件路径 (体力值 < 50)
      isLoaded: false // 图片是否加载完成
    },
    // 会员权益弹窗相关
    showMemberBenefitsModal: false,
    showVipPurchaseModal: false,
    vipPurchaseImage: '',
    selectedPriceOption: 'yearly', // 默认选中年度会员
    // 体力值规则弹窗相关
    showEnergyRulesModal: false,
    userInfoLoaded: false, // 标记是否已加载用户信息
    // 用户称号选择相关
    showTitleSelector: false, // 是否显示称号选择器
    availableTitles: ['初级养猫人'], // 默认称号，将从云端获取
    defaultTitles: [ // 默认称号列表，当云端没有称号时使用
      '初级养猫人'
    ],
    // 用户信息缓存配置
    userInfoCacheConfig: {
      cacheKey: 'userInfoCache',
      expirationKey: 'userInfoCacheExpiration',
      cacheDuration: 7 * 24 * 60 * 60 * 1000 // 缓存有效期，默认7天
    },
    // 用户限制信息
    userLimits: {
      clothesLimit: 0,
      outfitsLimit: 0,
      clothesCount: 0,
      outfitsCount: 0
    },
    limitLoaded: false, // 标记是否已加载限制信息
    uidDisplay: '', // 用于显示用户UID（openid的后10位）
    fullOpenid: '', // 存储用户完整的openid
    // 添加缓存相关配置
    limitCacheConfig: {
      cacheKey: 'userLimitsCache', // 缓存键名
      expirationKey: 'userLimitsCacheExpiration', // 缓存过期时间键名
      cacheDuration: 360 * 24 * 60 * 60 * 1000 // 缓存有效期，默认360天
    },
    // 管理员openid
    adminOpenid: 'o0xlr5Ac89ttaZeQaJJ_szlm8y2c',
    // 首次加载标志
    isFirstLoad: true,  // 添加首次加载标记
    // 请求锁，防止重复请求
    isLimitRequestInProgress: false,
    // 上次请求时间
    lastLimitRequestTime: 0,
    // 存储箱衣物数量
    storageCount: undefined,
    // 待办物品箱衣物数量
    todoCount: undefined,
    // 今天是否有待办事项
    hasTodayTodo: false,
    // 最近待办事项信息
    recentTodo: null,
    recentTodoDate: null,
    // 消息通知数据
    messages: [],
    unreadCount: 0,
    // 消息缓存配置
    messageCacheConfig: {
      cacheKey: 'userMessagesCache',
      expirationKey: 'userMessagesCacheExpiration',
      cacheDuration: 60 * 60 * 1000, // 1小时缓存有效期
      lastSyncTimeKey: 'lastMessageSyncTime'
    },
    // 消息同步锁
    isMessageSyncInProgress: false,

    // 勋章相关数据
    medals: {
      count: 0,
      earnedCount: 0,
      isLoading: false,
      lastUpdateTime: 0
    },
    // 主题任务进度相关数据
    themeTasks: {
      hasDone: [], // 已完成任务步骤
      currentTask: 0, // 当前总体进度(1-n)
      currentStep: 0, // 当前阶段进度(1-6)
      isLoading: false, // 加载状态
      titles: [], // 任务步骤标题
      descriptions: [], // 任务步骤描述
      taskImages: [ // 任务阶段图片（默认占位图，将被云端图片替换）
        '/image/placeholder.png',
        '/image/placeholder.png',
        '/image/placeholder.png',
        '/image/placeholder.png',
        '/image/placeholder.png',
        '/image/placeholder.png'
      ],
      // 主题任务描述，根据总体进度显示不同描述
      taskDescriptions: [
        "抓到猫猫可以根据卡片获得奖励   [集齐一套卡牌（6张不同的卡片)获得月度会员]",
        "探索四季时尚",
        "打造个性风格搭配",
        "建立专属穿搭系统",
        "成为时尚达人"
      ]
    },
    // 主题任务缓存配置
    tasksCacheConfig: {
      cacheKey: 'userTasksCache',
      expirationKey: 'userTasksCacheExpiration',
      cacheDuration: 24 * 60 * 60 * 1000 // 缓存有效期，默认1天
    },
    // 任务图片缓存配置
    taskImagesCacheConfig: {
      cacheKey: 'taskImagesCache',
      expirationKey: 'taskImagesCacheExpiration',
      cacheDuration: 7 * 24 * 60 * 60 * 1000, // 缓存有效期，默认7天
      localImagesMap: {} // 云端fileID到本地路径的映射
    },
    // 图片加载状态
    imageLoading: {
      isLoading: false,
      loadedCount: 0,
      totalCount: 6
    },
    // 每日任务相关数据
    dailyTasks: {
      isLoading: false,
      date: '', // 当前日期 YYYY-MM-DD 格式
      tasks: [
        { id: 'task1', name: '添加一件衣物', completed: false },
        { id: 'task2', name: '创建一套搭配', completed: false },
        { id: 'task3', name: '查看推荐搭配', completed: false }
      ],
      completedCount: 0, // 已完成任务数量
      totalCount: 3,     // 总任务数量
      canDrawPrize: false, // 是否可以抽奖
      rewardClaimed: false // 是否已领取奖励
    },
    // 每日任务缓存配置
    dailyTasksCacheConfig: {
      cacheKey: 'userDailyTasksCache',
      expirationKey: 'userDailyTasksCacheExpiration',
      cacheDuration: 24 * 60 * 60 * 1000, // 缓存有效期，默认为当天结束
      dailyResetFlag: 'dailyTasksResetFlag' // 标记是否已重置今日任务
    },
    // 请求锁，防止重复请求
    isDailyTaskRequestInProgress: false,
    // 任务卡片可见性
    isTaskCardVisible: false,
    // 奖励卡片可见性
    rewardCardVisible: false,
    // 奖励数据
    rewardData: {
      image: '',
      title: '',
      description: ''
    },
    // 断舍离箱数量
    discardCount: undefined,
    // 主题任务卡片数据
    themeTaskCard: {
      visible: false,
      imageUrl: '',
      title: '',
      description: '',
      stepNumber: 1,
      isCompleted: false
    },
    // 抽奖动画相关数据
    isPrizeDrawingVisible: false,
    prizeDrawingCards: [],
    prizeDrawingOffset: 0,
    currentCardIndex: 0,
    winningIndex: 0,
    currentPrize: null,

    // 主题完成卡片数据
    themeCompletionVisible: false,
  },

  onLoad: function() {
    console.log('设置页面加载');

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '设置'
    });

    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });

      // 应用导航栏样式
      this.applyThemeStyle(savedTheme);
    } else {
      // 默认应用秋季主题
      this.applyThemeStyle('autumn');
    }

    // 初始化视频广告
    this.initVideoAd();

    // 检查广告观看次数重置
    this.checkVideoAdWatchCount();

    // 初始化广告抽奖
    this.initAdLottery();

    // 检查广告抽奖次数重置
    this.checkAdLotteryCount();

    // 检查系统暗黑模式
    wx.getSystemInfo({
      success: (res) => {
        if (res.theme === 'dark') {
          this.setData({
            theme: 'dark'
          });
        }
      }
    });

    // 下载猫咪图片
    this.downloadCatImages();

    // 获取用户信息（优先使用缓存）
    this.getUserInfo();

    // 获取用户限制信息（优先使用缓存）
    this.getUserLimitsInfo(true);

    // 获取用户UID显示（openid后10位）
    this.getUserUidDisplay();

    // 获取存储箱衣物数量
    //this.updateStorageCount();

    // 获取断舍离箱衣物数量
    this.updateDiscardCount();

    // 获取待办物品箱衣物数量
    this.updateTodoCount();

    // 同步消息通知
    this.syncMessages();

    // 获取用户主题任务进度
    this.getUserTaskProgress();

    // 获取任务图片
    //this.getTaskImages();

    // 获取每日任务
    this.getDailyTasks();

    // 获取用户勋章数据
    this.getUserMedals();

    // 获取签到状态
    this.getCheckInStatus();

    // 用于处理页面可见性变化的事件
    this.handlePageVisibilityChange();
  },

  onShow: function() {
    // 获取应用实例
    const app = getApp();
    if (!this.data.userInfoLoaded) {
      this.getUserInfo();
    } else {
      // 检查会员状态和体力值
      this.checkMembershipAndEnergy();
    }
    if(app.globalData.dailyTasksNeedRefresh = true){
      app.globalData.dailyTasksNeedRefresh = false;
      this.setData({
        dailyTasksNeedRefresh: true,
        dailyTasks: wx.getStorageSync(this.data.dailyTasksCacheConfig.cacheKey),
      });
    }

    const todoClothesNeedRefresh = wx.getStorageSync('todoClothesneedRefresh');
    if(todoClothesNeedRefresh === true){
      wx.removeStorageSync('todoClothesneedRefresh');
      this.updateTodoCount();
    }


    // 如果是首次加载，将标志设置为false并跳过重复加载
    if (this.data.isFirstLoad) {
      console.log('设置页面首次加载，跳过数据刷新');
      this.setData({ isFirstLoad: false });
      return;
    }

    // 检查是否距离上次请求过了足够时间（至少3秒）
    const now = Date.now();
    const timeSinceLastRequest = now - this.data.lastLimitRequestTime;
    const MIN_REQUEST_INTERVAL = 3000; // 3秒

    if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
      console.log(`距离上次请求仅过了 ${timeSinceLastRequest}ms，小于最小间隔 ${MIN_REQUEST_INTERVAL}ms，跳过请求`);
      return;
    }

    // 检查是否需要刷新限制数据
    if (app.globalData && app.globalData.limitDataNeedRefresh) {
      console.log('检测到限制数据需要刷新');
      // 强制从服务器刷新用户限制信息
      this.getUserLimitsInfo(false);
      // 重置标记
      app.globalData.limitDataNeedRefresh = false;
    } else {
      // 检查缓存是否过期，仅在过期时更新
      const cacheKey = this.data.limitCacheConfig.cacheKey;
      const expirationKey = this.data.limitCacheConfig.expirationKey;

      const cachedData = wx.getStorageSync(cacheKey);
      const cacheExpiration = wx.getStorageSync(expirationKey);

      if (!cachedData || !cacheExpiration || now > cacheExpiration) {
        console.log('限制数据缓存已过期或不存在，从服务器获取');
        this.getUserLimitsInfo(false);
      } else {
        console.log('使用缓存的限制数据，有效期至:', new Date(cacheExpiration).toLocaleString());
      }
    }

    // 检查是否需要同步消息通知
    this.checkAndSyncMessages();

    // 检查是否需要重置每日任务（过了0点）
    this.checkDailyTasksReset();

    // 检查是否需要刷新每日任务数据
    if (app.globalData && app.globalData.dailyTasksNeedRefresh) {
      console.log('检测到每日任务数据需要刷新');
      // 强制从服务器刷新每日任务数据
      this.getDailyTasks();
      // 重置标记
      app.globalData.dailyTasksNeedRefresh = false;
    }

    // 检查是否需要刷新勋章数据
    if (app.globalData && app.globalData.medalsNeedRefresh) {
      console.log('检测到勋章数据需要刷新');
      // 强制从服务器刷新勋章数据
      this.getUserMedals(false);
      // 重置标记
      app.globalData.medalsNeedRefresh = false;
    }

    // 检查是否需要重置签到状态（过了0点）
    this.checkCheckInReset();
  },

  // 获取用户UID显示
  getUserUidDisplay: function() {
    const app = getApp();

    // 尝试从本地缓存获取openid
    const cachedOpenid = wx.getStorageSync('openid');
    if (cachedOpenid) {
      console.log('从缓存获取到openid:', cachedOpenid);
      this.setUidDisplay(cachedOpenid);
      return;
    }

    // 如果本地没有缓存，调用获取openid的方法
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        this.setData({
          uidDisplay: '获取失败'
        });
        return;
      }

      console.log('成功获取用户openid:', openid);
      this.setUidDisplay(openid);
    });
  },

  // 设置UID显示（取openid后10位）
  setUidDisplay: function(openid) {
    if (!openid) {
      this.setData({
        uidDisplay: '未知',
        fullOpenid: ''
      });
      return;
    }

    // 保存完整的openid
    this.setData({
      fullOpenid: openid
    });

    console.log('设置fullOpenid:', openid);

    // 取openid后10位作为UID显示
    const uidDisplay = openid.length > 10 ? openid.substr(openid.length - 10) : openid;
    this.setData({
      uidDisplay: uidDisplay
    });
  },

  // 显示主题选择器弹窗
  showThemeSelector: function() {
    this.setData({
      showThemeSelectorModal: true
    });
  },

  // 隐藏主题选择器弹窗
  hideThemeSelector: function() {
    this.setData({
      showThemeSelectorModal: false
    });
  },

  // 防止事件冒泡
  stopPropagation: function(e) {
    return false; // 阻止事件继续传播
  },

  // 选择主题风格
  selectTheme: function(e) {
    const newTheme = e.currentTarget.dataset.theme;
    if (newTheme === this.data.themeStyle) {
      // 如果已经是当前主题，只隐藏弹窗
      this.hideThemeSelector();
      return;
    }

    console.log('切换主题:', newTheme);

    this.setData({
      themeStyle: newTheme,
      showThemeSelectorModal: false // 选择后隐藏弹窗
    });

    // 保存设置到本地存储
    wx.setStorageSync('themeStyle', newTheme);

    // 设置当前页面的导航栏样式
    this.applyThemeStyle(newTheme);

    // 获取当前所有页面实例
    const pages = getCurrentPages();
    console.log('当前页面栈:', pages.map(p => p.route));

    // 尝试直接通知所有页面更新主题
    this.notifyAllPagesToUpdateTheme(pages, newTheme);

    // 如果当前页面堆栈中找不到搭配页面，则使用备用方案
    const outfitPage = pages.find(page => page.route && page.route.includes('wardrobe/outfit') && !page.route.includes('outfit_'));
    if (!outfitPage) {
      console.log('未在页面栈中找到搭配页面，使用备用方案');
      this.useBackupMethodForOutfitPage(newTheme);
    }

    // 提示用户主题已切换
    wx.showToast({
      title: '主题已切换',
      icon: 'success',
      duration: 1000
    });
  },

  // 通知所有页面更新主题
  notifyAllPagesToUpdateTheme: function(pages, newTheme) {
    // 通知首页更新主题
    const homePage = pages.find(page => page.route && page.route.includes('wardrobe/index'));
    if (homePage) {
      console.log('通知首页更新主题');
      homePage.setData({
        themeStyle: newTheme
      });
      // 确保首页立即应用新主题
      if (typeof homePage.applyThemeStyle === 'function') {
        homePage.applyThemeStyle(newTheme);
      } else if (typeof homePage.updateTheme === 'function') {
        homePage.updateTheme(newTheme);
      }
    }

    // 通知衣柜页面更新主题
    const closetPage = pages.find(page => page.route && page.route.includes('wardrobe/closet'));
    if (closetPage) {
      console.log('通知衣柜页面更新主题');
      closetPage.setData({
        themeStyle: newTheme
      });
      // 确保衣柜页面立即应用新主题
      if (typeof closetPage.updateTheme === 'function') {
        closetPage.updateTheme(newTheme);
      } else if (typeof closetPage.applyThemeStyle === 'function') {
        closetPage.applyThemeStyle(newTheme);
      }
    }

    // 通知搭配页面更新主题
    const outfitPage = pages.find(page => page.route && page.route.includes('wardrobe/outfit') && !page.route.includes('outfit_'));
    if (outfitPage) {
      console.log('通知搭配页面更新主题');
      // 先设置数据
      outfitPage.setData({
        themeStyle: newTheme,
        _forceRefresh: new Date().getTime()
      });

      // 然后尝试调用更新方法
      if (typeof outfitPage.updateTheme === 'function') {
        console.log('调用搭配页面的updateTheme方法');
        outfitPage.updateTheme(newTheme);
      } else if (typeof outfitPage.applyThemeStyle === 'function') {
        console.log('调用搭配页面的applyThemeStyle方法');
        outfitPage.applyThemeStyle(newTheme);
      }

      // 延迟再次设置以确保更新
      setTimeout(() => {
        if (outfitPage && outfitPage.setData) {
          outfitPage.setData({
            themeStyle: newTheme,
            _delayedRefresh: new Date().getTime()
          });
        }
      }, 300);
    }

    // 通知其他搭配相关页面
    this.notifyOutfitSubPages(pages, newTheme);
  },

  // 通知搭配子页面（创建、分类、详情等）
  notifyOutfitSubPages: function(pages, newTheme) {
    // 通知搭配创建页面更新主题
    const outfitCreatePage = pages.find(page => page.route && page.route.includes('wardrobe/outfit/outfit_create'));
    if (outfitCreatePage) {
      console.log('通知搭配创建页面更新主题');
      outfitCreatePage.setData({
        themeStyle: newTheme,
        _forceRefresh: new Date().getTime()
      });

      if (typeof outfitCreatePage.updateTheme === 'function') {
        outfitCreatePage.updateTheme(newTheme);
      } else if (typeof outfitCreatePage.applyThemeStyle === 'function') {
        outfitCreatePage.applyThemeStyle(newTheme);
      }
    }

    // 通知搭配分类页面更新主题
    const outfitCategoryPage = pages.find(page => page.route && page.route.includes('wardrobe/outfit/outfit_category'));
    if (outfitCategoryPage) {
      console.log('通知搭配分类页面更新主题');
      outfitCategoryPage.setData({
        themeStyle: newTheme,
        _forceRefresh: new Date().getTime()
      });

      if (typeof outfitCategoryPage.updateTheme === 'function') {
        outfitCategoryPage.updateTheme(newTheme);
      } else if (typeof outfitCategoryPage.applyThemeStyle === 'function') {
        outfitCategoryPage.applyThemeStyle(newTheme);
      }
    }

    // 通知搭配详情页面更新主题
    const outfitDetailPage = pages.find(page => page.route && page.route.includes('wardrobe/outfit/outfit_detail'));
    if (outfitDetailPage) {
      console.log('通知搭配详情页面更新主题');
      outfitDetailPage.setData({
        themeStyle: newTheme,
        _forceRefresh: new Date().getTime()
      });

      if (typeof outfitDetailPage.updateTheme === 'function') {
        outfitDetailPage.updateTheme(newTheme);
      } else if (typeof outfitDetailPage.applyThemeStyle === 'function') {
        outfitDetailPage.applyThemeStyle(newTheme);
      }
    }
  },

  // 当找不到搭配页面时的备用方案
  useBackupMethodForOutfitPage: function(newTheme) {
    // 这是一个备用方案，当无法直接访问搭配页面时使用
    // 通过全局事件或其他方式确保主题正确应用

    // 1. 确保本地存储已更新
    wx.setStorageSync('themeStyle', newTheme);

    // 2. 如果有其他可用的通知机制，可以在这里添加

    // 3. 如果有全局事件总线，可以触发主题更改事件
    const app = getApp();
    if (app && typeof app.globalThemeChanged === 'function') {
      app.globalThemeChanged(newTheme);
    }

    // 4. 考虑在下次导航到搭配页面时强制刷新
    console.log('设置标记，下次打开搭配页面时强制刷新主题');
    wx.setStorageSync('needRefreshOutfitTheme', true);
  },

  // 应用主题样式
  applyThemeStyle: function(themeName) {
    // 更新页面样式
    let pageStyle = {};

    if (themeName === 'autumn') {
      // 设置秋季主题样式
      pageStyle = {
        backgroundColor: this.data.colors.golden_batter,
        backgroundImage: 'none',
        titleColor: this.data.colors.cowhide_cocoa,
        cellBackgroundColor: 'rgba(255, 255, 255, 0.7)',
        footerColor: this.data.colors.cowhide_cocoa,
        decorationColors: [
          this.data.colors.olive_harvest,
          this.data.colors.spiced_wine,
          this.data.colors.toasted_caramel
        ]
      };

      // 设置秋季主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.colors.golden_batter, // 金黄色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });

      // 设置秋季主题TabBar
      wx.setTabBarStyle({
        backgroundColor: this.data.colors.golden_batter,
        borderStyle: 'black',
        color: this.data.colors.cowhide_cocoa,
        selectedColor: this.data.colors.spiced_wine
      });
    } else if (themeName === 'pinkBlue') {
      // 设置粉蓝主题样式
      pageStyle = {
        backgroundColor: this.data.pinkBlueColors.pinkLight,
        backgroundImage: `linear-gradient(to bottom, white, ${this.data.pinkBlueColors.pinkLight})`,
        titleColor: this.data.pinkBlueColors.pinkDark,
        cellBackgroundColor: 'rgba(255, 255, 255, 0.9)',
        footerColor: this.data.pinkBlueColors.blueDark,
        decorationColors: [
          this.data.pinkBlueColors.blueMedium,
          this.data.pinkBlueColors.pinkMedium,
          this.data.pinkBlueColors.blueLight
        ]
      };

      // 设置粉蓝主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.pinkBlueColors.pinkLight, // 浅粉色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });

      // 设置粉蓝主题TabBar
      wx.setTabBarStyle({
        backgroundColor: this.data.pinkBlueColors.pinkLight,
        borderStyle: 'black',
        color: this.data.pinkBlueColors.blueDark,
        selectedColor: this.data.pinkBlueColors.pinkDark
      });
    } else if (themeName === 'blackWhite') {
      // 设置黑白主题样式
      pageStyle = {
        backgroundColor: this.data.blackWhiteColors.offWhite,
        backgroundImage: 'none',
        titleColor: this.data.blackWhiteColors.black,
        cellBackgroundColor: this.data.blackWhiteColors.white,
        footerColor: this.data.blackWhiteColors.darkGray,
        decorationColors: [
          this.data.blackWhiteColors.darkGray,
          this.data.blackWhiteColors.mediumGray,
          this.data.blackWhiteColors.lightGray
        ]
      };

      // 设置黑白主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.blackWhiteColors.white, // 白色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });

      // 设置黑白主题TabBar
      wx.setTabBarStyle({
        backgroundColor: this.data.blackWhiteColors.white,
        borderStyle: 'black',
        color: this.data.blackWhiteColors.mediumGray,
        selectedColor: this.data.blackWhiteColors.black
      });
    }

    // 更新页面样式
    this.setData({
      pageStyle: pageStyle
    });
  },

  // 获取用户限制信息
  getUserLimitsInfo: function(useCacheIfAvailable = true) {
    console.log('获取用户限制信息, 使用缓存优先策略:', useCacheIfAvailable);

    // 检查是否已有请求在进行中，避免重复请求
    if (this.data.isLimitRequestInProgress) {
      console.log('已有限制信息请求在进行中，跳过重复请求');
      return;
    }

    // 更新上次请求时间
    this.setData({ lastLimitRequestTime: Date.now() });

    // 获取缓存配置
    const cacheKey = this.data.limitCacheConfig.cacheKey;
    const expirationKey = this.data.limitCacheConfig.expirationKey;
    const cacheDuration = this.data.limitCacheConfig.cacheDuration;

    // 如果可以使用缓存，且缓存存在并未过期，则使用缓存
    if (useCacheIfAvailable) {
      const cachedData = wx.getStorageSync(cacheKey);
      const cacheExpiration = wx.getStorageSync(expirationKey);
      const now = Date.now();

      if (cachedData && cacheExpiration && now < cacheExpiration) {
        console.log('使用缓存的限制信息');
        this.setData({
          userLimits: cachedData,
          limitLoaded: true
        });
        return;
      }
    }

    // 缓存不可用或已过期，从服务器获取
    console.log('从服务器获取限制信息');

    // 设置请求锁，防止重复请求
    this.setData({ isLimitRequestInProgress: true });

    // 获取应用实例
    const app = getApp();

    // 获取用户openid
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });

        // 尝试使用已有的缓存数据作为后备
        const cachedData = wx.getStorageSync(cacheKey);
        if (cachedData) {
          console.log('获取openid失败，使用缓存数据');
          this.setData({
            userLimits: cachedData,
            limitLoaded: true
          });
        }

        // 解锁请求
        this.setData({ isLimitRequestInProgress: false });
        return;
      }

      // 使用limitManager获取用户限制信息
      limitManager.getUserLimits(openid)
        .then(limits => {
          console.log('从服务器获取用户限制信息成功:', limits);

          // 更新UI
          this.setData({
            userLimits: limits,
            limitLoaded: true
          });

          // 更新缓存
          wx.setStorageSync(cacheKey, limits);
          // 设置缓存过期时间
          const expiration = Date.now() + cacheDuration;
          wx.setStorageSync(expirationKey, expiration);

          console.log('用户限制信息已缓存，有效期至:', new Date(expiration).toLocaleString());

          // 设置UID展示
          if (!this.data.uidDisplay) {
            this.setUidDisplay(openid);
          }
        })
        .catch(err => {
          console.error('获取用户限制信息失败:', err);
          wx.showToast({
            title: '获取限制信息失败',
            icon: 'none'
          });

          // 尝试使用过期的缓存作为后备
          const cachedData = wx.getStorageSync(cacheKey);
          if (cachedData) {
            console.log('获取失败，使用缓存数据');
            this.setData({
              userLimits: cachedData,
              limitLoaded: true
            });
          }
        })
        .finally(() => {
          // 无论成功或失败，请求完成后解锁
          console.log('限制信息请求完成，解除请求锁');
          this.setData({ isLimitRequestInProgress: false });
        });
    });
  },

  // 复制用户的openid到剪贴板
  copyUserOpenid: function() {
    if (!this.data.fullOpenid) {
      wx.showToast({
        title: '用户ID不可用',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.setClipboardData({
      data: this.data.fullOpenid,
      success: function() {
        wx.showToast({
          title: '用户ID已复制',
          icon: 'success',
          duration: 2000
        });
      }
    });
  },

  // 检查用户是否为VIP会员
  checkIsVipMember: function() {
    return new Promise((resolve, reject) => {
      const db = wx.cloud.database();

      // 获取应用实例
      const app = getApp();

      // 获取用户的OpenID
      app.getUserOpenId((err, openid) => {
        if (err) {
          console.error('获取用户openid失败:', err);
          resolve(false);
          return;
        }

        db.collection('users')
          .where({
            _openid: openid
          })
          .get()
          .then(res => {
            if (res.data && res.data.length > 0) {
              const userInfo = res.data[0];

              // 检查会员类型和过期时间
              const isVip = userInfo.memberType === 'VIP' &&
                          userInfo.memberExpireDate &&
                          new Date(userInfo.memberExpireDate) > new Date();

              resolve(isVip);
            } else {
              resolve(false);
            }
          })
          .catch(err => {
            console.error('获取用户会员信息失败:', err);
            resolve(false); // 出错时默认为非VIP
          });
      });
    });
  },

  // 复制小红书账号到剪贴板
  copyXiaohongshuAccount: function() {
    // 检查当前用户是否为管理员

    if (this.data.fullOpenid === this.data.adminOpenid) {
      // 如果是管理员，跳转到管理页面
      console.log('准备跳转到管理页面');
      wx.navigateTo({
        url: '/packageSettings/pages/admin/admin',
        success: () => {
          console.log('跳转到管理页面成功');
        },
        fail: (err) => {
          console.error('跳转到管理页面失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 无论是否为VIP用户，都显示VIP购买弹窗
      console.log('点击联系方式，显示VIP购买弹窗');
      this.showVipPurchase();
    }
  },

  // 获取存储箱衣物数量
  getStorageBoxCount: function() {
    console.log('获取存储箱衣物数量');

    // 获取应用实例
    const app = getApp();

    // 定义缓存键
    const CLOTHES_CACHE_KEY = 'user_clothes_cache';

    // 获取用户的OpenID
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        return;
      }

      // 尝试从本地缓存获取衣物数据
      const cacheKey = `${CLOTHES_CACHE_KEY}_${openid}`;
      const cachedData = wx.getStorageSync(cacheKey);

      // 检查缓存是否存在且有效
      if (cachedData && cachedData.clothes && cachedData.clothes.length > 0) {
        // 计算隐藏衣物数量
        const hiddenClothes = cachedData.clothes.filter(item => item.hiden === true);
        const hiddenCount = hiddenClothes.length;

        console.log(`存储箱中有 ${hiddenCount} 件衣物`);

        // 更新UI
        this.setData({
          storageCount: hiddenCount
        });
      } else {
        // 本地缓存不存在或无效，从云函数获取
        console.log('本地缓存不存在，从云端获取数据以计算存储箱数量');

        // 这里可以选择不立即获取，等用户点击存储箱时再获取
        // 或者调用云函数获取，代码类似于viewStorageBox方法

        // 临时显示未知数量
        this.setData({
          storageCount: undefined
        });
      }
    });
  },

  // 查看存储箱（显示被隐藏的衣物）
  viewStorageBox: function() {
    console.log('查看存储箱（隐藏衣物）');

    // 首先检查用户是否为VIP会员
    this.checkIsVipMember().then(isVip => {
      if (!isVip) {
        // 非VIP会员，显示提示
        wx.showModal({
          title: 'VIP专属功能',
          content: '换季箱是VIP会员专属功能，开通VIP会员即可使用此功能。',
          confirmText: '了解详情',
          cancelText: '取消',
          confirmColor: '#D4AF37',
          success: (res) => {
            if (res.confirm) {
              // 显示会员权益弹窗
              this.showMemberBenefits();
            }
          }
        });
        return;
      }

      // VIP会员，允许查看换季箱
      // 获取应用实例
      const app = getApp();

      // 定义缓存键（使用固定常量）
      const CLOTHES_CACHE_KEY = 'user_clothes_cache';

      // 获取用户的OpenID
      app.getUserOpenId((err, openid) => {
        if (err) {
          console.error('获取用户openid失败:', err);
          wx.hideLoading();
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
          return;
        }

      // 尝试从本地缓存获取衣物数据
      const cacheKey = `${CLOTHES_CACHE_KEY}_${openid}`;
      const cachedData = wx.getStorageSync(cacheKey);

      // 检查缓存是否存在且有效
      if (cachedData && cachedData.clothes && cachedData.clothes.length > 0) {
        console.log('从本地缓存加载衣物数据:', cachedData.clothes.length, '件');

        // 仅过滤出hiden为true的衣物
        const hiddenClothes = cachedData.clothes.filter(item => item.hiden === true);
        console.log(`共找到 ${hiddenClothes.length} 件隐藏衣物（从缓存）`);

        // 更新存储箱数量
        this.setData({
          storageCount: hiddenClothes.length
        });

        this.processStorageBoxData(hiddenClothes, openid);
        return;
      }

      // 本地缓存不存在或无效，从云函数获取
      console.log('本地缓存不存在或为空，从云端获取数据');

      // 调用云函数获取所有衣物
      wx.cloud.callFunction({
        name: 'getAllClothes',
        data: {
          userOpenId: openid
        }
      })
      .then(res => {
        wx.hideLoading();

        // 处理返回的数据
        const result = res.result || {};

        if (!result.success) {
          console.error('获取衣物列表失败:', result.error || '未知错误');
          wx.showToast({
            title: '获取衣物失败',
            icon: 'none'
          });
          return;
        }

        const allClothes = result.data || [];

        // 更新本地缓存
        try {
          const cacheData = {
            clothes: allClothes,
            timestamp: new Date().getTime()
          };

          // 更新缓存
          wx.setStorageSync(cacheKey, cacheData);
          console.log('本地衣物缓存已更新:', allClothes.length, '件');
        } catch (err) {
          console.error('更新本地缓存失败:', err);
        }

        // 仅过滤出hiden为true的衣物
        const hiddenClothes = allClothes.filter(item => item.hiden === true);
        console.log(`共找到 ${hiddenClothes.length} 件隐藏衣物（从云端）`);

        // 更新存储箱数量
        this.setData({
          storageCount: hiddenClothes.length
        });

        this.processStorageBoxData(hiddenClothes, openid);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取衣物列表失败:', err);
        wx.showToast({
          title: '获取衣物失败',
          icon: 'none',
          duration: 2000
        });
      });
    });
  })},

  // 处理存储箱数据并导航
  processStorageBoxData: function(hiddenClothes, openid) {
    wx.hideLoading();

    // 如果没有隐藏衣物，提示用户
    if (hiddenClothes.length === 0) {
      wx.showToast({
        title: '存储箱中暂无衣物',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 从本地缓存获取图片URL信息
    const URL_CACHE_KEY = 'clothes_image_urls';
    const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};

    // 处理衣物图片URL
    const processedClothes = hiddenClothes.map(item => {
      // 创建衣物副本，避免修改原始数据
      const clothingItem = {...item};

      // 优先检查processedImageFileID
      let fileID = clothingItem.processedImageFileID;
      if (fileID && cachedURLInfo[fileID]) {
        console.log(`使用抠图缓存图片 ${fileID} 用于衣物 ${clothingItem._id}`);
        clothingItem.tempImageUrl = `${wx.env.USER_DATA_PATH}/${cachedURLInfo[fileID].localPath}`;
        return clothingItem;
      }

      // 其次检查imageFileID
      fileID = clothingItem.imageFileID;
      if (fileID && cachedURLInfo[fileID]) {
        console.log(`使用原始缓存图片 ${fileID} 用于衣物 ${clothingItem._id}`);
        clothingItem.tempImageUrl = `${wx.env.USER_DATA_PATH}/${cachedURLInfo[fileID].localPath}`;
        return clothingItem;
      }

      // 如果都没有找到缓存图片，返回原始数据
      return clothingItem;
    });

    // 获取应用实例
    const app = getApp();

    // 将处理后的衣物数据保存到全局数据
    app.globalData.tempClothes = processedClothes;
    app.globalData.tempCategory = '存储箱';

    // 打开新页面展示存储箱衣物
    wx.navigateTo({
      url: '../wardrobe/category/category?category=' + encodeURIComponent('存储箱'),
      success: function() {
        console.log('成功跳转到存储箱页面');
      },
      fail: function(error) {
        console.error('跳转到存储箱页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 清除限制缓存
  clearLimitsCache: function() {
    const cacheKey = this.data.limitCacheConfig.cacheKey;
    const expirationKey = this.data.limitCacheConfig.expirationKey;

    try {
      wx.removeStorageSync(cacheKey);
      wx.removeStorageSync(expirationKey);
      console.log('用户限制信息缓存已清除');

      // 显示提示
      wx.showToast({
        title: '缓存已清除',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      console.error('清除限制信息缓存失败:', error);

      wx.showToast({
        title: '缓存清除失败',
        icon: 'none',
        duration: 1500
      });
    }
  },
  // 添加页面分享功能
  onShareAppMessage: function() {
    console.log('开始生成分享信息...');

    // 直接生成分享ID（同步方式）
    let shareId = '';
    try {
      // 获取当前用户openid
      const openid = this.data.userOpenId || wx.getStorageSync('userOpenId');
      if (!openid) {
        console.warn('无法获取用户openid，将使用空分享ID');
      } else {
        // 使用时间戳+随机数+openid后8位生成唯一ID
        const timestamp = new Date().getTime();
        const random = Math.floor(Math.random() * 10000);
        const openidSuffix = openid.substring(openid.length - 8);
        shareId = `${timestamp}_${random}_${openidSuffix}`;

        console.log('生成本地分享ID:', shareId);

        // 存储到本地，供后续使用
        this.setData({
          lastGeneratedShareId: shareId
        });

        // 异步将此ID同步到云数据库
        wx.cloud.callFunction({
          name: 'handleShareReward',
          data: {
            action: 'createShareRecord',
            localShareId: shareId
          }
        })
        .then(res => {
          console.log('分享ID已同步到云数据库:', res.result);
          taskHelper.updateTaskStatus(taskHelper.TASK_IDS.VIEW_RECOMMENDATION, true);
        })
        .catch(err => {
          console.error('分享ID同步失败:', err);
        });
      }
    } catch (error) {
      console.error('生成分享ID出错:', error);
    }

    // 生成分享路径
    const sharePath = '/page/wardrobe/closet/closet' + (shareId ? ('?shareId=' + shareId) : '');
    console.log('生成分享路径:', sharePath);

    // 返回分享信息
    return {
      title: '我的智能衣柜 - 轻松管理所有衣物',
      path: sharePath,
      imageUrl: '/image/share.png', // 使用现有的分享图标
      success: function(res) {
        console.log('分享成功:', res);
      },
      fail: function(err) {
        console.error('分享失败:', err);
      }
    };
  },

  // 检查并同步消息通知
  checkAndSyncMessages: function() {
    const now = Date.now();
    const lastSyncTimeKey = this.data.messageCacheConfig.lastSyncTimeKey;
    const lastSyncTime = wx.getStorageSync(lastSyncTimeKey) || 0;
    const syncInterval = this.data.messageCacheConfig.cacheDuration;

    // 判断是否需要同步消息（上次同步时间已超过缓存有效期）
    if (now - lastSyncTime >= syncInterval) {
      console.log('消息缓存已过期，需要同步消息通知');
      this.syncMessages();
    } else {
      console.log('消息缓存未过期，使用本地消息数据');
      // 从本地加载消息数据
      this.loadMessagesFromCache();
    }
  },

  // 同步消息通知数据
  syncMessages: function() {
    // 检查是否已有同步请求在进行中
    if (this.data.isMessageSyncInProgress) {
      console.log('已有消息同步请求在进行中，跳过');
      return;
    }

    // 设置同步锁，防止重复请求
    this.setData({ isMessageSyncInProgress: true });

    console.log('开始同步消息通知数据');

    // 调用云函数获取消息列表
    wx.cloud.callFunction({
      name: 'getMessages',
      data: {
        // 如果需要获取特定类型的消息，可以在这里添加参数
      }
    })
    .then(res => {
      const result = res.result || {};

      if (!result.success) {
        console.error('获取消息列表失败:', result.error || '未知错误');
        throw new Error(result.error || '获取消息列表失败');
      }

      // 获取消息列表
      const messageList = result.data || [];
      console.log('从云端获取到消息列表:', messageList.length, '条');

      // 将消息与本地已读状态合并
      this.mergeMessagesWithReadStatus(messageList);

      // 缓存消息数据
      this.cacheMessages(messageList);

      // 记录同步时间
      wx.setStorageSync(this.data.messageCacheConfig.lastSyncTimeKey, Date.now());
    })
    .catch(err => {
      console.error('同步消息失败:', err);

      // 尝试加载本地缓存的消息作为后备
      this.loadMessagesFromCache();

      // 可以选择是否显示错误提示
      // wx.showToast({
      //  title: '同步消息失败，使用缓存数据',
      //  icon: 'none'
      // });
    })
    .finally(() => {
      // 解除同步锁
      this.setData({ isMessageSyncInProgress: false });
    });
  },

  // 将消息与本地已读状态合并
  mergeMessagesWithReadStatus: function(messageList) {
    // 获取本地已读状态缓存
    const readStatusKey = 'messageReadStatus';
    const readStatus = wx.getStorageSync(readStatusKey) || {};

    // 将消息与已读状态合并
    const mergedMessages = messageList.map(msg => {
      return {
        ...msg,
        read: readStatus[msg._id] === true // 如果本地标记为已读，则设置为已读
      };
    });

    // 计算未读消息数量
    const unreadCount = mergedMessages.filter(msg => !msg.read).length;

    // 更新页面数据
    this.setData({
      messages: mergedMessages,
      unreadCount: unreadCount
    });
  },

  // 缓存消息数据
  cacheMessages: function(messageList) {
    const cacheKey = this.data.messageCacheConfig.cacheKey;
    const expirationKey = this.data.messageCacheConfig.expirationKey;
    const cacheDuration = this.data.messageCacheConfig.cacheDuration;

    try {
      // 缓存消息列表
      wx.setStorageSync(cacheKey, messageList);

      // 设置缓存过期时间
      const expiration = Date.now() + cacheDuration;
      wx.setStorageSync(expirationKey, expiration);

      console.log('消息列表已缓存，有效期至:', new Date(expiration).toLocaleString());
    } catch (err) {
      console.error('缓存消息列表失败:', err);
    }
  },

  // 从缓存加载消息
  loadMessagesFromCache: function() {
    const cacheKey = this.data.messageCacheConfig.cacheKey;
    const expirationKey = this.data.messageCacheConfig.expirationKey;
    const now = Date.now();

    try {
      // 获取缓存数据
      const cachedMessages = wx.getStorageSync(cacheKey);
      const cacheExpiration = wx.getStorageSync(expirationKey);

      // 检查缓存是否存在且未过期
      if (cachedMessages && cacheExpiration && now < cacheExpiration) {
        console.log('使用缓存的消息列表，有效期至:', new Date(cacheExpiration).toLocaleString());

        // 将消息与本地已读状态合并
        this.mergeMessagesWithReadStatus(cachedMessages);
      } else {
        console.log('消息缓存不存在或已过期，将重新同步');
        // 强制同步消息
        this.syncMessages();
      }
    } catch (err) {
      console.error('加载缓存消息失败:', err);
      // 出错时尝试重新同步
      this.syncMessages();
    }
  },

  // 查看消息
  viewMessages: function() {
    // 标记所有未读消息为已读
    this.markAllMessagesAsRead();

    // 导航到消息列表页面
    wx.navigateTo({
      url: 'messages/messages',
      success: () => {
        console.log('成功跳转到消息列表页面');
      },
      fail: (err) => {
        console.error('跳转到消息列表页面失败:', err);

        // 如果页面不存在，则显示消息弹窗
        this.showMessagesDialog();
      }
    });
  },

  // 显示消息弹窗
  showMessagesDialog: function() {
    if (this.data.messages.length === 0) {
      wx.showToast({
        title: '暂无消息通知',
        icon: 'none'
      });
      return;
    }

    // 构建消息内容
    let content = this.data.messages.map((msg, index) => {
      const date = new Date(msg.createTime);
      const dateStr = `${date.getMonth() + 1}月${date.getDate()}日`;
      return `${index + 1}. [${dateStr}] ${msg.title}`;
    }).join('\n\n');

    // 显示消息弹窗
    wx.showModal({
      title: '消息通知',
      content: content,
      showCancel: false,
      confirmText: '我知道了'
    });

    // 标记所有消息为已读
    this.markAllMessagesAsRead();
  },

  // 标记所有消息为已读
  markAllMessagesAsRead: function() {
    // 如果没有未读消息，则不需要操作
    if (this.data.unreadCount === 0) {
      return;
    }

    // 获取本地已读状态缓存
    const readStatusKey = 'messageReadStatus';
    const readStatus = wx.getStorageSync(readStatusKey) || {};

    // 标记所有消息为已读
    const messages = this.data.messages.map(msg => {
      readStatus[msg._id] = true;
      return {
        ...msg,
        read: true
      };
    });

    // 更新本地已读状态缓存
    wx.setStorageSync(readStatusKey, readStatus);

    // 更新页面数据
    this.setData({
      messages: messages,
      unreadCount: 0
    });
  },

  // 获取用户信息
  getUserInfo: function() {
    console.log('开始获取用户信息');
    // 先尝试从缓存获取用户信息
    const cacheKey = this.data.userInfoCacheConfig.cacheKey;
    const expirationKey = this.data.userInfoCacheConfig.expirationKey;

    const cachedData = wx.getStorageSync(cacheKey);
    const cacheExpiration = wx.getStorageSync(expirationKey);
    const now = Date.now();

    if (cachedData && cacheExpiration && now < cacheExpiration) {
      console.log('使用缓存的用户信息，有效期至:', new Date(cacheExpiration).toLocaleString());
      console.log('缓存中的用户信息:', JSON.stringify(cachedData));

      // 提取称号数组
      let availableTitles = this.data.defaultTitles; // 默认使用默认称号数组

      if (cachedData.availableTitles && Array.isArray(cachedData.availableTitles) && cachedData.availableTitles.length > 0) {
        availableTitles = cachedData.availableTitles;
        console.log('从缓存中读取称号数组:', JSON.stringify(availableTitles));
      }

      // 确保当前称号在称号数组中
      if (cachedData.title && !availableTitles.includes(cachedData.title)) {
        availableTitles.push(cachedData.title);
      }

      // 处理体力值，确保它在有效范围内
      let catEnergy = 50; // 默认值
      if (cachedData.catEnergy !== undefined) {
        catEnergy = Math.min(Math.max(0, cachedData.catEnergy), 500); // 确保在 0-500 范围内
      } else {
        // 如果缓存中没有体力值，则更新数据库
        this.updateUserCatEnergy(50);
      }

      // 处理会员信息
      let memberType = 'normal';
      let memberExpireDate = null;
      let memberDaysLeft = 0;

      if (cachedData.memberType && cachedData.memberExpireDate) {
        memberType = cachedData.memberType;
        memberExpireDate = cachedData.memberExpireDate;
        memberDaysLeft = this.calculateMemberDaysLeft(memberExpireDate);
      } else {
        // 如果缓存中没有会员信息，则设置为VIP会员并更新数据库
        console.log('缓存中没有会员信息，设置为VIP会员并更新数据库');
        memberType = 'VIP';
        memberDaysLeft = 15;
        // 设置新用户赠送会员提示标志
        this.setData({
          showNewUserVipTip: true
        });
        // 更新会员信息，赠送15天VIP会员
        this.updateUserMembership('VIP', 15);
      }

      this.setData({
        userInfo: {
          nickName: cachedData.nickName || '微信用户',
          avatarUrl: cachedData.avatarUrl || '/image/default-avatar.png',
          title: cachedData.title || '初级养猫人',
          catEnergy: catEnergy, // 猫咪体力值，最大值为500
          memberType: memberType,
          memberExpireDate: memberExpireDate,
          memberDaysLeft: memberDaysLeft
        },
        availableTitles: availableTitles,
        userInfoLoaded: true
      }, () => {
        console.log('用户信息已从缓存加载:', JSON.stringify(this.data.userInfo));
        console.log('称号数组已从缓存加载:', JSON.stringify(this.data.availableTitles));
      });
      return;
    }

    console.log('用户信息缓存不存在或已过期，从云函数获取');

    // 显示加载提示
    wx.showLoading({
      title: '获取用户信息...',
      mask: true
    });

    // 缓存不存在或已过期，调用云函数获取
    wx.cloud.callFunction({
      name: 'getUserInfo',
      data: {},
      success: res => {
        wx.hideLoading();
        console.log('获取用户信息成功, 原始返回:', JSON.stringify(res.result));

        // 根据云函数getUserInfo的返回结构调整处理逻辑
        if (res.result && res.result.success) {
          const userData = res.result.data;

          // 创建前端需要的用户信息对象
          const userInfo = {
            nickName: userData.nickName || '微信用户',
            avatarUrl: userData.avatarUrl || '/image/default-avatar.png',
            title: userData.title || '初级养猫人',
            catEnergy: userData.catEnergy !== undefined ? userData.catEnergy : 50, // 猫咪体力值，默认值为50，最大值为500
            memberType: userData.memberType || 'normal', // 会员类型
            memberExpireDate: userData.memberExpireDate || null // 会员过期时间
          };

          // 计算会员剩余天数
          const memberDaysLeft = this.calculateMemberDaysLeft(userInfo.memberExpireDate);
          userInfo.memberDaysLeft = memberDaysLeft;

          // 如果从云端获取的体力值为空，则设置默认值并更新数据库
          if (userData.catEnergy === undefined) {
            console.log('用户体力值为空，设置默认值并更新数据库');
            this.updateUserCatEnergy(50);
          }

          // 如果从云端获取的会员信息为空，则设置为VIP会员并更新数据库
          if (!userData.memberType || !userData.memberExpireDate) {
            console.log('用户会员信息为空，设置为VIP会员并更新数据库');
            // 设置新用户赠送会员提示标志
            this.setData({
              showNewUserVipTip: true
            });
            // 更新会员信息，赠送15天VIP会员
            this.updateUserMembership('VIP', 15);
            // 更新本地显示
            userInfo.memberType = 'VIP';
            userInfo.memberDaysLeft = 15;

            // 在设置用户信息后显示美观的确认对话框
            setTimeout(() => {
              wx.showModal({
                title: '新用户福利',
                content: '恭喜您获得 15 天VIP会员！您可以享受批量上传、每月赠送体力值、无限搭配等特权功能。',
                confirmText: '查看权益',
                cancelText: '我知道了',
                confirmColor: '#D4AF37',
                success: (res) => {
                  if (res.confirm) {
                    // 用户点击了查看权益，先关闭当前对话框，再显示会员权益
                    setTimeout(() => {
                      this.showMemberBenefits();
                    }, 100); // 等待对话框关闭后再显示会员权益
                  }
                }
              });
            }, 1000); // 等待1秒再显示提示框
          }

          // 处理称号数组
          let availableTitles = [];

          // 如果从云端获取到称号数组，使用云端数据
          if (userData.titles && Array.isArray(userData.titles) && userData.titles.length > 0) {
            availableTitles = userData.titles;
            console.log('从云端获取到称号数组:', JSON.stringify(availableTitles));
          } else {
            // 如果云端没有称号数组，使用默认称号数组
            availableTitles = this.data.defaultTitles;
            console.log('使用默认称号数组:', JSON.stringify(availableTitles));

            // 将默认称号数组保存到云端
            this.saveUserTitles(availableTitles);
          }

          // 确保当前称号在可选称号列表中
          if (userInfo.title && !availableTitles.includes(userInfo.title)) {
            availableTitles.push(userInfo.title);
          }

          console.log('解析后的用户信息:', JSON.stringify(userInfo));

          // 更新数据
          this.setData({
            userInfo: userInfo,
            availableTitles: availableTitles,
            userInfoLoaded: true
          }, () => {
            console.log('用户信息已更新到页面:', JSON.stringify(this.data.userInfo));
            console.log('可选称号已更新:', JSON.stringify(this.data.availableTitles));
          });

          // 将用户信息存入缓存
          const cacheDuration = this.data.userInfoCacheConfig.cacheDuration;
          const expirationTime = now + cacheDuration;

          // 创建包含称号数组的缓存对象
          const cacheData = {
            ...userInfo,
            availableTitles: availableTitles // 将称号数组也缓存到本地
          };

          wx.setStorageSync(cacheKey, cacheData);
          wx.setStorageSync(expirationKey, expirationTime);

          console.log('用户信息已缓存，有效期至:', new Date(expirationTime).toLocaleString());
        } else {
          console.warn('云函数返回失败或数据结构不符合预期:', res.result);
          this.setDefaultUserInfo();
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('获取用户信息失败:', err);
        this.setDefaultUserInfo();
      }
    });
  },

  // 获取用户主题任务进度
  getUserTaskProgress: function(forceRefresh = false) {
    console.log('获取用户主题任务进度', forceRefresh ? '(强制刷新)' : '');

    // 设置加载状态
    this.setData({
      'themeTasks.isLoading': true
    });

    // 先尝试从缓存获取任务进度，除非强制刷新
    const cacheKey = 'userTasksCache'
    const expirationKey = 'userTasksCacheExpiration'

    const cachedData = wx.getStorageSync(cacheKey);
    const cacheExpiration = wx.getStorageSync(expirationKey);
    const now = Date.now();

    if (!forceRefresh && cachedData && cacheExpiration && now < cacheExpiration) {
      console.log('使用缓存的任务进度信息，有效期至:', new Date(cacheExpiration).toLocaleString());

      // 从缓存中获取hasDone数组
      let hasDone = cachedData.hasDone || [];
      // 确保hasDone是数组类型
      if (typeof hasDone === 'string') {
        try {
          hasDone = JSON.parse(hasDone);
        } catch (err) {
          console.error('解析hasDone字符串失败:', err);
          hasDone = [];
        }
      }

      // 确保数组元素是数字类型
      hasDone = hasDone.map(step => Number(step));
      console.log('从缓存获取的hasDone数组(处理后):', JSON.stringify(hasDone));

      // 计算当前完成的步骤数量
      const currentProgress = hasDone.length;

      this.setData({
        'themeTasks.currentTask': cachedData.totalProgress || 1,
        'themeTasks.currentStep': currentProgress,
        'themeTasks.hasDone': hasDone, // 保存hasDone数组到data中
        'themeTasks.taskId': cachedData.taskId || `task${cachedData.totalProgress || 1}`,
        'themeTasks.isLoading': false
      });

      // 获取任务对应的图片，传递forceRefresh参数
      this.getTaskImages(cachedData.taskId || `task${cachedData.totalProgress || 1}`, forceRefresh);
      return;
    }

    // 获取应用实例
    const app = getApp();

    // 获取用户openid
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        this.setDefaultTaskProgress(forceRefresh);
        return;
      }

      // 调用云函数获取用户任务进度
      wx.cloud.callFunction({
        name: 'getUserTasks',
        data: {
          userOpenId: openid
        }
      })
      .then(res => {
        const result = res.result || {};

        if (!result.success) {
          console.error('获取任务进度失败:', result.error || '未知错误');
          this.setDefaultTaskProgress(forceRefresh);
          return;
        }

        const taskData = result.data || {};
        console.log('获取任务进度成功:', taskData);

        // 从返回数据中获取hasDone数组
        let hasDone = taskData.hasDone || [];
        // 确保hasDone是数组类型
        if (typeof hasDone === 'string') {
          try {
            hasDone = JSON.parse(hasDone);
          } catch (err) {
            console.error('解析hasDone字符串失败:', err);
            hasDone = [];
          }
        }

        // 确保数组元素是数字类型
        hasDone = hasDone.map(step => Number(step));
        console.log('从服务器获取的hasDone数组(处理后):', JSON.stringify(hasDone));

        // 计算当前完成的步骤数量
        const currentProgress = hasDone.length;

        // 确保有taskId
        const taskId = taskData.taskId || `task${taskData.totalProgress || 1}`;

        // 更新数据
        this.setData({
          'themeTasks.currentTask': taskData.totalProgress || 1,
          'themeTasks.currentStep': currentProgress,
          'themeTasks.hasDone': hasDone, // 保存hasDone数组到data中
          'themeTasks.taskId': taskId,
          'themeTasks.isLoading': false
        });

        // 缓存任务进度
        this.cacheTaskProgress({
          ...taskData,
          hasDone: hasDone
        });

        // 获取任务对应的图片，传递forceRefresh参数
        this.getTaskImages(taskId, forceRefresh);
      })
      .catch(err => {
        console.error('获取任务进度失败:', err);
        this.setDefaultTaskProgress(forceRefresh);
      });
    });
  },

  updateCurrentTaskProgress: function(hasDone) {
    console.log('更新任务进度，已完成步骤(原始):', JSON.stringify(hasDone));

    // 获取当前任务ID
    const taskId = this.data.themeTasks.taskId || 'task1';

    // 获取缓存键
    const cacheKey = `${this.data.tasksCacheConfig.cacheKey}`

    // 确保hasDone是数组类型
    if (typeof hasDone === 'string') {
      try {
        hasDone = JSON.parse(hasDone);
      } catch (err) {
        console.error('解析hasDone字符串失败:', err);
        hasDone = [];
      }
    }

    // 确保hasDone是数组
    if (!Array.isArray(hasDone)) {
      hasDone = [];
    }

    // 确保数组元素是数字类型
    hasDone = hasDone.map(step => Number(step));
    console.log('更新任务进度，已完成步骤(处理后):', JSON.stringify(hasDone));

    try {
      // 获取缓存数据
      const cacheData = wx.getStorageSync(cacheKey);
      if (cacheData) {
        // 更新hasDone和currentProgress
        cacheData.hasDone = hasDone;
        cacheData.currentProgress = hasDone.length;

        // 保存更新后的缓存
        wx.setStorageSync(cacheKey, cacheData);

        // 更新UI显示
        this.setData({
          'themeTasks.hasDone': hasDone,
          'themeTasks.currentStep': hasDone.length
        });

        console.log('任务进度已更新，hasDone:', JSON.stringify(hasDone), '当前步骤:', hasDone.length);
      }
    } catch (err) {
      console.error('更新任务进度缓存失败:', err);
    }
  },

  // 设置默认任务进度
  setDefaultTaskProgress: function(forceRefresh = false) {
    const defaultTaskId = 'task1';
    const defaultHasDone = []; // 默认没有完成任何步骤

    this.setData({
      'themeTasks.currentTask': 1,
      'themeTasks.currentStep': 0,
      'themeTasks.hasDone': defaultHasDone,
      'themeTasks.taskId': defaultTaskId,
      'themeTasks.isLoading': false
    });

    // 获取默认任务的图片，传递forceRefresh参数
    this.getTaskImages(defaultTaskId, forceRefresh);
  },

  // 缓存任务进度
  cacheTaskProgress: function(taskData) {
    const cacheKey = this.data.tasksCacheConfig.cacheKey;
    const expirationKey = this.data.tasksCacheConfig.expirationKey;
    const cacheDuration = this.data.tasksCacheConfig.cacheDuration;

    // 确保taskData包含hasDone数组
    if (!taskData.hasDone) {
      taskData.hasDone = [];
    }

    try {
      // 缓存任务进度
      wx.setStorageSync(cacheKey, taskData);

      // 设置缓存过期时间
      const expiration = Date.now() + cacheDuration;
      wx.setStorageSync(expirationKey, expiration);

      console.log('任务进度已缓存，有效期至:', new Date(expiration).toLocaleString());
    } catch (err) {
      console.error('缓存任务进度失败:', err);
    }
  },

  // 设置默认用户信息
  setDefaultUserInfo: function() {
    console.log('设置默认用户信息');
    // 失败时设置默认值
    this.setData({
      userInfo: {
        nickName: '微信用户',
        avatarUrl: '/image/default-avatar.png',
        title: '初级养猫人',
        catEnergy: 50, // 猫咪体力值，默认值为50，最大值为500
        memberType: 'VIP', // 默认会员类型
        memberDaysLeft: 15 // 默认会员剩余天数
      },
      userInfoLoaded: true,
      showNewUserVipTip: true // 显示新用户赠送会员提示
    }, () => {
      console.log('已设置默认用户信息');
      // 更新数据库中的体力值
      this.updateUserCatEnergy(50);
      // 更新数据库中的会员信息
      this.updateUserMembership('VIP', 15);

      // 显示美观的确认对话框
      setTimeout(() => {
        wx.showModal({
          title: '新用户福利',
          content: '恭喜您获得 15 天VIP会员！您可以享受批量上传、每月赠送体力值、无限搭配等特权功能。',
          confirmText: '查看权益',
          cancelText: '我知道了',
          confirmColor: '#D4AF37',
          success: (res) => {
            if (res.confirm) {
              // 用户点击了查看权益，先关闭当前对话框，再显示会员权益
              setTimeout(() => {
                this.showMemberBenefits();
              }, 100); // 等待对话框关闭后再显示会员权益
            }
          }
        });
      }, 500); // 等待500毫秒再显示提示框
    });
  },

  // 切换称号选择器显示状态
  toggleTitleSelector: function() {
    this.setData({
      showTitleSelector: !this.data.showTitleSelector
    });
  },

  // 选择用户称号
  selectUserTitle: function(e) {
    const selectedTitle = e.currentTarget.dataset.title;

    if (!selectedTitle) return;

    // 更新本地显示
    this.setData({
      'userInfo.title': selectedTitle,
      showTitleSelector: false
    });

    // 保存当前选中称号到云端
    this.saveUserTitle(selectedTitle);

    // 确保称号在称号数组中
    const titles = [...this.data.availableTitles];
    if (!titles.includes(selectedTitle)) {
      titles.push(selectedTitle);
      this.setData({
        availableTitles: titles
      });
      // 更新称号数组到云端
      this.saveUserTitles(titles);
    }
  },

  // 保存用户称号到云端
  saveUserTitle: function(title) {
    if (!title) return;

    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 获取用户openid
    const app = getApp();
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
        return;
      }

      // 调用云数据库更新用户称号
      const db = wx.cloud.database();
      db.collection('users').where({
        _openid: openid
      }).update({
        data: {
          title: title,
          updatedAt: db.serverDate()
        }
      }).then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '称号已更新',
          icon: 'success'
        });

        // 更新缓存
        const cacheKey = this.data.userInfoCacheConfig.cacheKey;
        const cachedData = wx.getStorageSync(cacheKey) || {};
        cachedData.title = title;

        // 确保称号在缓存的称号数组中
        if (!cachedData.availableTitles) {
          cachedData.availableTitles = this.data.availableTitles;
        } else if (!cachedData.availableTitles.includes(title)) {
          cachedData.availableTitles.push(title);
        }

        wx.setStorageSync(cacheKey, cachedData);

      }).catch(err => {
        console.error('更新用户称号失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      });
    });
  },

  // 保存用户称号数组到云端
  saveUserTitles: function(titles) {
    if (!titles || !Array.isArray(titles) || titles.length === 0) return;

    console.log('将称号数组保存到云端:', JSON.stringify(titles));

    // 获取用户openid
    const app = getApp();
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        return;
      }

      // 调用云数据库更新用户称号数组
      const db = wx.cloud.database();
      db.collection('users').where({
        _openid: openid
      }).update({
        data: {
          titles: titles,
          updatedAt: db.serverDate()
        }
      }).then(() => {
        console.log('称号数组已保存到云端');
      }).catch(err => {
        console.error('保存称号数组失败:', err);
      });
    });
  },

  // 更新用户猫咪体力值
  updateUserCatEnergy: function(energy) {
    if (energy === undefined || energy < 0) return;

    // 确保体力值不超过最大值500
    const catEnergy = Math.min(energy, 500);

    console.log('更新用户猫咪体力值:', catEnergy);

    // 获取用户openid
    const app = getApp();
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        return;
      }

      // 调用云数据库更新用户体力值
      const db = wx.cloud.database();
      db.collection('users').where({
        _openid: openid
      }).update({
        data: {
          catEnergy: catEnergy,
          updatedAt: db.serverDate()
        }
      }).then(() => {
        console.log('猫咪体力值已更新到云端:', catEnergy);

        // 更新本地显示
        this.setData({
          'userInfo.catEnergy': catEnergy
        });

        // 检查是否需要切换猫咪图片
        if (this.data.catImages.isLoaded) {
          // 如果体力值跨过50的阈值，则触发视图更新
          if ((this.data.userInfo.catEnergy < 50 && catEnergy >= 50) ||
              (this.data.userInfo.catEnergy >= 50 && catEnergy < 50)) {
            // 强制触发视图更新
            this.setData({
              'catImages.isLoaded': true
            });
          }
        }

        // 更新缓存
        const cacheKey = this.data.userInfoCacheConfig.cacheKey;
        const cachedData = wx.getStorageSync(cacheKey) || {};
        cachedData.catEnergy = catEnergy;
        wx.setStorageSync(cacheKey, cachedData);

      }).catch(err => {
        console.error('更新猫咪体力值失败:', err);
      });
    });
  },

  // 计算会员剩余天数
  calculateMemberDaysLeft: function(expireDate) {
    if (!expireDate) return 0;

    // 将字符串转换为日期对象
    const expireDateTime = new Date(expireDate).getTime();
    const now = Date.now();

    // 如果已过期，返回0
    if (expireDateTime <= now) return 0;

    // 计算剩余天数（向上取整）
    const daysLeft = Math.ceil((expireDateTime - now) / (24 * 60 * 60 * 1000));
    return daysLeft;
  },

  // 更新用户会员信息
  updateUserMembership: function(memberType, expireDays) {
    if (!memberType) return;

    // 计算过期时间
    const now = new Date();
    const expireDate = new Date(now.getTime() + expireDays * 24 * 60 * 60 * 1000);

    console.log('更新用户会员信息:', memberType, '过期天数:', expireDays);

    // 获取用户openid
    const app = getApp();
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        return;
      }

      // 调用云数据库更新用户会员信息
      const db = wx.cloud.database();
      db.collection('users').where({
        _openid: openid
      }).update({
        data: {
          memberType: memberType,
          memberExpireDate: expireDate,
          updatedAt: db.serverDate()
        }
      }).then(() => {
        console.log('会员信息已更新到云端:', memberType, expireDate);

        // 计算剩余天数
        const daysLeft = this.calculateMemberDaysLeft(expireDate);

        // 更新本地显示
        this.setData({
          'userInfo.memberType': memberType,
          'userInfo.memberExpireDate': expireDate,
          'userInfo.memberDaysLeft': daysLeft
        });

        // 更新缓存
        const cacheKey = this.data.userInfoCacheConfig.cacheKey;
        const cachedData = wx.getStorageSync(cacheKey) || {};
        cachedData.memberType = memberType;
        cachedData.memberExpireDate = expireDate;
        cachedData.memberDaysLeft = daysLeft;
        wx.setStorageSync(cacheKey, cachedData);

        // 如果是新用户赠送会员，显示美观的确认对话框
        if (memberType === 'VIP' && daysLeft > 0 && this.data.showNewUserVipTip) {
          // 重置提示标志
          this.setData({
            showNewUserVipTip: false
          });

          // 显示美观的确认对话框
          wx.showModal({
            title: '新用户福利',
            content: '恭喜您获得 ' + daysLeft + ' 天VIP会员！您可以享受批量上传、每月赠送体力值、无限搭配等特权功能。',
            confirmText: '查看权益',
            cancelText: '我知道了',
            confirmColor: '#D4AF37',
            success: (res) => {
              if (res.confirm) {
                // 用户点击了查看权益，先关闭当前对话框，再显示会员权益
                setTimeout(() => {
                  this.showMemberBenefits();
                }, 100); // 等待对话框关闭后再显示会员权益
              }
            }
          });
        }

      }).catch(err => {
        console.error('更新会员信息失败:', err);
      });
    });
  },

  // 获取任务图片
  getTaskImages: function(taskId, forceRefresh = false) {
    console.log('获取任务图片，任务ID:', taskId, forceRefresh ? '(强制刷新)' : '');

    // 如果没有提供taskId，使用当前任务ID或默认值
    if (!taskId) {
      taskId = this.data.themeTasks.taskId || 'task1';
    }

    // 获取缓存键（添加taskId以区分不同任务的缓存）
    const cacheKey = `${this.data.taskImagesCacheConfig.cacheKey}_${taskId}`;
    const expirationKey = `${this.data.taskImagesCacheConfig.expirationKey}_${taskId}`;

    // 设置加载状态
    this.setData({
      'imageLoading.isLoading': true,
      'imageLoading.loadedCount': 0
    });

    // 先尝试从缓存获取图片，除非强制刷新
    const cachedData = wx.getStorageSync(cacheKey);
    const cacheExpiration = wx.getStorageSync(expirationKey);
    const now = Date.now();

    if (!forceRefresh && cachedData && cacheExpiration && now < cacheExpiration) {
      console.log(`使用缓存的任务 ${taskId} 图片，有效期至:`, new Date(cacheExpiration).toLocaleString());

      // 更新本地图片映射
      this.setData({
        'taskImagesCacheConfig.localImagesMap': cachedData.localImagesMap || {},
        'themeTasks.taskImages': cachedData.taskImages || this.data.themeTasks.taskImages,
        'themeTasks.titles': cachedData.titles || [],
        'themeTasks.descriptions': cachedData.descriptions || [],
        'imageLoading.isLoading': false,
        'imageLoading.loadedCount': 6
      });
      return;
    }

    // 缓存不存在、已过期或强制刷新，从数据库获取
    console.log(`${forceRefresh ? '强制刷新，' : '缓存已过期或不存在，'}从云端获取任务 ${taskId} 图片`);

    // 调用云函数获取任务图片信息
    wx.cloud.callFunction({
      name: 'getTaskImages',
      data: {
        taskId: taskId // 传递任务ID
      }
    })
    .then(res => {
      const result = res.result || {};

      if (!result.success) {
        console.error('获取任务图片失败:', result.error || '未知错误');
        this.setData({
          'imageLoading.isLoading': false
        });
        return;
      }

      const imagesData = result.data || [];
      console.log(`获取任务 ${taskId} 图片成功:`, imagesData);

      // 检查是否有足够的图片
      if (imagesData.length < 6) {
        console.warn(`任务 ${taskId} 图片不足6张:`, imagesData.length);
        this.setData({
          'imageLoading.isLoading': false
        });
        return;
      }

      // 排序图片（确保step1-step6的顺序正确）
      imagesData.sort((a, b) => {
        const stepA = parseInt(a.step || '0');
        const stepB = parseInt(b.step || '0');
        return stepA - stepB;
      });

      // 提取fileID、title和description
      const fileIDList = imagesData.map(item => item.fileID);
      const titleList = imagesData.map(item => item.title || '');
      const descriptionList = imagesData.map(item => item.description || '');

      // 保存title和description到themeTasks
      this.setData({
        'themeTasks.titles': titleList,
        'themeTasks.descriptions': descriptionList
      });

      // 下载图片到本地
      this.downloadTaskImages(fileIDList, taskId);
    })
    .catch(err => {
      console.error(`获取任务 ${taskId} 图片失败:`, err);
      this.setData({
        'imageLoading.isLoading': false
      });
    });
  },

  // 下载任务图片到本地
  downloadTaskImages: function(fileIDList, taskId) {
    console.log(`下载任务 ${taskId} 图片到本地:`, fileIDList);

    if (!fileIDList || fileIDList.length === 0) {
      console.warn('文件ID列表为空');
      this.setData({
        'imageLoading.isLoading': false
      });
      return;
    }

    // 获取已缓存的图片信息
    const localImagesMap = this.data.taskImagesCacheConfig.localImagesMap || {};

    // 检查哪些图片需要下载
    const needDownloadFiles = fileIDList.filter(fileID => !localImagesMap[fileID]);

    // 如果所有图片都已缓存，直接使用缓存
    if (needDownloadFiles.length === 0) {
      console.log('所有图片已有本地缓存');

      // 准备更新的图片数组
      const taskImages = fileIDList.map(fileID => {
        return localImagesMap[fileID] ? `${wx.env.USER_DATA_PATH}/${localImagesMap[fileID]}` : '/image/placeholder.png';
      });

      // 更新数据
      this.setData({
        'themeTasks.taskImages': taskImages,
        'imageLoading.isLoading': false,
        'imageLoading.loadedCount': 6
      });

      // 缓存图片信息
      this.cacheTaskImages(fileIDList, localImagesMap, taskImages, taskId);
      return;
    }

    console.log('需要下载的图片数量:', needDownloadFiles.length);

    // 设置加载状态
    this.setData({
      'imageLoading.totalCount': needDownloadFiles.length,
      'imageLoading.loadedCount': 0
    });

    // 使用云函数获取临时文件URL（修改这部分）
    wx.cloud.callFunction({
      name: 'getTempFileURL',
      data: {
        fileList: needDownloadFiles
      }
    })
    .then(res => {
      const result = res.result || {};

      if (!result.success) {
        console.error('调用 getTempFileURL 云函数失败:', result.error || '未知错误');
        this.setData({
          'imageLoading.isLoading': false
        });
        return;
      }

      const fileList = result.fileList || [];
      console.log('获取临时文件URL成功, 获取到', fileList.length, '个文件URL');

      // 下载每个文件
      this.batchDownloadFiles(fileList, fileIDList, localImagesMap, taskId);
    })
    .catch(err => {
      console.error('获取临时文件URL失败:', err);
      this.setData({
        'imageLoading.isLoading': false
      });
    });
  },

  // 批量下载文件
  batchDownloadFiles: function(fileList, allFileIDs, localImagesMap, taskId) {
    if (!fileList || fileList.length === 0) {
      console.warn('临时文件URL列表为空');
      this.setData({
        'imageLoading.isLoading': false
      });
      return;
    }

    // 用于跟踪下载进度
    let downloadedCount = 0;
    let successCount = 0;

    // 新下载的文件ID到本地路径的映射
    const newLocalImagesMap = {...localImagesMap};

    // 创建下载任务
    const downloadTasks = fileList.map((file, index) => {
      return new Promise((resolve, reject) => {
        // 检查文件信息是否完整
        if (!file.fileID || !file.tempFileURL) {
          console.error('文件信息不完整:', file);
          resolve({
            fileID: file.fileID || `unknown_${index}`,
            success: false,
            error: '文件信息不完整'
          });
          return;
        }

        // 生成本地文件名
        const fileID = file.fileID;
        const timestamp = new Date().getTime();
        const randomNum = Math.floor(Math.random() * 1000);
        const fileExtMatch = file.tempFileURL.match(/\.([a-zA-Z0-9]+)(\?|$)/);
        const fileExt = fileExtMatch ? fileExtMatch[1] : 'png';
        const localFileName = `task_image_${index + 1}_${timestamp}_${randomNum}.${fileExt}`;

        // 下载文件
        wx.downloadFile({
          url: file.tempFileURL,
          success: res => {
            if (res.statusCode === 200) {
              // 保存到本地文件系统
              wx.getFileSystemManager().saveFile({
                tempFilePath: res.tempFilePath,
                filePath: `${wx.env.USER_DATA_PATH}/${localFileName}`,
                success: saveRes => {
                  console.log(`文件 ${fileID} 已保存到本地:`, saveRes.savedFilePath);

                  // 更新映射
                  newLocalImagesMap[fileID] = localFileName;
                  successCount++;

                  resolve({
                    fileID: fileID,
                    localPath: localFileName,
                    success: true
                  });
                },
                fail: err => {
                  console.error(`保存文件 ${fileID} 到本地失败:`, err);
                  resolve({
                    fileID: fileID,
                    success: false,
                    error: err
                  });
                }
              });
            } else {
              console.error(`下载文件 ${fileID} 失败，状态码:`, res.statusCode);
              resolve({
                fileID: fileID,
                success: false,
                error: `HTTP状态码 ${res.statusCode}`
              });
            }
          },
          fail: err => {
            console.error(`下载文件 ${fileID} 失败:`, err);
            resolve({
              fileID: fileID,
              success: false,
              error: err
            });
          },
          complete: () => {
            // 更新下载进度
            downloadedCount++;
            this.setData({
              'imageLoading.loadedCount': downloadedCount
            });
          }
        });
      });
    });

    // 等待所有下载任务完成
    Promise.all(downloadTasks)
      .then(results => {
        console.log('所有文件下载完成:', results);
        console.log(`成功下载 ${successCount}/${fileList.length} 个文件`);

        // 准备更新的图片数组
        const taskImages = allFileIDs.map(fileID => {
          return newLocalImagesMap[fileID] ? `${wx.env.USER_DATA_PATH}/${newLocalImagesMap[fileID]}` : '/image/placeholder.png';
        });

        // 更新数据
        this.setData({
          'themeTasks.taskImages': taskImages,
          'taskImagesCacheConfig.localImagesMap': newLocalImagesMap,
          'imageLoading.isLoading': false
        });

        // 缓存图片信息
        this.cacheTaskImages(allFileIDs, newLocalImagesMap, taskImages, taskId);
      })
      .catch(err => {
        console.error('文件下载过程中出错:', err);
        this.setData({
          'imageLoading.isLoading': false
        });
      });
  },

  // 缓存任务图片信息
  cacheTaskImages: function(fileIDList, localImagesMap, taskImages, taskId) {
    // 获取缓存键（添加taskId以区分不同任务的缓存）
    const cacheKey = `${this.data.taskImagesCacheConfig.cacheKey}_${taskId}`;
    const expirationKey = `${this.data.taskImagesCacheConfig.expirationKey}_${taskId}`;
    const cacheDuration = this.data.taskImagesCacheConfig.cacheDuration;

    try {
      // 准备缓存数据
      const cacheData = {
        taskId: taskId,
        fileIDList: fileIDList,
        localImagesMap: localImagesMap,
        taskImages: taskImages,
        titles: this.data.themeTasks.titles || [],
        descriptions: this.data.themeTasks.descriptions || [],
        timestamp: Date.now()
      };

      // 存储到缓存
      wx.setStorageSync(cacheKey, cacheData);

      // 设置缓存过期时间
      const expiration = Date.now() + cacheDuration;
      wx.setStorageSync(expirationKey, expiration);

      console.log(`任务 ${taskId} 图片信息已缓存，有效期至:`, new Date(expiration).toLocaleString());
    } catch (err) {
      console.error(`缓存任务 ${taskId} 图片信息失败:`, err);
    }
  },

  // 添加显示任务步骤描述的方法
  showTaskStepDescription: function(e) {
    // 获取点击的步骤索引
    const index = e.currentTarget.dataset.index;

    // 检查是否有描述信息
    if (!this.data.themeTasks.descriptions || !this.data.themeTasks.descriptions[index]) {
      console.log(`步骤 ${index + 1} 没有描述信息`);
      return;
    }

    // 获取标题和描述
    const title = this.data.themeTasks.titles[index] || `步骤 ${index + 1}`;
    const description = this.data.themeTasks.descriptions[index];
    const imageUrl = this.data.themeTasks.taskImages[index];
    const stepNumber = index + 1;
    const isCompleted = this.data.themeTasks.hasDone && this.data.themeTasks.hasDone.indexOf(stepNumber) !== -1;

    // 更新主题任务卡片数据
    this.setData({
      'themeTaskCard.visible': true,
      'themeTaskCard.imageUrl': imageUrl,
      'themeTaskCard.title': title,
      'themeTaskCard.description': description,
      'themeTaskCard.stepNumber': stepNumber,
      'themeTaskCard.isCompleted': isCompleted
    });
  },

  // 添加隐藏主题任务卡片的方法
  hideThemeTaskCard: function() {
    this.setData({
      'themeTaskCard.visible': false
    });
  },

  // 获取每日任务
  getDailyTasks: function() {
    console.log('获取每日任务数据');

    // 检查是否已有请求在进行中，避免重复请求
    if (this.data.isDailyTaskRequestInProgress) {
      console.log('已有每日任务请求在进行中，跳过重复请求');
      return;
    }

    // 设置请求状态
    this.setData({
      isDailyTaskRequestInProgress: true,
      'dailyTasks.isLoading': true
    });

    // 获取当前日期（YYYY-MM-DD格式）
    const today = new Date();
    const dateString = today.getFullYear() + '-' +
                      String(today.getMonth() + 1).padStart(2, '0') + '-' +
                      String(today.getDate()).padStart(2, '0');

    // 获取日期的 0 点时间戳，用于判断是否过了0点
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime();

    // 检查缓存
    const cacheKey = this.data.dailyTasksCacheConfig.cacheKey;
    const expirationKey = this.data.dailyTasksCacheConfig.expirationKey;
    const resetFlagKey = this.data.dailyTasksCacheConfig.dailyResetFlag;

    const cachedData = wx.getStorageSync(cacheKey);
    const cacheExpiration = wx.getStorageSync(expirationKey);
    const resetFlag = wx.getStorageSync(resetFlagKey);

    const now = Date.now();

    // 检查今日是否已重置任务
    const isResetToday = resetFlag && resetFlag >= todayStart;

    // 如果缓存存在、未过期、日期匹配当天、今日已重置过，则使用缓存数据
    if (cachedData && cacheExpiration && now < cacheExpiration &&
        cachedData.date === dateString && isResetToday) {
      console.log('使用缓存的每日任务数据，日期:', cachedData.date);

      // 更新UI
      this.setData({
        dailyTasks: {
          ...this.data.dailyTasks,
          ...cachedData,
          isLoading: false
        },
        isDailyTaskRequestInProgress: false
      });
      return;
    }

    // 需要从服务器获取或重置每日任务
    // 获取应用实例
    const app = getApp();

    // 获取用户openid
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        // 解锁请求状态
        this.setData({
          isDailyTaskRequestInProgress: false,
          'dailyTasks.isLoading': false
        });
        return;
      }

      // 调用云函数获取每日任务
      wx.cloud.callFunction({
        name: 'getDailyTasks',
        data: {
          userOpenId: openid,
          date: dateString
        }
      })
      .then(res => {
        const result = res.result || {};

        if (!result.success) {
          console.error('获取每日任务失败:', result.error || '未知错误');
          throw new Error(result.error || '获取每日任务失败');
        }

        console.log('获取每日任务成功:', result);

        // 提取任务数据
        const tasksData = result.data || {};

        // 计算已完成任务数量
        const tasks = tasksData.tasks || this.data.dailyTasks.tasks;
        const completedCount = tasks.filter(task => task.completed).length;
        const canDrawPrize = completedCount >= 3 && !(tasksData.rewardClaimed || false); // 所有任务完成且未领取奖励才能抽奖

        // 更新UI
        const dailyTasksData = {
          isLoading: false,
          date: dateString,
          tasks: tasks,
          completedCount: completedCount,
          totalCount: tasks.length,
          canDrawPrize: canDrawPrize,
          rewardClaimed: tasksData.rewardClaimed || false
        };

        this.setData({
          dailyTasks: dailyTasksData,
          isDailyTaskRequestInProgress: false
        });

        // 缓存任务数据
        this.cacheDailyTasks(dailyTasksData);

        // 设置今日已重置标记
        wx.setStorageSync(resetFlagKey, now);
      })
      .catch(err => {
        console.error('获取每日任务失败:', err);

        // 如果服务器获取失败，尝试使用本地默认任务
        this.useFallbackDailyTasks(dateString);

        // 解锁请求状态
        this.setData({
          isDailyTaskRequestInProgress: false,
          'dailyTasks.isLoading': false
        });
      });
    });
  },

  // 当服务器获取失败时，使用本地默认任务
  useFallbackDailyTasks: function(dateString) {
    console.log('使用本地默认每日任务');

    // 默认任务（全部未完成状态）
    const defaultTasks = [
      { id: 'task1', name: '添加一件衣物', completed: false },
      { id: 'task2', name: '使用AI创建一套搭配(今日穿搭-DS穿搭）', completed: false },
      { id: 'task3', name: '分享给一个朋友', completed: false }
    ];

    // 更新UI
    const dailyTasksData = {
      isLoading: false,
      date: dateString,
      tasks: defaultTasks,
      completedCount: 0,
      totalCount: defaultTasks.length,
      canDrawPrize: false
    };

    this.setData({
      dailyTasks: dailyTasksData
    });

    // 缓存任务数据
    this.cacheDailyTasks(dailyTasksData);
  },

  // 获取签到状态
  getCheckInStatus: function(useCache = true) {
    console.log('获取签到状态');

    // 如果已经在请求中，不重复请求
    if (this.data.isCheckInRequestInProgress) {
      console.log('签到状态请求正在进行中，跳过重复请求');
      return;
    }

    // 设置请求锁
    this.setData({
      isCheckInRequestInProgress: true,
      'checkInData.isLoading': true
    });

    // 获取当前日期（格式：YYYY-MM-DD）
    const today = new Date();
    const dateString = today.getFullYear() + '-' +
                     String(today.getMonth() + 1).padStart(2, '0') + '-' +
                     String(today.getDate()).padStart(2, '0');

    // 检查是否可以使用缓存
    if (useCache) {
      // 尝试从本地缓存获取签到数据
      const cacheKey = this.data.checkInCacheConfig.cacheKey;
      const expirationKey = this.data.checkInCacheConfig.expirationKey;
      const dailyResetFlag = this.data.checkInCacheConfig.dailyResetFlag;

      const cachedData = wx.getStorageSync(cacheKey);
      const cacheExpiration = wx.getStorageSync(expirationKey);
      const isResetToday = wx.getStorageSync(dailyResetFlag) === dateString;

      const now = Date.now();

      // 如果缓存存在、未过期、日期匹配当天、今日已重置过，则使用缓存数据
      if (cachedData && cacheExpiration && now < cacheExpiration &&
          isResetToday) {
        console.log('使用缓存的签到数据');

        // 更新UI
        this.setData({
          checkInData: {
            ...this.data.checkInData,
            ...cachedData,
            isLoading: false
          },
          isCheckInRequestInProgress: false
        });
        return;
      }
    }

    // 获取应用实例
    const app = getApp();

    // 获取用户openid
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        // 解锁请求状态
        this.setData({
          isCheckInRequestInProgress: false,
          'checkInData.isLoading': false
        });
        return;
      }

      // 调用云函数获取签到状态
      wx.cloud.callFunction({
        name: 'getCheckInStatus',
        data: {
          userOpenId: openid
        }
      })
      .then(res => {
        const result = res.result || {};

        if (!result.success) {
          console.error('获取签到状态失败:', result.error || '未知错误');
          throw new Error(result.error || '获取签到状态失败');
        }

        console.log('获取签到状态成功:', result);

        // 更新UI
        const checkInData = {
          isLoading: false,
          todayCheckedIn: result.todayCheckedIn || false,
          consecutiveDays: result.consecutiveDays || 0,
          last7Days: result.last7Days || [],
          nextRewards: result.nextRewards || {
            energy: result.isVIP ? 4 : 2,
            storage: 0
          },
          isVIP: result.isVIP || false
        };

        this.setData({
          checkInData: checkInData,
          isCheckInRequestInProgress: false
        });

        // 缓存签到数据
        this.cacheCheckInData(checkInData, dateString);
      })
      .catch(err => {
        console.error('获取签到状态失败:', err);

        // 解锁请求状态
        this.setData({
          isCheckInRequestInProgress: false,
          'checkInData.isLoading': false
        });

        // 显示错误提示
        wx.showToast({
          title: '获取签到状态失败',
          icon: 'none',
          duration: 2000
        });
      });
    });
  },

  // 缓存签到数据
  cacheCheckInData: function(checkInData, dateString) {
    const cacheKey = this.data.checkInCacheConfig.cacheKey;
    const expirationKey = this.data.checkInCacheConfig.expirationKey;
    const dailyResetFlag = this.data.checkInCacheConfig.dailyResetFlag;

    try {
      // 缓存签到数据
      wx.setStorageSync(cacheKey, checkInData);

      // 设置缓存过期时间
      const now = Date.now();
      const expirationTime = now + this.data.checkInCacheConfig.cacheDuration;
      wx.setStorageSync(expirationKey, expirationTime);

      // 标记今日已重置
      wx.setStorageSync(dailyResetFlag, dateString);

      console.log('签到数据已缓存，有效期至:', new Date(expirationTime).toLocaleString());
    } catch (err) {
      console.error('缓存签到数据失败:', err);
    }
  },

  // 检查是否需要重置签到状态（过了0点）
  checkCheckInReset: function() {
    console.log('检查是否需要重置签到状态');

    // 获取当前日期（格式：YYYY-MM-DD）
    const today = new Date();
    const dateString = today.getFullYear() + '-' +
                     String(today.getMonth() + 1).padStart(2, '0') + '-' +
                     String(today.getDate()).padStart(2, '0');

    // 获取上次重置标记
    const dailyResetFlag = this.data.checkInCacheConfig.dailyResetFlag;
    const lastResetDate = wx.getStorageSync(dailyResetFlag);

    // 如果上次重置日期不是今天，则需要重置
    if (lastResetDate !== dateString) {
      console.log('需要重置签到状态，上次重置日期:', lastResetDate, '当前日期:', dateString);

      // 清除签到缓存，确保从服务器获取最新数据
      this.clearCheckInCache();

      // 从服务器获取最新签到状态
      this.getCheckInStatus(false);
    } else {
      console.log('今日已重置过签到状态，无需重复重置');
    }
  },

  // 清除签到缓存
  clearCheckInCache: function() {
    const cacheKey = this.data.checkInCacheConfig.cacheKey;
    const expirationKey = this.data.checkInCacheConfig.expirationKey;

    try {
      wx.removeStorageSync(cacheKey);
      wx.removeStorageSync(expirationKey);
      console.log('签到缓存已清除');
    } catch (err) {
      console.error('清除签到缓存失败:', err);
    }
  },

  // 执行签到
  doCheckIn: function() {
    console.log('执行签到');

    // 如果签到数据正在加载中，不允许签到操作
    if (this.data.checkInData.isLoading) {
      console.log('签到数据正在加载中，不允许签到操作');
      wx.showToast({
        title: '数据加载中，请稍候',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 如果今日已签到，提示用户
    if (this.data.checkInData.todayCheckedIn) {
      wx.showToast({
        title: '今日已签到',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '签到中...',
      mask: true
    });

    // 获取应用实例
    const app = getApp();

    // 获取用户openid
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '签到失败，请重试',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 调用云函数执行签到
      wx.cloud.callFunction({
        name: 'checkIn',
        data: {
          userOpenId: openid
        }
      })
      .then(res => {
        wx.hideLoading();

        const result = res.result || {};

        if (!result.success) {
          console.error('签到失败:', result.error || '未知错误');
          throw new Error(result.error || '签到失败');
        }

        console.log('签到成功:', result);

        // 如果已经签到过，提示用户
        if (result.alreadyCheckedIn) {
          wx.showToast({
            title: '今日已签到',
            icon: 'none',
            duration: 2000
          });
          return;
        }

        // 获取签到数据
        const checkInData = result.checkInData;

        console.log('签到成功，返回数据:', checkInData);

        // 更新UI
        this.setData({
          'checkInData.todayCheckedIn': true,
          'checkInData.consecutiveDays': checkInData.consecutiveDays || 1,
          checkInRewardData: {
            title: `连续签到${checkInData.consecutiveDays || 1}天`,
            energy: checkInData.energyReward || 2,
            storage: checkInData.storageReward || 0,
            isVIP: checkInData.isVIP || false
          },
          checkInRewardVisible: true
        });

        // 更新用户信息（体力值和衣物容量已更新）
        this.updateUserInfoAfterCheckIn(checkInData);

        // 清除签到缓存，确保下次获取最新数据
        this.clearCheckInCache();

        // 延迟刷新签到状态，确保数据库操作完成
        setTimeout(() => {
          this.getCheckInStatus(false);
        }, 500);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('签到失败:', err);

        wx.showToast({
          title: '签到失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
    });
  },

  // 签到后更新用户信息
  updateUserInfoAfterCheckIn: function(checkInData) {
    console.log('签到后更新用户信息');

    // 获取当前用户信息
    const currentUserInfo = this.data.userInfo;

    // 计算新的体力值
    const newEnergy = Math.min(500, (currentUserInfo.catEnergy || 0) + (checkInData.energyReward || 2));

    // 计算新的衣物容量（如果有存储空间奖励）
    const newClothesLimit = (currentUserInfo.clothesLimit || 30) + (checkInData.storageReward || 0);

    // 更新本地UI
    this.setData({
      'userInfo.catEnergy': newEnergy,
      'userLimits.clothesLimit': newClothesLimit
    });

    // 更新本地缓存
    const userInfoCacheKey = this.data.userInfoCacheConfig.cacheKey;
    const cachedUserInfo = wx.getStorageSync(userInfoCacheKey) || {};
    cachedUserInfo.catEnergy = newEnergy;
    wx.setStorageSync(userInfoCacheKey, cachedUserInfo);

    const limitsCacheKey = this.data.limitCacheConfig.cacheKey;
    const cachedLimits = wx.getStorageSync(limitsCacheKey) || {};
    cachedLimits.clothesLimit = newClothesLimit;
    wx.setStorageSync(limitsCacheKey, cachedLimits);

    console.log('用户信息已更新 - 体力值:', newEnergy, '衣物容量:', newClothesLimit);

    // 获取完整的最新用户信息（异步，不阻塞UI更新）
    this.getUserInfo(false);
    this.getUserLimitsInfo(false);
  },

  // 关闭签到奖励卡片
  closeCheckInReward: function() {
    this.setData({
      checkInRewardVisible: false
    });
  },

  // 调试签到状态（长按签到按钮触发）
  debugCheckInStatus: function() {
    console.log('调试签到状态');

    // 显示当前签到数据
    const checkInData = this.data.checkInData;
    const debugInfo = {
      todayCheckedIn: checkInData.todayCheckedIn,
      consecutiveDays: checkInData.consecutiveDays,
      isLoading: checkInData.isLoading,
      cacheInfo: {
        cacheKey: this.data.checkInCacheConfig.cacheKey,
        cachedData: wx.getStorageSync(this.data.checkInCacheConfig.cacheKey),
        cacheExpiration: wx.getStorageSync(this.data.checkInCacheConfig.expirationKey),
        resetFlag: wx.getStorageSync(this.data.checkInCacheConfig.dailyResetFlag)
      }
    };

    wx.showModal({
      title: '签到状态调试',
      content: `今日已签到: ${debugInfo.todayCheckedIn}\n连续天数: ${debugInfo.consecutiveDays}\n加载中: ${debugInfo.isLoading}\n缓存重置标记: ${debugInfo.cacheInfo.resetFlag}`,
      confirmText: '强制刷新',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          // 清除缓存并强制刷新
          this.clearCheckInCache();
          this.getCheckInStatus(false);
        }
      }
    });
  },

  // 缓存每日任务数据
  cacheDailyTasks: function(tasksData) {
    const cacheKey = this.data.dailyTasksCacheConfig.cacheKey;
    const expirationKey = this.data.dailyTasksCacheConfig.expirationKey;
    const cacheDuration = this.data.dailyTasksCacheConfig.cacheDuration;

    try {
      // 缓存任务数据
      wx.setStorageSync(cacheKey, tasksData);

      // 设置缓存过期时间（今天的23:59:59）
      const today = new Date();
      const tomorrow = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
      const expiration = tomorrow.getTime() - 1; // 23:59:59

      wx.setStorageSync(expirationKey, expiration);

      console.log('每日任务数据已缓存，有效期至:', new Date(expiration).toLocaleString());
    } catch (err) {
      console.error('缓存每日任务数据失败:', err);
    }
  },

  // 检查是否需要重置每日任务
  checkDailyTasksReset: function() {
    // 获取当前日期的0点时间戳
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime();

    // 获取重置标记
    const resetFlagKey = this.data.dailyTasksCacheConfig.dailyResetFlag;
    const resetFlag = wx.getStorageSync(resetFlagKey);

    // 如果还没有重置标记或者重置标记小于今天的0点，表示需要重置
    if (!resetFlag || resetFlag < todayStart) {
      console.log('需要重置每日任务');

      // 清除缓存
      const cacheKey = this.data.dailyTasksCacheConfig.cacheKey;
      const expirationKey = this.data.dailyTasksCacheConfig.expirationKey;

      try {
        wx.removeStorageSync(cacheKey);
        wx.removeStorageSync(expirationKey);
      } catch (err) {
        console.error('清除每日任务缓存失败:', err);
      }

      // 重新获取每日任务
      this.getDailyTasks();
      return true;
    }

    return false;
  },

  // 显示每日任务卡片
  showDailyTasksCard: function() {
    console.log('显示每日任务卡片');

    if (this.data.dailyTasks.isLoading) {
      wx.showToast({
        title: '数据加载中...',
        icon: 'loading',
        duration: 2000
      });
      return;
    }

    // 显示自定义任务卡片
    this.setData({
      isTaskCardVisible: true
    });
  },

  // 隐藏任务卡片
  hideTaskCard: function() {
    this.setData({
      isTaskCardVisible: false
    });
  },

  // 处理抽奖按钮点击
  handleDrawPrize: function(skipEligibilityCheck = false) {
    console.log('用户点击了抽奖按钮', skipEligibilityCheck ? '（广告抽奖，跳过资格检查）' : '（任务完成抽奖）');

    // 如果是广告抽奖，直接准备抽奖动画
    if (skipEligibilityCheck === true) {
      console.log('广告抽奖，跳过资格检查');
      this.prepareDrawingAnimation(true); // 明确传递isAdLottery=true
      return;
    }

    // 否则检查抽奖资格（任务完成抽奖）
    this.checkCanDrawPrize().then(canDrawPrize => {
      if(!canDrawPrize){
        wx.showToast({
          title: '今日任务未完成或者已领取奖励',
          icon: 'none'
        });
        return;
      }

      // 准备抽奖动画数据（明确传递false表示这是任务完成抽奖，不是广告抽奖）
      this.prepareDrawingAnimation(false);
    }).catch(err => {
      console.error('检查抽奖资格失败:', err);
      wx.showToast({
        title: '检查抽奖资格失败',
        icon: 'none'
      });
    });
  },

  // 准备抽奖动画数据
  prepareDrawingAnimation: function(isAdLottery = false) {
    // 确保 isAdLottery 是布尔值
    isAdLottery = isAdLottery === true;
    console.log('准备抽奖动画', isAdLottery ? '（广告抽奖）' : '（任务抽奖）');

    // 显示抽奖动画加载中
    wx.showLoading({
      title: '准备抽奖...',
      mask: true
    });

    // 先通过云函数获取奖项数据
    const taskId = this.data.dailyTasks.tasks[0].id;
    wx.cloud.callFunction({
      name: 'getTaskImages',
      data: {
        taskId: taskId
      }
    })
    .then(res => {
      wx.hideLoading();
      const result = res.result || {};
      if (!result.success) {
        console.error('获取任务信息失败:', result.error || '未知错误');
        wx.showToast({
          title: '抽奖失败，请重试',
          icon: 'none'
        });
        return;
      }

      const prizes = result.data || {};
      if (!prizes || prizes.length === 0) {
        console.error('没有找到奖项数据');
        wx.showToast({
          title: '抽奖配置错误',
          icon: 'none'
        });
        return;
      }

      // 确定实际抽中的奖品
      const prize = this.drawPrize(prizes);
      console.log('抽中奖品:', prize);

      // 保存抽中的奖品，供动画结束后使用
      this.setData({
        currentPrize: prize
      });

      // 复制主题任务图片作为抽奖卡片
      let cards = [];

      // 获取设备信息
      const systemInfo = wx.getSystemInfoSync();
      const screenWidth = systemInfo.windowWidth;
      const centerPosition = screenWidth / 2;

      // 如果有主题任务图片，使用它们作为抽奖卡片
      if (this.data.themeTasks.taskImages && this.data.themeTasks.taskImages.length > 0) {
        // 创建抽奖卡片数组
        for (let i = 0; i < this.data.themeTasks.taskImages.length; i++) {
          cards.push({
            imageUrl: this.data.themeTasks.taskImages[i],
            title: this.data.themeTasks.titles[i] || `任务${i + 1}`,
            prize: prizes[i] || prize // 使用对应的奖品或默认奖品
          });
        }

        // 重复卡片以增加动画效果，确保至少有16张卡片
        const originalLength = cards.length;
        for (let i = 0; i < 3; i++) {
          for (let j = 0; j < originalLength; j++) {
            cards.push({...cards[j]});
          }
        }

        // 显示抽奖动画界面
        this.setData({
          isPrizeDrawingVisible: true,
          prizeDrawingCards: cards,
          prizeDrawingOffset: centerPosition - 90, // 初始位置，卡片宽度的一半
          currentCardIndex: 0
        });

        // 开始抽奖动画
        setTimeout(() => {
          this.startDrawingAnimation(prize, isAdLottery);
        }, 500);
      } else {
        // 如果没有图片，直接处理抽奖结果
        this.processPrizeResult(prize, isAdLottery);
      }
    })
    .catch(err => {
      wx.hideLoading();
      console.error('抽奖失败:', err);
      wx.showToast({
        title: '抽奖失败，请重试',
        icon: 'none'
      });
    });
  },

  // 开始抽奖动画
  startDrawingAnimation: function(prize, isAdLottery = false) {
    // 确保 isAdLottery 是布尔值
    isAdLottery = isAdLottery === true;
    console.log('开始抽奖动画', isAdLottery ? '（广告抽奖）' : '（任务抽奖）');
    const cards = this.data.prizeDrawingCards;
    let winningIndex = -1;

    // 找到与中奖奖品匹配的卡片索引
    // 选择后半段的卡片，确保有足够的滚动动画
    const minIndex = Math.floor(cards.length / 2);
    const maxIndex = cards.length - 3;

    // 在后半段卡片中找到与奖品匹配的卡片
    for (let i = minIndex; i <= maxIndex; i++) {
      if (cards[i].prize && cards[i].prize.taskId === prize.taskId &&
          cards[i].prize.step === prize.step) {
        winningIndex = i;
        break;
      }
    }

    // 如果没有找到匹配的卡片，随机选择一个
    if (winningIndex === -1) {
      winningIndex = Math.floor(Math.random() * (maxIndex - minIndex + 1)) + minIndex;
    }

    this.setData({
      winningIndex: winningIndex
    });

    // 获取设备信息
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const centerPosition = screenWidth / 2;

    // 计算最终偏移位置：中心位置 - (卡片索引 * (卡片宽度+边距)) - 卡片宽度/2
    const cardWidth = 180; // rpx
    const cardMargin = 20; // rpx
    const pxRatio = screenWidth / 750; // rpx到px的转换比例
    const cardWidthPx = cardWidth * pxRatio;
    const cardMarginPx = cardMargin * pxRatio;

    const finalOffset = centerPosition - (winningIndex * (cardWidthPx + cardMarginPx)) - (cardWidthPx / 2);

    // 启动动画，分3个阶段
    // 1. 快速滚动阶段
    let duration = 0;
    let currentOffset = this.data.prizeDrawingOffset;
    const firstPhaseDistance = Math.abs(finalOffset - currentOffset) * 0.7;
    const firstPhaseDuration = 2000; // 2秒

    // 设置快速滚动的动画
    this.animateDrawing(currentOffset - firstPhaseDistance, firstPhaseDuration, 'cubic-bezier(0.25, 0.1, 0.25, 1)');
    duration += firstPhaseDuration;

    // 2. 减速阶段
    const secondPhaseDistance = Math.abs(finalOffset - currentOffset) * 0.25;
    const secondPhaseDuration = 1500; // 1.5秒

    setTimeout(() => {
      this.animateDrawing(currentOffset - firstPhaseDistance - secondPhaseDistance, secondPhaseDuration, 'cubic-bezier(0.42, 0, 0.58, 1)');
    }, duration);
    duration += secondPhaseDuration;

    // 3. 最终定位阶段
    const finalPhaseDuration = 1000; // 1秒

    setTimeout(() => {
      this.animateDrawing(finalOffset, finalPhaseDuration, 'cubic-bezier(0.25, 0.46, 0.45, 0.94)');

      // 更新当前卡片索引以显示高亮效果
      this.setData({
        currentCardIndex: winningIndex
      });

      // 动画结束后的处理
      setTimeout(() => {
        this.finishDrawingAnimation(isAdLottery);
      }, finalPhaseDuration + 1000); // 多等待1秒让用户看清结果
    }, duration);
  },

  // 结束抽奖动画
  finishDrawingAnimation: function(isAdLottery = false) {
    // 确保 isAdLottery 是布尔值
    isAdLottery = isAdLottery === true;
    console.log('结束抽奖动画', isAdLottery ? '（广告抽奖）' : '（任务抽奖）');
    // 隐藏抽奖动画
    this.setData({
      isPrizeDrawingVisible: false
    });

    // 处理抽奖结果，使用之前保存的奖品
    const prize = this.data.currentPrize;
    if (prize) {
      this.processPrizeResult(prize, isAdLottery);
    } else {
      console.error('找不到抽中的奖品数据');
      wx.showToast({
        title: '抽奖出错，请重试',
        icon: 'none'
      });
    }
  },

  // 处理抽奖结果
  processPrizeResult: function(prize, isAdLottery = false) {
    // 确保 isAdLottery 是布尔值
    isAdLottery = isAdLottery === true;
    console.log('处理抽奖结果:', prize, isAdLottery ? '（广告抽奖）' : '（任务抽奖）');

    const userOpenId = wx.getStorageSync('userOpenId');

    // 通过奖品更新用户任务状态
    wx.cloud.callFunction({
      name: 'updateUserTasks',
      data: {
        userOpenId: userOpenId,
        taskId: prize.taskId,
        step: Number(prize.step) // 确保step是数字类型
      }
    })
    .then(res => {
      console.log('更新用户任务状态成功:', res);
      //为用户添加奖励
      if(prize.reward === 'clothesLimit'){
        limitManager.increaseClothesLimit(userOpenId,parseInt(prize.rewardAmount)).then(()=>{
          this.getUserLimitsInfo(false);
        });

      }else if(prize.reward === 'outfitLimit'){
        limitManager.increaseOutfitsLimit(userOpenId,parseInt(prize.rewardAmount)).then(()=>{
          this.getUserLimitsInfo(false);
        });
      }else if(prize.reward === 'energy'){
        // 增加用户体力值
        energyManager.increaseEnergy(parseInt(prize.rewardAmount), userOpenId).then(result => {
          console.log('增加体力值成功:', result);
          // 刷新用户信息
          this.getUserInfo();
        }).catch(err => {
          console.error('增加体力值失败:', err);
        });
      }
      // 更新用户任务本地缓存以更新UI显示
      if(res.result.success){
        const newTaskDetails = res.result.data;
        console.log('获取到的新任务详情:', JSON.stringify(newTaskDetails));

        // 处理hasDone数组
        let updatedHasDone = newTaskDetails.hasDone || [];

        // 确保hasDone是数组类型
        if (typeof updatedHasDone === 'string') {
          try {
            updatedHasDone = JSON.parse(updatedHasDone);
          } catch (err) {
            console.error('解析hasDone数组失败:', err);
            updatedHasDone = [];
          }
        }

        // 确保hasDone是数组
        if (!Array.isArray(updatedHasDone)) {
          updatedHasDone = [];
        }

        // 确保数组元素是数字类型
        updatedHasDone = updatedHasDone.map(step => Number(step));

        console.log('处理后的hasDone数组:', JSON.stringify(updatedHasDone));
        console.log('hasDone数组类型:', typeof updatedHasDone);
        console.log('hasDone是否是数组:', Array.isArray(updatedHasDone));
        if (updatedHasDone.length > 0) {
          console.log('hasDone第一个元素类型:', typeof updatedHasDone[0]);
          console.log('hasDone第一个元素值:', updatedHasDone[0]);
        }

        if (updatedHasDone.length == 6) {
          // 完成一个大主题，准备进入下一个主题
          console.log('恭喜完成整个主题任务！');

          // 奖励用户一个月的VIP会员
          this.rewardUserWithVipMembership(userOpenId, prize.taskId);
        }

        // 更新UI进度和hasDone数组
        this.updateCurrentTaskProgress(updatedHasDone);

        // 更新当前data中的hasDone数组
        this.setData({
          'userTaskDetails.hasDone': updatedHasDone
        });

        // 更新cache中的hasDone数组
        const userTaskDetails = wx.getStorageSync('userTaskDetails') || {};
        userTaskDetails.hasDone = updatedHasDone;
        wx.setStorageSync('userTaskDetails', userTaskDetails);
      }

      // 如果不是广告抽奖，才更新每日任务状态
      if (!isAdLottery) {
        // 获取当前日期
        const today = new Date();
        const dateString = today.getFullYear() + '-' +
                          String(today.getMonth() + 1).padStart(2, '0') + '-' +
                          String(today.getDate()).padStart(2, '0');

        // 更新每日任务状态，标记奖励已领取
        wx.cloud.callFunction({
          name: 'updateDailyTask',
          data: {
            taskId: this.data.dailyTasks.tasks[0].id, // 使用第一个任务的ID
            date: dateString,
            rewardClaimed: true // 标记奖励已领取
          }
        })
        .then(res => {
          console.log('更新任务状态-奖励已领取:', res.result);
        })
        .catch(err => {
          console.error('更新任务状态-奖励已领取失败:', err);
        });
      } else {
        console.log('广告抽奖，不更新每日任务状态');
      }

      let imagePath = '';
      try {
        const cacheData = wx.getStorageSync('taskImagesCache_' + prize.taskId);
        if(cacheData && cacheData.localImagesMap && prize.fileID && cacheData.localImagesMap[prize.fileID]) {
          imagePath = `${wx.env.USER_DATA_PATH}/${cacheData.localImagesMap[prize.fileID]}`;
          console.log('获取到奖品图片路径:', imagePath);
        } else {
          console.warn('未找到奖品图片缓存:', prize.fileID);
          imagePath = '/image/placeholder.png';
        }
      } catch (err) {
        console.error('获取缓存图片失败:', err);
        imagePath = '/image/placeholder.png';
      }
      // 显示奖励卡片
      this.showRewardCard(prize, imagePath, isAdLottery);
    })
    .catch(err => {
      console.error('更新用户任务状态失败:', err);
      wx.showToast({
        title: '领取奖励失败',
        icon: 'none'
      });
    });
  },

  // 显示抽奖界面 (只在没有图片时作为备用方案)
  PrizeDrawing: function() {
    console.log('使用备用抽奖逻辑');

    // 显示抽奖动画
    wx.showLoading({
      title: '抽奖中...',
      mask: true
    });

    // 使用prepareDrawingAnimation作为统一入口
    this.prepareDrawingAnimation();
  },

  // 根据概率抽取奖品
  drawPrize: function(prizes) {
    // 计算总概率
    let totalPercent = 0;
    prizes.forEach(prize => {
      totalPercent += (Number(prize.precent) || 0);
    });

    // 如果总概率为0，随机返回一个奖品
    if (totalPercent <= 0) {
      const randomIndex = Math.floor(Math.random() * prizes.length);
      return prizes[randomIndex];
    }

    // 根据概率范围抽奖
    const randomNum = Math.random() * totalPercent;
    let currentSum = 0;

    for (let i = 0; i < prizes.length; i++) {
      currentSum += (Number(prizes[i].precent) || 0);
      if (randomNum <= currentSum) {
        return prizes[i];
      }
    }

    // 保底，返回最后一个奖品
    return prizes[0];
  },

  // 设置抽奖动画的位置
  animateDrawing: function(targetOffset, duration, timingFunction) {
    // 创建动画实例
    const animation = wx.createAnimation({
      duration: duration,
      timingFunction: timingFunction || 'linear',
      delay: 0
    });

    // 设置变换
    animation.translateX(targetOffset).step();

    // 更新页面数据
    this.setData({
      prizeCardsAnimation: animation.export(),
      prizeDrawingOffset: targetOffset
    });
  },

  // 检查是否可以抽奖
  checkCanDrawPrize: function(){
    return new Promise((resolve, reject) => {
      const db = wx.cloud.database();
      const today = new Date();
      const dateString = today.getFullYear() + '-' +
                       String(today.getMonth() + 1).padStart(2, '0') + '-' +
                       String(today.getDate()).padStart(2, '0');
      const userOpenId = wx.getStorageSync('userOpenId');
      db.collection('dailyTasks').where({
        date: dateString,
        userOpenId: userOpenId
      }).get().then(res => {
        console.log('获取每日任务成功:', res.data);
        const dailyTasks = res.data[0];
        const allTasks = dailyTasks.tasks;
        const completedCount = allTasks.filter(task => task.completed).length;
        const hasReward = dailyTasks.rewardClaimed;
        const canDrawPrize = completedCount >= 3 && !hasReward;
        resolve(canDrawPrize); // 返回结果
      }).catch(err => {
        console.error('获取每日任务失败:', err);
        reject(err); // 返回错误
      });
    });
  },

  // 显示奖励卡片
  showRewardCard: function(prize, imagePath, isAdLottery = false) {
    // 确保 isAdLottery 是布尔值
    isAdLottery = isAdLottery === true;
    console.log('显示奖励卡片', isAdLottery ? '（广告抽奖）' : '（任务抽奖）');
    // 根据奖励类型生成奖励描述
    let rewardDesc = '';
    if (prize.reward === 'clothesLimit') {
      rewardDesc = `恭喜获得: 衣物上限 +${prize.rewardAmount}`;
    } else if (prize.reward === 'outfitLimit') {
      rewardDesc = `恭喜获得: 搭配上限 +${prize.rewardAmount}`;
    } else if (prize.reward === 'storageLimit') {
      rewardDesc = `恭喜获得: 存储空间 +${prize.rewardAmount}`;
    } else if (prize.reward === 'energy') {
      rewardDesc = `恭喜获得: 猫咪体力值 +${prize.rewardAmount}`;
    } else {
      rewardDesc = `恭喜获得: ${prize.description || '奖励'}`;
    }

    // 设置奖励卡片数据
    this.setData({
      rewardCardVisible: true,
      rewardData: {
        image: imagePath,
        title: prize.title || '恭喜中奖',
        description: prize.description || rewardDesc
      }
    });

    // 只有当不是广告抽奖时，才标记每日任务奖励已领取
    if (!isAdLottery) {
      console.log('标记每日任务奖励已领取');
      // 标记当前用户已领取今日奖励
      this.setData({
        'dailyTasks.canDrawPrize': false,
        'dailyTasks.rewardClaimed': true
      });

      // 更新本地缓存
      const dailyTasksData = {
        ...this.data.dailyTasks,
        canDrawPrize: false,
        rewardClaimed: true
      };

      this.cacheDailyTasks(dailyTasksData);
    } else {
      console.log('广告抽奖，不影响每日任务奖励状态');
    }
  },

  // 关闭奖励卡片
  closeRewardCard: function() {
    this.setData({
      rewardCardVisible: false
    });
  },

  // 奖励用户一个月的VIP会员并重置任务进度
  rewardUserWithVipMembership: function(userOpenId, taskId) {
    console.log('开始奖励用户VIP会员和重置任务进度');

    // 获取应用实例
    const app = getApp();

    // 获取用户openid
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        return;
      }

      // 查询用户当前会员信息
      const db = wx.cloud.database();
      db.collection('users').where({
        _openid: openid
      }).get().then(res => {
        if (res.data && res.data.length > 0) {
          const userData = res.data[0];

          // 计算新的过期时间
          let newExpireDate;
          const now = new Date();

          if (userData.memberType === 'VIP' && userData.memberExpireDate) {
            // 如果已经是VIP会员，在当前过期时间基础上增加30天
            const currentExpireDate = new Date(userData.memberExpireDate);

            // 如果当前会员已过期，从现在开始计算30天
            if (currentExpireDate < now) {
              newExpireDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
            } else {
              // 如果当前会员未过期，在原过期时间基础上增加30天
              newExpireDate = new Date(currentExpireDate.getTime() + 30 * 24 * 60 * 60 * 1000);
            }
          } else {
            // 如果不是VIP会员，设置为从现在开始的30天
            newExpireDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
          }

          // 更新用户会员信息
          db.collection('users').where(
            {
              _openid: openid
            }
          ).update({
            data: {
              memberType: 'VIP',
              memberExpireDate: newExpireDate,
              updatedAt: db.serverDate()
            }
          }).then(() => {
            console.log('会员信息已更新到云端:', 'VIP', newExpireDate);

            // 计算剩余天数
            const daysLeft = this.calculateMemberDaysLeft(newExpireDate);

            // 更新本地显示
            this.setData({
              'userInfo.memberType': 'VIP',
              'userInfo.memberExpireDate': newExpireDate,
              'userInfo.memberDaysLeft': daysLeft
            });

            // 更新缓存
            const cacheKey = this.data.userInfoCacheConfig.cacheKey;
            const cachedData = wx.getStorageSync(cacheKey) || {};
            cachedData.memberType = 'VIP';
            cachedData.memberExpireDate = newExpireDate;
            cachedData.memberDaysLeft = daysLeft;
            wx.setStorageSync(cacheKey, cachedData);

            // 重置用户任务进度
            wx.cloud.callFunction({
              name: 'resetUserTasks',
              data: {
                userOpenId: userOpenId,
                taskId: taskId
              }
            })
            .then(res => {
              console.log('重置用户任务进度成功:', res.result);

              // 清除本地缓存
              const cacheKey = this.data.tasksCacheConfig.cacheKey;
              const expirationKey = this.data.tasksCacheConfig.expirationKey;
              wx.removeStorageSync(cacheKey);
              wx.removeStorageSync(expirationKey);

              // 更新UI显示
              this.setData({
                'themeTasks.hasDone': [],
                'themeTasks.currentStep': 0
              });

              // 显示主题完成卡片
              this.setData({
                themeCompletionVisible: true
              });

              // 刷新任务进度
              this.getUserTaskProgress(true);
            })
            .catch(err => {
              console.error('重置用户任务进度失败:', err);
            });
          }).catch(err => {
            console.error('更新会员信息失败:', err);
          });
        }
      }).catch(err => {
        console.error('查询用户信息失败:', err);
      });
    });
  },

  // 检查每日任务是否可以抽奖


  // 更新任务完成状态
  updateTaskStatus: function(taskId, completed) {
    console.log('更新任务完成状态:', taskId, completed);

    if (!taskId) {
      console.error('缺少任务ID');
      return;
    }

    // 获取当前日期
    const today = new Date();
    const dateString = today.getFullYear() + '-' +
                      String(today.getMonth() + 1).padStart(2, '0') + '-' +
                      String(today.getDate()).padStart(2, '0');

    // 调用云函数更新任务状态
    wx.cloud.callFunction({
      name: 'updateDailyTask',
      data: {
        taskId: taskId,
        completed: completed !== undefined ? completed : true,
        date: dateString
      }
    })
    .then(res => {
      const result = res.result || {};

      if (!result.success) {
        console.error('更新任务完成状态失败:', result.error || '未知错误');
        return;
      }

      console.log('更新任务完成状态成功:', result.data);

      // 更新本地任务数据
      const updatedTasks = result.data.tasks || [];
      const completedCount = result.data.completedCount || 0;
      const canDrawPrize = result.data.canDrawPrize || false;

      this.setData({
        'dailyTasks.tasks': updatedTasks,
        'dailyTasks.completedCount': completedCount,
        'dailyTasks.canDrawPrize': canDrawPrize
      });

      // 更新缓存
      const dailyTasksData = {
        ...this.data.dailyTasks,
        tasks: updatedTasks,
        completedCount: completedCount,
        canDrawPrize: canDrawPrize
      };

      this.cacheDailyTasks(dailyTasksData);
    })
    .catch(err => {
      console.error('调用更新任务状态云函数失败:', err);
    });
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    return false; // 阻止事件继续传播
  },

  // 更新断舍离箱数量
  updateDiscardCount: function() {
    console.log('更新断舍离箱数量');

    // 获取应用实例
    const app = getApp();

    // 定义缓存键（使用固定常量）
    const CLOTHES_CACHE_KEY = 'user_clothes_cache';

    // 获取用户的OpenID
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        return;
      }

      // 尝试从本地缓存获取衣物数据
      const cacheKey = `${CLOTHES_CACHE_KEY}_${openid}`;
      const cachedData = wx.getStorageSync(cacheKey);

      // 检查缓存是否存在且有效
      if (cachedData && cachedData.clothes && cachedData.clothes.length > 0) {
        // 计算标记为断舍离的衣物数量
        const discardClothes = cachedData.clothes.filter(item => item.wantToDiscard === true);
        const discardCount = discardClothes.length;

        console.log(`断舍离箱中有 ${discardCount} 件衣物`);

        // 更新UI
        this.setData({
          discardCount: discardCount
        });
      } else {
        // 本地缓存不存在或无效，从云函数获取
        console.log('本地缓存不存在，从云端获取数据以计算断舍离箱数量');

        // 临时显示未知数量
        this.setData({
          discardCount: undefined
        });
      }
    });
  },

  // 查看断舍离箱（显示被标记为断舍离的衣物）
  viewDiscardBox: function() {
    console.log('查看断舍离箱（断舍离衣物）');

    // 首先检查用户是否为VIP会员
    this.checkIsVipMember().then(isVip => {
      if (!isVip) {
        // 非VIP会员，显示提示
        wx.showModal({
          title: 'VIP专属功能',
          content: '断舍离箱是VIP会员专属功能，开通VIP会员即可使用此功能。',
          confirmText: '了解详情',
          cancelText: '取消',
          confirmColor: '#D4AF37',
          success: (res) => {
            if (res.confirm) {
              // 显示会员权益弹窗
              this.showMemberBenefits();
            }
          }
        });
        return;
      }

      // VIP会员，允许查看断舍离箱
      // 获取应用实例
      const app = getApp();

      // 定义缓存键（使用固定常量）
      const CLOTHES_CACHE_KEY = 'user_clothes_cache';

      // 获取用户的OpenID
      app.getUserOpenId((err, openid) => {
        if (err) {
          console.error('获取用户openid失败:', err);
          wx.hideLoading();
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
          return;
        }

      // 尝试从本地缓存获取衣物数据
      const cacheKey = `${CLOTHES_CACHE_KEY}_${openid}`;
      const cachedData = wx.getStorageSync(cacheKey);

      // 检查缓存是否存在且有效
      if (cachedData && cachedData.clothes && cachedData.clothes.length > 0) {
        console.log('从本地缓存加载衣物数据:', cachedData.clothes.length, '件');

        // 仅过滤出wantToDiscard为true的衣物
        const discardClothes = cachedData.clothes.filter(item => item.wantToDiscard === true);
        console.log(`共找到 ${discardClothes.length} 件断舍离衣物（从缓存）`);

        // 更新断舍离箱数量
        this.setData({
          discardCount: discardClothes.length
        });

        this.processDiscardBoxData(discardClothes, openid);
        return;
      }

      // 本地缓存不存在或无效，从云函数获取
      console.log('本地缓存不存在或为空，从云端获取数据');

      // 调用云函数获取所有衣物
      wx.cloud.callFunction({
        name: 'getAllClothes',
        data: {
          userOpenId: openid
        }
      })
      .then(res => {
        wx.hideLoading();

        // 处理返回的数据
        const result = res.result || {};

        if (!result.success) {
          console.error('获取衣物列表失败:', result.error || '未知错误');
          wx.showToast({
            title: '获取衣物失败',
            icon: 'none'
          });
          return;
        }

        const allClothes = result.data || [];

        // 更新本地缓存
        try {
          const cacheData = {
            clothes: allClothes,
            timestamp: new Date().getTime()
          };

          // 更新缓存
          wx.setStorageSync(cacheKey, cacheData);
          console.log('本地衣物缓存已更新:', allClothes.length, '件');
        } catch (err) {
          console.error('更新本地缓存失败:', err);
        }

        // 仅过滤出wantToDiscard为true的衣物
        const discardClothes = allClothes.filter(item => item.wantToDiscard === true);
        console.log(`共找到 ${discardClothes.length} 件断舍离衣物（从云端）`);

        // 更新断舍离箱数量
        this.setData({
          discardCount: discardClothes.length
        });

        this.processDiscardBoxData(discardClothes, openid);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取衣物列表失败:', err);
        wx.showToast({
          title: '获取衣物失败',
          icon: 'none',
          duration: 2000
        });
      });
    });
  })},

  // 处理断舍离箱数据并导航
  processDiscardBoxData: function(discardClothes, openid) {
    wx.hideLoading();

    // 如果没有断舍离衣物，提示用户
    if (discardClothes.length === 0) {
      wx.showToast({
        title: '断舍离箱中暂无衣物，可在衣柜页面长按衣物标记断舍离',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 从本地缓存获取图片URL信息
    const URL_CACHE_KEY = 'clothes_image_urls';
    const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};

    // 处理衣物图片URL
    const processedClothes = discardClothes.map(item => {
      // 创建衣物副本，避免修改原始数据
      const clothingItem = {...item};

      // 优先检查processedImageFileID
      let fileID = clothingItem.processedImageFileID;
      if (fileID && cachedURLInfo[fileID]) {
        console.log(`使用抠图缓存图片 ${fileID} 用于衣物 ${clothingItem._id}`);
        clothingItem.tempImageUrl = `${wx.env.USER_DATA_PATH}/${cachedURLInfo[fileID].localPath}`;
        return clothingItem;
      }

      // 其次检查imageFileID
      fileID = clothingItem.imageFileID;
      if (fileID && cachedURLInfo[fileID]) {
        console.log(`使用原始缓存图片 ${fileID} 用于衣物 ${clothingItem._id}`);
        clothingItem.tempImageUrl = `${wx.env.USER_DATA_PATH}/${cachedURLInfo[fileID].localPath}`;
        return clothingItem;
      }

      // 如果都没有找到缓存图片，返回原始数据
      return clothingItem;
    });

    // 获取应用实例
    const app = getApp();

    // 将处理后的衣物数据保存到全局数据
    app.globalData.tempClothes = processedClothes;
    app.globalData.tempCategory = '断舍离箱';

    // 打开新页面展示断舍离箱衣物
    wx.navigateTo({
      url: '../wardrobe/category/category?category=' + encodeURIComponent('断舍离箱'),
      success: function() {
        console.log('成功跳转到断舍离箱页面');
      },
      fail: function(error) {
        console.error('跳转到断舍离箱页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 更新待办物品箱数量
  updateTodoCount: function() {
    console.log('更新待办物品箱数量');

    // 获取应用实例
    const app = getApp();

    // 定义缓存键（使用固定常量）
    const CLOTHES_CACHE_KEY = 'user_clothes_cache';

    // 获取用户的OpenID
    app.getUserOpenId((err, openid) => {
      if (err) {
        console.error('获取用户openid失败:', err);
        return;
      }

      // 尝试从本地缓存获取衣物数据
      const cacheKey = `${CLOTHES_CACHE_KEY}_${openid}`;
      const cachedData = wx.getStorageSync(cacheKey);

      // 检查缓存是否存在且有效
      if (cachedData && cachedData.clothes && cachedData.clothes.length > 0) {
        // 计算有待办事项的衣物数量
        const todoClothes = cachedData.clothes.filter(item => {
          // 检查是否有todos数组
          if (!item.todos || !item.todos.length) return false;

          // 检查是否至少有一个未完成的待办事项
          return item.todos.some(todo => !todo.completed);
        });
        const todoCount = todoClothes.length;

        console.log(`待办物品箱中有 ${todoCount} 件衣物`);

        // 查找最近的待办事项
        let nearestTodo = null;
        let nearestDate = null;
        let hasTodayTodo = false;
        const now = new Date();
        const todayStr = now.toISOString().split('T')[0]; // 今天的日期，格式：YYYY-MM-DD

        // 遍历所有带待办事项的衣物
        todoClothes.forEach(cloth => {
          if (!cloth.todos || !cloth.todos.length) return;

          // 遍历该衣物的所有待办事项
          cloth.todos.forEach(todo => {
            if (todo.completed) return;
            if (!todo.dueDate) return;

            // 检查是否为今天的待办事项
            const todoDate = new Date(todo.dueDate);
            const todoDateStr = todoDate.toISOString().split('T')[0];

            // 如果有今天的待办事项，标记hasTodayTodo为true
            if (todoDateStr === todayStr) {
              hasTodayTodo = true;
            }

            // 过滤掉已过期的待办
            if (todoDate < now && todoDateStr !== todayStr) return;

            // 如果还没有找到最近待办事项或者当前待办事项更近
            if (!nearestDate || todoDate < nearestDate) {
              nearestTodo = {
                content: todo.content || '待办事项',
                clothName: cloth.name || '衣物',
                clothId: cloth._id
              };
              nearestDate = todoDate;
            }
          });
        });

        // 格式化最近待办日期
        let formattedDate = null;
        if (nearestDate) {
          const isToday = nearestDate.toISOString().split('T')[0] === todayStr;

          if (isToday) {
            formattedDate = '今天';
          } else {
            // 格式化为MM-DD
            const month = nearestDate.getMonth() + 1;
            const day = nearestDate.getDate();
            formattedDate = `${month}月${day}日`;
          }
        }

        // 更新UI
        this.setData({
          todoCount: todoCount,
          recentTodo: nearestTodo,
          recentTodoDate: formattedDate,
          hasTodayTodo: hasTodayTodo
        });
      } else {
        // 本地缓存不存在或无效，从云函数获取
        console.log('本地缓存不存在，从云端获取数据以计算待办物品箱数量');

        // 临时显示未知数量
        this.setData({
          todoCount: undefined,
          recentTodo: null,
          recentTodoDate: null,
          hasTodayTodo: false
        });
      }
    });
  },

  // 查看待办物品箱（显示有待办事项的衣物）
  viewTodoBox: function() {
    console.log('查看待办物品箱（待办事项衣物）');

    // 首先检查用户是否为VIP会员
    this.checkIsVipMember().then(isVip => {
      if (!isVip) {
        // 非VIP会员，显示提示
        wx.showModal({
          title: 'VIP专属功能',
          content: '待办物品箱是VIP会员专属功能，开通VIP会员即可使用此功能。',
          confirmText: '了解详情',
          cancelText: '取消',
          confirmColor: '#D4AF37',
          success: (res) => {
            if (res.confirm) {
              // 显示会员权益弹窗
              this.showMemberBenefits();
            }
          }
        });
        return;
      }

      // VIP会员，允许查看待办物品箱
      // 获取应用实例
      const app = getApp();

      // 定义缓存键（使用固定常量）
      const CLOTHES_CACHE_KEY = 'user_clothes_cache';

      // 获取用户的OpenID
      app.getUserOpenId((err, openid) => {
        if (err) {
          console.error('获取用户openid失败:', err);
          wx.hideLoading();
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
          return;
        }

      // 尝试从本地缓存获取衣物数据
      const cacheKey = `${CLOTHES_CACHE_KEY}_${openid}`;
      const cachedData = wx.getStorageSync(cacheKey);

      // 检查缓存是否存在且有效
      if (cachedData && cachedData.clothes && cachedData.clothes.length > 0) {
        console.log('从本地缓存加载衣物数据:', cachedData.clothes.length, '件');

        // 仅过滤出有待办事项的衣物
        const todoClothes = cachedData.clothes.filter(item => {
          // 检查是否有todos数组
          if (!item.todos || !item.todos.length) return false;

          // 检查是否至少有一个未完成的待办事项
          return item.todos.some(todo => !todo.completed);
        });

        console.log(`共找到 ${todoClothes.length} 件待办事项衣物（从缓存）`);

        // 更新待办物品箱数量
        this.setData({
          todoCount: todoClothes.length
        });

        this.processTodoBoxData(todoClothes, openid);
        return;
      }

      // 本地缓存不存在或无效，从云函数获取
      console.log('本地缓存不存在或为空，从云端获取数据');
      // 调用云函数获取所有衣物
      wx.cloud.callFunction({
        name: 'getAllClothes',
        data: {
          userOpenId: openid
        }
      })
      .then(res => {
        wx.hideLoading();

        // 处理返回的数据
        const result = res.result || {};

        if (!result.success) {
          console.error('获取衣物列表失败:', result.error || '未知错误');
          wx.showToast({
            title: '获取衣物失败',
            icon: 'none'
          });
          return;
        }

        const allClothes = result.data || [];

        // 更新本地缓存
        try {
          const cacheData = {
            clothes: allClothes,
            timestamp: new Date().getTime()
          };

          // 更新缓存
          wx.setStorageSync(cacheKey, cacheData);
          console.log('本地衣物缓存已更新:', allClothes.length, '件');
        } catch (err) {
          console.error('更新本地缓存失败:', err);
        }

        // 仅过滤出有待办事项的衣物
        const cacheData = res.result;
        const todoClothes = cacheData.data.filter(item => {
          // 检查是否有todos数组
          if (!item.todos || !item.todos.length) return false;

          // 检查是否至少有一个未完成的待办事项
          return item.todos.some(todo => !todo.completed);
        });
        console.log(`共找到 ${todoClothes.length} 件待办事项衣物（从云端）`);

        // 更新待办物品箱数量
        this.setData({
          todoCount: todoClothes.length
        });

        this.processTodoBoxData(todoClothes, openid);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取衣物列表失败:', err);
        wx.showToast({
          title: '获取衣物失败',
          icon: 'none',
          duration: 2000
        });
      });
    });
  })},

  // 处理待办物品箱数据并导航
  processTodoBoxData: function(todoClothes, openid) {
    wx.hideLoading();

    // 如果没有待办事项衣物，提示用户
    if (todoClothes.length === 0) {
      wx.showToast({
        title: '待办物品箱中暂无衣物，可在衣柜页面为衣物添加待办事项',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 从本地缓存获取图片URL信息
    const URL_CACHE_KEY = 'clothes_image_urls';
    const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};

    // 处理衣物图片URL
    const processedClothes = todoClothes.map(item => {
      // 创建衣物副本，避免修改原始数据
      const clothingItem = {...item};

      // 优先检查processedImageFileID
      let fileID = clothingItem.processedImageFileID;
      if (fileID && cachedURLInfo[fileID]) {
        console.log(`使用抠图缓存图片 ${fileID} 用于衣物 ${clothingItem._id}`);
        clothingItem.tempImageUrl = `${wx.env.USER_DATA_PATH}/${cachedURLInfo[fileID].localPath}`;
        return clothingItem;
      }

      // 其次检查imageFileID
      fileID = clothingItem.imageFileID;
      if (fileID && cachedURLInfo[fileID]) {
        console.log(`使用原始缓存图片 ${fileID} 用于衣物 ${clothingItem._id}`);
        clothingItem.tempImageUrl = `${wx.env.USER_DATA_PATH}/${cachedURLInfo[fileID].localPath}`;
        return clothingItem;
      }

      // 如果都没有找到缓存图片，返回原始数据
      return clothingItem;
    });

    // 获取应用实例
    const app = getApp();

    // 将处理后的衣物数据保存到全局数据
    app.globalData.tempClothes = processedClothes;
    app.globalData.tempCategory = '待办物品箱';

    // 打开新页面展示待办物品箱衣物
    wx.navigateTo({
      url: '../wardrobe/category/category?category=' + encodeURIComponent('待办物品箱'),
      success: function() {
        console.log('成功跳转到待办物品箱页面');
      },
      fail: function(error) {
        console.error('跳转到待办物品箱页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 刷新按钮点击事件 - 强制从服务器获取最新任务进度
  refreshTaskProgress: function() {
    // 显示刷新中的提示
    wx.showToast({
      title: '刷新中...',
      icon: 'loading',
      duration: 2000
    });

    // 调用getUserTaskProgress并传入forceRefresh=true参数，强制从服务器获取最新数据
    this.getUserTaskProgress(true);
  },

  // 缓存任务进度
  cacheTaskProgress: function(taskData) {
    // 缓存任务进度数据
    const cacheKey = this.data.tasksCacheConfig.cacheKey;
    const expirationKey = this.data.tasksCacheConfig.expirationKey;
    const cacheDuration = this.data.tasksCacheConfig.cacheDuration;

    try {
      // 缓存任务数据
      wx.setStorageSync(cacheKey, taskData);

      // 设置缓存过期时间
      const expiration = Date.now() + cacheDuration;
      wx.setStorageSync(expirationKey, expiration);

      console.log('任务进度已缓存，有效期至:', new Date(expiration).toLocaleString());
    } catch (err) {
      console.error('缓存任务进度失败:', err);
    }
  },

  // 获取用户勋章数据
  getUserMedals: function(useCacheIfAvailable = true) {
    console.log('获取用户勋章数据');

    // 设置勋章加载状态
    this.setData({
      'medals.isLoading': true
    });

    // 获取用户OpenID
    const userOpenId = wx.getStorageSync('openid');
    if (!userOpenId) {
      console.error('用户OpenID不可用，无法获取勋章数据');
      this.setData({
        'medals.isLoading': false
      });
      return;
    }

    // 获取勋章数据
    medalManager.getUserMedals(userOpenId, useCacheIfAvailable)
      .then(data => {
        console.log('获取勋章数据成功:', data);

        this.setData({
          'medals.count': data.medals.length,
          'medals.earnedCount': data.earnedCount,
          'medals.isLoading': false,
          'medals.lastUpdateTime': Date.now()
        });
      })
      .catch(err => {
        console.error('获取勋章数据失败:', err);

        this.setData({
          'medals.isLoading': false
        });
      });
  },

  // 查看勋章页面
  viewMedals: function() {
    console.log('查看勋章页面');

    wx.navigateTo({
      url: 'medals/medals',
      success: () => {
        console.log('成功跳转到勋章页面');
      },
      fail: (err) => {
        console.error('跳转到勋章页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 显示会员权益弹窗
  showMemberBenefits: function() {
    console.log('显示会员权益弹窗');
    this.setData({
      showMemberBenefitsModal: true,
      selectedPriceOption: 'yearly' // 默认选中年度会员
    });
  },

  // 选择价格选项
  selectPriceOption: function(e) {
    const option = e.currentTarget.dataset.option;
    console.log('选择价格选项:', option);
    this.setData({
      selectedPriceOption: option
    });
  },

  // 隐藏会员权益弹窗
  hideMemberBenefits: function() {
    console.log('隐藏会员权益弹窗');
    this.setData({
      showMemberBenefitsModal: false
    });
  },

  // 显示VIP购买弹窗
  showVipPurchase: function() {
    const option = this.data.selectedPriceOption;
    const isVip = this.data.userInfo.memberType === 'VIP';
    console.log('显示VIP购买弹窗, 选择的价格选项:', option, '是否为VIP:', isVip);

    // 根据选择的价格选项显示不同的提示
    let price = '';
    switch(option) {
      case 'monthly':
        price = '2.88元';
        break;
      case 'quarterly':
        price = '7.99元';
        break;
      case 'yearly':
        price = '21.7元'; // 使用七折后的价格
        break;
    }



    // 下载VIP购买图片
    this.downloadVipPurchaseImage();
  },

  // 隐藏VIP购买弹窗
  hideVipPurchase: function() {
    console.log('隐藏VIP购买弹窗');
    this.setData({
      showVipPurchaseModal: false
    });
  },

  // 下载VIP购买图片
  downloadVipPurchaseImage: function() {
    console.log('开始下载VIP购买图片');

    // 尝试从缓存中获取图片路径
    const cachedImage = wx.getStorageSync('vipPurchaseImageCache');
    if (cachedImage && cachedImage.tempFilePath) {
      console.log('从缓存中获取VIP购买图片路径');
      this.setData({
        vipPurchaseImage: cachedImage.tempFilePath,
        showVipPurchaseModal: true
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 下载图片
    wx.cloud.downloadFile({
      fileID: 'cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/vip.jpg',
      success: res => {
        console.log('下载VIP购买图片成功:', res.tempFilePath);

        // 隐藏加载提示
        wx.hideLoading();

        // 更新数据
        this.setData({
          vipPurchaseImage: res.tempFilePath,
          showVipPurchaseModal: true
        });

        // 缓存图片路径
        wx.setStorageSync('vipPurchaseImageCache', {
          tempFilePath: res.tempFilePath,
          timestamp: Date.now()
        });
      },
      fail: err => {
        console.error('下载VIP购买图片失败:', err);

        // 隐藏加载提示
        wx.hideLoading();

        // 显示错误提示
        wx.showToast({
          title: '加载图片失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 注意：已移除 preventTouchMove 方法，允许弹窗内容滑动

  // 检查会员状态和体力值
  checkMembershipAndEnergy: function() {
    console.log('检查会员状态和体力值');

    this.getUserInfo();

    setTimeout(() => {
    const userInfo = this.data.userInfo;
    const userOpenId = wx.getStorageSync('userOpenId');

    if (!userInfo || !userOpenId) {
      console.log('用户信息不完整，无法检查会员状态');
      return;
    }

    // 检查会员是否过期
    if (userInfo.memberType === 'VIP' && userInfo.memberDaysLeft <= 0) {
      console.log('会员已过期，更新会员状态');

      // 调用云函数更新会员状态
      wx.cloud.callFunction({
        name: 'updateUserMembership',
        data: {
          openid: userOpenId,
          memberType: 'regular' // 将会员类型更新为普通会员
        },
        success: res => {
          console.log('会员状态更新成功:', res);

          // 更新本地用户信息
          this.setData({
            'userInfo.memberType': 'regular',
            'userInfo.memberDaysLeft': 0
          });

          // 显示美观的确认提示框
          wx.showModal({
            title: '会员已过期',
            content: '您的VIP会员已过期，无法享受会员特权。继续开通会员可享受批量上传、每月赠送体力值等特权。',
            confirmText: '立即开通',
            cancelText: '稍后再说',
            confirmColor: '#D4AF37',
            success: (res) => {
              if (res.confirm) {
                // 用户点击了立即开通
                this.showMemberBenefits();
              }
            }
          });

          // 刷新用户信息
          wx.removeStorageSync('userInfoCache');
          wx.removeStorageSync('userInfoCacheExpiration')
          this.getUserInfo();
        },
        fail: err => {
          console.error('更新会员状态失败:', err);
        }
      });
    }

    // 检查是否需要增加体力值（每月增加一次）
    if (userInfo.memberType === 'VIP') {
      // 获取上次增加体力值的时间
      const lastEnergyAddTime = wx.getStorageSync('lastEnergyAddTime_' + userOpenId);
      const now = new Date();
      const currentMonth = now.getFullYear() + '-' + (now.getMonth() + 1); // 当前年月，格式如 "2023-5"

      // 如果没有记录或者上次增加的月份不是当前月，则增加体力值
      if (!lastEnergyAddTime || lastEnergyAddTime !== currentMonth) {
        console.log('当月首次登录，为VIP会员增加体力值');

        // 计算新的体力值，最大不超过500
        const currentEnergy = userInfo.catEnergy || 0;
        const newEnergy = Math.min(currentEnergy + 200, 500);

        // 调用云函数更新体力值
        wx.cloud.callFunction({
          name: 'updateUserEnergy',
          data: {
            openid: userOpenId,
            catEnergy: newEnergy
          },
          success: res => {
            console.log('体力值更新成功:', res);

            // 更新本地用户信息
            this.setData({
              'userInfo.catEnergy': newEnergy
            });

            // 记录本次增加体力值的时间
            wx.setStorageSync('lastEnergyAddTime_' + userOpenId, currentMonth);

            // 刷新用户信息
            wx.removeStorageSync('userInfoCache');
            wx.removeStorageSync('userInfoCacheExpiration');
            this.getUserInfo();

            // 在刷新用户信息后等待一小段时间再显示提示框
            setTimeout(() => {
              console.log('尝试显示会员福利到账提示框');
              wx.showModal({
                title: '会员福利到账',
                content: '恭喜您获得了本月的VIP会员福利！您的猫咪体力值增加了300点，可用于生成AI搭配、分析衣柜等功能。',
                confirmText: '查看体力值',
                cancelText: '我知道了',
                confirmColor: '#5C9EAD',
                success: (res) => {
                  if (res.confirm) {
                    // 用户点击了查看体力值
                    this.showEnergyRules();
                  }
                }
              });
            }, 500); // 等待500毫秒再显示提示框
          },
          fail: err => {
            console.error('更新体力值失败:', err);
          }
        });
      }
    }
  },1000);
  },

  // 显示体力值规则弹窗
  showEnergyRules: function() {
    console.log('显示体力值规则弹窗');
    this.setData({
      showEnergyRulesModal: true
    });
  },

  // 隐藏体力值规则弹窗
  hideEnergyRules: function() {
    console.log('隐藏体力值规则弹窗');
    this.setData({
      showEnergyRulesModal: false
    });
  },

  // 下载猫咪图片
  downloadCatImages: function() {
    console.log('开始下载猫咪图片');

    // 检查是否已经加载过图片
    if (this.data.catImages.isLoaded) {
      console.log('猫咪图片已经加载过，跳过下载');
      return;
    }

    // 尝试从缓存中获取图片路径
    const cachedImages = wx.getStorageSync('catImagesCache');
    if (cachedImages && cachedImages.happyCat && cachedImages.tireCat) {
      console.log('从缓存中获取猫咪图片路径');
      this.setData({
        'catImages.happyCat': cachedImages.happyCat,
        'catImages.normalCat': cachedImages.normalCat,
        'catImages.tireCat': cachedImages.tireCat,
        'catImages.isLoaded': true
      }, () => {
        console.log('从缓存加载猫咪图片成功，更新头像显示');
      });
      return;
    }

    // 下载图片
    const fileList = [
      {
        fileID: 'cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/happy_cat.jpg',
        name: 'happyCat'
      },
      {
        fileID: 'cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/normal_cat.jpg',
        name: 'normalCat'
      },
      {
        fileID: 'cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/tire_cat.jpg',
        name: 'tireCat'
      }
    ];

    // 显示加载提示
    wx.showLoading({
      title: '加载猫咪图片...',
      mask: true
    });

    // 创建下载任务
    const downloadPromises = fileList.map(file => {
      return new Promise((resolve, reject) => {
        wx.cloud.downloadFile({
          fileID: file.fileID,
          success: res => {
            console.log(`下载 ${file.name} 成功:`, res.tempFilePath);
            resolve({
              name: file.name,
              tempFilePath: res.tempFilePath
            });
          },
          fail: err => {
            console.error(`下载 ${file.name} 失败:`, err);
            reject(err);
          }
        });
      });
    });

    // 等待所有下载任务完成
    Promise.all(downloadPromises)
      .then(results => {
        // 隐藏加载提示
        wx.hideLoading();

        // 处理下载结果
        const imageData = {};
        results.forEach(result => {
          imageData[result.name] = result.tempFilePath;
        });

        // 更新数据
        this.setData({
          'catImages.happyCat': imageData.happyCat,
          'catImages.normalCat': imageData.normalCat,
          'catImages.tireCat': imageData.tireCat,
          'catImages.isLoaded': true
        }, () => {
          // 图片加载完成后触发视图更新
          console.log('猫咪图片加载完成，更新头像显示');
        });

        // 缓存图片路径
        wx.setStorageSync('catImagesCache', {
          happyCat: imageData.happyCat,
          normalCat: imageData.normalCat,
          tireCat: imageData.tireCat,
          timestamp: Date.now()
        });

        console.log('猫咪图片下载完成');
      })
      .catch(err => {
        // 隐藏加载提示
        wx.hideLoading();
        console.error('猫咪图片下载失败:', err);

        // 显示错误提示
        wx.showToast({
          title: '加载图片失败',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 通用广告关闭回调处理函数
  handleAdClose: function(res) {
    // 获取当前广告类型
    const adType = this.data.currentAdType;
    console.log('广告关闭回调', adType, res);

    // 重置广告类型
    this.setData({
      currentAdType: ''
    });

    // 如果用户观看完成广告
    if (res && res.isEnded) {
      console.log('用户观看完成广告，发放奖励', adType);

      // 根据广告类型处理不同的奖励
      if (adType === 'energy') {
        // 增加体力值
        this.addEnergyAfterAd();

        // 更新观看次数
        this.updateVideoAdWatchCount();
      } else if (adType === 'lottery') {
        // 更新抽奖次数
        this.updateAdLotteryCount();

        // 执行抽奖逻辑，明确标记为广告抽奖（跳过资格检查）
        console.log('广告观看完成，触发广告抽奖流程');
        this.handleDrawPrize(true); // 传递true表示这是广告抽奖
      }
    } else {
      console.log('用户提前关闭广告，不发放奖励');
      wx.showToast({
        title: '需观看完整广告才能获得奖励',
        icon: 'none'
      });
    }
  },

  // 初始化视频广告
  initVideoAd: function() {
    console.log('初始化视频广告');

    // 检查是否支持激励视频广告
    if (wx.createRewardedVideoAd) {
      // 创建激励视频广告实例
      this.videoAd = wx.createRewardedVideoAd({
        adUnitId: this.data.videoAdData.adUnitId
      });

      // 监听加载事件
      this.videoAd.onLoad(() => {
        console.log('激励视频广告加载成功');
      });

      // 监听错误事件
      this.videoAd.onError((err) => {
        console.error('激励视频广告加载失败', err);
        wx.showToast({
          title: '广告加载失败',
          icon: 'none'
        });
      });

      // 监听关闭事件，使用通用回调函数
      this.videoAd.onClose((res) => {
        this.handleAdClose(res);
      });
    } else {
      console.log('当前微信版本不支持激励视频广告');
    }
  },

  // 显示视频广告
  showVideoAd: function() {
    console.log('尝试显示视频广告');

    // 检查是否超过每日观看次数限制
    if (this.data.videoAdData.watchCount >= this.data.videoAdData.maxWatchCount) {
      wx.showToast({
        title: `今日已观看${this.data.videoAdData.maxWatchCount}次，明天再来吧`,
        icon: 'none'
      });
      return;
    }

    // 检查是否初始化了广告实例
    if (!this.videoAd) {
      this.initVideoAd();
      if (!this.videoAd) {
        wx.showToast({
          title: '广告加载失败，请稍后再试',
          icon: 'none'
        });
        return;
      }
    }

    // 设置当前广告类型
    this.setData({
      currentAdType: 'energy'
    });

    // 显示广告
    this.videoAd.show().catch(() => {
      // 失败重试
      this.videoAd.load()
        .then(() => {
          // 重新设置当前广告类型
          this.setData({
            currentAdType: 'energy'
          });
          return this.videoAd.show();
        })
        .catch(err => {
          // 重置当前广告类型
          this.setData({
            currentAdType: ''
          });
          console.error('激励视频广告显示失败', err);
          wx.showToast({
            title: '广告显示失败，请稍后再试',
            icon: 'none'
          });
        });
    });
  },

  // 观看广告后增加体力值
  addEnergyAfterAd: function() {
    // 获取当前体力值
    const currentEnergy = this.data.userInfo.catEnergy || 0;
    // 增加后的体力值，最大不超过500
    const newEnergy = Math.min(currentEnergy + 10, 500);

    // 获取用户openid
    const userOpenId = wx.getStorageSync('userOpenId');
    if (!userOpenId) {
      console.error('获取用户openid失败');
      return;
    }

    // 调用云函数更新体力值
    wx.cloud.callFunction({
      name: 'updateUserEnergy',
      data: {
        openid: userOpenId,
        catEnergy: newEnergy
      },
      success: res => {
        console.log('体力值更新成功:', res);

        // 更新本地用户信息
        this.setData({
          'userInfo.catEnergy': newEnergy
        });

        // 显示提示
        wx.showToast({
          title: '获得10点体力值',
          icon: 'success'
        });

        // 刷新用户信息
        wx.removeStorageSync('userInfoCache');
        wx.removeStorageSync('userInfoCacheExpiration');
        this.getUserInfo();
      },
      fail: err => {
        console.error('更新体力值失败:', err);
        wx.showToast({
          title: '更新体力值失败',
          icon: 'none'
        });
      }
    });
  },

  // 更新广告观看次数
  updateVideoAdWatchCount: function() {
    const today = new Date().toLocaleDateString();
    const watchCount = this.data.videoAdData.watchCount + 1;

    this.setData({
      'videoAdData.watchCount': watchCount,
      'videoAdData.lastWatchDate': today
    });

    // 将观看次数保存到本地缓存
    wx.setStorageSync('videoAdWatchCount', watchCount);
    wx.setStorageSync('videoAdLastWatchDate', today);
  },

  // 检查广告观看次数是否需要重置
  checkVideoAdWatchCount: function() {
    const today = new Date().toLocaleDateString();
    const lastWatchDate = wx.getStorageSync('videoAdLastWatchDate');
    const watchCount = wx.getStorageSync('videoAdWatchCount') || 0;

    // 如果是新的一天，重置观看次数
    if (lastWatchDate !== today) {
      this.setData({
        'videoAdData.watchCount': 0,
        'videoAdData.lastWatchDate': today
      });

      // 更新本地缓存
      wx.setStorageSync('videoAdWatchCount', 0);
      wx.setStorageSync('videoAdLastWatchDate', today);
    } else {
      // 否则使用缓存的观看次数
      this.setData({
        'videoAdData.watchCount': watchCount,
        'videoAdData.lastWatchDate': lastWatchDate
      });
    }
  },

  // 初始化广告抽奖
  initAdLottery: function() {
    console.log('初始化广告抽奖');

    // 检查是否支持激励视频广告
    if (wx.createRewardedVideoAd) {
      // 创建激励视频广告实例
      this.adLotteryAd = wx.createRewardedVideoAd({
        adUnitId: this.data.adLotteryData.adUnitId
      });

      // 监听加载事件
      this.adLotteryAd.onLoad(() => {
        console.log('抽奖广告加载成功');
      });

      // 监听错误事件
      this.adLotteryAd.onError((err) => {
        console.error('抽奖广告加载失败', err);
        wx.showToast({
          title: '广告加载失败',
          icon: 'none'
        });
      });

      // 监听关闭事件，使用通用回调函数
      this.adLotteryAd.onClose((res) => {
        this.handleAdClose(res);
      });
    } else {
      console.log('当前微信版本不支持激励视频广告');
    }
  },

  // 显示广告抽奖
  showAdLottery: function() {
    console.log('尝试显示抽奖广告 - 这是广告抽奖流程');

    // 检查是否超过每日抽奖次数限制
    if (this.data.adLotteryData.lotteryCount >= this.data.adLotteryData.maxLotteryCount) {
      wx.showToast({
        title: `今日已抽奖${this.data.adLotteryData.maxLotteryCount}次，明天再来吧`,
        icon: 'none'
      });
      return;
    }

    // 检查是否初始化了广告实例
    if (!this.adLotteryAd) {
      this.initAdLottery();
      if (!this.adLotteryAd) {
        wx.showToast({
          title: '广告加载失败，请稍后再试',
          icon: 'none'
        });
        return;
      }
    }

    // 设置当前广告类型
    this.setData({
      currentAdType: 'lottery'
    });

    // 显示广告
    this.adLotteryAd.show().catch(() => {
      // 失败重试
      this.adLotteryAd.load()
        .then(() => {
          // 重新设置当前广告类型
          this.setData({
            currentAdType: 'lottery'
          });
          return this.adLotteryAd.show();
        })
        .catch(err => {
          // 重置当前广告类型
          this.setData({
            currentAdType: ''
          });
          console.error('抽奖广告显示失败', err);
          wx.showToast({
            title: '广告显示失败，请稍后再试',
            icon: 'none'
          });
        });
    });
  },

  // 更新广告抽奖次数
  updateAdLotteryCount: function() {
    const today = new Date().toLocaleDateString();
    const lotteryCount = this.data.adLotteryData.lotteryCount + 1;

    this.setData({
      'adLotteryData.lotteryCount': lotteryCount,
      'adLotteryData.lastLotteryDate': today
    });

    // 将抽奖次数保存到本地缓存
    wx.setStorageSync('adLotteryCount', lotteryCount);
    wx.setStorageSync('adLotteryLastDate', today);
  },

  // 检查广告抽奖次数是否需要重置
  checkAdLotteryCount: function() {
    const today = new Date().toLocaleDateString();
    const lastLotteryDate = wx.getStorageSync('adLotteryLastDate');
    const lotteryCount = wx.getStorageSync('adLotteryCount') || 0;

    // 如果是新的一天，重置抽奖次数
    if (lastLotteryDate !== today) {
      this.setData({
        'adLotteryData.lotteryCount': 0,
        'adLotteryData.lastLotteryDate': today
      });

      // 更新本地缓存
      wx.setStorageSync('adLotteryCount', 0);
      wx.setStorageSync('adLotteryLastDate', today);
    } else {
      // 否则使用缓存的抽奖次数
      this.setData({
        'adLotteryData.lotteryCount': lotteryCount,
        'adLotteryData.lastLotteryDate': lastLotteryDate
      });
    }
  },

  // 任务步骤图片加载失败时的处理（本地+云端回退方案）
  onTaskStepImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    if (typeof index === 'undefined') return;

    // 获取当前图片路径和 fileID
    const currentImg = this.data.themeTasks.taskImages[index];
    const taskId = this.data.themeTasks.taskId || 'task1';
    // 获取 fileIDList
    let fileIDList = [];
    try {
      const cacheData = wx.getStorageSync(`${this.data.taskImagesCacheConfig.cacheKey}_${taskId}`);
      if (cacheData && Array.isArray(cacheData.fileIDList)) {
        fileIDList = cacheData.fileIDList;
      }
    } catch (err) {}
    if (!fileIDList[index]) return;
    const fileID = fileIDList[index];

    // 防止死循环：如果已经是云端临时链接，直接用占位图
    if (currentImg && typeof currentImg === 'string' && currentImg.startsWith('https://')) {
      let taskImages = this.data.themeTasks.taskImages.slice();
      taskImages[index] = '/image/placeholder.png';
      this.setData({ 'themeTasks.taskImages': taskImages });
      return;
    }

    // 检查本地文件是否存在
    const fs = wx.getFileSystemManager();
    if (currentImg && currentImg.startsWith(wx.env.USER_DATA_PATH)) {
      try {
        fs.access({
          path: currentImg,
          success: () => {
            // 文件存在但加载失败，可能是格式问题，直接回退到云端
            getCloudUrlAndSet.call(this);
          },
          fail: () => {
            // 文件不存在，回退到云端
            getCloudUrlAndSet.call(this);
          }
        });
      } catch (err) {
        getCloudUrlAndSet.call(this);
      }
    } else {
      // 不是本地路径，直接回退到云端
      getCloudUrlAndSet.call(this);
    }

    // 内部函数：获取云端临时链接并更新图片
    function getCloudUrlAndSet() {
      wx.cloud.callFunction({
        name: 'getTempFileURL',
        data: { fileList: [fileID] }
      }).then(res => {
        const result = res.result || {};
        if (result.success && result.fileList && result.fileList[0] && result.fileList[0].tempFileURL) {
          let taskImages = this.data.themeTasks.taskImages.slice();
          taskImages[index] = result.fileList[0].tempFileURL;
          this.setData({ 'themeTasks.taskImages': taskImages });
        } else {
          // 云端也失败，显示占位图
          let taskImages = this.data.themeTasks.taskImages.slice();
          taskImages[index] = '/image/placeholder.png';
          this.setData({ 'themeTasks.taskImages': taskImages });
        }
      }).catch(err => {
        // 云端也失败，显示占位图
        let taskImages = this.data.themeTasks.taskImages.slice();
        taskImages[index] = '/image/placeholder.png';
        this.setData({ 'themeTasks.taskImages': taskImages });
      });
    }
  },

  // VIP购买图片加载失败时的处理（本地+云端回退方案）
  onVipPurchaseImageError: function() {
    // 云端fileID写死在downloadVipPurchaseImage里
    const fileID = 'cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/vip.jpg';
    // 防止死循环：如果已经是云端临时链接，直接用占位图
    if (this.data.vipPurchaseImage && this.data.vipPurchaseImage.startsWith('https://')) {
      this.setData({ vipPurchaseImage: '/image/placeholder.png' });
      return;
    }
    // 获取云端临时链接
    wx.cloud.getTempFileURL({
      fileList: [fileID],
      success: res => {
        if (res.fileList && res.fileList[0] && res.fileList[0].tempFileURL) {
          this.setData({ vipPurchaseImage: res.fileList[0].tempFileURL });
        } else {
          this.setData({ vipPurchaseImage: '/image/placeholder.png' });
        }
      },
      fail: () => {
        this.setData({ vipPurchaseImage: '/image/placeholder.png' });
      }
    });
  },

  // 猫咪头像图片加载失败时的处理（本地+云端回退方案）
  onCatAvatarError: function(e) {
    const type = e.currentTarget.dataset.type; // happyCat/normalCat/tireCat
    if (!type) return;
    // fileID映射
    const fileIDMap = {
      happyCat: 'cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/happy_cat.jpg',
      normalCat: 'cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/normal_cat.jpg',
      tireCat: 'cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/tire_cat.jpg'
    };
    const fileID = fileIDMap[type];
    if (!fileID) return;
    // 防止死循环：如果已经是云端临时链接，直接用占位图
    if (this.data.catImages[type] && this.data.catImages[type].startsWith('https://')) {
      let catImages = Object.assign({}, this.data.catImages);
      catImages[type] = '/image/placeholder.png';
      this.setData({ catImages });
      return;
    }
    // 获取云端临时链接
    wx.cloud.getTempFileURL({
      fileList: [fileID],
      success: res => {
        if (res.fileList && res.fileList[0] && res.fileList[0].tempFileURL) {
          let catImages = Object.assign({}, this.data.catImages);
          catImages[type] = res.fileList[0].tempFileURL;
          this.setData({ catImages });
        } else {
          let catImages = Object.assign({}, this.data.catImages);
          catImages[type] = '/image/placeholder.png';
          this.setData({ catImages });
        }
      },
      fail: () => {
        let catImages = Object.assign({}, this.data.catImages);
        catImages[type] = '/image/placeholder.png';
        this.setData({ catImages });
      }
    });
  },
});