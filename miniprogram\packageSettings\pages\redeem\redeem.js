// 兑换码页面JavaScript
const app = getApp()

Page({
  data: {
    userOpenId: '',
    activeTab: 'capacity', // 当前激活的标签：'capacity' 或 'membership'
    redeemCode: '',     // 用户输入的容量兑换码
    membershipCode: '', // 用户输入的会员兑换码
    isRedeeming: false, // 是否正在兑换容量码
    isRedeemingMembership: false, // 是否正在兑换会员码
    redeemHistory: [],  // 兑换历史
    redeemResult: null, // 兑换结果
    showResult: false,  // 是否显示兑换结果
    limits: {           // 用户的容量限制信息
      clothesLimit: 0,
      outfitsLimit: 0,
      clothesCount: 0,
      outfitsCount: 0
    },
    userInfo: {         // 用户会员信息
      memberType: '',
      memberDaysLeft: 0
    }
  },

  // 页面加载时
  onLoad: function(options) {
    // 如果有指定标签参数，切换到相应标签
    if (options && options.tab) {
      this.setData({
        activeTab: options.tab
      });
    }

    // 获取用户OpenID
    this.getUserOpenId();
  },

  // 页面显示时
  onShow: function() {
    // 刷新用户信息和兑换历史
    if (this.data.userOpenId) {
      this.getUserLimits();
      this.getUserInfo();
      this.getRedeemHistory();
    }
  },

  // 获取用户OpenID
  getUserOpenId: function() {
    const that = this;
    app.getUserOpenId(function(err, openid) {
      if (!err) {
        that.setData({
          userOpenId: openid
        });
        // 获取用户信息和兑换历史
        that.getUserLimits();
        that.getUserInfo();
        that.getRedeemHistory();
      } else {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    });
  },

  // 获取用户会员信息
  getUserInfo: function() {
    const that = this;
    const db = wx.cloud.database();

    db.collection('users')
      .where({
        _openid: that.data.userOpenId
      })
      .get()
      .then(res => {
        if (res.data && res.data.length > 0) {
          const userInfo = res.data[0];

          // 计算会员剩余天数
          let memberDaysLeft = 0;
          let memberType = userInfo.memberType || '';

          if (userInfo.memberType === 'VIP' && userInfo.memberExpireDate) {
            const now = new Date();
            const expireDate = new Date(userInfo.memberExpireDate);
            const diffTime = expireDate - now;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            memberDaysLeft = diffDays > 0 ? diffDays : 0;

            // 如果已过期，则更新会员类型
            if (memberDaysLeft <= 0) {
              memberType = '';
            }
          }

          that.setData({
            'userInfo.memberType': memberType,
            'userInfo.memberDaysLeft': memberDaysLeft
          });
        }
      })
      .catch(err => {
        console.error('获取用户会员信息失败:', err);
      });
  },

  // 获取用户限制信息
  getUserLimits: function() {
    const that = this;
    const db = wx.cloud.database();

    db.collection('users')
      .where({
        _openid: that.data.userOpenId
      })
      .get()
      .then(res => {
        if (res.data && res.data.length > 0) {
          const userInfo = res.data[0];
          that.setData({
            limits: {
              clothesLimit: userInfo.clothesLimit || 30,
              outfitsLimit: userInfo.outfitsLimit || 10,
              clothesCount: userInfo.clothesCount || 0,
              outfitsCount: userInfo.outfitsCount || 0
            }
          });
        }
      })
      .catch(err => {
        console.error('获取用户限制信息失败:', err);
      });
  },

  // 获取兑换历史
  getRedeemHistory: function() {
    const that = this;
    const db = wx.cloud.database();

    db.collection('redemptionHistory')
      .where({
        userId: that.data.userOpenId
      })
      .orderBy('redeemedAt', 'desc') // 按兑换时间降序排列
      .limit(10) // 最多显示10条历史记录
      .get()
      .then(res => {
        that.setData({
          redeemHistory: res.data
        });
      })
      .catch(err => {
        console.error('获取兑换历史失败:', err);
      });
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 处理容量兑换码输入变化
  onRedeemCodeInput: function(e) {
    this.setData({
      redeemCode: e.detail.value.trim()
    });
  },

  // 处理会员兑换码输入变化
  onMembershipCodeInput: function(e) {
    this.setData({
      membershipCode: e.detail.value.trim()
    });
  },

  // 处理从剪贴板粘贴（容量兑换码）
  pasteFromClipboard: function() {
    const that = this;
    wx.getClipboardData({
      success: function(res) {
        const clipData = res.data.trim();
        if (clipData) {
          that.setData({
            redeemCode: clipData
          });
          wx.showToast({
            title: '已粘贴',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '剪贴板为空',
            icon: 'none'
          });
        }
      },
      fail: function() {
        wx.showToast({
          title: '粘贴失败',
          icon: 'none'
        });
      }
    });
  },

  // 处理从剪贴板粘贴（会员兑换码）
  pasteFromClipboardMembership: function() {
    const that = this;
    wx.getClipboardData({
      success: function(res) {
        const clipData = res.data.trim();
        if (clipData) {
          that.setData({
            membershipCode: clipData
          });
          wx.showToast({
            title: '已粘贴',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '剪贴板为空',
            icon: 'none'
          });
        }
      },
      fail: function() {
        wx.showToast({
          title: '粘贴失败',
          icon: 'none'
        });
      }
    });
  },

  // 提交兑换码
  submitRedeemCode: function() {
    const code = this.data.redeemCode;

    // 验证兑换码是否为空
    if (!code) {
      wx.showToast({
        title: '请输入兑换码',
        icon: 'none'
      });
      return;
    }

    // 设置为兑换中状态
    this.setData({
      isRedeeming: true
    });

    wx.showLoading({
      title: '兑换中...',
      mask: true
    });

    const that = this;

    // 调用云函数验证兑换码
    wx.cloud.callFunction({
      name: 'redeemCode',
      data: {
        code: code
      },
      success: function(res) {
        console.log('兑换结果:', res);

        // 隐藏loading
        wx.hideLoading();

        // 设置兑换结果
        that.setData({
          redeemResult: res.result,
          showResult: true,
          isRedeeming: false
        });

        // 如果兑换成功，刷新用户限制信息和兑换历史
        if (res.result && res.result.success) {
          that.getUserLimits();
          that.getRedeemHistory();
          // 强制从服务器刷新用户限制信息
          const app = getApp();
          app.globalData.limitDataNeedRefresh = true;

          // 清空兑换码输入
          that.setData({
            redeemCode: ''
          });
        }
      },
      fail: function(err) {
        console.error('调用云函数失败:', err);

        // 隐藏loading
        wx.hideLoading();

        // 设置兑换结果为失败
        that.setData({
          redeemResult: {
            success: false,
            message: '网络错误，请稍后再试'
          },
          showResult: true,
          isRedeeming: false
        });
      }
    });
  },

  // 提交会员兑换码
  submitMembershipCode: function() {
    const code = this.data.membershipCode;

    // 验证兑换码是否为空
    if (!code) {
      wx.showToast({
        title: '请输入兑换码',
        icon: 'none'
      });
      return;
    }

    // 设置为兑换中状态
    this.setData({
      isRedeemingMembership: true
    });

    wx.showLoading({
      title: '兑换中...',
      mask: true
    });

    const that = this;

    // 调用会员兑换码云函数
    wx.cloud.callFunction({
      name: 'redeemMembershipCode',
      data: {
        code: code
      },
      success: function(res) {
        console.log('会员兑换结果:', res);

        // 隐藏loading
        wx.hideLoading();

        // 设置兑换结果
        that.setData({
          redeemResult: res.result,
          showResult: true,
          isRedeemingMembership: false
        });

        // 如果兑换成功，刷新用户信息和兑换历史
        if (res.result && res.result.success) {
          that.getUserInfo();
          that.getRedeemHistory();

          // 清空兑换码输入
          that.setData({
            membershipCode: ''
          });

          // 强制刷新用户信息缓存
          wx.removeStorageSync('userInfoCache');
          wx.removeStorageSync('userInfoCacheExpiration');
        }
      },
      fail: function(err) {
        console.error('调用会员兑换云函数失败:', err);

        // 隐藏loading
        wx.hideLoading();

        // 设置兑换结果为失败
        that.setData({
          redeemResult: {
            success: false,
            message: '网络错误，请稍后再试'
          },
          showResult: true,
          isRedeemingMembership: false
        });
      }
    });
  },

  // 关闭结果弹窗
  closeResultModal: function() {
    this.setData({
      showResult: false
    });
  },

  // 格式化日期时间
  formatDateTime: function(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');

    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 格式化日期
  formatDate: function(dateStr) {
    if (!dateStr) return '';

    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    return `${year}-${month}-${day}`;
  }
});