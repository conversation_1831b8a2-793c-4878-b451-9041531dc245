// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 检查必要参数
  if (!event.clothingIds || !Array.isArray(event.clothingIds) || event.clothingIds.length === 0) {
    return {
      success: false,
      message: '缺少衣物ID列表'
    }
  }
  
  try {
    // 创建批量删除任务
    const deletePromises = event.clothingIds.map(id => {
      return db.collection('clothes').doc(id).remove()
    })
    
    // 执行批量删除
    const deleteResults = await Promise.all(deletePromises)
    
    // 计算删除成功的数量
    let deletedCount = 0
    deleteResults.forEach(result => {
      deletedCount += result.stats.removed
    })
    
    return {
      success: true,
      deleted: deletedCount,
      message: `成功删除了${deletedCount}件衣物`
    }
  } catch (error) {
    console.error('批量删除衣物失败:', error)
    return {
      success: false,
      message: '删除失败: ' + error.message
    }
  }
}
