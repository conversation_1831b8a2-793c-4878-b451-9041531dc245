// 图片编辑组件
Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    imageUrl: {
      type: String,
      value: ''
    }
  },

  data: {
    // 画布相关
    canvas: null,
    ctx: null,
    canvasWidth: 0,
    canvasHeight: 0,

    // 图片相关
    image: null,
    imageWidth: 0,
    imageHeight: 0,
    rotation: 0,
    flipHorizontal: false,  // 水平镜像
    flipVertical: false,    // 垂直镜像

    // 编辑工具
    brushMode: false,
    brushSize: 20,
    sliderTouching: false,

    // 历史记录
    history: [],
    historyIndex: -1,
    canUndo: false,
    canRedo: false,

    // 触摸状态
    isDrawing: false,
    lastPoint: null
  },

  observers: {
    'show': function(show) {
      if (show) {
        console.log('图片编辑器显示，图片URL:', this.data.imageUrl);
        // 延迟一下确保DOM渲染完成
        setTimeout(() => {
          this.initCanvas();
        }, 200);
      } else {
        console.log('图片编辑器隐藏');
        // 重置状态
        this.setData({
          canvas: null,
          ctx: null,
          image: null,
          rotation: 0,
          flipHorizontal: false,
          flipVertical: false,
          brushMode: false,
          history: [],
          historyIndex: -1,
          canUndo: false,
          canRedo: false
        });
      }
    },
    'imageUrl': function(imageUrl) {
      if (this.data.show && imageUrl && this.data.canvas) {
        console.log('图片URL变化，重新加载图片:', imageUrl);
        this.loadImage();
      }
    }
  },

  methods: {
    // 初始化画布
    initCanvas() {
      console.log('开始初始化画布');
      const query = this.createSelectorQuery();
      query.select('#editCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          console.log('画布查询结果:', res);
          if (res[0] && res[0].node) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            const dpr = wx.getSystemInfoSync().pixelRatio;
            const { width, height } = res[0];

            console.log('画布尺寸:', width, height, 'DPR:', dpr);

            canvas.width = width * dpr;
            canvas.height = height * dpr;
            ctx.scale(dpr, dpr);

            this.setData({
              canvas: canvas,
              ctx: ctx,
              canvasWidth: width,
              canvasHeight: height
            });

            console.log('画布初始化完成，开始加载图片');
            this.loadImage();
          } else {
            console.error('画布节点获取失败:', res);
            wx.showToast({
              title: '画布初始化失败',
              icon: 'none'
            });
          }
        });
    },

    // 加载图片
    loadImage() {
      if (!this.data.canvas || !this.data.imageUrl) {
        console.log('加载图片失败，画布或图片URL为空:', this.data.canvas, this.data.imageUrl);
        return;
      }

      console.log('开始加载图片:', this.data.imageUrl);
      const image = this.data.canvas.createImage();

      image.onload = () => {
        console.log('图片加载成功，尺寸:', image.width, image.height);
        this.setData({
          image: image,
          imageWidth: image.width,
          imageHeight: image.height,
          rotation: 0,
          flipHorizontal: false,
          flipVertical: false
        });

        this.drawImage();
        
        // 延迟保存初始状态，确保图片已经绘制完成
        setTimeout(() => {
          this.saveToHistory();
        }, 100);
      };

      image.onerror = (err) => {
        console.error('图片加载失败:', err);
        wx.showToast({
          title: '图片加载失败',
          icon: 'none'
        });
      };

      image.src = this.data.imageUrl;
    },

    // 绘制图片
    drawImage() {
      if (!this.data.ctx || !this.data.image) {
        console.log('绘制图片失败，ctx或image为空:', this.data.ctx, this.data.image);
        return;
      }

      console.log('开始绘制图片');
      const ctx = this.data.ctx;
      const canvas = this.data.canvas;
      const image = this.data.image;

      // 清空画布
      ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);

      // 计算图片显示尺寸和位置
      const canvasRatio = this.data.canvasWidth / this.data.canvasHeight;
      const imageRatio = this.data.imageWidth / this.data.imageHeight;

      let drawWidth, drawHeight;
      if (imageRatio > canvasRatio) {
        drawWidth = this.data.canvasWidth * 0.95;
        drawHeight = drawWidth / imageRatio;
      } else {
        drawHeight = this.data.canvasHeight * 0.95;
        drawWidth = drawHeight * imageRatio;
      }

      const x = (this.data.canvasWidth - drawWidth) / 2;
      const y = (this.data.canvasHeight - drawHeight) / 2;

      console.log('绘制参数:', { 
        x, y, drawWidth, drawHeight, 
        rotation: this.data.rotation,
        flipHorizontal: this.data.flipHorizontal,
        flipVertical: this.data.flipVertical
      });

      // 保存当前状态
      ctx.save();

      // 移动到图片中心点
      ctx.translate(x + drawWidth / 2, y + drawHeight / 2);

      // 旋转
      ctx.rotate((this.data.rotation * Math.PI) / 180);

      // 镜像
      const scaleX = this.data.flipHorizontal ? -1 : 1;
      const scaleY = this.data.flipVertical ? -1 : 1;
      ctx.scale(scaleX, scaleY);

      // 绘制图片
      ctx.drawImage(image, -drawWidth / 2, -drawHeight / 2, drawWidth, drawHeight);

      // 恢复状态
      ctx.restore();

      console.log('图片绘制完成');
    },

    // 旋转图片
    rotateLeft() {
      this.setData({
        rotation: this.data.rotation - 90
      });
      this.drawImage();
      this.saveToHistory();
    },

    rotateRight() {
      this.setData({
        rotation: this.data.rotation + 90
      });
      this.drawImage();
      this.saveToHistory();
    },

    // 水平镜像
    flipHorizontal() {
      const newState = !this.data.flipHorizontal;
      console.log('水平镜像切换:', this.data.flipHorizontal, '->', newState);
      this.setData({
        flipHorizontal: newState
      });
      this.drawImage();
      this.saveToHistory();
    },

    // 垂直镜像
    flipVertical() {
      const newState = !this.data.flipVertical;
      console.log('垂直镜像切换:', this.data.flipVertical, '->', newState);
      this.setData({
        flipVertical: newState
      });
      this.drawImage();
      this.saveToHistory();
    },

    // 切换画笔模式
    toggleBrushMode() {
      this.setData({
        brushMode: !this.data.brushMode
      });
    },

    // 画笔大小改变（原slider方法，保留备用）
    onBrushSizeChange(e) {
      this.setData({
        brushSize: e.detail.value
      });
    },

    // 滑块触摸开始
    onSliderTouchStart(e) {
      console.log('滑块触摸开始');
      this.setData({
        sliderTouching: true
      });
      this.updateSliderValue(e);
    },

    // 滑块触摸移动
    onSliderTouchMove(e) {
      if (!this.data.sliderTouching) return;
      this.updateSliderValue(e);
    },

    // 滑块触摸结束
    onSliderTouchEnd(e) {
      console.log('滑块触摸结束');
      this.setData({
        sliderTouching: false
      });
    },

    // 更新滑块值
    updateSliderValue(e) {
      const touch = e.touches[0];
      const query = this.createSelectorQuery();
      
      query.select('.brush-slider-track')
        .boundingClientRect((rect) => {
          if (!rect) return;
          
          // 计算触摸点相对于滑块轨道的位置
          // 使用pageX而不是clientX，更准确
          const x = touch.pageX - rect.left;
          const percentage = Math.max(0, Math.min(1, x / rect.width));
          
          // 将百分比转换为画笔大小值 (5-50)
          const newSize = Math.round(5 + percentage * 45);
          
          console.log('滑块更新:', {
            pageX: touch.pageX,
            rectLeft: rect.left,
            x: x,
            width: rect.width,
            percentage: percentage,
            newSize: newSize
          });
          
          this.setData({
            brushSize: newSize
          });
        })
        .exec();
    },

    // 画布触摸开始
    onCanvasTouchStart(e) {
      if (!this.data.brushMode) return;

      const touch = e.touches[0];
      // 使用小程序的坐标系统
      const x = touch.x;
      const y = touch.y;

      this.setData({
        isDrawing: true,
        lastPoint: { x, y }
      });
    },

    // 画布触摸移动
    onCanvasTouchMove(e) {
      if (!this.data.brushMode || !this.data.isDrawing) return;

      const touch = e.touches[0];
      // 使用小程序的坐标系统
      const x = touch.x;
      const y = touch.y;

      this.drawBrushStroke(this.data.lastPoint, { x, y });

      this.setData({
        lastPoint: { x, y }
      });
    },

    // 画布触摸结束
    onCanvasTouchEnd() {
      if (this.data.isDrawing) {
        this.setData({
          isDrawing: false,
          lastPoint: null
        });
        this.saveToHistory();
      }
    },

    // 绘制画笔笔触
    drawBrushStroke(from, to) {
      if (!this.data.ctx) return;

      const ctx = this.data.ctx;

      ctx.save();
      ctx.globalCompositeOperation = 'destination-out'; // 擦除模式，实现透明效果
      ctx.lineWidth = this.data.brushSize;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      ctx.beginPath();
      ctx.moveTo(from.x, from.y);
      ctx.lineTo(to.x, to.y);
      ctx.stroke();

      ctx.restore();
    },

    // 保存到历史记录
    saveToHistory() {
      if (!this.data.canvas) return;

      try {
        // 使用 canvasToTempFilePath 保存当前画布状态
        wx.canvasToTempFilePath({
          canvasId: 'editCanvas',
          canvas: this.data.canvas,
          fileType: 'png',
          quality: 1.0,
          success: (res) => {
            console.log('保存历史记录成功:', res.tempFilePath);
            
            // 截取当前索引之前的历史记录，丢弃后面的记录
            const history = this.data.history.slice(0, this.data.historyIndex + 1);
            
            // 添加新的历史记录
            history.push({
              tempFilePath: res.tempFilePath,
              timestamp: Date.now(),
              // 保存当前的变换状态
              rotation: this.data.rotation,
              flipHorizontal: this.data.flipHorizontal,
              flipVertical: this.data.flipVertical
            });

            // 限制历史记录数量，避免内存过大
            const maxHistoryLength = 10;
            if (history.length > maxHistoryLength) {
              history.shift(); // 移除最早的记录
            }

            this.setData({
              history: history,
              historyIndex: history.length - 1,
              canUndo: history.length > 1,
              canRedo: false
            });

            console.log('历史记录状态:', {
              historyLength: history.length,
              historyIndex: this.data.historyIndex,
              canUndo: this.data.canUndo,
              canRedo: this.data.canRedo
            });
          },
          fail: (err) => {
            console.error('保存历史记录失败:', err);
          }
        }, this);
      } catch (err) {
        console.error('保存历史记录异常:', err);
      }
    },

    // 撤销
    undo() {
      if (!this.data.canUndo) {
        console.log('无法撤销，canUndo:', this.data.canUndo);
        wx.showToast({
          title: '无法撤销',
          icon: 'none',
          duration: 1000
        });
        return;
      }

      const newIndex = this.data.historyIndex - 1;
      console.log('执行撤销，从索引', this.data.historyIndex, '到', newIndex);
      this.restoreFromHistory(newIndex);
      
      wx.showToast({
        title: '已撤销',
        icon: 'success',
        duration: 1000
      });
    },

    // 重做
    redo() {
      if (!this.data.canRedo) {
        console.log('无法重做，canRedo:', this.data.canRedo);
        wx.showToast({
          title: '无法重做',
          icon: 'none',
          duration: 1000
        });
        return;
      }

      const newIndex = this.data.historyIndex + 1;
      console.log('执行重做，从索引', this.data.historyIndex, '到', newIndex);
      this.restoreFromHistory(newIndex);
      
      wx.showToast({
        title: '已重做',
        icon: 'success',
        duration: 1000
      });
    },

    // 从历史记录恢复
    restoreFromHistory(index) {
      if (index < 0 || index >= this.data.history.length) {
        console.error('无效的历史记录索引:', index, '历史记录长度:', this.data.history.length);
        return;
      }

      const historyItem = this.data.history[index];
      console.log('恢复历史记录:', index, historyItem);

      if (!historyItem || !historyItem.tempFilePath) {
        console.error('历史记录项无效:', historyItem);
        return;
      }

      // 创建图片对象来加载历史记录中的图片
      const image = this.data.canvas.createImage();

      image.onload = () => {
        console.log('历史记录图片加载成功，开始恢复画布');
        
        // 清空画布
        this.data.ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);
        
        // 直接绘制历史记录中保存的最终图片
        this.data.ctx.drawImage(image, 0, 0, this.data.canvasWidth, this.data.canvasHeight);

        // 恢复变换状态
        const newData = {
          historyIndex: index,
          canUndo: index > 0,
          canRedo: index < this.data.history.length - 1
        };

        // 如果历史记录中保存了变换状态，则恢复它们
        if (historyItem.rotation !== undefined) {
          newData.rotation = historyItem.rotation;
        }
        if (historyItem.flipHorizontal !== undefined) {
          newData.flipHorizontal = historyItem.flipHorizontal;
        }
        if (historyItem.flipVertical !== undefined) {
          newData.flipVertical = historyItem.flipVertical;
        }

        this.setData(newData);

        console.log('历史记录恢复完成:', {
          index: index,
          canUndo: newData.canUndo,
          canRedo: newData.canRedo,
          rotation: newData.rotation,
          flipHorizontal: newData.flipHorizontal,
          flipVertical: newData.flipVertical
        });
      };

      image.onerror = (err) => {
        console.error('加载历史记录图片失败:', err);
        wx.showToast({
          title: '恢复失败',
          icon: 'none'
        });
      };

      image.src = historyItem.tempFilePath;
    },

    // 重置
    reset() {
      console.log('重置图片编辑器');
      
      this.setData({
        rotation: 0,
        flipHorizontal: false,
        flipVertical: false,
        brushMode: false,
        history: [],
        historyIndex: -1,
        canUndo: false,
        canRedo: false
      });

      this.drawImage();
      
      // 延迟保存重置后的状态
      setTimeout(() => {
        this.saveToHistory();
      }, 100);
    },

    // 确认保存
    onConfirm() {
      if (!this.data.canvas) return;

      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      // 使用canvasToTempFilePath方法
      wx.canvasToTempFilePath({
        canvasId: 'editCanvas',
        canvas: this.data.canvas,
        fileType: 'png',
        quality: 1.0,
        success: (res) => {
          wx.hideLoading();
          console.log('图片保存成功:', res.tempFilePath);
          this.triggerEvent('confirm', {
            tempFilePath: res.tempFilePath
          });
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('保存图片失败:', err);
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      }, this);
    },

    // 取消
    onCancel() {
      this.triggerEvent('cancel');
    },

    // 阻止触摸穿透
    preventTouchMove() {
      return false;
    }
  }
});
