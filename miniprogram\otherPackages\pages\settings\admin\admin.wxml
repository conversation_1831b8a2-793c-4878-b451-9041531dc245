<view class="page" style="background-color: {{pageStyle.backgroundColor}}; background-image: {{pageStyle.backgroundImage}};">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title" style="color: {{pageStyle.titleColor}};">管理员控制台</view>
  </view>

  <!-- 功能链接区域 -->
  <view class="function-links" style="background-color: {{pageStyle.cellBackgroundColor}};">
    <navigator url="/page/settings/admin/redeem_codes/redeem_codes" class="function-link">
      <view class="link-icon" style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : pinkBlueColors.pinkDark}};">🎁</view>
      <view class="link-text" style="color: {{pageStyle.titleColor}};">兑换码管理</view>
      <view class="link-arrow" style="color: {{pageStyle.arrowColor}};">></view>
    </navigator>
  </view>

  <!-- 功能选项卡 -->
  <view class="tab-container">
    <view class="tab {{activeTab === 'query' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="query"
          style="{{activeTab === 'query' ? 'background-color: ' + (themeStyle === 'autumn' ? colors.spiced_wine : pinkBlueColors.pinkDark) + '; color: white;' : 'color: ' + pageStyle.titleColor + ';'}}">
      查询用户限制
    </view>
    <view class="tab {{activeTab === 'modify' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="modify"
          style="{{activeTab === 'modify' ? 'background-color: ' + (themeStyle === 'autumn' ? colors.olive_harvest : pinkBlueColors.blueDark) + '; color: white;' : 'color: ' + pageStyle.titleColor + ';'}}">
      修改用户限制
    </view>
    <view class="tab {{activeTab === 'insert' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="insert"
          style="{{activeTab === 'insert' ? 'background-color: ' + (themeStyle === 'autumn' ? colors.toasted_caramel : pinkBlueColors.blueMedium) + '; color: white;' : 'color: ' + pageStyle.titleColor + ';'}}">
      数据插入
    </view>
    <view class="tab {{activeTab === 'medals' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="medals"
          style="{{activeTab === 'medals' ? 'background-color: ' + (themeStyle === 'autumn' ? colors.cowhide_cocoa : pinkBlueColors.blueDark) + '; color: white;' : 'color: ' + pageStyle.titleColor + ';'}}">
      勋章管理
    </view>
  </view>

  <!-- 查询功能区域 -->
  <block wx:if="{{activeTab === 'query'}}">
    <view class="function-area" style="background-color: {{pageStyle.cellBackgroundColor}};">
      <view class="section-title" style="color: {{pageStyle.titleColor}};">
        查询用户限制信息
      </view>

      <!-- 输入区域 -->
      <view class="input-section">
        <input class="input" placeholder="输入用户OpenID（支持模糊匹配）" value="{{queryOpenid}}" bindinput="onQueryInput" />

        <!-- 显示模糊匹配结果 -->
        <view class="match-list" wx:if="{{matchedUsers.length > 0}}">
          <view class="match-item" wx:for="{{matchedUsers}}" wx:key="_openid" bindtap="selectUser" data-openid="{{item._openid}}">
            <text>{{item._openid}}</text>
            <text class="match-info" wx:if="{{item.nickName}}">{{item.nickName}}</text>
          </view>
        </view>

        <button class="btn"
                bindtap="queryUserLimits"
                style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : pinkBlueColors.pinkDark}}; color: white;">
          查询
        </button>
      </view>

      <!-- 错误提示 -->
      <view class="error-msg" wx:if="{{queryError}}">{{queryError}}</view>

      <!-- 查询结果显示 -->
      <view class="result-section" wx:if="{{queryResult}}">
        <view class="result-title" style="color: {{pageStyle.titleColor}};">查询结果</view>

        <view class="result-item">
          <text class="label">OpenID:</text>
          <text class="value">{{queryOpenid}}</text>
        </view>

        <view class="result-item">
          <text class="label">衣服上限:</text>
          <text class="value">{{queryResult.clothesLimit}} 件</text>
        </view>

        <view class="result-item">
          <text class="label">当前衣服数量:</text>
          <text class="value">{{queryResult.clothesCount}} 件</text>
        </view>

        <view class="result-item">
          <text class="label">搭配上限:</text>
          <text class="value">{{queryResult.outfitsLimit}} 套</text>
        </view>

        <view class="result-item">
          <text class="label">当前搭配数量:</text>
          <text class="value">{{queryResult.outfitsCount}} 套</text>
        </view>

        <!-- 进度条展示 -->
        <view class="progress-section">
          <view class="progress-item">
            <text class="progress-label">衣服使用情况:</text>
            <progress percent="{{queryResult.clothesCount / queryResult.clothesLimit * 100}}"
                     stroke-width="3"
                     activeColor="{{themeStyle === 'autumn' ? colors.spiced_wine : pinkBlueColors.pinkDark}}"
                     backgroundColor="{{themeStyle === 'autumn' ? colors.golden_batter : pinkBlueColors.blueLight}}"/>
            <text class="progress-text">{{queryResult.clothesCount}}/{{queryResult.clothesLimit}}</text>
          </view>

          <view class="progress-item">
            <text class="progress-label">搭配使用情况:</text>
            <progress percent="{{queryResult.outfitsCount / queryResult.outfitsLimit * 100}}"
                     stroke-width="3"
                     activeColor="{{themeStyle === 'autumn' ? colors.olive_harvest : pinkBlueColors.blueDark}}"
                     backgroundColor="{{themeStyle === 'autumn' ? colors.golden_batter : pinkBlueColors.pinkLight}}"/>
            <text class="progress-text">{{queryResult.outfitsCount}}/{{queryResult.outfitsLimit}}</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 修改功能区域 -->
  <block wx:if="{{activeTab === 'modify'}}">
    <view class="function-area" style="background-color: {{pageStyle.cellBackgroundColor}};">
      <view class="section-title" style="color: {{pageStyle.titleColor}};">
        修改用户限制信息
      </view>

      <!-- 输入区域 -->
      <view class="input-section">
        <input class="input" placeholder="输入用户OpenID（支持模糊匹配）" value="{{modifyOpenid}}" bindinput="onModifyInput" />

        <!-- 显示模糊匹配结果 -->
        <view class="match-list" wx:if="{{matchedUsersForModify.length > 0}}">
          <view class="match-item" wx:for="{{matchedUsersForModify}}" wx:key="_openid" bindtap="selectUserForModify" data-openid="{{item._openid}}">
            <text>{{item._openid}}</text>
            <text class="match-info" wx:if="{{item.nickName}}">{{item.nickName}}</text>
          </view>
        </view>
      </view>

      <!-- 当前选中用户信息 -->
      <view class="selected-user" wx:if="{{selectedUserForModify}}">
        <view class="selected-user-title" style="color: {{pageStyle.titleColor}};">当前选中用户</view>

        <view class="selected-user-info">
          <view class="info-item">
            <text class="label">OpenID:</text>
            <text class="value">{{modifyOpenid}}</text>
          </view>

          <view class="info-item" wx:if="{{selectedUserForModify.clothesLimit !== undefined}}">
            <text class="label">当前衣服上限:</text>
            <text class="value">{{selectedUserForModify.clothesLimit}} 件</text>
          </view>

          <view class="info-item" wx:if="{{selectedUserForModify.outfitsLimit !== undefined}}">
            <text class="label">当前搭配上限:</text>
            <text class="value">{{selectedUserForModify.outfitsLimit}} 套</text>
          </view>
        </view>
      </view>

      <!-- 修改区域 -->
      <view class="modify-section" wx:if="{{modifyOpenid}}">
        <view class="modify-item">
          <text class="modify-label">增加衣服上限:</text>
          <input class="modify-input" type="number" value="{{clothesLimitIncrement}}" bindinput="onClothesLimitInput" />
          <text class="modify-unit">件</text>
          <button class="modify-btn"
                  bindtap="increaseClothesLimit"
                  style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : pinkBlueColors.pinkDark}}; color: white;">
            增加
          </button>
        </view>

        <view class="modify-item">
          <text class="modify-label">增加搭配上限:</text>
          <input class="modify-input" type="number" value="{{outfitsLimitIncrement}}" bindinput="onOutfitsLimitInput" />
          <text class="modify-unit">套</text>
          <button class="modify-btn"
                  bindtap="increaseOutfitsLimit"
                  style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : pinkBlueColors.blueDark}}; color: white;">
            增加
          </button>
        </view>
      </view>

      <!-- 错误提示 -->
      <view class="error-msg" wx:if="{{modifyError}}">{{modifyError}}</view>

      <!-- 成功提示 -->
      <view class="success-msg" wx:if="{{modifyResult}}">{{modifyResult}}</view>
    </view>
  </block>

  <!-- 数据插入功能区域 -->
  <block wx:if="{{activeTab === 'insert'}}">
    <view class="function-area" style="background-color: {{pageStyle.cellBackgroundColor}};">
      <view class="section-title" style="color: {{pageStyle.titleColor}};">
        数据插入功能
      </view>

      <!-- 随机插入区域 -->
      <view class="insert-section">
        <view class="section-subtitle" style="color: {{pageStyle.titleColor}};">随机插入</view>
        <view class="input-section">
          <view class="input-group">
            <text class="input-label">插入数量:</text>
            <input class="input" type="number" value="{{insertCount}}" bindinput="onInsertCountInput" />
          </view>
          <button class="btn"
                  bindtap="randomInsert"
                  style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : pinkBlueColors.pinkDark}}; color: white;">
            随机插入
          </button>
        </view>
      </view>

      <!-- 指定插入区域 -->
      <view class="insert-section">
        <view class="section-subtitle" style="color: {{pageStyle.titleColor}};">指定插入</view>
        <view class="input-section">
          <input class="input" placeholder="输入用户OpenID（支持模糊匹配）" value="{{targetOpenid}}" bindinput="onTargetOpenidInput" />

          <!-- 显示模糊匹配结果 -->
          <view class="match-list" wx:if="{{matchedUsersForInsert.length > 0}}">
            <view class="match-item" wx:for="{{matchedUsersForInsert}}" wx:key="_openid" bindtap="selectUserForInsert" data-openid="{{item._openid}}">
              <text>{{item._openid}}</text>
              <text class="match-info" wx:if="{{item.nickName}}">{{item.nickName}}</text>
            </view>
          </view>

          <button class="btn"
                  bindtap="targetInsert"
                  style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : pinkBlueColors.blueDark}}; color: white;">
            指定插入
          </button>
        </view>
      </view>

      <!-- 错误提示 -->
      <view class="error-msg" wx:if="{{insertError}}">{{insertError}}</view>

      <!-- 成功提示 -->
      <view class="success-msg" wx:if="{{insertResult}}">{{insertResult}}</view>
    </view>
  </block>

  <!-- 勋章管理功能区域 -->
  <block wx:if="{{activeTab === 'medals'}}">
    <view class="function-area" style="background-color: {{pageStyle.cellBackgroundColor}};">
      <view class="section-title" style="color: {{pageStyle.titleColor}};">
        勋章管理功能
      </view>

      <!-- 初始化勋章数据 -->
      <view class="insert-section">
        <view class="section-subtitle" style="color: {{pageStyle.titleColor}}">初始化勋章数据</view>
        <view class="description" style="color: {{pageStyle.titleColor}}; opacity: 0.8;">
          创建默认勋章数据并上传勋章图片到云存储。这将创建一个新的medals集合，如果已存在则会先清空。
        </view>
        <button class="btn"
                bindtap="initMedalsData"
                style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : pinkBlueColors.pinkDark}}; color: white;">
          初始化勋章数据
        </button>
      </view>

      <!-- 勋章管理说明 -->
      <view class="info-section">
        <view class="info-title" style="color: {{pageStyle.titleColor}}">勋章系统说明</view>
        <view class="info-content" style="color: {{pageStyle.titleColor}}; opacity: 0.8;">
          <view class="info-item">• 勋章系统包含两个集合：medals（勋章定义）和user_medals（用户获得的勋章）</view>
          <view class="info-item">• 初始化后，系统会创建10个默认勋章</view>
          <view class="info-item">• 用户完成相应任务后，可以通过awardMedal云函数获得勋章</view>
          <view class="info-item">• 勋章图片存储在云存储的medals文件夹中</view>
        </view>
      </view>
    </view>
  </block>
</view>