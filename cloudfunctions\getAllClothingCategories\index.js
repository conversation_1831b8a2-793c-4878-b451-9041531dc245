// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()
  const MAX_LIMIT = 100 // 每批次最大获取数量
  const userOpenId = event.userOpenId || wxContext.OPENID
  
  console.log('开始获取所有衣物类别数据，用户ID:', userOpenId)

  // 构建查询条件 - 同时支持_openid和openid两种字段名
  let query = db.command.or([
    { _openid: userOpenId },
    { openid: userOpenId }
  ])

  try {
    // 先获取总数
    const countResult = await ('clothes').where(query).count()
    const total = countResult.total
    console.log('需要统计的衣物总数:', total)

    // 如果没有数据，返回空数组
    if (total === 0) {
      return { 
        success: true,
        data: [],
        total: 0
      }
    }

    // 计算需要分几次获取
    const batchTimes = Math.ceil(total / MAX_LIMIT)
    console.log('需要分', batchTimes, '次获取类别数据')

    // 承载所有读操作的 promise 的数组
    const tasks = []
    for (let i = 0; i < batchTimes; i++) {
      const promise = db.collection('clothes')
        .where(query)
        .skip(i * MAX_LIMIT)
        .limit(MAX_LIMIT)
        .field({ category: true }) // 只获取类别字段，减少数据量
        .get()
      tasks.push(promise)
    }

    // 等待所有
    const results = await Promise.all(tasks)
    console.log('所有批次查询完成')

    // 合并结果
    let allData = []
    for (let i = 0; i < results.length; i++) {
      const result = results[i]
      console.log(`第${i+1}批类别数据数量:`, result.data.length)
      allData = allData.concat(result.data)
    }

    console.log('成功获取所有衣物类别数据，总数量:', allData.length)

    return {
      success: true,
      data: allData,
      total: total,
      openid: userOpenId
    }
  } catch (err) {
    console.error('获取衣物类别数据失败:', err)
    return {
      success: false,
      error: err,
      message: '获取衣物类别数据失败'
    }
  }
} 