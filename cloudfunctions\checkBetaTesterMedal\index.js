// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 内测勋章ID
const BETA_TESTER_MEDAL_ID = 'de2bd33968045d830055fd414381224f';
// 截止日期：2023年5月1日
const CUTOFF_DATE = new Date('2025-05-01T00:00:00.000Z');

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const userOpenId = event.userOpenId || wxContext.OPENID

  try {
    // 1. 检查用户是否已经获得内测勋章
    const userMedalResult = await db.collection('user_medals')
      .where({
        userOpenId: userOpenId,
        medalId: BETA_TESTER_MEDAL_ID
      })
      .get()

    // 如果用户已经获得内测勋章，直接返回
    if (userMedalResult.data && userMedalResult.data.length > 0) {
      return {
        success: true,
        awarded: false,
        message: '用户已获得内测勋章'
      }
    }

    // 2. 检查用户首次登录时间
    const userResult = await db.collection('users')
      .where({
        _openid: userOpenId
      })
      .get()

    if (!userResult.data || userResult.data.length === 0) {
      return {
        success: false,
        awarded: false,
        error: '用户不存在'
      }
    }

    const user = userResult.data[0]

    // 检查用户创建时间或最后登录时间是否在截止日期之前
    const userLastLogin = user.lastLogin || null

    let isEligible = false

    if (userLastLogin && new Date(userLastLogin) < CUTOFF_DATE) {
      isEligible = true
    }

    // 如果用户不符合条件，返回结果
    if (!isEligible) {
      return {
        success: true,
        awarded: false,
        message: '用户不符合内测勋章条件'
      }
    }

    // 3. 获取内测勋章信息
    const medalResult = await db.collection('medals')
      .doc(BETA_TESTER_MEDAL_ID)
      .get()

    if (!medalResult.data) {
      return {
        success: false,
        awarded: false,
        error: '内测勋章不存在'
      }
    }

    const medal = medalResult.data

    // 获取勋章全局编号
    const globalNumberResult = await cloud.callFunction({
      name: 'getMedalGlobalNumber',
      data: {
        medalId: BETA_TESTER_MEDAL_ID
      }
    })

    if (!globalNumberResult.result || !globalNumberResult.result.success) {
      console.error('获取勋章全局编号失败:', globalNumberResult)
      return {
        success: false,
        awarded: false,
        error: '获取勋章全局编号失败'
      }
    }

    const globalNumber = globalNumberResult.result.globalNumber

    // 4. 为用户添加内测勋章
    const result = await db.collection('user_medals').add({
      data: {
        userOpenId: userOpenId,
        medalId: BETA_TESTER_MEDAL_ID,
        medalName: medal.name || '内测勋章',
        medalDescription: medal.description || '感谢您参与小程序内测',
        medalImageFileID: medal.imageFileID || '',
        earnedTime: db.serverDate(),
        createTime: db.serverDate(),
        globalNumber: globalNumber
      }
    })





    return {
      success: true,
      awarded: true,
      medalId: BETA_TESTER_MEDAL_ID,
      medalName: medal.name || '内测勋章',
      message: '内测勋章授予成功'
    }
  } catch (error) {
    console.error('检查内测勋章失败:', error)
    return {
      success: false,
      awarded: false,
      error: error.message
    }
  }
}
