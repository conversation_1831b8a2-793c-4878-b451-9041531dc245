/* components/themeCompletionCard/themeCompletionCard.wxss */

/* 模态框背景 */
.theme-completion-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
}

.theme-completion-modal.visible {
  opacity: 1;
}

.theme-completion-modal.hidden {
  opacity: 0;
  pointer-events: none;
}

/* 卡片容器 */
.theme-completion-container {
  width: 84%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.2);
  padding: 40rpx 30rpx;
  position: relative;
  overflow: hidden;
  transform: translateY(80rpx);
  opacity: 0;
}

/* 标题区域 */
.theme-completion-header {
  margin-bottom: 30rpx;
  text-align: center;
}

.theme-completion-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.theme-completion-icon {
  font-size: 40rpx;
  margin-right: 12rpx;
  animation: bounce 1s ease infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

.theme-completion-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 卡片展示区域 */
.theme-completion-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16rpx;
  margin: 20rpx 0;
}

.theme-completion-card-item {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  animation: cardAppear 0.5s ease forwards;
  opacity: 0;
  transform: scale(0.8);
}

@keyframes cardAppear {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 为每张卡片设置不同的延迟，创造依次出现的效果 */
.theme-completion-card-item:nth-child(1) {
  animation-delay: 0.1s;
}
.theme-completion-card-item:nth-child(2) {
  animation-delay: 0.2s;
}
.theme-completion-card-item:nth-child(3) {
  animation-delay: 0.3s;
}
.theme-completion-card-item:nth-child(4) {
  animation-delay: 0.4s;
}
.theme-completion-card-item:nth-child(5) {
  animation-delay: 0.5s;
}
.theme-completion-card-item:nth-child(6) {
  animation-delay: 0.6s;
}

.theme-completion-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.theme-completion-card-check {
  position: absolute;
  right: 4rpx;
  bottom: 4rpx;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #4CAF50;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

/* 奖励信息 */
.theme-completion-reward {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx 0;
  padding: 20rpx;
  background-color: rgba(255, 215, 0, 0.1);
  border-radius: 16rpx;
  animation: glowing 2s infinite;
}

@keyframes glowing {
  0%, 100% {
    box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.6);
  }
}

.theme-completion-reward-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
  animation: rotate 3s infinite linear;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(10deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(-10deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.theme-completion-reward-text {
  display: flex;
  flex-direction: column;
  font-weight: bold;
}

.theme-completion-reward-days {
  font-size: 24rpx;
  opacity: 0.8;
  margin-top: 6rpx;
}

/* 按钮区域 */
.theme-completion-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  gap: 20rpx;
}

.theme-completion-button {
  flex: 1;
  padding: 20rpx 0;
  border-radius: 40rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.theme-completion-button:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.theme-completion-button-primary {
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.theme-completion-button-secondary {
  background-color: transparent;
  border: 2rpx solid;
}

/* 提示信息 */
.theme-completion-hint {
  text-align: center;
  font-size: 24rpx;
  margin-top: 20rpx;
  opacity: 0.7;
}

/* 五彩纸屑动画 */
.confetti-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.confetti {
  position: absolute;
  width: 10rpx;
  height: 20rpx;
  opacity: 0;
}

.confetti-container.animate .confetti {
  animation: confettiFall 5s ease-in-out forwards;
}

@keyframes confettiFall {
  0% {
    transform: translateY(-100rpx) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(1000rpx) rotate(360deg);
    opacity: 0;
  }
}

/* 为每个五彩纸屑设置不同的颜色、位置和动画延迟 */
.confetti-1 {
  background-color: #f44336;
  left: 10%;
  animation-delay: 0.1s !important;
}

.confetti-2 {
  background-color: #2196F3;
  left: 20%;
  animation-delay: 0.3s !important;
}

.confetti-3 {
  background-color: #FFEB3B;
  left: 30%;
  animation-delay: 0.5s !important;
}

.confetti-4 {
  background-color: #4CAF50;
  left: 40%;
  animation-delay: 0.2s !important;
}

.confetti-5 {
  background-color: #9C27B0;
  left: 50%;
  animation-delay: 0.4s !important;
}

.confetti-6 {
  background-color: #FF9800;
  left: 60%;
  animation-delay: 0.6s !important;
}

.confetti-7 {
  background-color: #03A9F4;
  left: 70%;
  animation-delay: 0.3s !important;
}

.confetti-8 {
  background-color: #E91E63;
  left: 80%;
  animation-delay: 0.5s !important;
}

.confetti-9 {
  background-color: #8BC34A;
  left: 90%;
  animation-delay: 0.2s !important;
}

.confetti-10 {
  background-color: #FFC107;
  left: 95%;
  animation-delay: 0.4s !important;
}
