/* 筛选页面样式 */
/* 定义颜色变量 */
page {
  /* 高级清新配色方案 */
  --primary-bg: #f0f9f6; /* 主背景色：浅薄荷绿 */
  --secondary-bg: #e3f4f1; /* 次要背景色 */
  --card-bg: #ffffff; /* 卡片背景色 */
  --nav-bg: linear-gradient(to right, #5bbfb7, #78d9b9); /* 导航栏渐变背景 */
  --primary-btn: #5bbfb7; /* 主按钮颜色：湖水绿 */
  --active-btn: #3da99f; /* 活动按钮颜色：深湖水绿 */
  --text-primary: #333333; /* 主文本颜色 */
  --text-secondary: #666666; /* 次要文本颜色 */
  --text-light: #ffffff; /* 浅色文本 */
  --border-color: #e0f2ee; /* 边框颜色 */
  --active-color: #5bbfb7; /* 活跃项的颜色 */
  --shadow-color: rgba(91, 191, 183, 0.15); /* 阴影颜色 */
}

.filter-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--primary-bg);
  padding-top: 0; /* 移除顶部内边距 */
}

/* 自定义导航栏样式 */
.custom-nav {
  display: flex;
  align-items: center;
  padding: calc(env(safe-area-inset-top) + 20rpx) 30rpx 20rpx; /* 调整padding，顶部增加安全区域高度 */
  position: relative;
  margin-bottom: 20rpx;
  background: var(--nav-bg);
  border-radius: 0; /* 移除圆角 */
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  width: 100%; /* 确保宽度100% */
  box-sizing: border-box; /* 使padding包含在宽度内 */
}

.nav-left {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}

.back-icon {
  font-size: 44rpx;
  color: var(--text-light);
  font-weight: 500;
}

.nav-title {
  display: flex;
  flex-direction: column;
  flex: 1; /* 让标题占据中间所有空间 */
  padding-top: 50rpx; /* 向下移动标题文字 */
}

.title-text {
  font-size: 40rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  margin-bottom: 8rpx;
  color: var(--text-light);
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2); /* 添加文字阴影增强可读性 */
}

.subtitle-text {
  font-size: 24rpx;
  font-weight: 400;
  opacity: 0.9;
  color: var(--text-light);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.15); /* 添加文字阴影增强可读性 */
}

/* 导航栏右侧按钮 */
.nav-right {
  display: flex;
  align-items: center;
}

.nav-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconfont {
  font-size: 42rpx;
  color: var(--text-light);
}

/* 主体内容区样式 */
.filter-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 130rpx - env(safe-area-inset-top)); /* 减去导航栏高度和顶部安全区域 */
  margin-top: -20rpx; /* 让内容区上移，减少导航栏和内容区之间的间隙 */
}

/* 左侧类别列表样式 */
.category-list {
  width: 180rpx;
  background-color: var(--secondary-bg);
  overflow-y: auto;
  height: 100%;
  border-right: 1rpx solid var(--border-color);
  box-shadow: 2rpx 0 10rpx var(--shadow-color);
}

.category-item {
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid var(--border-color);
  position: relative;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: var(--card-bg);
  color: var(--active-color); /* 选中状态的颜色 */
  border-left: 4rpx solid var(--active-color);
}

.category-name {
  font-size: 28rpx;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
}

.category-count {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-top: 4rpx;
}

/* 右侧内容区样式 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--card-bg);
  height: 100%;
  overflow: hidden;
  border-radius: 20rpx 0 0 0;
  box-shadow: -2rpx 0 10rpx var(--shadow-color);
}

/* 搜索框样式 */
.search-container {
  padding: 20rpx 30rpx 10rpx;
  background-color: var(--card-bg);
  border-bottom: 1rpx solid var(--border-color);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--secondary-bg);
  border-radius: 32rpx;
  padding: 0 20rpx;
  height: 64rpx;
  position: relative;
}

.search-input {
  flex: 1;
  height: 100%;
  margin-left: 16rpx;
  font-size: 28rpx;
  color: var(--text-primary);
}

.clear-icon {
  padding: 10rpx;
}

/* 筛选按钮栏样式 */
.filter-tabs {
  display: flex;
  overflow-x: auto;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  background-color: var(--card-bg);
  white-space: nowrap;
  align-items: center;
}

.filter-tab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
  background-color: var(--secondary-bg);
  position: relative;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.filter-tab-text {
  margin-right: 6rpx;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.filter-tab-icon {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.filter-tab.active {
  background-color: var(--primary-btn);
  color: var(--text-light);
  box-shadow: 0 2rpx 8rpx var(--shadow-color);
}

.filter-tab.active .filter-tab-icon {
  color: var(--text-light);
}

.filter-tab.has-selected {
  color: var(--active-color);
  border: 1rpx solid var(--active-color);
  background-color: rgba(91, 191, 183, 0.1);
}

.clear-all-tab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: var(--active-color);
  border: 1rpx solid var(--active-color);
  margin-left: auto;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.clear-all-tab:active {
  background-color: rgba(91, 191, 183, 0.2);
}

.clear-all-icon {
  font-size: 24rpx;
  margin-right: 6rpx;
}

/* 筛选面板样式 */
.filter-panels {
  border-bottom: 1rpx solid var(--border-color);
  max-height: 300rpx;
  overflow-y: auto;
  background-color: var(--secondary-bg);
}

.filter-panel {
  padding: 20rpx 30rpx;
}

.filter-panel-content {
  display: flex;
  flex-wrap: wrap;
}

.filter-item {
  padding: 10rpx 20rpx;
  background-color: var(--card-bg);
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 15rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.filter-item.active {
  background-color: var(--primary-btn);
  color: var(--text-light);
  border: 1rpx solid var(--primary-btn);
  box-shadow: 0 2rpx 8rpx var(--shadow-color);
}

/* 衣物列表样式 */
.clothes-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--secondary-bg);
}

.clothes-list-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-primary);
  font-weight: 500;
}

.clothes-count {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.clothes-list {
  flex: 1;
  height: 0; /* 配合flex: 1使滚动生效 */
}

.clothes-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  justify-content: flex-start; /* 确保衣物从左到右排列 */
  align-items: stretch; /* 确保所有衣物高度一致 */
}

.clothes-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: var(--card-bg);
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx var(--shadow-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box; /* 确保边框不会增加元素的实际宽度 */
  border: 1rpx solid transparent; /* 添加透明边框，以便选中时不会改变元素大小 */
}

.clothes-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 5rpx var(--shadow-color);
}

.clothes-image-container {
  width: 100%;
  height: 300rpx;
  position: relative;
  background-color: #f9f9f9;
}

.clothes-image {
  width: 100%;
  height: 100%;
}

.price-tag {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  background-color: var(--primary-btn);
  color: var(--text-light);
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.clothes-info {
  padding: 15rpx;
}

.clothes-name {
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.clothes-meta {
  display: flex;
  font-size: 24rpx;
  color: var(--text-secondary);
}

.clothes-category {
  margin-right: 15rpx;
}

.no-data-tip {
  width: 100%;
  text-align: center;
  padding: 80rpx 0;
  color: var(--text-secondary);
  font-size: 28rpx;
}

/* 加载更多指示器 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  color: var(--text-secondary);
  font-size: 26rpx;
}

.loading-spinner {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid var(--secondary-bg);
  border-top: 3rpx solid var(--primary-btn);
  border-radius: 50%;
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 选中状态样式 - 参考季节选择器的实现 */
.clothes-item.selected {
  border: 3rpx solid #07c160;
  background: linear-gradient(to bottom, rgba(230, 247, 237, 0.5), rgba(212, 237, 218, 0.5));
  width: calc(50% - 20rpx); /* 确保选中状态下仍然保持每行两件衣物 */
  box-sizing: border-box; /* 确保边框不会增加元素的实际宽度 */
}

.clothes-image-container {
  position: relative;
}

/* 选择复选框 */
.selection-checkbox {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  padding: 6rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2), inset 0 0 4rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.selection-checkbox icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.clothes-item.selected .selection-checkbox {
  background: linear-gradient(145deg, #06b058, #07c160);
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.4), inset 0 0 6rpx rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  border: 1rpx solid rgba(7, 193, 96, 0.3);
}

/* 确保选中状态下不会影响布局 */
.clothes-item.selected {
  transform: none;
  margin: 10rpx;
}

.clothes-item.selected .selection-checkbox icon {
  transform: scale(1.1);
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.2));
}

/* 批量编辑工具栏 */
.selection-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.selection-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: #ffffff;
  border: 1px solid #ddd;
  font-size: 24rpx;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.selection-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.selection-btn.select-all-btn {
  padding-left: 16rpx;
}

/* 工具栏中的复选框 */
.toolbar-checkbox {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15), inset 0 0 4rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.toolbar-checkbox.selected {
  background: linear-gradient(145deg, #06b058, #07c160);
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.4), inset 0 0 4rpx rgba(255, 255, 255, 0.3);
  border: 1rpx solid rgba(7, 193, 96, 0.3);
}

.selection-btn.edit {
  background-color: #07c160;
  color: #ffffff;
  border: 1px solid #07c160;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.selection-btn.edit:active {
  background-color: #06b054;
}

.selection-btn.delete {
  background-color: #ff5252;
  color: #ffffff;
  border: 1px solid #ff5252;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 82, 82, 0.3);
}

.selection-btn.delete:active {
  background-color: #e64a4a;
}

.selection-btn.cancel {
  background-color: #f2f2f2;
  color: #333;
  border: 1px solid #ddd;
}

/* 批量编辑弹窗样式 */
.batch-edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.batch-edit-modal-content {
  width: 92%;
  max-width: 650rpx;
  max-height: 90vh;
  background-color: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.25);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(30rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.batch-edit-modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(to right, #f8f8f8, #ffffff);
}

.batch-edit-modal-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  position: relative;
  padding-left: 20rpx;
}

.batch-edit-modal-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #07c160, #09e374);
  border-radius: 4rpx;
}

.close-btn {
  font-size: 44rpx;
  color: #999;
  line-height: 1;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:active {
  background-color: #f0f0f0;
  color: #666;
}

.batch-edit-modal-body {
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.batch-edit-tip {
  font-size: 24rpx;
  color: #ff9800;
  margin-bottom: 30rpx;
  padding: 15rpx 20rpx;
  background-color: #fff9e6;
  border-radius: 12rpx;
  border-left: 6rpx solid #ff9800;
  box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.15);
  line-height: 1.5;
}

.batch-edit-row {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  padding: 20rpx 25rpx;
  border-bottom: none;
  transition: all 0.2s;
  background-color: #ffffff;
  border-radius: 15rpx;
  box-shadow:
    0 4rpx 12rpx rgba(0, 0, 0, 0.12),
    0 2rpx 4rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.batch-edit-row:last-child {
  margin-bottom: 20rpx;
}

.batch-edit-row:active {
  transform: translateY(2rpx);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.08),
    0 1rpx 2rpx rgba(0, 0, 0, 0.04);
  background-color: #fdfdfd;
}

.batch-edit-row::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(to bottom, #07c160, #09e374);
  border-radius: 3rpx 0 0 3rpx;
  opacity: 0.8;
}

.batch-edit-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  margin-right: 15rpx;
}

.batch-edit-input {
  flex: 1;
  height: 72rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
  transition: all 0.3s;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.batch-edit-input:focus {
  border-color: #07c160;
  box-shadow: 0 0 0 2rpx rgba(7, 193, 96, 0.2);
  background-color: #ffffff;
}

.batch-edit-value {
  flex: 1;
  height: 72rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f9f9f9;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  color: #333;
}

.batch-edit-value:active {
  background-color: #f0f0f0;
}

.arrow-right {
  font-size: 24rpx;
  color: #999;
}

.batch-edit-actions {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #eee;
  background-color: #ffffff;
  position: sticky;
  bottom: 0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-btn {
  margin-left: 20rpx;
  padding: 0 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.action-btn.save {
  background: linear-gradient(135deg, #07c160, #09e374);
  color: #ffffff;
  font-weight: 500;
  min-width: 200rpx;
}

.action-btn.save:active {
  background: linear-gradient(135deg, #06b058, #08d268);
}

.action-btn.cancel {
  background-color: #f2f2f2;
  color: #666;
  border: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

/* 衣物详情弹窗样式 */
.card-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 30rpx;
}

.modal-content {
  width: 90%;
  max-width: 680rpx;
  background-color: var(--card-bg);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  max-height: 80vh; /* 添加最大高度限制 */
  display: flex;
  flex-direction: column; /* 使用flex布局更好控制子元素 */
  margin-top: 10rpx; /* 将卡片向下移动10rpx */
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 2rpx solid var(--border-color);
  background: var(--nav-bg);
  color: var(--text-light);
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-light);
}

.close-btn {
  font-size: 44rpx;
  color: var(--text-light);
  padding: 0 10rpx;
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:active {
  background-color: rgba(255, 255, 255, 0.25);
  transform: scale(0.95);
}

.modal-body {
  padding: 25rpx 20rpx;
  overflow-y: auto; /* 添加垂直滚动 */
  flex: 1; /* 占用剩余空间 */
  max-height: calc(80vh - 100rpx); /* 减去header和其他元素的高度 */
  background-color: var(--primary-bg);
}

.item-image-container {
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 15rpx;
  border: 1rpx solid #f1f1f1;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.item-image {
  height: 400rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.cost-effectiveness-tag {
  position: absolute;
  bottom: 10rpx;
  left: 10rpx;
  background-color: rgba(52, 152, 219, 0.8);
  color: white;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.discard-mark {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: rgba(231, 76, 60, 0.95);
  color: white;
  font-size: 24rpx;
  padding: 6rpx 15rpx;
  border-radius: 10rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

.detail-row {
  display: flex;
  padding: 18rpx 25rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s;
}

.detail-row:active {
  background-color: #f9f9f9;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

.remark-value {
  display: block;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.6;
  color: #555;
  font-size: 26rpx;
  padding: 10rpx 0;
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  padding: 25rpx 30rpx;
  border-top: 1rpx solid #eeeeee;
  gap: 40rpx; /* 增加按钮之间的间距 */
  position: sticky;
  bottom: 0;
  background-color: #fff;
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  text-align: center;
  font-size: 30rpx;
  border-radius: 50rpx;
  margin: 0; /* 移除原来的margin */
  max-width: 280rpx; /* 限制最大宽度 */
  transition: opacity 0.2s, transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 6rpx 12rpx var(--shadow-color);
}

.action-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx var(--shadow-color);
}

.delete-btn {
  background: linear-gradient(to right, #ff7878, #ff9494);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.edit {
  background: linear-gradient(to right, #5bbfb7, #78d9b9);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 修改其他按钮颜色 */
.category-picker-actions .confirm,
.date-picker-actions .confirm,
.season-picker-actions .confirm,
.edit-actions .save,
.header-save-btn {
  background: linear-gradient(to right, #5bbfb7, #78d9b9);
}

/* 标题栏保存按钮样式 */
.header-save-btn {
  background: linear-gradient(to right, #5bbfb7, #78d9b9);
  color: white;
  border: none;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 40rpx;
  margin-right: 40rpx;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
  transition: all 0.3s;
  line-height: 1.6;
  max-width: 200rpx;
  width: auto;
}

.header-save-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 编辑弹窗样式 */
.edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 10rpx;
  z-index: 1000;
}

.edit-modal-content {
  width: 85%;
  max-height: 90vh;
  background-color: #f9f9f9;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.18),
              0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: modal-enter 0.3s ease;
}

.edit-modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eeeeee;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.edit-modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.edit-modal-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #5bbfb7, #78d9b9);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.edit-modal-header .close-btn {
  font-size: 44rpx;
  font-weight: bold;
  color: #333;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.edit-modal-header .close-btn:active {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

.edit-modal-body {
  padding: 24rpx;
  overflow-y: auto;
  max-height: 70vh;
  background-color: #f5f5f5;
}

.edit-section {
  background-color: transparent;
  margin-bottom: 24rpx;
  overflow: hidden;
  padding: 0;
  box-shadow: none;
}

.edit-row {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  padding: 20rpx 25rpx;
  border-bottom: none;
  transition: all 0.2s;
  background-color: #ffffff;
  border-radius: 15rpx;
  box-shadow:
    0 4rpx 12rpx rgba(0, 0, 0, 0.12),
    0 2rpx 4rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.edit-row:last-child {
  margin-bottom: 20rpx;
}

.edit-row:active {
  transform: translateY(2rpx);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.08),
    0 1rpx 2rpx rgba(0, 0, 0, 0.04);
  background-color: #fdfdfd;
}

.edit-row::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 3rpx 0 0 3rpx;
  opacity: 0.8;
}

.edit-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  margin-right: 15rpx;
}

.edit-input {
  flex: 1;
  height: 72rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
  transition: all 0.3s;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.edit-input:focus {
  border-color: #3399ff;
  box-shadow: 0 0 0 2rpx rgba(51, 153, 255, 0.2);
  background-color: #ffffff;
}

.edit-value {
  flex: 1;
  height: 72rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f9f9f9;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.edit-value:active {
  background-color: #f0f0f0;
}

.edit-actions {
  display: flex;
  justify-content: space-between;
  position: sticky;
  bottom: 0;
  background-color: #f5f5f5;
  padding: 20rpx 30rpx;
  z-index: 1002;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.edit-actions .action-btn {
  flex: 1;
  margin: 0 15rpx;
  height: 80rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  transition: all 0.3s;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.edit-actions .action-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.edit-actions .delete {
  background: linear-gradient(to right, #ff5733, #ff7c55);
  color: white;
  border: none;
}

.edit-actions .save {
  background: linear-gradient(to right, #3399ff, #66aaff);
  color: white;
  border: none;
}

/* 编辑输入容器样式 */
.edit-input-container {
  flex: 1;
  position: relative;
}

/* 备注文本区域样式 */
.edit-textarea {
  flex: 1;
  min-height: 120rpx;
  max-height: 300rpx;
  width: 100%;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 16rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 1.6;
  background-color: #f9f9f9;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.edit-textarea:focus {
  border-color: #3399ff;
  box-shadow: 0 0 0 2rpx rgba(51, 153, 255, 0.2);
  background-color: #ffffff;
}

/* 类别选择器样式 */
.category-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.category-picker-content {
  width: 75%;
  background-color: #f9f9f9;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.18),
              0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: modal-enter 0.3s ease;
}

.category-picker-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.category-picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.category-picker-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.category-options {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  max-height: 60vh;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.category-option {
  margin-bottom: 20rpx;
  padding: 20rpx 25rpx;
  background-color: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12),
              0 2rpx 4rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  transition: all 0.2s;
  position: relative;
  border: none;
  height: auto;
  width: auto;
  box-sizing: border-box;
  justify-content: flex-start;
}

.category-option:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08),
              0 1rpx 2rpx rgba(0, 0, 0, 0.04);
  background-color: #fdfdfd;
}

.category-option::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 3rpx 0 0 3rpx;
  opacity: 0.8;
}

.category-option text {
  margin-left: 12rpx;
}

.category-option.selected {
  background-color: #e6f7ed;
  color: #155724;
  border: none;
  box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.15);
}

.category-picker-actions {
  display: flex;
  border-top: 1rpx solid #eee;
}

.category-picker-actions .action-btn {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  border-radius: 0;
  transition: all 0.3s;
}

.category-picker-actions .cancel {
  background-color: #f8f9fa;
  color: #333;
}

.category-picker-actions .confirm {
  background: linear-gradient(to right, #3399ff, #66aaff);
  color: white;
}

/* 日期选择器样式 */
.date-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.date-picker-content {
  width: 75%;
  background-color: #f9f9f9;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.18),
              0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: modal-enter 0.3s ease;
}

.date-picker-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.date-picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.date-picker-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.date-picker-body {
  padding: 36rpx 24rpx;
  display: flex;
  justify-content: center;
  background-color: #f5f5f5;
}

.date-picker-current {
  font-size: 36rpx;
  color: #333;
  padding: 12rpx 24rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  background-color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12),
              0 2rpx 4rpx rgba(0, 0, 0, 0.08);
}

.date-picker-actions {
  display: flex;
  border-top: 1rpx solid #eee;
}

.date-picker-actions .action-btn {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  border-radius: 0;
  transition: all 0.3s;
}

.date-picker-actions .cancel {
  background-color: #f8f9fa;
  color: #333;
}

.date-picker-actions .confirm {
  background: linear-gradient(to right, #3399ff, #66aaff);
  color: white;
}

/* 季节选择器样式 */
.season-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.season-picker-content {
  width: 75%;
  background-color: #f9f9f9;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.18),
              0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: modal-enter 0.3s ease;
}

.season-picker-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.season-picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.season-picker-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3399ff, #66ccff);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.season-options {
  padding: 24rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background-color: #f5f5f5;
}

.season-option {
  width: 45%;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background-color: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12),
              0 2rpx 4rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  transition: all 0.2s;
  position: relative;
  border: none;
}

.season-option:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08),
              0 1rpx 2rpx rgba(0, 0, 0, 0.04);
  background-color: #fdfdfd;
}

.season-option icon {
  margin-right: 8rpx;
}

.season-option text {
  margin-left: 5rpx;
}

.season-option.selected {
  background-color: #e6f7ed;
  color: #155724;
  border: none;
  box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.15);
}

.season-picker-actions {
  display: flex;
  border-top: 1rpx solid #eee;
}

.season-picker-actions .action-btn {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  border-radius: 0;
  transition: all 0.3s;
}

.season-picker-actions .cancel {
  background-color: #f8f9fa;
  color: #333;
}

.season-picker-actions .confirm {
  background: linear-gradient(to right, #3399ff, #66aaff);
  color: white;
}

/* 历史值气泡样式 */
.suggestion-bubbles {
  position: absolute;
  top: -8rpx;
  left: 0;
  width: 100%;
  transform: translateY(-100%);
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
  z-index: 1003;
  padding: 10rpx;
  border: 1rpx solid #e0e0e0;
  overflow: hidden;
}

.bubbles-scroll {
  width: 100%;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

.bubble-container {
  display: flex;
  flex-wrap: nowrap;
  padding: 6rpx;
  width: max-content; /* 确保容器宽度能容纳所有子元素 */
}

.suggestion-bubble {
  display: inline-block;
  margin: 6rpx;
  padding: 8rpx 20rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #333;
  white-space: nowrap;
  transition: all 0.2s;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0; /* 防止气泡被压缩 */
  z-index: 1003
}

.suggestion-bubble:active {
  background-color: #e0e0e0;
  transform: scale(0.98);
  z-index: 1003
}

/* 隐藏滚动条但保留滚动功能 */
.bubbles-scroll::-webkit-scrollbar {
  display: none;
}

/* 自定义类别输入框 */
.custom-category-input {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid var(--border-color);
}

/* 衣柜选择器滚动区域样式 */
.wardrobe-options-scroll {
  max-height: 60vh;
  width: 100%;
  background-color: var(--secondary-bg);
}

.arrow-right {
  color: var(--text-secondary);
  font-size: 24rpx;
}

@keyframes modal-enter {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 新增卡片样式 */
.detail-card {
  background-color: var(--card-bg);
  border-radius: 15rpx;
  box-shadow:
    0 6rpx 16rpx var(--shadow-color),
    0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
  width: 100%;
}

.detail-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-primary);
  padding: 20rpx 25rpx;
  border-bottom: 1rpx solid var(--border-color);
  background: linear-gradient(to bottom, var(--card-bg), var(--secondary-bg));
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 6rpx var(--shadow-color);
  position: relative;
}

.card-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #5bbfb7, #78d9b9);
  border-radius: 4rpx;
  margin-right: 15rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.card-content {
  padding: 10rpx 0;
}


/* 增强iOS上输入框的触摸反馈 */
.edit-input:active,
.todo-form-input:active {
  opacity: 0.8;
}

/* 确保iOS上弹窗中的输入框可以正常交互 */
.edit-modal-content input {
  -webkit-appearance: none;
  z-index: 1001; /* 确保高于其他元素 */
}

/* 消除iOS上input的默认内阴影 */
input {
  -webkit-appearance: none;
  box-shadow: none !important;
}