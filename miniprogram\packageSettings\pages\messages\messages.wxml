<view class="page {{themeStyle === 'autumn' ? 'page-autumn' : (themeStyle === 'pinkBlue' ? 'page-pinkBlue' : 'page-blackWhite')}}" data-weui-theme="{{theme}}" style="background-color: {{pageStyle.backgroundColor}}; background-image: {{pageStyle.backgroundImage}};">
  <!-- 装饰元素 -->
  <view class="decoration-element decoration-1" style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.darkGray)}};"></view>
  <view class="decoration-element decoration-2" style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkMedium : blackWhiteColors.mediumGray)}};"></view>
  <view class="decoration-element decoration-3" style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueLight : blackWhiteColors.lightGray)}};"></view>

  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 页面标题 -->
    <view class="page-title" style="color: {{pageStyle.titleColor}};">
      系统消息
      <view class="clear-button" bindtap="clearAllMessages">清空</view>
    </view>

    <!-- 加载中提示 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 消息列表 -->
    <view wx:if="{{!loading && messages.length > 0}}" class="message-list">
      <view wx:for="{{messages}}" wx:key="index"
            class="message-item {{item.read ? 'message-read' : 'message-unread'}}"
            bindtap="onMessageTap" data-index="{{index}}"
            style="background-color: {{pageStyle.cellBackgroundColor}};">
        <view class="message-header">
          <view class="message-title" style="color: {{pageStyle.messageColor}};">
            {{item.title}}
            <text wx:if="{{item.hasImage}}" class="message-image-icon">🖼️</text>
          </view>
          <view class="message-time" style="color: {{pageStyle.timeColor}};">
            {{item.createTime ? util.formatTime(item.createTime) : (item.createAt ? util.formatTime(item.createAt) : '')}}
          </view>
        </view>
        <view class="message-content" style="color: {{pageStyle.titleColor}};">
          {{item.content}}
        </view>
        <view wx:if="{{!item.read}}" class="unread-dot" style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}};"></view>
      </view>
    </view>

    <!-- 无消息提示 -->
    <view wx:if="{{!loading && messages.length === 0}}" class="empty-message" style="color: {{pageStyle.titleColor}};">
      <view class="empty-icon">📭</view>
      <view class="empty-text">暂无消息通知</view>
    </view>
  </view>

  <!-- 消息详情弹窗 -->
  <view class="message-detail-overlay" wx:if="{{messageDetail.show}}" bindtap="closeMessageDetail">
    <view class="message-detail-container"
          style="background-color: {{pageStyle.detailBackgroundColor}}; box-shadow: 0 8rpx 24rpx {{pageStyle.detailShadowColor}};"
          catchtap="{{true}}">
      <view class="message-detail-header">
        <view class="message-detail-title" style="color: {{pageStyle.messageColor}};">
          {{messageDetail.title}}
        </view>
        <view class="message-detail-time" style="color: {{pageStyle.timeColor}};">
          {{messageDetail.time}}
        </view>
      </view>

      <!-- 消息内容 -->
      <view class="message-detail-content" style="color: {{pageStyle.detailTextColor}};">
        {{messageDetail.content}}
      </view>

      <!-- 消息图片 -->
      <view wx:if="{{messageDetail.hasImage}}" class="message-detail-image-container">
        <view wx:if="{{!messageDetail.imageUrl && messageDetail.imageFileID}}" class="image-loading">
          <view class="loading-spinner"></view>
          <view class="loading-text">图片加载中...</view>
        </view>
        <image wx:if="{{messageDetail.imageUrl}}"
               class="message-detail-image"
               src="{{messageDetail.imageUrl}}"
               mode="widthFix"
               bindtap="previewMessageImage"
               binderror="onImageError"
               data-fileid="{{messageDetail.imageFileID}}"></image>
      </view>

      <!-- 关闭按钮 -->
      <view class="message-detail-button"
            bindtap="closeMessageDetail"
            style="background-color: {{pageStyle.detailButtonColor}};">
        关闭
      </view>
    </view>
  </view>
</view>

<!-- 日期格式化工具 -->
<wxs module="util">
  function formatTime(timestamp) {
    if (!timestamp) return '';
    var date = getDate(timestamp);
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();

    // 小于10的数字前面补0
    month = month < 10 ? '0' + month : month;
    day = day < 10 ? '0' + day : day;
    hour = hour < 10 ? '0' + hour : hour;
    minute = minute < 10 ? '0' + minute : minute;

    return month + '-' + day + ' ' + hour + ':' + minute;
  }

  module.exports = {
    formatTime: formatTime
  };
</wxs>