// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    return {
      success: false,
      error: '未获取到用户身份'
    }
  }
  
  try {
    // 查询用户的自定义穿搭类型
    const result = await db.collection('customOutfitCategories')
      .where({
        _openid: openid
      })
      .get()
    
    if (result.data && result.data.length > 0) {
      return {
        success: true,
        data: result.data[0].categories || []
      }
    } else {
      return {
        success: true,
        data: []
      }
    }
  } catch (error) {
    console.error('获取自定义穿搭类型失败:', error)
    return {
      success: false,
      error: error.message || '获取失败'
    }
  }
}
