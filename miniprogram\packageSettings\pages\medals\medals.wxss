/* 页面容器 */
.page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 0;
  box-sizing: border-box;
}

/* 勋章滚动视图 */
.medals-scroll-view {
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}

/* 勋章容器 */
.medals-container {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  padding: 30rpx;
}

/* 勋章分区 */
.medals-section {
  background-color: white;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.1), 0 5rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.medals-section-title {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.medals-section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #a4e75a;
  border-radius: 4rpx;
}

/* 勋章网格 - 通用样式 */
.medals-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  justify-content: center;
}

/* 已获得勋章项目布局 */
.medals-section:nth-child(1) .medal-item {
  width: 90%;
  max-width: 300rpx;
  margin: 0 auto;
  padding: 20rpx;
}

/* 已获得勋章图片容器 */
.medals-section:nth-child(1) .medal-image-container {
  width: 140rpx;
  height: 140rpx;
}

/* 已获得勋章图片 */
.medals-section:nth-child(1) .medal-image {
  width: 120rpx;
  height: 120rpx;
}

/* 未获得勋章项目布局 */
.medals-section:nth-child(2) .medal-item {
  width: 90%;
  max-width: 300rpx;
  margin: 0 auto;
  padding: 30rpx 20rpx;
}

/* 未获得勋章图片容器 */
.medals-section:nth-child(2) .medal-image-container {
  width: 140rpx;
  height: 140rpx;
}

/* 未获得勋章图片 */
.medals-section:nth-child(2) .medal-image {
  width: 120rpx;
  height: 120rpx;
}

/* 隐藏未获得勋章 */
.hide-unearned-medals {
  display: none;
}

/* 勋章项 */
.medal-item {
  width: 200rpx;
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  top: 0;
}

.medal-item:hover {
  top: -5rpx;
  box-shadow: 0 12rpx 25rpx rgba(0, 0, 0, 0.18), 0 6rpx 12rpx rgba(0, 0, 0, 0.12);
}

.medal-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1), 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.medal-item-unearned {
  background-color: rgba(255, 255, 255, 0.5);
  width: auto;
  margin: 0 auto;
  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.1), 0 3rpx 6rpx rgba(0, 0, 0, 0.05);
}

/* 勋章图片容器 */
.medal-image-container {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.medal-image {
  width: 100rpx;
  height: 100rpx;
  object-fit: contain;
  transition: transform 0.3s ease;
  cursor: pointer;
  position: relative;
  filter: drop-shadow(0 0 5rpx rgba(255, 215, 0, 0.3));
}

.medal-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.9) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-25deg);
  animation: shimmer 4s infinite ease-in-out;
  z-index: 2;
  box-shadow: 0 0 10rpx 3rpx rgba(255, 255, 255, 0.2);
}

.medal-image:active {
  transform: scale(0.95);
}

.medal-image:active::after {
  opacity: 1;
}

.medal-image-unearned {
  opacity: 0.5;
  filter: grayscale(100%);
}

.medal-image-unearned::after {
  display: none;
}

/* 勋章名称 */
.medal-name {
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 5rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* 勋章日期 */
.medal-date {
  font-size: 22rpx;
  text-align: center;
  color: #999;
}

/* 勋章提示 */
.medal-hint {
  font-size: 22rpx;
  text-align: center;
  color: #999;
  font-style: italic;
}

/* 无勋章提示 */
.no-medals-message {
  width: 100%;
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}

/* 勋章详情模态框 */
.medal-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  width: 100vw;
  height: 100vh;
}

.medal-detail-modal.visible {
  opacity: 1;
  visibility: visible;
}

.medal-detail-container {
  width: 90%;
  max-width: 650rpx;
  position: relative;
  max-height: 85vh;
  overflow-y: auto;
  margin: 0 auto;
}

/* 白色内容卡片 */
.medal-detail-content-card {
  width: 100%;
  background-color: white;
  border-radius: 15rpx;
  padding: 50rpx 30rpx 30rpx;
  box-shadow: 0 25rpx 50rpx rgba(0, 0, 0, 0.35), 0 15rpx 20rpx rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  margin: 0 auto;
}

/* 勋章详情标题 */
.medal-detail-header {
  text-align: center;
  margin-bottom: 30rpx;
  width: 100%;
}

.medal-detail-title {
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  background-color: #000;
  color: #fff !important;
  padding: 15rpx 30rpx;
  border-radius: 10rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  position: relative;
  max-width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.medal-title-text {
  display: inline-block;
  padding: 0 5rpx;
}

/* Decorative horizontal lines */
.medal-detail-title::before,
.medal-detail-title::after {
  content: '';
  display: inline-block;
  width: 70rpx;
  height: 3rpx;
  background: linear-gradient(90deg, rgba(255,255,255,0.2), #fff, rgba(255,255,255,0.2));
  margin: 0 15rpx;
  position: relative;
  vertical-align: middle;
  box-shadow: 0 0 5rpx rgba(255, 255, 255, 0.7);
}

.medal-detail-title::before {
  margin-right: 20rpx;
}

.medal-detail-title::after {
  margin-left: 20rpx;
}

/* 勋章详情图片 */
.medal-detail-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
  width: 100%;
}

/* Removed the gold outer glow */

.medal-detail-image {
  width: 280rpx;
  height: 280rpx;
  object-fit: contain;
  transition: transform 0.3s ease;
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.medal-detail-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.9) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-25deg);
  animation: shimmer 4s infinite ease-in-out;
  box-shadow: 0 0 15rpx 5rpx rgba(255, 255, 255, 0.3);
}

@keyframes shimmer {
  0% {
    left: -150%;
  }
  50% {
    left: 150%;
  }
  50.01% {
    left: -150%;
  }
  100% {
    left: -150%;
  }
}

/* Removed the gold glow animation */

.medal-detail-image:active {
  transform: scale(0.95);
}

.medal-detail-image-unearned {
  opacity: 0.5;
  filter: grayscale(100%);
}

.medal-detail-image-unearned::after {
  display: none;
}

.medal-image-hint {
  font-size: 22rpx;
  color: #888;
  margin-top: 15rpx;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 勋章信息卡片 - 新样式 */
.medal-detail-info-card {
  width: 100%;
  background-color: #f9f9f9;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-sizing: border-box;
}

/* 勋章描述容器 */
.medal-description-container {
  font-size: 32rpx;
  line-height: 1.6;
  text-align: center;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

/* 勋章提示容器 */
.medal-hint-container {
  font-size: 28rpx;
  text-align: center;
  color: #666;
  font-style: italic;
  margin-bottom: 20rpx;
}

/* 勋章编号容器 */
.medal-number-container {
  background-color: #222;
  border-radius: 10rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
  margin-top: 20rpx;
  border: 1px solid rgba(255, 215, 0, 0.3);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2), inset 0 1rpx 3rpx rgba(255, 255, 255, 0.1);
}

/* 勋章全局编号 */

.medal-global-number::after {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: none;
  text-shadow: 0 0 8rpx rgba(255, 215, 0, 0.8);
  -webkit-text-fill-color: transparent;
}

/* Add animation to the gradient */
@keyframes shimmerGradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.medal-global-number {
  background: linear-gradient(45deg, #ffffff 0%, #fdbe2f  25%, #ffffff 50%, #fdbe2f  75%, #ffffff 100%);
  background-size: 200% 200%;
  animation: shimmerGradient 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 30rpx;
  font-weight: 700;
  text-align: center;
  position: relative;
  padding: 5rpx 0;
  margin-bottom: 5rpx;
}

/* 获得时间 */
.medal-earned-time {
  font-size: 24rpx;
  color: #aaa;
  text-align: center;
  padding: 5rpx 15rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  width: 90%;
  padding-top: 10rpx;
}

/* Removed medal-detail-button styles */

/* 关闭按钮 */
.medal-detail-close-button {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 46rpx;
  color: #333;
  cursor: pointer;
  background-color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  z-index: 10;
  padding: 20rpx;
  margin: -10rpx;
}

.medal-detail-close-button:active {
  transform: scale(0.95);
  background-color: #333;
  box-shadow: 0 3rpx 6rpx rgba(0, 0, 0, 0.2);
}

/* 切换按钮容器 */
.toggle-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-left: 20rpx;
}

/* 切换按钮 */
.toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: #f0f0f0;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  font-size: 22rpx;
  color: #666;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.toggle-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.5) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-25deg);
  animation: toggleButtonShimmer 2s infinite;
}

@keyframes toggleButtonShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 200%;
  }
}

.toggle-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  background-color: #e0e0e0;
}

/* 主题样式 */
.page-autumn {
  /* 秋季主题特定样式 */
}

.page-pinkBlue {
  /* 粉蓝主题特定样式 */
}

.page-blackWhite {
  /* 黑白主题特定样式 */
}
