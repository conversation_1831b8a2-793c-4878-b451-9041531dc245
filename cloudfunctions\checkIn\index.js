// 每日签到云函数
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const userOpenId = event.userOpenId || wxContext.OPENID

  if (!userOpenId) {
    return {
      success: false,
      error: '无法获取用户ID'
    }
  }

  try {
    // 获取当前日期（格式：YYYY-MM-DD）
    const today = new Date()
    const dateString = today.getFullYear() + '-' +
                     String(today.getMonth() + 1).padStart(2, '0') + '-' +
                     String(today.getDate()).padStart(2, '0')

    // 查询用户今日是否已签到
    const checkInsCollection = db.collection('checkIns')
    const todayCheckIn = await checkInsCollection.where({
      userOpenId: userOpenId,
      date: dateString
    }).get()

    // 如果今日已签到，返回已签到信息
    if (todayCheckIn.data && todayCheckIn.data.length > 0) {
      return {
        success: true,
        alreadyCheckedIn: true,
        checkInData: todayCheckIn.data[0]
      }
    }

    // 查询用户最近的签到记录，按日期降序排列
    const recentCheckIns = await checkInsCollection.where({
      userOpenId: userOpenId
    }).orderBy('date', 'desc').limit(1).get()

    let consecutiveDays = 1
    let lastCheckInDate = null

    // 如果有最近的签到记录，检查是否是连续签到
    if (recentCheckIns.data && recentCheckIns.data.length > 0) {
      lastCheckInDate = recentCheckIns.data[0].date

      // 计算昨天的日期字符串
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayString = yesterday.getFullYear() + '-' +
                            String(yesterday.getMonth() + 1).padStart(2, '0') + '-' +
                            String(yesterday.getDate()).padStart(2, '0')

      console.log('检查连续签到:', {
        lastCheckInDate: lastCheckInDate,
        yesterdayString: yesterdayString,
        lastConsecutiveDays: recentCheckIns.data[0].consecutiveDays
      })

      // 如果最后签到日期是昨天，则连续天数+1
      if (lastCheckInDate === yesterdayString) {
        consecutiveDays = (recentCheckIns.data[0].consecutiveDays || 0) + 1
        console.log('连续签到，天数+1:', consecutiveDays)
      } else {
        console.log('非连续签到，重置为1天')
        consecutiveDays = 1
      }
    } else {
      console.log('首次签到，设置为1天')
    }

    // 查询用户信息，检查是否是VIP会员
    const userInfo = await db.collection('users').where({
      _openid: userOpenId
    }).get()

    let isVIP = false
    if (userInfo.data && userInfo.data.length > 0) {
      const user = userInfo.data[0]
      // 检查用户是否是VIP会员且会员未过期
      isVIP = user.memberType === 'VIP' && new Date(user.memberExpireDate) > today
    }

    // 计算奖励
    let energyReward = 10 // 默认奖励10点体力值
    let storageReward = 0 // 默认不奖励存储空间

    // 如果是VIP会员，奖励翻倍
    if (isVIP) {
      energyReward *= 2
    }

    // 如果连续签到7天，奖励2个衣物存储空间
    if (consecutiveDays % 7 === 0) {
      storageReward = 2
      // 如果是VIP会员，存储空间奖励也翻倍
      if (isVIP) {
        storageReward *= 2
      }
    }

    // 开始事务
    const transaction = await db.startTransaction()

    try {
      // 1. 创建签到记录
      const checkInResult = await transaction.collection('checkIns').add({
        data: {
          userOpenId: userOpenId,
          date: dateString,
          checkInTime: db.serverDate(),
          consecutiveDays: consecutiveDays,
          energyReward: energyReward,
          storageReward: storageReward,
          isVIP: isVIP
        }
      })

      // 2. 更新用户体力值
      if (energyReward > 0) {
        // 获取当前体力值
        const userData = userInfo.data[0]
        const currentEnergy = userData.catEnergy || 0
        const maxEnergy = 500 // 最大体力值

        // 计算新的体力值，不超过最大值
        const newEnergy = Math.min(maxEnergy, currentEnergy + energyReward)

        // 更新用户体力值
        await transaction.collection('users').doc(userData._id).update({
          data: {
            catEnergy: newEnergy,
            updatedAt: db.serverDate()
          }
        })

        // 记录体力值变化日志
        await transaction.collection('energyChangeLogs').add({
          data: {
            userOpenId: userOpenId,
            changeType: 'increase',
            changeAmount: energyReward,
            reason: '每日签到奖励',
            beforeValue: currentEnergy,
            afterValue: newEnergy,
            createdAt: db.serverDate()
          }
        })
      }

      // 3. 如果有存储空间奖励，更新用户衣物上限
      if (storageReward > 0) {
        const userData = userInfo.data[0]
        const currentClothesLimit = userData.clothesLimit || 30

        // 更新用户衣物上限
        await transaction.collection('users').doc(userData._id).update({
          data: {
            clothesLimit: currentClothesLimit + storageReward,
            updatedAt: db.serverDate()
          }
        })

        // 记录衣物上限增加的日志
        await transaction.collection('userLimitChangeLogs').add({
          data: {
            userOpenId: userOpenId,
            changeType: 'increase',
            limitType: 'clothesLimit',
            changeAmount: storageReward,
            reason: '连续签到奖励',
            beforeValue: currentClothesLimit,
            afterValue: currentClothesLimit + storageReward,
            createdAt: db.serverDate()
          }
        })
      }

      // 提交事务
      await transaction.commit()

      // 返回签到结果
      return {
        success: true,
        alreadyCheckedIn: false,
        checkInData: {
          userOpenId: userOpenId,
          date: dateString,
          consecutiveDays: consecutiveDays,
          energyReward: energyReward,
          storageReward: storageReward,
          isVIP: isVIP
        }
      }
    } catch (err) {
      // 回滚事务
      await transaction.rollback()
      console.error('签到事务执行失败:', err)
      throw err
    }
  } catch (error) {
    console.error('签到失败:', error)
    return {
      success: false,
      error: error.message || '签到失败'
    }
  }
}

// 辅助函数：格式化日期为YYYY-MM-DD
function formatDate(date) {
  return date.getFullYear() + '-' +
         String(date.getMonth() + 1).padStart(2, '0') + '-' +
         String(date.getDate()).padStart(2, '0')
}
