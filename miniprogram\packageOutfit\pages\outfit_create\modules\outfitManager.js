/**
 * 搭配管理模块
 * 负责搭配数据的保存和管理
 */

const canvasManager = require('./canvasManager');
const imageManager = require('./imageManager');
// 导入限制管理模块
const limitManager = require('../../../common/limitManager');

/**
 * 生成搭配图片
 * @param {string} canvasId - 画布ID
 * @param {Array} canvasItems - 画布项数组
 * @param {number} canvasWidth - 画布宽度
 * @param {number} canvasHeight - 画布高度
 * @returns {Promise<string>} 包含上传后文件ID的Promise
 */
function generateOutfitImage(canvasId, canvasItems, canvasWidth, canvasHeight) {
  return new Promise(async (resolve, reject) => {
    try {
      // 先绘制画布
      await canvasManager.drawCanvas(canvasId, canvasItems, canvasWidth, canvasHeight);

      // 延迟一下确保绘制完成
      setTimeout(async () => {
        try {
          // 将画布转为临时文件
          const tempFilePath = await imageManager.generateImageFromCanvas(canvasId);

          // 上传图片到云存储
          const fileID = await imageManager.uploadOutfitImage(tempFilePath);

          // 返回文件ID
          resolve(fileID);
        } catch (error) {
          console.error('生成或上传搭配图片失败:', error);
          reject(error);
        }
      }, 300);
    } catch (error) {
      console.error('绘制画布失败:', error);
      reject(error);
    }
  });
}

/**
 * 检查用户是否为VIP会员
 * @param {String} userOpenId - 用户OpenID
 * @returns {Promise<Boolean>} 如果是VIP会员返回true，否则返回false
 */
function checkIsVipMember(userOpenId) {
  return new Promise((resolve, reject) => {
    const db = wx.cloud.database();

    db.collection('users')
      .where({
        _openid: userOpenId
      })
      .get()
      .then(res => {
        if (res.data && res.data.length > 0) {
          const userInfo = res.data[0];

          // 检查会员类型和过期时间
          const isVip = userInfo.memberType === 'VIP' &&
                      userInfo.memberExpireDate &&
                      new Date(userInfo.memberExpireDate) > new Date();

          resolve(isVip);
        } else {
          resolve(false);
        }
      })
      .catch(err => {
        console.error('获取用户会员信息失败:', err);
        resolve(false); // 出错时默认为非VIP
      });
  });
}

/**
 * 保存搭配数据到数据库
 * @param {string} outfitName - 搭配名称
 * @param {string} imageFileID - 搭配图片文件ID
 * @param {Array} canvasItems - 画布项数组
 * @param {string} category - 搭配类型
 * @returns {Promise<Object>} 包含保存结果的Promise
 */
function saveOutfitToDatabase(outfitName, imageFileID, canvasItems, category = 'daily') {
  return new Promise((resolve, reject) => {
    // 获取应用实例
    const app = getApp();

    // 首先尝试从本地缓存获取openid
    let cachedOpenId = wx.getStorageSync('openid');

    if (cachedOpenId) {
      // 如果缓存中有openid，直接使用
      proceedWithSaving(cachedOpenId);
    } else {
      // 如果缓存中没有，则使用app中的方法获取
      app.getUserOpenId((err, openid) => {
        if (err || !openid) {
          console.error('无法获取用户OpenID，无法保存搭配', err);
          reject(new Error('无法获取用户OpenID，请检查登录状态'));
          return;
        }

        // 获取成功后，保存到本地缓存
        wx.setStorageSync('openid', openid);
        proceedWithSaving(openid);
      });
    }

    // 使用openid继续保存过程
    function proceedWithSaving(userOpenId) {
      // 首先检查用户是否为VIP会员
      checkIsVipMember(userOpenId)
        .then(isVip => {
          if (isVip) {
            // VIP会员不检查穿搭限制，直接保存
            console.log('VIP会员，跳过穿搭限制检查');
            wx.showToast({
              title: '已使用会员权限，无限制创建穿搭',
              icon: 'success',
              duration: 2000
            });
            return saveOutfitData(userOpenId);
          } else {
            // 非VIP会员，检查用户穿搭数量是否达到限制
            return limitManager.checkOutfitsLimit(userOpenId)
              .then(canAddOutfit => {
                if (!canAddOutfit) {
                  // 如果已达到限制，拒绝添加并返回错误
                  return Promise.reject(new Error('您的穿搭数量已达到限制，无法创建更多穿搭，请升级会员'));
                }
                return saveOutfitData(userOpenId);
              });
          }
        })
        .then(result => {
          resolve(result);
        })
        .catch(err => {
          console.error('保存穿搭失败:', err);
          reject(err);
        });
    }

    // 保存穿搭数据的函数
    function saveOutfitData(userOpenId) {
      // 处理画布项，确保它们是可以存储的格式
      const processedItems = canvasItems.map(item => {
        // 如果画布项不包含衣物ID，则添加一个空ID
        if (!item.clothesId && item.type === 'clothes') {
          item.clothesId = '';
        }
        return item;
      }).filter(item => item !== null);

      // 创建数据库记录对象
      const db = wx.cloud.database();
      const outfitsCollection = db.collection('outfits');
      const now = new Date();

      // 构造搭配数据
      const outfitData = {
        name: outfitName || '未命名搭配',
        imageFileID: imageFileID || '',
        items: processedItems,
        category: category || 'daily', // 主要类型字段
        type: category || 'daily',     // 兼容性类型字段，确保两个字段都设置
        createTime: now,
        updateTime: now,
        isDefault: false
      };

      console.log('保存到数据库的搭配数据:', {
        name: outfitData.name,
        category: outfitData.category,
        type: outfitData.type
      });

      // 添加到数据库
      return outfitsCollection.add({
        data: outfitData
      })
        .then(res => {
          console.log('搭配保存成功:', res);
          wx.setStorageSync('needRefreshOutfits', true);
          wx.setStorageSync('needRefreshOutfitSummary', true);
          wx.setStorageSync('needRefreshWardrobeSummary', true)
          // 更新用户穿搭计数
          limitManager.updateOutfitsCount(userOpenId, 1)
            .then(() => {
              console.log('更新穿搭计数成功');

              // 设置全局标记，指示限制数据需要刷新
              app.globalData.limitDataNeedRefresh = true;
            })
            .catch(err => {
              console.error('更新穿搭计数失败:', err);
            });

          // 清除穿搭页面的本地缓存，确保数据更新
          try {
            // 清除主页穿搭缓存
            const outfitsCacheKey = 'outfit_page_outfits_cache_' + userOpenId;
            wx.removeStorage({
              key: outfitsCacheKey,
              success: () => {
                console.log('成功清除穿搭页面缓存');
              },
              fail: (err) => {
                console.warn('清除穿搭页面缓存失败:', err);
              }
            });

            // 清除分类页面的穿搭缓存
            const keys = wx.getStorageInfoSync().keys;
            keys.forEach(key => {
              if (key.startsWith('outfit_page_outfits_cache_')) {
                wx.removeStorage({
                  key: key,
                  fail: (err) => {
                    console.warn(`清除缓存失败: ${key}`, err);
                  }
                });
              }
            });

            console.log('已清除所有穿搭相关缓存，确保数据更新');
          } catch (cacheErr) {
            console.warn('清除缓存出错:', cacheErr);
          }

          // 返回保存结果
          return {
            success: true,
            outfitId: res._id,
            outfitData: { ...outfitData, _id: res._id }
          };
        })
        .catch(err => {
          console.error('搭配保存失败:', err);
          throw err;
        });
    }
  });
}

/**
 * 获取用户保存的搭配列表
 * @returns {Promise<Array>} 包含搭配列表的Promise
 */
function getOutfitList() {
  return new Promise((resolve, reject) => {
    // 获取应用实例
    const app = getApp();

    // 首先尝试从本地缓存获取openid
    let cachedOpenId = wx.getStorageSync('openid');

    if (cachedOpenId) {
      // 如果缓存中有openid，直接使用
      fetchOutfits(cachedOpenId);
    } else {
      // 如果缓存中没有，则使用app中的方法获取
      app.getUserOpenId((err, openid) => {
        if (err || !openid) {
          console.error('无法获取用户OpenID，无法获取搭配列表', err);
          reject(new Error('无法获取用户OpenID，请检查登录状态'));
          return;
        }

        // 获取成功后，保存到本地缓存
        wx.setStorageSync('openid', openid);
        fetchOutfits(openid);
      });
    }

    // 使用openid获取搭配列表
    function fetchOutfits(userOpenId) {
      const db = wx.cloud.database();

      db.collection('outfits')
        .where({
          _openid: userOpenId
        })
        .orderBy('createTime', 'desc')
        .get()
        .then(res => {
          resolve(res.data);
        })
        .catch(err => {
          console.error('获取搭配列表失败:', err);
          reject(err);
        });
    }
  });
}

/**
 * 删除搭配记录
 * @param {string} outfitId - 搭配ID
 * @returns {Promise<object>} 包含删除结果的Promise
 */
function deleteOutfit(outfitId) {
  return new Promise((resolve, reject) => {
    // 获取应用实例
    const app = getApp();

    // 首先尝试从本地缓存获取openid
    let cachedOpenId = wx.getStorageSync('openid');

    if (cachedOpenId) {
      // 如果缓存中有openid，直接使用
      proceedWithDeletion(cachedOpenId);
    } else {
      // 如果缓存中没有，则使用app中的方法获取
      app.getUserOpenId((err, openid) => {
        if (err || !openid) {
          console.error('无法获取用户OpenID，无法删除搭配', err);
          reject(new Error('无法获取用户OpenID，请检查登录状态'));
          return;
        }

        // 获取成功后，保存到本地缓存
        wx.setStorageSync('openid', openid);
        proceedWithDeletion(openid);
      });
    }

    // 使用openid继续删除过程
    function proceedWithDeletion(userOpenId) {
      const db = wx.cloud.database();

      // 先获取搭配信息
      db.collection('outfits')
        .doc(outfitId)
        .get()
        .then(res => {
          const outfitData = res.data;

          // 删除搭配记录
          return db.collection('outfits')
            .doc(outfitId)
            .remove()
            .then(() => outfitData);
        })
        .then(outfitData => {
          // 如果搭配有图片，删除云存储中的图片
          if (outfitData.imageFileID) {
            return wx.cloud.deleteFile({
              fileList: [outfitData.imageFileID]
            }).then(() => outfitData);
          }
          return outfitData;
        })
        .then(outfitData => {
          // 更新用户穿搭计数
          return limitManager.updateOutfitsCount(userOpenId, -1)
            .then(() => {
              console.log('更新穿搭计数成功');

              // 设置全局标记，指示限制数据需要刷新
              app.globalData.limitDataNeedRefresh = true;

              return outfitData;
            })
            .catch(err => {
              console.error('更新穿搭计数失败:', err);
              return outfitData;
            });
        })
        .then(outfitData => {
          console.log('搭配删除成功');
          resolve({
            success: true,
            outfitData
          });
        })
        .catch(err => {
          console.error('删除搭配失败:', err);
          reject(err);
        });
    }
  });
}

/**
 * 生成搭配预览数据
 * @param {Object} outfit - 搭配数据
 * @returns {Promise<Object>} 包含预览数据的Promise
 */
function generateOutfitPreview(outfit) {
  return new Promise((resolve, reject) => {
    if (!outfit || !outfit.imageFileID) {
      reject(new Error('无效的搭配数据'));
      return;
    }

    // 获取搭配图片的临时URL
    wx.cloud.getTempFileURL({
      fileList: [outfit.imageFileID],
      success: res => {
        const fileList = res.fileList;
        if (fileList && fileList.length > 0) {
          const tempFileURL = fileList[0].tempFileURL;

          // 构建预览数据
          const previewData = {
            ...outfit,
            tempImageUrl: tempFileURL,
            formatDate: formatDate(outfit.createTime)
          };

          resolve(previewData);
        } else {
          reject(new Error('获取图片临时URL失败'));
        }
      },
      fail: err => {
        console.error('获取搭配图片URL失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
  if (!date) return '';

  // 如果是时间戳或字符串，转为Date对象
  if (!(date instanceof Date)) {
    date = new Date(date);
  }

  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  return `${year}.${month}.${day}`;
}

/**
 * 获取完整的穿搭数据（包括衣物详情）
 * @param {string} outfitId - 穿搭ID
 * @returns {Promise<Object>} 包含完整穿搭数据的Promise
 */
function getCompleteOutfitData(outfitId) {
  return new Promise((resolve, reject) => {
    if (!outfitId) {
      console.error('穿搭ID不能为空');
      reject(new Error('穿搭ID不能为空'));
      return;
    }

    const db = wx.cloud.database();

    db.collection('outfits')
      .doc(outfitId)
      .get()
      .then(res => {
        if (!res.data) {
          throw new Error('未找到穿搭数据');
        }

        const outfitData = res.data;
        resolve(outfitData);
      })
      .catch(err => {
        console.error('获取完整穿搭数据失败:', err);
        reject(err);
      });
  });
}

/**
 * 更新衣物的穿着统计数据
 * @param {Array} items - 衣物项数组
 * @returns {Promise<void>} Promise
 */
function updateClothingWearStats(items) {
  return new Promise(async (resolve, reject) => {
    if (!items || !Array.isArray(items) || items.length === 0) {
      console.log('没有衣物需要更新穿着统计');
      resolve();
      return;
    }

    const db = wx.cloud.database();
    const _ = db.command;

    try {
      console.log('开始更新衣物穿着统计，衣物数量:', items.length);

      // 获取今天的日期
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // 格式化日期为字符串格式: YYYY-MM-DD
      const formattedDate = formatDate(today);
      console.log('格式化后的穿着日期:', formattedDate);

      // 收集所有需要更新的衣物ID
      const clothingIds = [];

      items.forEach(item => {
        // 检查是否有clothingId
        if (item.clothingId) {
          clothingIds.push(item.clothingId);
        // 尝试从originalClothing中获取ID
        } else if (item.originalClothing && item.originalClothing._id) {
          clothingIds.push(item.originalClothing._id);
        }
      });

      if (clothingIds.length === 0) {
        console.log('没有有效的衣物ID，跳过更新穿着统计');
        resolve();
        return;
      }

      console.log('需要更新的衣物ID:', clothingIds);

      // 更新每个衣物的穿着次数和最近穿着时间
      const updatePromises = clothingIds.map(clothingId => {
        return db.collection('clothes')
          .doc(clothingId)
          .update({
            data: {
              // 穿着次数+1
              wornCount: _.inc(1),
              // 更新最近穿着时间为格式化的字符串
              lastWornDate: formattedDate
            }
          })
          .then(res => {
            console.log(`衣物[${clothingId}]穿着统计更新成功:`, res);
            return res;
          })
          .catch(err => {
            console.error(`衣物[${clothingId}]穿着统计更新失败:`, err);
            // 单个衣物更新失败不影响整体流程
            return null;
          });
      });

      // 等待所有更新完成
      await Promise.all(updatePromises);
      console.log('所有衣物穿着统计更新完成');
      resolve();
    } catch (err) {
      console.error('更新衣物穿着统计时发生错误:', err);
      // 出错时也视为完成，不影响主流程
      resolve();
    }
  });
}

/**
 * 保存今日穿搭
 * @param {Object} outfitData - 穿搭数据对象
 * @param {string} userOpenId - 用户OpenID
 * @param {string} existingRecordId - 现有记录ID（可选）
 * @returns {Promise<Object>} 包含保存结果的Promise
 */
function saveTodayOutfit(outfitData, userOpenId, existingRecordId) {
  return new Promise(async (resolve, reject) => {
    if (!outfitData || !userOpenId) {
      console.error('穿搭数据或用户ID不能为空');
      reject(new Error('穿搭数据或用户ID不能为空'));
      return;
    }

    console.log('保存今日穿搭记录:', outfitData);

    const db = wx.cloud.database();

    // 获取今天的日期
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 准备要保存的数据
    const saveData = {
      outfitId: outfitData._id || outfitData.id,
      outfitName: outfitData.name,
      outfitType: outfitData.type || outfitData.category,
      date: today,
      //_openid: userOpenId,
      createTime: db.serverDate()
    };

    try {
      // 如果outfitData中没有完整的items数据，尝试获取完整的穿搭数据
      let completeOutfitData = outfitData;

      if (!outfitData.items || outfitData.items.length === 0) {
        try {
          completeOutfitData = await getCompleteOutfitData(saveData.outfitId);
          console.log('获取到完整的穿搭数据:', completeOutfitData);
        } catch (err) {
          console.warn('无法获取完整穿搭数据，将使用原始数据:', err);
        }
      }

      // 更新衣物的穿着统计数据（不阻塞主流程）
      if (completeOutfitData.items && completeOutfitData.items.length > 0) {
        updateClothingWearStats(completeOutfitData.items)
          .then(() => {
            console.log('衣物穿着统计更新成功');
          })
          .catch(err => {
            console.error('衣物穿着统计更新失败:', err);
          });
      }

      // 如果有现有记录，则更新
      if (existingRecordId) {
        db.collection('dailyOutfits')
          .doc(existingRecordId)
          .update({
            data: {
              outfitId: saveData.outfitId,
              outfitName: saveData.outfitName,
              outfitType: saveData.outfitType,
              updateTime: db.serverDate()
            }
          })
          .then(res => {
            console.log('更新今日穿搭成功:', res);
            resolve({
              success: true,
              message: '今日穿搭已更新',
              data: {
                ...saveData,
                _id: existingRecordId
              }
            });
          })
          .catch(err => {
            console.error('更新今日穿搭失败:', err);
            reject(err);
          });
      } else {
        // 否则新建记录
        db.collection('dailyOutfits')
          .add({
            data: saveData
          })
          .then(res => {
            console.log('保存今日穿搭成功:', res);
            resolve({
              success: true,
              message: '今日穿搭已保存',
              data: {
                ...saveData,
                _id: res._id
              }
            });

            // 设置标记，通知页面刷新
            wx.setStorageSync('needRefreshOutfits', true);
          })
          .catch(err => {
            console.error('保存今日穿搭失败:', err);
            reject(err);
          });
      }
    } catch (err) {
      console.error('保存今日穿搭过程中发生错误:', err);
      reject(err);
    }
  });
}

module.exports = {
  generateOutfitImage,
  saveOutfitToDatabase,
  getOutfitList,
  deleteOutfit,
  generateOutfitPreview,
  saveTodayOutfit,
  checkIsVipMember
};
