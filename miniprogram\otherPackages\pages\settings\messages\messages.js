Page({
  data: {
    messages: [],
    loading: true,
    themeStyle: 'autumn',  // 默认主题
    colors: {
      cowhide_cocoa: '#442D1C',   // 深棕色
      spiced_wine: '#74301C',     // 红棕色
      toasted_caramel: '#84592B', // 焦糖色
      olive_harvest: '#9D9167',   // 橄榄色
      golden_batter: '#E8D1A7',   // 金黄色
    },
    pinkBlueColors: {
      pinkDark: '#D47C99',       // 深粉色
      pinkMedium: '#EEA0B2',     // 中粉色
      pinkLight: '#F9C9D6',      // 浅粉色
      blueLight: '#CBE0F9',      // 浅蓝色
      blueMedium: '#97C8E5',     // 中蓝色
      blueDark: '#5EA0D0',       // 深蓝色
    },
    blackWhiteColors: {
      black: '#000000',          // 纯黑
      darkGray: '#333333',      // 深灰
      mediumGray: '#666666',    // 中灰
      lightGray: '#CCCCCC',     // 浅灰
      white: '#FFFFFF',         // 纯白
      offWhite: '#dee3ec',      // 灰白
    },
    pageStyle: {},  // 页面样式对象
    // 消息详情弹窗
    messageDetail: {
      show: false,
      title: '',
      content: '',
      imageUrl: '',
      imageFileID: '',
      time: '',
      hasImage: false
    },
    // 图片缓存配置
    imageCacheConfig: {
      cacheKey: 'messageImageCache',
      cacheDuration: 7 * 24 * 60 * 60 * 1000 // 7天缓存有效期
    }
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '消息通知'
    });

    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });

      // 应用主题样式
      this.applyThemeStyle(savedTheme);
    } else {
      // 默认应用秋季主题
      this.applyThemeStyle('autumn');
    }

    // 加载消息数据
    this.loadMessages();
  },

  // 应用主题样式
  applyThemeStyle: function(themeName) {
    // 更新页面样式
    let pageStyle = {};

    if (themeName === 'autumn') {
      // 设置秋季主题样式
      pageStyle = {
        backgroundColor: this.data.colors.golden_batter,
        backgroundImage: 'none',
        titleColor: this.data.colors.cowhide_cocoa,
        cellBackgroundColor: 'rgba(255, 255, 255, 0.7)',
        timeColor: this.data.colors.cowhide_cocoa,
        messageColor: this.data.colors.spiced_wine,
        decorationColors: [
          this.data.colors.olive_harvest,
          this.data.colors.spiced_wine,
          this.data.colors.toasted_caramel
        ],
        detailBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        detailShadowColor: 'rgba(116, 48, 28, 0.2)',
        detailButtonColor: this.data.colors.spiced_wine,
        detailTextColor: this.data.colors.cowhide_cocoa
      };

      // 设置秋季主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.colors.golden_batter, // 金黄色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });

    } else if (themeName === 'pinkBlue') {
      // 设置粉蓝主题样式
      pageStyle = {
        backgroundColor: this.data.pinkBlueColors.pinkLight,
        backgroundImage: `linear-gradient(to bottom, white, ${this.data.pinkBlueColors.pinkLight})`,
        titleColor: this.data.pinkBlueColors.pinkDark,
        cellBackgroundColor: 'rgba(255, 255, 255, 0.9)',
        timeColor: this.data.pinkBlueColors.blueDark,
        messageColor: this.data.pinkBlueColors.pinkDark,
        decorationColors: [
          this.data.pinkBlueColors.blueMedium,
          this.data.pinkBlueColors.pinkMedium,
          this.data.pinkBlueColors.blueLight
        ],
        detailBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        detailShadowColor: 'rgba(212, 124, 153, 0.2)',
        detailButtonColor: this.data.pinkBlueColors.pinkDark,
        detailTextColor: this.data.pinkBlueColors.blueDark
      };

      // 设置粉蓝主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.pinkBlueColors.pinkLight, // 浅粉色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });
    } else if (themeName === 'blackWhite') {
      // 设置黑白主题样式
      pageStyle = {
        backgroundColor: this.data.blackWhiteColors.offWhite,
        backgroundImage: 'none',
        titleColor: this.data.blackWhiteColors.black,
        cellBackgroundColor: this.data.blackWhiteColors.white,
        timeColor: this.data.blackWhiteColors.mediumGray,
        messageColor: this.data.blackWhiteColors.black,
        decorationColors: [
          this.data.blackWhiteColors.darkGray,
          this.data.blackWhiteColors.mediumGray,
          this.data.blackWhiteColors.lightGray
        ],
        detailBackgroundColor: this.data.blackWhiteColors.white,
        detailShadowColor: 'rgba(0, 0, 0, 0.2)',
        detailButtonColor: this.data.blackWhiteColors.black,
        detailTextColor: this.data.blackWhiteColors.darkGray
      };

      // 设置黑白主题导航栏
      wx.setNavigationBarColor({
        frontColor: '#000000', // 黑色文字
        backgroundColor: this.data.blackWhiteColors.white, // 白色背景
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      });
    }

    // 更新页面样式
    this.setData({
      pageStyle: pageStyle
    });
  },

  // 从缓存加载消息
  loadMessages: function() {
    this.setData({ loading: true });

    // 获取消息缓存键名
    const cacheKey = 'userMessagesCache';
    const readStatusKey = 'messageReadStatus';

    try {
      // 获取缓存的消息和已读状态
      const cachedMessages = wx.getStorageSync(cacheKey) || [];
      const readStatus = wx.getStorageSync(readStatusKey) || {};

      console.log('加载消息数据:', cachedMessages.length, '条');

      // 合并消息数据和已读状态
      let messages = cachedMessages.map(msg => {
        return {
          ...msg,
          read: readStatus[msg._id] === true, // 如果本地标记为已读，则设置为已读
          hasImage: !!(msg.imageUrl || msg.imageFileID) // 判断是否包含图片
        };
      });

      // 按时间倒序排序（最新的在最前面）
      messages.sort((a, b) => {
        return new Date(b.createTime || b.createAt || 0) - new Date(a.createTime || a.createAt || 0);
      });

      // 处理可能包含fileID的图片
      this.processMessagesWithFileID(messages);

      // 更新页面数据
      this.setData({
        messages: messages,
        loading: false
      });

      // 标记所有消息为已读
      this.markAllMessagesAsRead(messages, readStatus);
    } catch (err) {
      console.error('加载消息失败:', err);

      // 显示错误提示
      wx.showToast({
        title: '加载消息失败',
        icon: 'none'
      });

      this.setData({ loading: false });
    }
  },

  // 处理包含fileID的图片消息
  processMessagesWithFileID: function(messages) {
    if (!messages || messages.length === 0) return;

    // 获取图片缓存
    const cacheKey = this.data.imageCacheConfig.cacheKey;
    const imageCache = wx.getStorageSync(cacheKey) || {};

    // 记录需要获取临时链接的fileID
    const fileIDsToProcess = [];

    // 先尝试从缓存获取
    messages.forEach((msg, index) => {
      // 检查是否有fileID
      if (msg.imageFileID) {
        // 首先检查缓存
        if (imageCache[msg.imageFileID] &&
            imageCache[msg.imageFileID].tempUrl &&
            imageCache[msg.imageFileID].expireTime > Date.now()) {
          // 使用缓存的临时URL
          console.log('使用缓存的图片URL:', msg.imageFileID);
          msg.imageUrl = imageCache[msg.imageFileID].tempUrl;
        } else {
          // 需要获取临时链接
          fileIDsToProcess.push({
            fileID: msg.imageFileID,
            index: index
          });
        }
      }
    });

    // 如果有需要处理的fileID
    if (fileIDsToProcess.length > 0) {
      console.log('需要处理的fileID数量:', fileIDsToProcess.length);

      // 批量获取临时链接
      const fileIDs = fileIDsToProcess.map(item => item.fileID);
      wx.cloud.getTempFileURL({
        fileList: fileIDs,
        success: res => {
          console.log('获取临时链接成功:', res);

          // 获取当前消息列表
          const updatedMessages = [...this.data.messages];

          // 处理获取的临时链接
          res.fileList.forEach((file, i) => {
            if (file.tempFileURL) {
              const fileID = file.fileID;
              const msgIndex = fileIDsToProcess[i].index;

              // 更新消息对象中的imageUrl
              if (updatedMessages[msgIndex]) {
                updatedMessages[msgIndex].imageUrl = file.tempFileURL;
              }

              // 更新缓存
              imageCache[fileID] = {
                tempUrl: file.tempFileURL,
                expireTime: Date.now() + this.data.imageCacheConfig.cacheDuration
              };
            }
          });

          // 更新页面数据
          this.setData({
            messages: updatedMessages
          });

          // 保存缓存
          wx.setStorageSync(cacheKey, imageCache);
        },
        fail: err => {
          console.error('获取临时链接失败:', err);
        }
      });
    }
  },

  // 标记所有消息为已读
  markAllMessagesAsRead: function(messages, readStatus) {
    // 如果没有传入参数，使用当前数据
    messages = messages || this.data.messages;

    // 获取已读状态缓存
    const readStatusKey = 'messageReadStatus';
    readStatus = readStatus || wx.getStorageSync(readStatusKey) || {};

    // 标记所有消息为已读
    let hasUnread = false;

    messages.forEach(msg => {
      if (!msg.read) {
        readStatus[msg._id] = true;
        hasUnread = true;
      }
    });

    // 如果有未读消息，更新已读状态缓存
    if (hasUnread) {
      console.log('更新消息已读状态');
      wx.setStorageSync(readStatusKey, readStatus);

      // 更新页面数据中的已读状态
      const updatedMessages = messages.map(msg => ({
        ...msg,
        read: true
      }));

      this.setData({ messages: updatedMessages });
    }
  },

  // 点击消息项
  onMessageTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const message = this.data.messages[index];

    if (!message) return;

    // 根据消息类型执行不同操作
    if (message.url) {
      // 如果消息包含URL，跳转到对应页面
      wx.navigateTo({
        url: message.url,
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 显示美观的消息详情弹窗
      const time = message.createTime || message.createAt;
      const formattedTime = time ? this.formatTime(new Date(time)) : '';

      this.setData({
        'messageDetail.show': true,
        'messageDetail.title': message.title || '消息详情',
        'messageDetail.content': message.content || '暂无详细内容',
        'messageDetail.imageUrl': message.imageUrl || '',
        'messageDetail.imageFileID': message.imageFileID || '',
        'messageDetail.time': formattedTime,
        'messageDetail.hasImage': !!(message.imageUrl || message.imageFileID)
      });

      // 如果只有fileID但没有临时URL
      if (message.imageFileID && !message.imageUrl) {
        this.getImageTempUrl(message.imageFileID);
      }
    }
  },

  // 获取图片临时链接
  getImageTempUrl: function(fileID) {
    if (!fileID) return;

    // 获取图片缓存
    const cacheKey = this.data.imageCacheConfig.cacheKey;
    const imageCache = wx.getStorageSync(cacheKey) || {};

    // 检查缓存
    if (imageCache[fileID] &&
        imageCache[fileID].tempUrl &&
        imageCache[fileID].expireTime > Date.now()) {
      // 使用缓存的临时URL
      console.log('使用缓存的图片URL:', fileID);
      this.setData({
        'messageDetail.imageUrl': imageCache[fileID].tempUrl
      });
      return;
    }

    // 获取临时链接
    wx.cloud.getTempFileURL({
      fileList: [fileID],
      success: res => {
        console.log('获取图片临时链接成功:', res);

        if (res.fileList && res.fileList[0] && res.fileList[0].tempFileURL) {
          const tempUrl = res.fileList[0].tempFileURL;

          // 更新弹窗数据
          this.setData({
            'messageDetail.imageUrl': tempUrl
          });

          // 更新缓存
          imageCache[fileID] = {
            tempUrl: tempUrl,
            expireTime: Date.now() + this.data.imageCacheConfig.cacheDuration
          };

          wx.setStorageSync(cacheKey, imageCache);
        }
      },
      fail: err => {
        console.error('获取图片临时链接失败:', err);
        wx.showToast({
          title: '图片加载失败',
          icon: 'none'
        });
      }
    });
  },

  // 关闭消息详情弹窗
  closeMessageDetail: function() {
    this.setData({
      'messageDetail.show': false
    });
  },

  // 预览消息图片
  previewMessageImage: function() {
    const imageUrl = this.data.messageDetail.imageUrl;
    if (!imageUrl) return;

    wx.previewImage({
      urls: [imageUrl],
      current: imageUrl
    });
  },

  // 处理图片加载错误
  onImageError: function(e) {
    console.error('图片加载错误:', e);
    const fileID = e.currentTarget.dataset.fileid;

    if (!fileID) return;

    // 清除当前的临时链接
    this.setData({
      'messageDetail.imageUrl': ''
    });

    // 清除缓存中对应的链接
    const cacheKey = this.data.imageCacheConfig.cacheKey;
    const imageCache = wx.getStorageSync(cacheKey) || {};
    if (imageCache[fileID]) {
      delete imageCache[fileID];
      wx.setStorageSync(cacheKey, imageCache);
    }

    // 尝试重新获取临时链接
    setTimeout(() => {
      this.getImageTempUrl(fileID);
    }, 500);
  },

  // 格式化时间
  formatTime: function(date) {
    const month = this.padZero(date.getMonth() + 1);
    const day = this.padZero(date.getDate());
    const hour = this.padZero(date.getHours());
    const minute = this.padZero(date.getMinutes());

    return `${month}-${day} ${hour}:${minute}`;
  },

  // 数字补零
  padZero: function(num) {
    return num < 10 ? '0' + num : num;
  },

  // 下载并缓存图片到本地文件系统
  downloadImage: function(fileID) {
    if (!fileID) return Promise.reject(new Error('fileID为空'));

    // 图片本地存储缓存配置
    const localCacheKey = 'messageImageLocalCache';
    const localCache = wx.getStorageSync(localCacheKey) || {};

    // 检查是否已有本地缓存
    if (localCache[fileID] && localCache[fileID].localPath) {
      console.log('使用本地缓存的图片:', fileID);
      return Promise.resolve(localCache[fileID].localPath);
    }

    return new Promise((resolve, reject) => {
      // 先获取临时URL
      wx.cloud.downloadFile({
        fileID: fileID,
        success: res => {
          if (res.tempFilePath) {
            // 保存到本地文件系统
            const localPath = `${wx.env.USER_DATA_PATH}/${fileID.replace(/[\/:.]/g, '_')}`;
            wx.getFileSystemManager().saveFile({
              tempFilePath: res.tempFilePath,
              filePath: localPath,
              success: saveRes => {
                console.log('图片保存到本地成功:', saveRes);

                // 更新本地缓存
                localCache[fileID] = {
                  localPath: localPath,
                  saveTime: Date.now()
                };

                wx.setStorageSync(localCacheKey, localCache);
                resolve(localPath);
              },
              fail: err => {
                console.error('图片保存到本地失败:', err);
                reject(err);
              }
            });
          } else {
            reject(new Error('下载图片失败，无临时文件路径'));
          }
        },
        fail: err => {
          console.error('下载图片失败:', err);
          reject(err);
        }
      });
    });
  },

  // 刷新消息
  onPullDownRefresh: function() {
    console.log('下拉刷新，重新获取消息');

    // 调用云函数获取最新消息
    wx.cloud.callFunction({
      name: 'getMessages',
      data: {}
    })
    .then(res => {
      const result = res.result || {};

      if (!result.success) {
        throw new Error(result.error || '获取消息列表失败');
      }

      // 获取消息列表
      const messageList = result.data || [];
      console.log('从云端获取到消息列表:', messageList.length, '条');

      // 缓存消息数据
      const cacheKey = 'userMessagesCache';
      const expirationKey = 'userMessagesCacheExpiration';
      const cacheDuration = 60 * 60 * 1000; // 1小时缓存有效期

      wx.setStorageSync(cacheKey, messageList);
      // 设置缓存过期时间
      const expiration = Date.now() + cacheDuration;
      wx.setStorageSync(expirationKey, expiration);

      // 记录同步时间
      wx.setStorageSync('lastMessageSyncTime', Date.now());

      // 重新加载消息
      this.loadMessages();

      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    })
    .catch(err => {
      console.error('刷新消息失败:', err);

      wx.showToast({
        title: '刷新消息失败',
        icon: 'none'
      });

      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    });
  },

  // 清除所有消息记录
  clearAllMessages: function() {
    wx.showModal({
      title: '清除消息',
      content: '确定要清除所有消息记录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除消息缓存
          const cacheKey = 'userMessagesCache';
          const readStatusKey = 'messageReadStatus';

          wx.removeStorageSync(cacheKey);
          wx.removeStorageSync(readStatusKey);

          // 更新页面
          this.setData({
            messages: []
          });

          wx.showToast({
            title: '已清除所有消息',
            icon: 'success'
          });
        }
      }
    });
  },

  // 清除图片缓存
  clearImageCache: function() {
    // 清除临时URL缓存
    const cacheKey = this.data.imageCacheConfig.cacheKey;
    wx.removeStorageSync(cacheKey);

    // 清除本地文件缓存
    const localCacheKey = 'messageImageLocalCache';
    const localCache = wx.getStorageSync(localCacheKey) || {};

    // 删除本地文件
    Object.keys(localCache).forEach(fileID => {
      const localPath = localCache[fileID].localPath;
      if (localPath) {
        wx.getFileSystemManager().unlink({
          filePath: localPath,
          fail: err => {
            console.log('删除本地文件失败:', err);
          }
        });
      }
    });

    // 清除本地缓存记录
    wx.removeStorageSync(localCacheKey);

    console.log('已清除所有图片缓存');
  }
});