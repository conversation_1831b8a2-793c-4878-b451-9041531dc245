/* 杂志风格页面样式 */

/* 自定义导航栏 */
.custom-nav-bar {
  background-color: transparent !important;
  position: fixed !important;
  top: 0;
  width: 100%;
  z-index: 100;
}

/* 主容器 */
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;
  padding-top: 0; /* 去除顶部内边距 */
  perspective: 1000px; /* 增加3D视角效果 */
}

/* 加载状态 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid transparent;
  border-radius: 50%;
  border-top-color: #442D1C;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 30rpx;
  color: #442D1C;
}

/* 返回按钮 */
.back-button {
  position: fixed;
  top: 160rpx; /* 调整位置适应自定义导航栏 */
  left: 30rpx;
  z-index: 30; /* 提高层级确保在顶部 */
  display: flex;
  align-items: center;
  height: 60rpx;
  padding: 0 15rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.back-icon {
  width: 20rpx;
  height: 20rpx;
  border-top: 3rpx solid #442D1C;
  border-right: 3rpx solid #442D1C;
  transform: rotate(-135deg);
  margin-right: 10rpx;
}

.back-button text {
  font-size: 26rpx;
}

/* 杂志主体内容 */
.magazine-content {
  flex: 1;
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  perspective: 1500px;
  padding-top: 40rpx;
  padding-bottom: 50rpx; /* 增加底部留白 */
}

/* 调整杂志书页层叠效果 */
.magazine-page-stack-layer-3 {
  position: absolute;
  width: 91%;
  height: 78%;
  max-height: 78vh;
  background-color: #e6e6e6;
  border-radius: 8rpx;
  box-shadow: 0 10rpx 15rpx rgba(0, 0, 0, 0.06);
  z-index: 1;
  transform: translateZ(-40px);
  top: 140rpx;
  left: 50%;
  margin-left: -45.5%;
  opacity: 0.7; /* 降低底层透明度 */
}

.magazine-page-stack-layer-2 {
  position: absolute;
  width: 91.5%;
  height: 79%;
  max-height: 79vh;
  background-color: #e9e9e9;
  border-radius: 8rpx;
  box-shadow: 0 10rpx 15rpx rgba(0, 0, 0, 0.07);
  z-index: 2;
  transform: translateZ(-30px);
  top: 135rpx;
  left: 50%;
  margin-left: -45.75%;
  opacity: 0.85; /* 降低中层透明度 */
}

.magazine-page-stack-layer-1 {
  position: absolute;
  width: 92%;
  height: 80%;
  max-height: 80vh;
  background-color: #f2f2f2;
  border-radius: 8rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.08);
  z-index: 3;
  transform: translateZ(-20px);
  top: 132rpx;
  left: 50%;
  margin-left: -46%;
  opacity: 0.95; /* 稍微降低顶层透明度 */
}

/* 杂志书本效果 */
.magazine-page {
  width: 92%;
  height: 81%;
  max-height: 81vh;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: visible;
  border-radius: 8rpx;
  background-color: #fff;
  box-shadow:
    0 10rpx 30rpx rgba(0, 0, 0, 0.15),
    2rpx 0 8rpx rgba(0, 0, 0, 0.08),
    -5rpx 0 15rpx rgba(0, 0, 0, 0.08);
  transform-origin: center left;
  transition: transform 0.3s ease;
  margin-top: 130rpx;
  transform-style: preserve-3d;
  position: relative;
  z-index: 10;
}

/* 书页层叠效果 - 背后的页面 */
.magazine-page::before {
  content: '';
  position: absolute;
  left: -5rpx;
  top: -5rpx;
  width: 100%;
  height: 100%;
  background-color: #f9f9f9;
  border-radius: 8rpx 20rpx 20rpx 8rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: -1;
  transform: translateZ(-10px) rotate(-1deg);
}

/* 书页边缘褶皱效果 */
.magazine-page::after {
  content: '';
  position: absolute;
  right: -5rpx;
  top: 5rpx;
  width: 15rpx;
  height: 98%;
  background-color: #f0f0f0;
  border-radius: 0 8rpx 8rpx 0;
  box-shadow: 2rpx 0 5rpx rgba(0, 0, 0, 0.1);
  transform: rotate(1deg);
  z-index: 3;
}

/* 书页褶皱纹理效果 */
.magazine-page::before,
.magazine-page::after {
  background-image:
    linear-gradient(to bottom,
      transparent 0%,
      rgba(0, 0, 0, 0.02) 20%,
      transparent 30%,
      rgba(0, 0, 0, 0.02) 50%,
      transparent 60%,
      rgba(0, 0, 0, 0.02) 80%,
      transparent 100%);
}

.magazine-page .right-pages-stack {
  position: absolute;
  right: -2rpx;
  top: 0;
  height: 100%;
  overflow: hidden;
  z-index: 3;
}

.right-pages-stack::before,
.right-pages-stack::after {
  content: '';
  position: absolute;
  right: 0;
  height: 100%;
  border-radius: 0 8rpx 8rpx 0;
  background-color: #f9f9f9;
}

.right-pages-stack::before {
  width: 10rpx;
  box-shadow: 2rpx 0 3rpx rgba(0, 0, 0, 0.05);
  transform: rotate(0.5deg);
  right: 2rpx;
  /* 添加纹理 */
  background-image:
    linear-gradient(to bottom,
      transparent 0%,
      rgba(0, 0, 0, 0.02) 20%,
      transparent 40%,
      rgba(0, 0, 0, 0.02) 60%,
      transparent 80%,
      rgba(0, 0, 0, 0.02) 100%);
}

.right-pages-stack::after {
  width: 6rpx;
  box-shadow: 1rpx 0 2rpx rgba(0, 0, 0, 0.03);
  transform: rotate(0.3deg);
  right: 5rpx;
  /* 添加纹理 */
  background-image:
    linear-gradient(to bottom,
      rgba(0, 0, 0, 0.03) 0%,
      transparent 30%,
      rgba(0, 0, 0, 0.03) 50%,
      transparent 70%,
      rgba(0, 0, 0, 0.03) 100%);
}

/* 书脊效果 */
.magazine-spine {
  position: absolute;
  left: -10rpx; /* 调整位置，使其更突出 */
  top: 0;
  width: 25rpx; /* 增加宽度 */
  height: 100%;
  background: linear-gradient(to right,
    rgba(0, 0, 0, 0.3),
    rgba(0, 0, 0, 0.15) 40%,
    rgba(0, 0, 0, 0.08) 60%,
    transparent);
  border-radius: 5rpx 0 0 5rpx;
  z-index: 5; /* 提高z-index确保书脊在上层 */
  /* 书脊纹理效果 */
  background-image:
    linear-gradient(to bottom,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.1) 5%,
      transparent 5%,
      transparent 95%,
      rgba(255, 255, 255, 0.1) 95%,
      rgba(255, 255, 255, 0.2) 100%),
    linear-gradient(to right,
      rgba(0, 0, 0, 0.4),
      rgba(0, 0, 0, 0.2) 40%,
      rgba(0, 0, 0, 0.1) 60%,
      transparent);
  pointer-events: none; /* 确保点击事件能穿透到下层 */
  box-shadow: -3rpx 0 8rpx rgba(0, 0, 0, 0.2); /* 添加阴影增强立体感 */
}

/* 书籍封面边缘效果 */
.magazine-cover::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20rpx;
  height: 100%;
  background: linear-gradient(to left,
    rgba(0, 0, 0, 0.05),
    transparent 80%);
  pointer-events: none;
  z-index: 4;
}

/* 增加右侧书页边缘效果 */
.magazine-cover::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20rpx;
  height: 100%;
  background: linear-gradient(to left,
    rgba(0, 0, 0, 0.05),
    transparent 80%);
  pointer-events: none;
  z-index: 4;
}

/* 书页内容 */
.magazine-cover, .outfit-content {
  position: relative;
  z-index: 3;
  /* 为内容添加内边距，增加留白 */
  padding: 30rpx;
}

/* 杂志封面 */
.magazine-cover {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  padding-top: 0;
  background-color: #fff;
  box-sizing: border-box; /* 确保内边距计算在内 */
}

/* 封面模特图片容器 */
.cover-model-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
  padding: 30rpx 30rpx 60rpx 30rpx; /* 增加内边距，底部更大以留出空间给页码 */
  box-sizing: border-box; /* 确保内边距计算在内 */
}

/* AI评分标签 */
.ai-score-badge {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 20;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.ai-score-value {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  line-height: 1;
}

.ai-score-label {
  font-size: 18rpx;
  color: white;
  margin-top: 4rpx;
}

.cover-model {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #fff;
  border-radius: 4rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 添加长按提示样式 */
.cover-model-container::after {
  content: '长按上传封面图';
  position: absolute;
  left: 50%;
  top: 10rpx;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  opacity: 0;
  transition: opacity 0.3s;
  white-space: nowrap;
  z-index: 5;
}

.cover-model-container:active::after {
  opacity: 1;
}

/* 长按提示文字 */
.longpress-hint {
  position: absolute;
  top: 15rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  opacity: 0.7;
  z-index: 5;
  white-space: nowrap;
  animation: fade-in-out 3s ease-in-out infinite;
}

@keyframes fade-in-out {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.7; }
}

/* 搭配名称显示 */
.outfit-name-display {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  max-width: 70%;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #222;
  text-align: right;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-left: 6rpx solid #84592B; /* 添加左侧边框，增强视觉效果 */
}

/* 为不同主题适配样式 */
.theme-autumn .outfit-name-display {
  border-left-color: #84592B;
}

.theme-pinkBlue .outfit-name-display {
  border-left-color: #D47C99;
}

.theme-blackWhite .outfit-name-display {
  border-left-color: #000000;
}

/* 底部信息区域 - 调整位置 */
.cover-bottom-info {
  position: absolute;
  bottom: 30rpx; /* 增加与页码的距离 */
  left: 0;
  width: 100%;
  padding: 15rpx 30rpx; /* 增加左右内边距 */
  z-index: 3;
}

/* 尺码信息 */
.size-info {
  display: flex;
  flex-direction: column;
  font-size: 18rpx;
  line-height: 1.4;
  color: #333;
  max-width: 60%; /* 限制最大宽度 */
  text-align: left;
}

.size-info text {
  white-space: nowrap;
  margin-bottom: 2rpx;
}

/* 页码 - 调整到底部中间 */
.page-number {
  position: absolute;
  bottom: 15rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24rpx;
  font-weight: 500;
  color: #000;
  text-align: center;
  width: 120rpx;
  background-color: rgba(255, 255, 255, 0.7); /* 添加半透明背景 */
  padding: 4rpx 10rpx;
  border-radius: 15rpx;
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.1);
}

/* 点击查看提示 */
.tap-to-view {
  position: absolute;
  bottom: 120rpx;
  left: 50%;
  transform: translateX(-50%);
  padding: 10rpx 30rpx;
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  font-size: 24rpx;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
  opacity: 0;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.3;
    transform: translateX(-50%) scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1.05);
  }
  100% {
    opacity: 0.3;
    transform: translateX(-50%) scale(0.95);
  }
}

/* 返回封面按钮 */
.back-to-cover {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 10rpx 20rpx;
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  font-size: 24rpx;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.cover-image-container {
  width: 100%;
  height: 70%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 50%);
}

.cover-title {
  position: absolute;
  bottom: 30rpx;
  left: 40rpx;
  max-width: 80%;
  z-index: 2;
}

.magazine-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
  margin-bottom: 10rpx;
  text-transform: uppercase;
  letter-spacing: 2rpx;
}

.outfit-title {
  font-size: 60rpx;
  font-weight: bold;
  color: #fff;
  line-height: 1.2;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.outfit-subtitle {
  font-size: 30rpx;
  color: #fff;
  margin-top: 20rpx;
  opacity: 0.9;
}

/* 杂志内容 */
.outfit-content {
  width: 100%;
  height: 100%;
  padding: 40rpx 50rpx 50rpx; /* 减少内边距，给内容留更多空间 */
  display: flex;
  flex-direction: column;
  background-color: #fff;
  background-image:
    linear-gradient(90deg, rgba(0,0,0,0.03) 0px, transparent 1px),
    linear-gradient(rgba(0,0,0,0.03) 0px, transparent 1px);
  background-size: 20px 20px;
  background-position: center;
  position: relative;
  overflow: visible;
  box-sizing: border-box; /* 确保内边距计算在内 */
}

/* 调整内容页中的书脊位置 */
.outfit-content .magazine-spine {
  position: absolute;
  left: -1rpx;
  top: 0;
  height: 100%;
  z-index: 10; /* 确保书脊在最上层 */
}

/* 为内容页增加左侧阴影 */
.outfit-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 20rpx;
  height: 100%;
  background: linear-gradient(to right,
    rgba(0, 0, 0, 0.05),
    transparent 80%);
  pointer-events: none;
  z-index: 4;
}

/* 为内容页增加右侧阴影 */
.outfit-content .right-side-shadow {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20rpx;
  height: 100%;
  background: linear-gradient(to left,
    rgba(0, 0, 0, 0.05),
    transparent 80%);
  pointer-events: none;
  z-index: 4;
}

.content-header {
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(0,0,0,0.1);
  margin-bottom: 30rpx;
}

.content-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.content-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-right: 15rpx;
}

/* 内联AI评分 */
.ai-score-inline {
  font-size: 24rpx;
  font-weight: bold;
  color: white;
  min-width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.content-date {
  font-size: 26rpx;
  color: #666;
}

.content-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 内容过多时允许滚动 */
  padding-right: 10rpx; /* 右侧添加内边距给滚动条留空间 */
  box-sizing: border-box; /* 确保内边距计算在内 */
}

.outfit-description {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 30rpx; /* 减少下边距 */
  text-align: justify; /* 两端对齐，使文本更美观 */
}

.outfit-items-section {
  flex: 1;
  min-height: 150rpx; /* 确保即使没有多少物品也有一定高度 */
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  height: 32rpx;
  width: 8rpx;
  background-color: #84592B;
  border-radius: 4rpx;
}

.outfit-items-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 三列等宽布局 */
  gap: 20rpx; /* 增加间距 */
  margin: 0;
  width: 100%;
}

.outfit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.item-image-container {
  width: 100%;
  padding-bottom: 100%; /* 保持1:1比例 */
  position: relative;
  overflow: hidden;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1);
}

.item-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 10rpx;
  background-color: #fff;
  box-sizing: border-box; /* 确保内边距计算在内 */
}

.item-name {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 5rpx; /* 添加左右内边距 */
  box-sizing: border-box; /* 确保内边距计算在内 */
}

/* 杂志底部 */
.magazine-footer {
  padding: 30rpx;
  text-align: center;
  font-size: 24rpx;
  color: #999;
  border-top: 1rpx solid rgba(0,0,0,0.1);
}

/* 翻页动画效果增强 */
/* 向左翻页（上一页）*/
@keyframes page-turn-left {
  0% {
    transform: rotateY(0deg) translateX(0);
    z-index: 5;
  }
  50% {
    transform: rotateY(-10deg) translateX(10%);
    z-index: 5;
    box-shadow:
      30rpx 10rpx 40rpx rgba(0, 0, 0, 0.4),
      30rpx 0 15rpx rgba(0, 0, 0, 0.15);
  }
  100% {
    transform: rotateY(0deg) translateX(0);
    z-index: 3;
  }
}

/* 向右翻页（下一页）*/
@keyframes page-turn-right {
  0% {
    transform: rotateY(0deg) translateX(0);
    z-index: 5;
  }
  50% {
    transform: rotateY(10deg) translateX(-10%);
    z-index: 5;
    box-shadow:
      -30rpx 10rpx 40rpx rgba(0, 0, 0, 0.4),
      -30rpx 0 15rpx rgba(0, 0, 0, 0.15);
  }
  100% {
    transform: rotateY(0deg) translateX(0);
    z-index: 3;
  }
}

.page-turn-right {
  animation: page-turn-right 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  position: relative;
  perspective: 2000px;
  transform-style: preserve-3d;
}

.page-turn-right::after {
  content: '';
  position: absolute;
  left: -100%; /* 从左侧进入 */
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.08);
  border-radius: 8rpx;
  box-shadow: 8rpx 0 20rpx rgba(0, 0, 0, 0.15);
  animation: page-slide-right 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  z-index: 4;
  pointer-events: none;
  transform-origin: center right;
}

.page-turn-left {
  animation: page-turn-left 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  position: relative;
  perspective: 2000px;
  transform-style: preserve-3d;
}

.page-turn-left::after {
  content: '';
  position: absolute;
  right: -100%; /* 从右侧进入 */
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.08);
  border-radius: 8rpx;
  box-shadow: -8rpx 0 20rpx rgba(0, 0, 0, 0.15);
  animation: page-slide-left 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  z-index: 4;
  pointer-events: none;
  transform-origin: center left;
}

/* 纸张滑过动画增强 - 交换left和right */
@keyframes page-slide-right {
  0% {
    transform: translateX(100%) rotateY(0deg);
    opacity: 0;
  }
  20% {
    transform: translateX(80%) rotateY(-30deg);
    opacity: 0.8;
  }
  60% {
    transform: translateX(20%) rotateY(-15deg);
    opacity: 0.9;
  }
  100% {
    transform: translateX(0) rotateY(0deg);
    opacity: 0;
  }
}

@keyframes page-slide-left {
  0% {
    transform: translateX(-100%) rotateY(0deg);
    opacity: 0;
  }
  20% {
    transform: translateX(-80%) rotateY(30deg);
    opacity: 0.8;
  }
  60% {
    transform: translateX(-20%) rotateY(15deg);
    opacity: 0.9;
  }
  100% {
    transform: translateX(0) rotateY(0deg);
    opacity: 0;
  }
}

/* 纸张翻转阴影效果增强 */
.page-shadow {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(0,0,0,0.08), rgba(0,0,0,0));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 6;
}

.page-turn-right .page-shadow {
  left: 0;
  opacity: 1;
  animation: shadow-fade-right 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  background: linear-gradient(to left, rgba(0,0,0,0.08), rgba(0,0,0,0));
}

.page-turn-left .page-shadow {
  right: 0;
  opacity: 1;
  animation: shadow-fade-left 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  background: linear-gradient(to right, rgba(0,0,0,0.08), rgba(0,0,0,0));
}

@keyframes shadow-fade-right {
  0% { opacity: 0; }
  35% { opacity: 0.7; }
  100% { opacity: 0; }
}

@keyframes shadow-fade-left {
  0% { opacity: 0; }
  35% { opacity: 0.7; }
  100% { opacity: 0; }
}

/* 添加翻页时的纸张飞过效果 */
.magazine-page.page-turn-right::before,
.magazine-page.page-turn-left::before {
  content: '';
  position: absolute;
  top: 0;
  width: 80%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.05);
  z-index: 10;
  pointer-events: none;
  transform-origin: center;
  animation-duration: 0.8s;
  animation-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  animation-fill-mode: forwards;
}

.magazine-page.page-turn-right::before {
  right: -80%;
  animation-name: paper-fly-right;
}

.magazine-page.page-turn-left::before {
  left: -80%;
  animation-name: paper-fly-left;
}

@keyframes paper-fly-right {
  0% {
    transform: translateX(0) rotateY(0deg);
    opacity: 0;
  }
  40% {
    transform: translateX(-60%) rotateY(-5deg);
    opacity: 0.25;
  }
  80% {
    transform: translateX(-120%) rotateY(-3deg);
    opacity: 0.15;
  }
  100% {
    transform: translateX(-150%) rotateY(0deg);
    opacity: 0;
  }
}

@keyframes paper-fly-left {
  0% {
    transform: translateX(0) rotateY(0deg);
    opacity: 0;
  }
  40% {
    transform: translateX(60%) rotateY(5deg);
    opacity: 0.25;
  }
  80% {
    transform: translateX(120%) rotateY(3deg);
    opacity: 0.15;
  }
  100% {
    transform: translateX(150%) rotateY(0deg);
    opacity: 0;
  }
}

/* 页面边缘波纹效果 */
.magazine-page.page-turn-right .ripple-effect,
.magazine-page.page-turn-left .ripple-effect {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 15;
  pointer-events: none;
  overflow: hidden;
}

.magazine-page.page-turn-right .ripple-effect::after,
.magazine-page.page-turn-left .ripple-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  animation-duration: 0.8s;
  animation-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  animation-fill-mode: forwards;
  transform: translate(-50%, -50%);
}

.magazine-page.page-turn-right .ripple-effect::after {
  left: 0%;
  width: 300rpx;
  height: 300rpx;
  animation-name: ripple-right;
}

.magazine-page.page-turn-left .ripple-effect::after {
  left: 100%;
  width: 300rpx;
  height: 300rpx;
  animation-name: ripple-left;
}

@keyframes ripple-right {
  0% {
    transform: translate(0%, -50%) scale(0.5);
    opacity: 0;
  }
  30% {
    transform: translate(-20%, -50%) scale(1);
    opacity: 0.2;
  }
  100% {
    transform: translate(-60%, -50%) scale(1.2);
    opacity: 0;
  }
}

@keyframes ripple-left {
  0% {
    transform: translate(-100%, -50%) scale(0.5);
    opacity: 0;
  }
  30% {
    transform: translate(-80%, -50%) scale(1);
    opacity: 0.2;
  }
  100% {
    transform: translate(-40%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* 页码指示器 */
.page-indicator {
  position: fixed;
  bottom: 25rpx; /* 调整位置 */
  right: 40rpx;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  z-index: 20;
}

.indicator-text {
  font-size: 22rpx;
  color: #333;
  position: relative;
}

/* 拖动状态样式 */
.indicator-text .dragging {
  opacity: 0.5;
}

/* 页码滑块 */
.page-slider {
  position: absolute;
  bottom: -80rpx;
  left: -150rpx;
  width: 400rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.25);
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  z-index: 100;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.slider-track {
  position: absolute;
  width: 360rpx;
  height: 6rpx;
  background-color: #ddd;
  border-radius: 3rpx;
}

.slider-thumb {
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  background-color: #84592B;
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.3);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.slider-label {
  position: absolute;
  top: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  background-color: white;
  padding: 4rpx 16rpx;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.25);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  min-width: 40rpx;
  text-align: center;
}

/* 翻页按钮 */
.page-buttons {
  position: fixed;
  bottom: 25rpx; /* 调整位置 */
  left: 40rpx;
  display: flex;
  align-items: center;
  z-index: 20;
}

.page-button {
  width: 70rpx; /* 减小大小 */
  height: 70rpx; /* 减小大小 */
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  margin-right: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.button-icon {
  width: 20rpx;
  height: 20rpx;
  border-top: 3rpx solid #442D1C;
  border-right: 3rpx solid #442D1C;
}

.prev-icon {
  transform: rotate(-135deg);
}

.next-icon {
  transform: rotate(45deg);
}

/* 创建按钮 */
.create-button {
  position: fixed;
  bottom: 25rpx; /* 调整位置 */
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70rpx; /* 减小高度 */
  padding: 0 30rpx; /* 减小内边距 */
  background-color: #84592B;
  color: #fff;
  font-size: 26rpx; /* 减小字体大小 */
  border-radius: 35rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
  z-index: 20;
}

.create-button .plus-icon {
  font-size: 32rpx; /* 减小字体大小 */
  margin-right: 8rpx; /* 减小边距 */
}

/* 空状态展示 */
.empty-state {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 40rpx;
  margin-top: 130rpx; /* 增加与导航栏的距离，与杂志页保持一致 */
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  text-align: center;
  margin-bottom: 60rpx;
}

/* 主题样式定制 */
.theme-autumn .section-title::before {
  background-color: #84592B;
}

.theme-autumn .create-button {
  background-color: #84592B;
}

.theme-pinkBlue .section-title::before {
  background-color: #D47C99;
}

.theme-pinkBlue .create-button {
  background-color: #D47C99;
}

.theme-blackWhite .section-title::before {
  background-color: #000000;
}

.theme-blackWhite .create-button {
  background-color: #000000;
}

/* 纸张纹理效果 */
.outfit-content .paper-texture {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/png;base64,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');
  opacity: 0.05; /* 增强纹理效果 */
  z-index: 1;
  pointer-events: none;
  mix-blend-mode: multiply; /* 增加混合模式 */
}

/* 杂志封面中的书脊 */
.magazine-cover .magazine-spine {
  left: -10rpx;
  top: 0;
  height: 100%;
  z-index: 10; /* 确保书脊在最上层 */
}

/* 增加翻页边缘的光影效果 */
.page-turn-right,
.page-turn-left {
  position: relative;
  overflow: visible !important;
}

.page-turn-right::before,
.page-turn-left::before {
  content: '';
  position: absolute;
  top: 0;
  height: 100%;
  width: 10px;
  z-index: 30;
  pointer-events: none;
}

.page-turn-right::before {
  left: -5px;
  background: linear-gradient(to right,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0) 100%);
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.15);
  animation: edge-shadow-right 0.8s ease-in-out;
}

.page-turn-left::before {
  right: -5px;
  background: linear-gradient(to left,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0) 100%);
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.15);
  animation: edge-shadow-left 0.8s ease-in-out;
}

@keyframes edge-shadow-right {
  0% { opacity: 0; }
  20% { opacity: 1; }
  100% { opacity: 0; }
}

@keyframes edge-shadow-left {
  0% { opacity: 0; }
  20% { opacity: 1; }
  100% { opacity: 0; }
}

/* 翻页过程中的书页褶皱纹理 */
.page-turn-right .page-crease,
.page-turn-left .page-crease {
  position: absolute;
  top: 0;
  height: 100%;
  width: 20px;
  z-index: 25;
  pointer-events: none;
  opacity: 0;
  animation-duration: 0.6s;
  animation-timing-function: ease-in-out;
  animation-fill-mode: forwards;
}

.page-turn-right .page-crease {
  right: 0;
  background: linear-gradient(to left,
    rgba(0,0,0,0.1) 0%,
    rgba(0,0,0,0.05) 50%,
    transparent 100%);
  animation-name: show-crease-right;
}

.page-turn-left .page-crease {
  left: 0;
  background: linear-gradient(to right,
    rgba(0,0,0,0.1) 0%,
    rgba(0,0,0,0.05) 50%,
    transparent 100%);
  animation-name: show-crease-left;
}

@keyframes show-crease-right {
  0% { opacity: 0; }
  30% { opacity: 1; }
  80% { opacity: 0.8; }
  100% { opacity: 0; }
}

@keyframes show-crease-left {
  0% { opacity: 0; }
  30% { opacity: 1; }
  80% { opacity: 0.8; }
  100% { opacity: 0; }
}