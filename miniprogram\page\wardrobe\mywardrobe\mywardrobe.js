// 引入公共工具
const closetUtils = require('../closet/modules/closetUtils');

Page({
  data: {
    openDrawer: null, // 当前打开的抽屉
    activeCard: null, // 当前选中的卡片
    activeCategory: null, // 当前选中卡片的类别
    doorOpen: false, // 右侧玻璃门是否打开
    leftDoorOpen: false, // 左侧左推拉门是否打开
    rightDoorOpen: false, // 左侧右推拉门是否打开
    
    // 分类名称映射表
    categoryNames: {
      tops: '上衣',
      outerwear: '外套',
      pants: '裤子',
      shoes: '鞋子',
      accessories: '首饰',
      special: '特殊物品'
    },
    
    // 上衣数据
    topsData: [
      { id: 't1', name: '白色衬衫', color: '白色', season: '四季', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 't2', name: '浅蓝衬衫', color: '浅蓝', season: '春夏', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 't3', name: '条纹T恤', color: '蓝白条纹', season: '春夏', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' }
    ],
    
    // 外套数据
    outerwearData: [
      { id: 'o1', name: '驼色大衣', color: '驼色', season: '秋冬', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 'o2', name: '黑色西装', color: '黑色', season: '四季', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 'o3', name: '牛仔外套', color: '蓝色', season: '春秋', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' }
    ],
    
    // 裤子数据
    pantsData: [
      { id: 'p1', name: '牛仔裤', color: '深蓝', season: '四季', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 'p2', name: '西装裤', color: '黑色', season: '四季', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 'p3', name: '休闲裤', color: '卡其色', season: '春秋', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' }
    ],
    
    // 鞋子数据
    shoesData: [
      { id: 's1', name: '黑色皮鞋', color: '黑色', season: '四季', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 's2', name: '运动鞋', color: '白色', season: '四季', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 's3', name: '靴子', color: '棕色', season: '秋冬', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' }
    ],
    
    // 配饰数据
    accessoriesData: [
      { id: 'a1', name: '手表', color: '银色', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 'a2', name: '项链', color: '金色', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 'a3', name: '戒指', color: '金色', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 'a4', name: '耳环', color: '银色', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' }
    ],
    
    // 特殊物品
    specialItems: [
      { id: 'sp1', name: '礼服裙', color: '红色', season: '四季', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' },
      { id: 'sp2', name: '婚纱', color: '白色', season: '四季', img: 'https://img.mp.sohu.com/upload/20170711/3cd781cc80484e22b7ef37f7dac8d7cc_th.png' }
    ]
  },
  
  onLoad: function() {
    // 页面加载时的初始化逻辑
    this.loadClothesData();
  },
  
  // 加载衣物数据
  loadClothesData: function() {
    // 实际应用中，这里应该从数据库加载数据
    // 这里使用的是静态数据
    console.log('加载衣物数据');
  },
  
  // 切换抽屉打开/关闭状态
  toggleDrawer: function(e) {
    const drawer = e.currentTarget.dataset.drawer;
    
    if (this.data.openDrawer === drawer) {
      this.setData({
        openDrawer: null
      });
    } else {
      this.setData({
        openDrawer: drawer
      });
    }
  },
  
  // 切换右侧玻璃门
  toggleDoor: function() {
    this.setData({
      doorOpen: !this.data.doorOpen
    });
  },
  
  // 切换左侧左推拉门
  toggleLeftDoor: function() {
    this.setData({
      leftDoorOpen: !this.data.leftDoorOpen
    });
  },
  
  // 切换左侧右推拉门
  toggleRightDoor: function() {
    this.setData({
      rightDoorOpen: !this.data.rightDoorOpen
    });
  },
  
  // 处理卡片点击事件
  handleCardClick: function(e) {
    const item = e.currentTarget.dataset.item;
    const category = e.currentTarget.dataset.category;
    
    this.setData({
      activeCard: item,
      activeCategory: category
    });
  },
  
  // 关闭卡片详情弹窗
  closeCardDetail: function() {
    this.setData({
      activeCard: null,
      activeCategory: null
    });
  },
  
  // 防止冒泡，用于弹窗内容点击事件不传递到外层
  preventBubble: function() {
    // 空函数，只用于阻止事件冒泡
  },
  
  // 添加新物品
  addNewItem: function() {
    // 跳转到添加衣物页面
    wx.navigateTo({
      url: '../add/add'
    });
  },
  
  // 创建搭配
  createOutfit: function() {
    // 跳转到创建搭配页面
    wx.navigateTo({
      url: '../outfit/outfit_create/outfit_create'
    });
  },
  
  // 页面分享设置
  onShareAppMessage: function() {
    return {
      title: '我的原木衣柜 - 衣柜助手',
      path: '/page/wardrobe/mywardrobe/mywardrobe'
    };
  }
}); 