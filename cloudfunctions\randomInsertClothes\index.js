const cloud = require('wx-server-sdk');
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 示例数据
const sampleData = {
  category: "鞋子",
  name: "跑丢的猫咪",
  flag: "clothespulsfive",
  color: "棕色",
  imageFileID: "cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/微信图片_20250319075206.jpg",
  processedImageFileID: "cloud://cloud1-3gi97kso9ab01185.636c-cloud1-3gi97kso9ab01185-1303166775/微信图片_20250319075206.jpg"
};

// 随机选择分类
const categories = ["鞋子", "上衣", "裙子", "外套", "配饰"];

exports.main = async (event, context) => {
  const { count = 1 } = event;
  let successCount = 0;
  
  try {
    // 计算24小时前的时间戳
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 12);

    // 获取最近24小时登录的随机用户
    const usersResult = await db.collection('users')
      .aggregate()
      .match({
        lastLogin: _.gte(twentyFourHoursAgo)
      })
      .sample({
        size: count
      })
      .end();
    
    console.log('获取用户结果:', usersResult);
    
    if (!usersResult.list || usersResult.list.length === 0) {
      return {
        success: false,
        message: '没有找到最近24小时内登录的用户'
      };
    }
    
    // 为每个用户插入数据
    for (const user of usersResult.list) {
      try {
        // 随机选择分类
        const randomCategory = categories[Math.floor(Math.random() * categories.length)];
        
        // 创建新数据
        const newData = {
          ...sampleData,
          _openid: user._openid,
          category: randomCategory
        };
        
        // 插入数据
        await db.collection('clothes').add({
          data: newData
        });
        
        successCount++;
      } catch (err) {
        console.error('插入单条数据失败:', err);
        continue;
      }
    }
    
    return {
      success: true,
      successCount,
      message: `成功插入${successCount}条数据, 插入用户: ${usersResult.list.map(user => user._openid).join(', ')}`
    };
    
  } catch (err) {
    console.error('随机插入数据失败:', err);
    return {
      success: false,
      message: err.message || '随机插入数据失败'
    };
  }
}; 