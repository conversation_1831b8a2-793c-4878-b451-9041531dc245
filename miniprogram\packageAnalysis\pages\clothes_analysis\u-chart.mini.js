/*
 * uCharts (R)
 * 高性能跨平台图表库，支持H5、APP、小程序（微信/支付宝/百度/头条/QQ/360/快手）、Vue、Taro等支持canvas的框架平台
 * Copyright (C) 2018-2022 QIUN (R) 秋云 https://www.ucharts.cn All rights reserved.
 * Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
 * 复制使用请保留本段注释，感谢支持开源！
 * 
 * uCharts (R) 官方网站
 * https://www.uCharts.cn
 * 
 * 开源地址:
 * https://gitee.com/uCharts/uCharts
 * 
 * uni-app插件市场地址：
 * http://ext.dcloud.net.cn/plugin?id=271
 * 
 */
"use strict";var config={version:"v2.5.0-20230101",padding:[10,10,10,10],rotate:false,fontSize:13,fontColor:"#666666",dataPointShape:["circle","circle","circle","circle"],color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],linearColor:["#0EE2F8","#2BDCA8","#FA7D8D","#EB88E2","#2AE3A0","#0EE2F8","#EB88E2","#6773E3","#F78A85"],pieChartLinePadding:15,pieChartTextPadding:5,titleFontSize:20,subtitleFontSize:15};var assign=function(t,...e){if(t==null){throw new TypeError("[uCharts] Cannot convert undefined or null to object")}if(!e||e.length<=0){return t}function a(t,i){for(let e in i){t[e]=t[e]&&t[e].toString()==="[object Object]"?a(t[e],i[e]):t[e]=i[e]}return t}e.forEach(e=>{t=a(t,e)});return t};var util={toFixed:function e(t,i){i=i||2;if(this.isFloat(t)){t=t.toFixed(i)}return t},isFloat:function e(t){return t%1!==0},approximatelyEqual:function e(t,i){return Math.abs(t-i)<1e-10},isSameSign:function e(t,i){return Math.abs(t)===t&&Math.abs(i)===i||Math.abs(t)!==t&&Math.abs(i)!==i},isSameXCoordinateArea:function e(t,i){return this.isSameSign(t.x,i.x)},isCollision:function e(t,i){t.end={};t.end.x=t.start.x+t.width;t.end.y=t.start.y-t.height;i.end={};i.end.x=i.start.x+i.width;i.end.y=i.start.y-i.height;var a=i.start.x>t.end.x||i.end.x<t.start.x||i.end.y>t.start.y||i.start.y<t.end.y;return!a}};function getH5Offset(e){e.mp={changedTouches:[]};e.mp.changedTouches.push({x:e.offsetX,y:e.offsetY});return e}function hexToRgb(e,t){var i=/^#?([a-f\d])([a-f\d])([a-f\d])$/i;var a=e.replace(i,function(e,t,i,a){return t+t+i+i+a+a});var r=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(a);var n=parseInt(r[1],16);var o=parseInt(r[2],16);var s=parseInt(r[3],16);return"rgba("+n+","+o+","+s+","+t+")"}function isInAngleRange(e,t,i){function a(e){while(e<0){e+=2*Math.PI}while(e>2*Math.PI){e-=2*Math.PI}return e}e=a(e);t=a(t);i=a(i);if(t>i){i+=2*Math.PI;if(e<t){e+=2*Math.PI}}return e>=t&&e<=i}function convertCoordinateOrigin(e,t,i){return{x:i.x+e,y:i.y-t}}function avoidCollision(e,t){if(t){while(util.isCollision(e,t)){if(e.start.x>0){e.start.y--}else if(e.start.x<0){e.start.y++}else{if(e.start.y>0){e.start.y++}else{e.start.y--}}}}return e}function fixPieSeries(t,i,e){let a=[];if(t.length>0&&t[0].data.constructor.toString().indexOf("Array")>-1){i._pieSeries_=t;let e=t[0].data;for(var r=0;r<e.length;r++){e[r].formatter=t[0].formatter;e[r].data=e[r].value;a.push(e[r])}i.series=a}else{a=t}return a}function fillSeries(t,i,a){var r=0;for(var n=0;n<t.length;n++){let e=t[n];if(!e.color){e.color=a.color[r];r=(r+1)%a.color.length}if(!e.linearIndex){e.linearIndex=n}if(!e.index){e.index=0}if(!e.type){e.type=i.type}if(typeof e.show=="undefined"){e.show=true}if(!e.type){e.type=i.type}if(!e.pointShape){e.pointShape="circle"}if(!e.legendShape){switch(e.type){case"line":e.legendShape="line";break;case"column":case"bar":e.legendShape="rect";break;case"area":case"mount":e.legendShape="triangle";break;default:e.legendShape="circle"}}}return t}function fillCustomColor(e,t,i,a){var r=t||[];if(e=="custom"&&r.length==0){r=a.linearColor}if(e=="custom"&&r.length<i.length){let e=i.length-r.length;for(var n=0;n<e;n++){r.push(a.linearColor[(n+1)%a.linearColor.length])}}return r}function measureText(i,e,t){var a=0;i=String(i);t=false;if(t!==false&&t!==undefined&&t.setFontSize&&t.measureText){t.setFontSize(e);return t.measureText(i).width}else{var i=i.split("");for(let t=0;t<i.length;t++){let e=i[t];if(/[a-zA-Z]/.test(e)){a+=7}else if(/[0-9]/.test(e)){a+=5.5}else if(/\./.test(e)){a+=2.7}else if(/-/.test(e)){a+=3.25}else if(/:/.test(e)){a+=2.5}else if(/[\u4e00-\u9fa5]/.test(e)){a+=10}else if(/\(|\)/.test(e)){a+=3.73}else if(/\s/.test(e)){a+=2.5}else if(/%/.test(e)){a+=8}else{a+=10}}return a*e/10}}function dataCombine(e){return e.reduce(function(e,t){return(e.data?e.data:e).concat(t.data)},[])}function dataCombineStack(e,t){var i=new Array(t);for(var a=0;a<i.length;a++){i[a]=0}for(var r=0;r<e.length;r++){for(var a=0;a<i.length;a++){i[a]+=e[r].data[a]}}return e.reduce(function(e,t){return(e.data?e.data:e).concat(t.data).concat(i)},[])}function getTouches(e,t,i){let a,r;if(e.clientX){if(t.rotate){r=t.height-e.clientX*t.pix;a=(e.pageY-i.currentTarget.offsetTop-t.height/t.pix/2*(t.pix-1))*t.pix}else{a=e.clientX*t.pix;r=(e.pageY-i.currentTarget.offsetTop-t.height/t.pix/2*(t.pix-1))*t.pix}}else{if(t.rotate){r=t.height-e.x*t.pix;a=e.y*t.pix}else{a=e.x*t.pix;r=e.y*t.pix}}return{x:a,y:r}}function getSeriesDataItem(t,a,i){var r=[];var n=[];var o=a.constructor.toString().indexOf("Array")>-1;if(o){let e=filterSeries(t);for(var s=0;s<i.length;s++){n.push(e[i[s]])}}else{n=t}for(let e=0;e<n.length;e++){let t=n[e];let i=-1;if(o){i=a[e]}else{i=a}if(t.data[i]!==null&&typeof t.data[i]!=="undefined"&&t.show){let e={};e.color=t.color;e.type=t.type;e.style=t.style;e.pointShape=t.pointShape;e.disableLegend=t.disableLegend;e.legendShape=t.legendShape;e.name=t.name;e.show=t.show;e.data=t.formatter?t.formatter(t.data[i]):t.data[i];r.push(e)}}return r}function filterSeries(t){let i=[];for(let e=0;e<t.length;e++){if(t[e].show==true){i.push(t[e])}}return i}function findCurrentIndex(n,o,t,e){var i=arguments.length>4&&arguments[4]!==undefined?arguments[4]:0;var s={index:-1,group:[]};var a=t.chartData.eachSpacing/2;let r=[];if(o&&o.length>0){if(!t.categories){a=0}else{for(let e=1;e<t.chartData.xAxisPoints.length;e++){r.push(t.chartData.xAxisPoints[e]-a)}if((t.type=="line"||t.type=="area")&&t.xAxis.boundaryGap=="justify"){r=t.chartData.xAxisPoints}}if(isInExactChartArea(n,t,e)){if(!t.categories){let i=Array(o.length);for(let t=0;t<o.length;t++){i[t]=Array(o[t].length);for(let e=0;e<o[t].length;e++){i[t][e]=Math.abs(o[t][e].x-n.x)}}let t=Array(i.length);let a=Array(i.length);for(let e=0;e<i.length;e++){t[e]=Math.min.apply(null,i[e]);a[e]=i[e].indexOf(t[e])}let r=Math.min.apply(null,t);s.index=[];for(let e=0;e<t.length;e++){if(t[e]==r){s.group.push(e);s.index.push(a[e])}}}else{r.forEach(function(e,t){if(n.x+i+a>e){s.index=t}})}}}return s}function findLegendIndex(n,e,t){let o=-1;let s=0;if(isInExactLegendArea(n,e.area)){let a=e.points;let r=-1;for(let e=0,t=a.length;e<t;e++){let i=a[e];for(let t=0;t<i.length;t++){r+=1;let e=i[t]["area"];if(e&&n.x>e[0]-s&&n.x<e[2]+s&&n.y>e[1]-s&&n.y<e[3]+s){o=r;break}}}return o}return o}function isInExactLegendArea(e,t){return e.x>t.start.x&&e.x<t.end.x&&e.y>t.start.y&&e.y<t.end.y}function isInExactChartArea(e,t,i){return e.x<=t.width-t.area[1]+10&&e.x>=t.area[3]-10&&e.y>=t.area[0]&&e.y<=t.height-t.area[2]}function findPieChartCurrentIndex(e,t,i){var a=-1;var r=getPieDataPoints(t.series);if(t&&t.center&&isInExactPieChartArea(e,t.center,t.radius)){var n=Math.atan2(t.center.y-e.y,e.x-t.center.x);n=-n;if(i.extra.pie&&i.extra.pie.offsetAngle){n=n-i.extra.pie.offsetAngle*Math.PI/180}if(i.extra.ring&&i.extra.ring.offsetAngle){n=n-i.extra.ring.offsetAngle*Math.PI/180}for(var o=0,s=r.length;o<s;o++){if(isInAngleRange(n,r[o]._start_,r[o]._start_+r[o]._proportion_*2*Math.PI)){a=o;break}}}return a}function isInExactPieChartArea(e,t,i){return Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2)<=Math.pow(i,2)}function splitPoints(e,i){var a=[];var r=[];e.forEach(function(e,t){if(i.connectNulls){if(e!==null){r.push(e)}}else{if(e!==null){r.push(e)}else{if(r.length){a.push(r)}r=[]}}});if(r.length){a.push(r)}return a}function calLegendData(s,l,t,e,h){let f={area:{start:{x:0,y:0},end:{x:0,y:0},width:0,height:0,wholeWidth:0,wholeHeight:0},points:[],widthArr:[],heightArr:[]};if(l.legend.show===false){e.legendData=f;return f}let x=l.legend.padding*l.pix;let p=l.legend.margin*l.pix;let d=l.legend.fontSize?l.legend.fontSize*l.pix:t.fontSize;let c=15*l.pix;let g=5*l.pix;let u=Math.max(l.legend.lineHeight*l.pix,d);if(l.legend.position=="top"||l.legend.position=="bottom"){let a=[];let r=0;let n=[];let o=[];for(let i=0;i<s.length;i++){let e=s[i];const y=e.legendText?e.legendText:e.name;let t=c+g+measureText(y||"undefined",d,h)+l.legend.itemGap*l.pix;if(r+t>l.width-l.area[1]-l.area[3]){a.push(o);n.push(r-l.legend.itemGap*l.pix);r=t;o=[e]}else{r+=t;o.push(e)}}if(o.length){a.push(o);n.push(r-l.legend.itemGap*l.pix);f.widthArr=n;let e=Math.max.apply(null,n);switch(l.legend.float){case"left":f.area.start.x=l.area[3];f.area.end.x=l.area[3]+e+2*x;break;case"right":f.area.start.x=l.width-l.area[1]-e-2*x;f.area.end.x=l.width-l.area[1];break;default:f.area.start.x=(l.width-e)/2-x;f.area.end.x=(l.width+e)/2+x}f.area.width=e+2*x;f.area.wholeWidth=e+2*x;f.area.height=a.length*u+2*x;f.area.wholeHeight=a.length*u+2*x+2*p;f.points=a}}else{let e=s.length;let t=l.height-l.area[0]-l.area[2]-2*p-2*x;let i=Math.min(Math.floor(t/u),e);f.area.height=i*u+x*2;f.area.wholeHeight=i*u+x*2;switch(l.legend.float){case"top":f.area.start.y=l.area[0]+p;f.area.end.y=l.area[0]+p+f.area.height;break;case"bottom":f.area.start.y=l.height-l.area[2]-p-f.area.height;f.area.end.y=l.height-l.area[2]-p;break;default:f.area.start.y=(l.height-f.area.height)/2;f.area.end.y=(l.height+f.area.height)/2}let a=e%i===0?e/i:Math.floor(e/i+1);let r=[];for(let t=0;t<a;t++){let e=s.slice(t*i,t*i+i);r.push(e)}f.points=r;if(r.length){for(let e=0;e<r.length;e++){let i=r[e];let a=0;for(let t=0;t<i.length;t++){let e=c+g+measureText(i[t].name||"undefined",d,h)+l.legend.itemGap*l.pix;if(e>a){a=e}}f.widthArr.push(a);f.heightArr.push(i.length*u+x*2)}let t=0;for(let e=0;e<f.widthArr.length;e++){t+=f.widthArr[e]}f.area.width=t-l.legend.itemGap*l.pix+2*x;f.area.wholeWidth=f.area.width+x}}switch(l.legend.position){case"top":f.area.start.y=l.area[0]+p;f.area.end.y=l.area[0]+p+f.area.height;break;case"bottom":f.area.start.y=l.height-l.area[2]-f.area.height-p;f.area.end.y=l.height-l.area[2]-p;break;case"left":f.area.start.x=l.area[3];f.area.end.x=l.area[3]+f.area.width;break;case"right":f.area.start.x=l.width-l.area[1]-f.area.width;f.area.end.x=l.width-l.area[1];break}e.legendData=f;return f}function calCategoriesData(e,a,t,i,r){var n={angle:0,xAxisHeight:a.xAxis.lineHeight*a.pix+a.xAxis.marginTop*a.pix};var o=a.xAxis.fontSize*a.pix;var s=e.map(function(e,t){var i=a.xAxis.formatter?a.xAxis.formatter(e,t,a):e;return measureText(String(i),o,r)});var l=Math.max.apply(this,s);if(a.xAxis.rotateLabel==true){n.angle=a.xAxis.rotateAngle*Math.PI/180;let e=a.xAxis.marginTop*a.pix*2+Math.abs(l*Math.sin(n.angle));e=e<o+a.xAxis.marginTop*a.pix*2?e+a.xAxis.marginTop*a.pix*2:e;n.xAxisHeight=e}if(a.enableScroll&&a.xAxis.scrollShow){n.xAxisHeight+=6*a.pix}if(a.xAxis.disabled){n.xAxisHeight=0}return n}function getPieDataPoints(i,a){var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:1;var n=0;var o=0;for(let t=0;t<i.length;t++){let e=i[t];e.data=e.data===null?0:e.data;n+=e.data}for(let t=0;t<i.length;t++){let e=i[t];e.data=e.data===null?0:e.data;if(n===0){e._proportion_=1/i.length*r}else{e._proportion_=e.data/n*r}e._radius_=a}for(let t=0;t<i.length;t++){let e=i[t];e._start_=o;o+=2*e._proportion_*Math.PI}return i}function getPieTextMaxLength(a,r,n,o){a=getPieDataPoints(a);let s=0;for(let i=0;i<a.length;i++){let e=a[i];let t=e.formatter?e.formatter(+e._proportion_.toFixed(2)):util.toFixed(e._proportion_*100)+"%";s=Math.max(s,measureText(t,e.textSize*o.pix||r.fontSize,n))}return s}function contextRotate(e,t){if(t.rotateLock!==true){e.translate(t.height,0);e.rotate(90*Math.PI/180)}else if(t._rotate_!==true){e.translate(t.height,0);e.rotate(90*Math.PI/180);t._rotate_=true}}function drawPointShape(e,t,i,a,r){a.beginPath();if(r.dataPointShapeType=="hollow"){a.setStrokeStyle(t);a.setFillStyle(r.background);a.setLineWidth(2*r.pix)}else{a.setStrokeStyle("#ffffff");a.setFillStyle(t);a.setLineWidth(1*r.pix)}if(i==="diamond"){e.forEach(function(e,t){if(e!==null){a.moveTo(e.x,e.y-4.5);a.lineTo(e.x-4.5,e.y);a.lineTo(e.x,e.y****);a.lineTo(e.x****,e.y);a.lineTo(e.x,e.y-4.5)}})}else if(i==="circle"){e.forEach(function(e,t){if(e!==null){a.moveTo(e.x*****r.pix,e.y);a.arc(e.x,e.y,3*r.pix,0,2*Math.PI,false)}})}else if(i==="square"){e.forEach(function(e,t){if(e!==null){a.moveTo(e.x-3.5,e.y-3.5);a.rect(e.x-3.5,e.y-3.5,7,7)}})}else if(i==="triangle"){e.forEach(function(e,t){if(e!==null){a.moveTo(e.x,e.y-4.5);a.lineTo(e.x-4.5,e.y****);a.lineTo(e.x****,e.y****);a.lineTo(e.x,e.y-4.5)}})}else if(i==="none"){return}a.closePath();a.fill();a.stroke()}function drawActivePoint(e,t,i,a,r,n,o){if(!r.tooltip){return}if(r.tooltip.group.length>0&&r.tooltip.group.includes(o)==false){return}var s=typeof r.tooltip.index==="number"?r.tooltip.index:r.tooltip.index[r.tooltip.group.indexOf(o)];a.beginPath();if(n.activeType=="hollow"){a.setStrokeStyle(t);a.setFillStyle(r.background);a.setLineWidth(2*r.pix)}else{a.setStrokeStyle("#ffffff");a.setFillStyle(t);a.setLineWidth(1*r.pix)}if(i==="diamond"){e.forEach(function(e,t){if(e!==null&&s==t){a.moveTo(e.x,e.y-4.5);a.lineTo(e.x-4.5,e.y);a.lineTo(e.x,e.y****);a.lineTo(e.x****,e.y);a.lineTo(e.x,e.y-4.5)}})}else if(i==="circle"){e.forEach(function(e,t){if(e!==null&&s==t){a.moveTo(e.x*****r.pix,e.y);a.arc(e.x,e.y,3*r.pix,0,2*Math.PI,false)}})}else if(i==="square"){e.forEach(function(e,t){if(e!==null&&s==t){a.moveTo(e.x-3.5,e.y-3.5);a.rect(e.x-3.5,e.y-3.5,7,7)}})}else if(i==="triangle"){e.forEach(function(e,t){if(e!==null&&s==t){a.moveTo(e.x,e.y-4.5);a.lineTo(e.x-4.5,e.y****);a.lineTo(e.x****,e.y****);a.lineTo(e.x,e.y-4.5)}})}else if(i==="none"){return}a.closePath();a.fill();a.stroke()}function drawRingTitle(e,t,i,a){var r=e.title.fontSize||t.titleFontSize;var n=e.subtitle.fontSize||t.subtitleFontSize;var o=e.title.name||"";var s=e.subtitle.name||"";var f=e.title.color||e.fontColor;var x=e.subtitle.color||e.fontColor;var p=o?r:0;var d=s?n:0;var l=5;if(s){var c=measureText(s,n*e.pix,i);var u=a.x-c/2+(e.subtitle.offsetX||0)*e.pix;var h=a.y+n*e.pix/2+(e.subtitle.offsetY||0)*e.pix;if(o){h+=(p*e.pix+l)/2}i.beginPath();i.setFontSize(n*e.pix);i.setFillStyle(x);i.fillText(s,u,h);i.closePath();i.stroke()}if(o){var g=measureText(o,r*e.pix,i);var y=a.x-g/2+(e.title.offsetX||0);var v=a.y+r*e.pix/2+(e.title.offsetY||0)*e.pix;if(s){v-=(d*e.pix+l)/2}i.beginPath();i.setFontSize(r*e.pix);i.setFillStyle(f);i.fillText(o,y,v);i.closePath();i.stroke()}}function drawPieText(o,x,p,d,e,s){var c=p.pieChartLinePadding;var u=[];var g=null;var y=o.map(function(e,t){var i=e.formatter?e.formatter(e,t,o,x):util.toFixed(e._proportion_.toFixed(4)*100)+"%";i=e.labelText?e.labelText:i;var a=2*Math.PI-(e._start_+2*Math.PI*e._proportion_/2);if(e._rose_proportion_){a=2*Math.PI-(e._start_+2*Math.PI*e._rose_proportion_/2)}var r=e.color;var n=e._radius_;return{arc:a,text:i,color:r,radius:n,textColor:e.textColor,textSize:e.textSize,labelShow:e.labelShow}});for(let f=0;f<y.length;f++){let e=y[f];let t=Math.cos(e.arc)*(e.radius+c);let i=Math.sin(e.arc)*(e.radius+c);let a=Math.cos(e.arc)*e.radius;let r=Math.sin(e.arc)*e.radius;let n=t>=0?t+p.pieChartTextPadding:t-p.pieChartTextPadding;let o=i;let s=measureText(e.text,e.textSize*x.pix||p.fontSize,d);let l=o;if(g&&util.isSameXCoordinateArea(g.start,{x:n})){if(n>0){l=Math.min(o,g.start.y)}else if(t<0){l=Math.max(o,g.start.y)}else{if(o>0){l=Math.max(o,g.start.y)}else{l=Math.min(o,g.start.y)}}}if(n<0){n-=s}let h={lineStart:{x:a,y:r},lineEnd:{x:t,y:i},start:{x:n,y:l},width:s,height:p.fontSize,text:e.text,color:e.color,textColor:e.textColor,textSize:e.textSize};g=avoidCollision(h,g);u.push(g)}for(let o=0;o<u.length;o++){if(y[o].labelShow===false){continue}let e=u[o];let t=convertCoordinateOrigin(e.lineStart.x,e.lineStart.y,s);let i=convertCoordinateOrigin(e.lineEnd.x,e.lineEnd.y,s);let a=convertCoordinateOrigin(e.start.x,e.start.y,s);d.setLineWidth(1*x.pix);d.setFontSize(e.textSize*x.pix||p.fontSize);d.beginPath();d.setStrokeStyle(e.color);d.setFillStyle(e.color);d.moveTo(t.x,t.y);let r=e.start.x<0?a.x+e.width:a.x;let n=e.start.x<0?a.x-5:a.x+5;d.quadraticCurveTo(i.x,i.y,r,a.y);d.moveTo(t.x,t.y);d.stroke();d.closePath();d.beginPath();d.moveTo(a.x+e.width,a.y);d.arc(r,a.y,2*x.pix,0,2*Math.PI);d.closePath();d.fill();d.beginPath();d.setFontSize(e.textSize*x.pix||p.fontSize);d.setFillStyle(e.textColor||x.fontColor);d.fillText(e.text,n,a.y+3);d.closePath();d.stroke();d.closePath()}}function drawToolTip(t,r,n,i,o,a,d){var s=assign({},{showBox:true,showArrow:true,showCategory:false,bgColor:"#000000",bgOpacity:.7,borderColor:"#000000",borderWidth:0,borderRadius:0,borderOpacity:.7,boxPadding:3,fontColor:"#FFFFFF",fontSize:13,lineHeight:20,legendShow:true,legendShape:"auto",splitLine:true},n.extra.tooltip);if(s.showCategory==true&&n.categories){t.unshift({text:n.categories[n.tooltip.index],color:null})}var l=s.fontSize*n.pix;var c=s.lineHeight*n.pix;var h=s.boxPadding*n.pix;var f=l;var u=5*n.pix;if(s.legendShow==false){f=0;u=0}var x=s.showArrow?8*n.pix:0;var g=false;if(n.type=="line"||n.type=="mount"||n.type=="area"||n.type=="candle"||n.type=="mix"){if(s.splitLine==true){drawToolTipSplitLine(n.tooltip.offset.x,n,i,o)}}r=assign({x:0,y:0},r);r.y-=8*n.pix;var y=t.map(function(e){return measureText(e.text,l,o)});var p=f+u+4*h+Math.max.apply(null,y);var v=2*h+t.length*c;if(s.showBox==false){return}if(r.x-Math.abs(n._scrollDistance_||0)+x+p>n.width){g=true}if(v+r.y>n.height){r.y=n.height-v}o.beginPath();o.setFillStyle(hexToRgb(s.bgColor,s.bgOpacity));o.setLineWidth(s.borderWidth*n.pix);o.setStrokeStyle(hexToRgb(s.borderColor,s.borderOpacity));var e=s.borderRadius;if(g){if(p+x>n.width){r.x=n.width+Math.abs(n._scrollDistance_||0)+x+(p-n.width)}if(p>r.x){r.x=n.width+Math.abs(n._scrollDistance_||0)+x+(p-n.width)}if(s.showArrow){o.moveTo(r.x,r.y+10*n.pix);o.lineTo(r.x-x,r.y+10*n.pix+5*n.pix)}o.arc(r.x-x-e,r.y+v-e,e,0,Math.PI/2,false);o.arc(r.x-x-Math.round(p)+e,r.y+v-e,e,Math.PI/2,Math.PI,false);o.arc(r.x-x-Math.round(p)+e,r.y+e,e,-Math.PI,-Math.PI/2,false);o.arc(r.x-x-e,r.y+e,e,-Math.PI/2,0,false);if(s.showArrow){o.lineTo(r.x-x,r.y+10*n.pix-5*n.pix);o.lineTo(r.x,r.y+10*n.pix)}}else{if(s.showArrow){o.moveTo(r.x,r.y+10*n.pix);o.lineTo(r.x+x,r.y+10*n.pix-5*n.pix)}o.arc(r.x+x+e,r.y+e,e,-Math.PI,-Math.PI/2,false);o.arc(r.x+x+Math.round(p)-e,r.y+e,e,-Math.PI/2,0,false);o.arc(r.x+x+Math.round(p)-e,r.y+v-e,e,0,Math.PI/2,false);o.arc(r.x+x+e,r.y+v-e,e,Math.PI/2,Math.PI,false);if(s.showArrow){o.lineTo(r.x+x,r.y+10*n.pix+5*n.pix);o.lineTo(r.x,r.y+10*n.pix)}}o.closePath();o.fill();if(s.borderWidth>0){o.stroke()}if(s.legendShow){t.forEach(function(e,t){if(e.color!==null){o.beginPath();o.setFillStyle(e.color);var i=r.x+x+2*h;var a=r.y+(c-l)/2+c*t+h+1;if(g){i=r.x-p-x+2*h}switch(e.legendShape){case"line":o.moveTo(i,a+.5*f-2*n.pix);o.fillRect(i,a+.5*f-2*n.pix,f,4*n.pix);break;case"triangle":o.moveTo(i+7.5*n.pix,a+.5*f-5*n.pix);o.lineTo(i*****n.pix,a+.5*f+5*n.pix);o.lineTo(i+12.5*n.pix,a+.5*f+5*n.pix);o.lineTo(i+7.5*n.pix,a+.5*f-5*n.pix);break;case"diamond":o.moveTo(i+7.5*n.pix,a+.5*f-5*n.pix);o.lineTo(i*****n.pix,a+.5*f);o.lineTo(i+7.5*n.pix,a+.5*f+5*n.pix);o.lineTo(i+12.5*n.pix,a+.5*f);o.lineTo(i+7.5*n.pix,a+.5*f-5*n.pix);break;case"circle":o.moveTo(i+7.5*n.pix,a+.5*f);o.arc(i+7.5*n.pix,a+.5*f,5*n.pix,0,2*Math.PI);break;case"rect":o.moveTo(i,a+.5*f-5*n.pix);o.fillRect(i,a+.5*f-5*n.pix,15*n.pix,10*n.pix);break;case"square":o.moveTo(i+2*n.pix,a+.5*f-5*n.pix);o.fillRect(i+2*n.pix,a+.5*f-5*n.pix,10*n.pix,10*n.pix);break;default:o.moveTo(i,a+.5*f-5*n.pix);o.fillRect(i,a+.5*f-5*n.pix,15*n.pix,10*n.pix)}o.closePath();o.fill()}})}t.forEach(function(e,t){var i=r.x+x+2*h+f+u;if(g){i=r.x-p-x+2*h+f+u}var a=r.y+c*t+(c-l)/2-1+h+l;o.beginPath();o.setFontSize(l);o.setTextBaseline("normal");o.setFillStyle(s.fontColor);o.fillText(e.text,i,a);o.closePath();o.stroke()})}function drawToolTipBridge(e,t,i,a,r,n){var o=e.extra.tooltip||{};if(o.horizentalLine&&e.tooltip&&a===1&&(e.type=="line"||e.type=="area"||e.type=="column"||e.type=="mount"||e.type=="candle"||e.type=="mix")){drawToolTipHorizentalLine(e,t,i,r,n)}i.save();if(e._scrollDistance_&&e._scrollDistance_!==0&&e.enableScroll===true){i.translate(e._scrollDistance_,0)}if(e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&a===1){drawToolTip(e.tooltip.textList,e.tooltip.offset,e,t,i,r,n)}i.restore()}function drawLegend(e,s,y,l,t){if(s.legend.show===false){return}let h=t.legendData;let i=h.points;let f=h.area;let x=s.legend.padding*s.pix;let p=s.legend.fontSize*s.pix;let d=15*s.pix;let c=5*s.pix;let u=s.legend.itemGap*s.pix;let g=Math.max(s.legend.lineHeight*s.pix,p);l.beginPath();l.setLineWidth(s.legend.borderWidth*s.pix);l.setStrokeStyle(s.legend.borderColor);l.setFillStyle(s.legend.backgroundColor);l.moveTo(f.start.x,f.start.y);l.rect(f.start.x,f.start.y,f.width,f.height);l.closePath();l.fill();l.stroke();i.forEach(function(a,e){let t=0;let i=0;t=h.widthArr[e];i=h.heightArr[e];let r=0;let n=0;if(s.legend.position=="top"||s.legend.position=="bottom"){switch(s.legend.float){case"left":r=f.start.x+x;break;case"right":r=f.start.x+f.width-t;break;default:r=f.start.x+(f.width-t)/2}n=f.start.y+x+e*g}else{if(e==0){t=0}else{t=h.widthArr[e-1]}r=f.start.x+x+t;n=f.start.y+x+(f.height-i)/2}l.setFontSize(y.fontSize);for(let i=0;i<a.length;i++){let e=a[i];e.area=[0,0,0,0];e.area[0]=r;e.area[1]=n;e.area[3]=n+g;l.beginPath();l.setLineWidth(1*s.pix);l.setStrokeStyle(e.show?e.color:s.legend.hiddenColor);l.setFillStyle(e.show?e.color:s.legend.hiddenColor);switch(e.legendShape){case"line":l.moveTo(r,n+.5*g-2*s.pix);l.fillRect(r,n+.5*g-2*s.pix,15*s.pix,4*s.pix);break;case"triangle":l.moveTo(r+7.5*s.pix,n+.5*g-5*s.pix);l.lineTo(r*****s.pix,n+.5*g+5*s.pix);l.lineTo(r+12.5*s.pix,n+.5*g+5*s.pix);l.lineTo(r+7.5*s.pix,n+.5*g-5*s.pix);break;case"diamond":l.moveTo(r+7.5*s.pix,n+.5*g-5*s.pix);l.lineTo(r*****s.pix,n+.5*g);l.lineTo(r+7.5*s.pix,n+.5*g+5*s.pix);l.lineTo(r+12.5*s.pix,n+.5*g);l.lineTo(r+7.5*s.pix,n+.5*g-5*s.pix);break;case"circle":l.moveTo(r+7.5*s.pix,n+.5*g);l.arc(r+7.5*s.pix,n+.5*g,5*s.pix,0,2*Math.PI);break;case"rect":l.moveTo(r,n+.5*g-5*s.pix);l.fillRect(r,n+.5*g-5*s.pix,15*s.pix,10*s.pix);break;case"square":l.moveTo(r+5*s.pix,n+.5*g-5*s.pix);l.fillRect(r+5*s.pix,n+.5*g-5*s.pix,10*s.pix,10*s.pix);break;case"none":break;default:l.moveTo(r,n+.5*g-5*s.pix);l.fillRect(r,n+.5*g-5*s.pix,15*s.pix,10*s.pix)}l.closePath();l.fill();l.stroke();r+=d+c;let t=.5*g+.5*p-2;const o=e.legendText?e.legendText:e.name;l.beginPath();l.setFontSize(p);l.setFillStyle(e.show?s.legend.fontColor:s.legend.hiddenColor);l.fillText(o,r,n+t);l.closePath();l.stroke();if(s.legend.position=="top"||s.legend.position=="bottom"){r+=measureText(o,p,l)+u;e.area[2]=r}else{e.area[2]=r+measureText(o,p,l)+u;r-=d+c;n+=g}}})}function drawPieDataPoints(e,r,t,n){var i=arguments.length>4&&arguments[4]!==undefined?arguments[4]:1;var o=assign({},{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,ringWidth:30,customRadius:0,border:false,borderWidth:2,borderColor:"#FFFFFF",centerColor:"#FFFFFF",linearType:"none",customColor:[]},r.type=="pie"?r.extra.pie:r.extra.ring);var s={x:r.area[3]+(r.width-r.area[1]-r.area[3])/2,y:r.area[0]+(r.height-r.area[0]-r.area[2])/2};if(t.pieChartLinePadding==0){t.pieChartLinePadding=o.activeRadius*r.pix}var a=Math.min((r.width-r.area[1]-r.area[3])/2-t.pieChartLinePadding-t.pieChartTextPadding-t._pieTextMaxLength_,(r.height-r.area[0]-r.area[2])/2-t.pieChartLinePadding-t.pieChartTextPadding);a=a<10?10:a;if(o.customRadius>0){a=o.customRadius*r.pix}e=getPieDataPoints(e,a,i);var h=o.activeRadius*r.pix;o.customColor=fillCustomColor(o.linearType,o.customColor,e,t);e=e.map(function(e){e._start_+=o.offsetAngle*Math.PI/180;return e});e.forEach(function(e,t){if(r.tooltip){if(r.tooltip.index==t){n.beginPath();n.setFillStyle(hexToRgb(e.color,o.activeOpacity||.5));n.moveTo(s.x,s.y);n.arc(s.x,s.y,e._radius_+h,e._start_,e._start_+2*e._proportion_*Math.PI);n.closePath();n.fill()}}n.beginPath();n.setLineWidth(o.borderWidth*r.pix);n.lineJoin="round";n.setStrokeStyle(o.borderColor);var i=e.color;if(o.linearType=="custom"){var a;if(n.createCircularGradient){a=n.createCircularGradient(s.x,s.y,e._radius_)}else{a=n.createRadialGradient(s.x,s.y,0,s.x,s.y,e._radius_)}a.addColorStop(0,hexToRgb(o.customColor[e.linearIndex],1));a.addColorStop(1,hexToRgb(e.color,1));i=a}n.setFillStyle(i);n.moveTo(s.x,s.y);n.arc(s.x,s.y,e._radius_,e._start_,e._start_+2*e._proportion_*Math.PI);n.closePath();n.fill();if(o.border==true){n.stroke()}});if(r.type==="ring"){var l=a*.6;if(typeof o.ringWidth==="number"&&o.ringWidth>0){l=Math.max(0,a-o.ringWidth*r.pix)}n.beginPath();n.setFillStyle(o.centerColor);n.moveTo(s.x,s.y);n.arc(s.x,s.y,l,0,2*Math.PI);n.closePath();n.fill()}if(r.dataLabel!==false&&i===1){drawPieText(e,r,t,n,a,s)}if(i===1&&r.type==="ring"){drawRingTitle(r,t,n,s)}return{center:s,radius:a,series:e}}function drawCanvas(e,t){t.save();t.translate(0,.5);t.restore();t.draw()}var Timing={easeIn:function e(t){return Math.pow(t,3)},easeOut:function e(t){return Math.pow(t-1,3)+1},easeInOut:function e(t){if((t/=.5)<1){return.5*Math.pow(t,3)}else{return.5*(Math.pow(t-2,3)+2)}},linear:function e(t){return t}};function Animation(r){this.isStop=false;r.duration=typeof r.duration==="undefined"?1e3:r.duration;r.timing=r.timing||"easeInOut";var n=17;function e(){if(typeof setTimeout!=="undefined"){return function(t,e){setTimeout(function(){var e=+new Date;t(e)},e)}}else if(typeof requestAnimationFrame!=="undefined"){return requestAnimationFrame}else{return function(e){e(null)}}}var o=e();var s=null;var l=function e(t){if(t===null||this.isStop===true){r.onProcess&&r.onProcess(1);r.onAnimationFinish&&r.onAnimationFinish();return}if(s===null){s=t}if(t-s<r.duration){var i=(t-s)/r.duration;var a=Timing[r.timing];i=a(i);r.onProcess&&r.onProcess(i);o(l,n)}else{r.onProcess&&r.onProcess(1);r.onAnimationFinish&&r.onAnimationFinish()}};l=l.bind(this);o(l,n)}Animation.prototype.stop=function(){this.isStop=true};function drawCharts(e,a,r,n){var t=this;var o=a.series;if(e==="pie"||e==="ring"||e==="mount"||e==="rose"||e==="funnel"){o=fixPieSeries(o,a,r)}var s=a.categories;if(e==="mount"){s=[];for(let e=0;e<o.length;e++){if(o[e].show!==false)s.push(o[e].name)}a.categories=s}o=fillSeries(o,a,r);var f=a.animation?a.duration:0;t.animationInstance&&t.animationInstance.stop();var i=null;if(e=="candle"){let e=assign({},a.extra.candle.average);if(e.show){i=calCandleMA(e.day,e.name,e.color,o[0].data);i=fillSeries(i,a,r);a.seriesMA=i}else if(a.seriesMA){i=a.seriesMA=fillSeries(a.seriesMA,a,r)}else{i=o}}else{i=o}a._series_=o=filterSeries(o);a.area=new Array(4);for(let e=0;e<4;e++){a.area[e]=a.padding[e]*a.pix}var x=calLegendData(i,a,r,a.chartData,n),p=x.area.wholeHeight,d=x.area.wholeWidth;switch(a.legend.position){case"top":a.area[0]+=p;break;case"bottom":a.area[2]+=p;break;case"left":a.area[3]+=d;break;case"right":a.area[1]+=d;break}let l={},h=0;if(a.type==="line"||a.type==="column"||a.type==="mount"||a.type==="area"||a.type==="mix"||a.type==="candle"||a.type==="scatter"||a.type==="bubble"||a.type==="bar"){l=calYAxisData(o,a,r,n);h=l.yAxisWidth;if(a.yAxis.showTitle){let t=0;for(let e=0;e<a.yAxis.data.length;e++){t=Math.max(t,a.yAxis.data[e].titleFontSize?a.yAxis.data[e].titleFontSize*a.pix:r.fontSize)}a.area[0]+=t}let t=0,i=0;for(let e=0;e<h.length;e++){if(h[e].position=="left"){if(i>0){a.area[3]+=h[e].width+a.yAxis.padding*a.pix}else{a.area[3]+=h[e].width}i+=1}else if(h[e].position=="right"){if(t>0){a.area[1]+=h[e].width+a.yAxis.padding*a.pix}else{a.area[1]+=h[e].width}t+=1}}}else{r.yAxisWidth=h}a.chartData.yAxisData=l;if(a.categories&&a.categories.length&&a.type!=="radar"&&a.type!=="gauge"&&a.type!=="bar"){a.chartData.xAxisData=getXAxisPoints(a.categories,a,r);let e=calCategoriesData(a.categories,a,r,a.chartData.xAxisData.eachSpacing,n),t=e.xAxisHeight,i=e.angle;r.xAxisHeight=t;r._xAxisTextAngle_=i;a.area[2]+=t;a.chartData.categoriesData=e}else{if(a.type==="line"||a.type==="area"||a.type==="scatter"||a.type==="bubble"||a.type==="bar"){a.chartData.xAxisData=calXAxisData(o,a,r,n);s=a.chartData.xAxisData.rangesFormat;let e=calCategoriesData(s,a,r,a.chartData.xAxisData.eachSpacing,n),t=e.xAxisHeight,i=e.angle;r.xAxisHeight=t;r._xAxisTextAngle_=i;a.area[2]+=t;a.chartData.categoriesData=e}else{a.chartData.xAxisData={xAxisPoints:[]}}}if(e==="pie"||e==="ring"||e==="rose"){r._pieTextMaxLength_=a.dataLabel===false?0:getPieTextMaxLength(i,r,n,a)}switch(e){case"pie":this.animationInstance=new Animation({timing:a.timing,duration:f,onProcess:function e(t){n.clearRect(0,0,a.width,a.height);if(a.rotate){contextRotate(n,a)}a.chartData.pieData=drawPieDataPoints(o,a,r,n,t);drawLegend(a.series,a,r,n,a.chartData);drawToolTipBridge(a,r,n,t);drawCanvas(a,n)},onAnimationFinish:function e(){t.uevent.trigger("renderComplete")}});break}}function uChartsEvent(){this.events={}}uChartsEvent.prototype.addEventListener=function(e,t){this.events[e]=this.events[e]||[];this.events[e].push(t)};uChartsEvent.prototype.delEventListener=function(e){this.events[e]=[]};uChartsEvent.prototype.trigger=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++){t[i]=arguments[i]}var a=t[0];var r=t.slice(1);if(!!this.events[a]){this.events[a].forEach(function(e){try{e.apply(null,r)}catch(e){}})}};var uCharts=function e(i){i.pix=i.pixelRatio?i.pixelRatio:1;i.fontSize=i.fontSize?i.fontSize:13;i.fontColor=i.fontColor?i.fontColor:config.fontColor;if(i.background==""||i.background=="none"){i.background="#FFFFFF"}i.title=assign({},i.title);i.subtitle=assign({},i.subtitle);i.duration=i.duration?i.duration:1e3;i.legend=assign({},{show:true,position:"bottom",float:"center",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",borderWidth:0,padding:5,margin:5,itemGap:10,fontSize:i.fontSize,lineHeight:i.fontSize,fontColor:i.fontColor,formatter:{},hiddenColor:"#CECECE"},i.legend);i.extra=assign({tooltip:{legendShape:"auto"}},i.extra);i.rotate=i.rotate?true:false;i.animation=i.animation?true:false;i.rotate=i.rotate?true:false;i.canvas2d=i.canvas2d?true:false;let t=assign({},config);t.color=i.color?i.color:t.color;if(i.type=="pie"){t.pieChartLinePadding=i.dataLabel===false?0:i.extra.pie.labelWidth*i.pix||t.pieChartLinePadding*i.pix}t.pieChartTextPadding=i.dataLabel===false?0:t.pieChartTextPadding*i.pix;t.rotate=i.rotate;if(i.rotate){let e=i.width;let t=i.height;i.width=t;i.height=e}i.padding=i.padding?i.padding:t.padding;t.yAxisWidth=config.yAxisWidth*i.pix;t.fontSize=i.fontSize*i.pix;t.titleFontSize=config.titleFontSize*i.pix;t.subtitleFontSize=config.subtitleFontSize*i.pix;if(!i.context){throw new Error("[uCharts] 未获取到context！注意：v2.0版本后，需要自行获取canvas的绘图上下文并传入opts.context！")}this.context=i.context;if(!this.context.setTextAlign){this.context.setStrokeStyle=function(e){return this.strokeStyle=e};this.context.setLineWidth=function(e){return this.lineWidth=e};this.context.setLineCap=function(e){return this.lineCap=e};this.context.setFontSize=function(e){return this.font=e+"px sans-serif"};this.context.setFillStyle=function(e){return this.fillStyle=e};this.context.setTextAlign=function(e){return this.textAlign=e};this.context.setTextBaseline=function(e){return this.textBaseline=e};this.context.setShadow=function(e,t,i,a){this.shadowColor=a;this.shadowOffsetX=e;this.shadowOffsetY=t;this.shadowBlur=i};this.context.draw=function(){}}if(!this.context.setLineDash){this.context.setLineDash=function(e){}}i.chartData={};this.uevent=new uChartsEvent;this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};this.opts=i;this.config=t;drawCharts.call(this,i.type,i,t,this.context)};uCharts.prototype.updateData=function(){let e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};this.opts=assign({},this.opts,e);this.opts.updateData=true;drawCharts.call(this,this.opts.type,this.opts,this.config,this.context)};uCharts.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()};uCharts.prototype.addEventListener=function(e,t){this.uevent.addEventListener(e,t)};uCharts.prototype.delEventListener=function(e){this.uevent.delEventListener(e)};uCharts.prototype.getCurrentDataIndex=function(t){var i=null;if(t.changedTouches){i=t.changedTouches[0]}else{i=t.mp.changedTouches[0]}if(i){let e=getTouches(i,this.opts,t);if(this.opts.type==="pie"||this.opts.type==="ring"){return findPieChartCurrentIndex({x:e.x,y:e.y},this.opts.chartData.pieData,this.opts)}else if(this.opts.type==="rose"){return findRoseChartCurrentIndex({x:e.x,y:e.y},this.opts.chartData.pieData,this.opts)}else if(this.opts.type==="radar"){return findRadarChartCurrentIndex({x:e.x,y:e.y},this.opts.chartData.radarData,this.opts.categories.length)}else if(this.opts.type==="funnel"){return findFunnelChartCurrentIndex({x:e.x,y:e.y},this.opts.chartData.funnelData)}else if(this.opts.type==="map"){return findMapChartCurrentIndex({x:e.x,y:e.y},this.opts)}else if(this.opts.type==="word"){return findWordChartCurrentIndex({x:e.x,y:e.y},this.opts.chartData.wordCloudData)}else if(this.opts.type==="bar"){return findBarChartCurrentIndex({x:e.x,y:e.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}else{return findCurrentIndex({x:e.x,y:e.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}}return-1};uCharts.prototype.getLegendDataIndex=function(t){var i=null;if(t.changedTouches){i=t.changedTouches[0]}else{i=t.mp.changedTouches[0]}if(i){let e=getTouches(i,this.opts,t);return findLegendIndex({x:e.x,y:e.y},this.opts.chartData.legendData)}return-1};uCharts.prototype.touchLegend=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};var i=null;if(e.changedTouches){i=e.changedTouches[0]}else{i=e.mp.changedTouches[0]}if(i){var a=getTouches(i,this.opts,e);var r=this.getLegendDataIndex(e);if(r>=0){if(this.opts.type=="candle"){this.opts.seriesMA[r].show=!this.opts.seriesMA[r].show}else{this.opts.series[r].show=!this.opts.series[r].show}this.opts.animation=t.animation?true:false;this.opts._scrollDistance_=this.scrollOption.currentOffset;drawCharts.call(this,this.opts.type,this.opts,this.config,this.context)}}};uCharts.prototype.showToolTip=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};var i=null;if(e.changedTouches){i=e.changedTouches[0]}else{i=e.mp.changedTouches[0]}if(!i){console.log("[uCharts] 未获取到event坐标信息")}var a=getTouches(i,this.opts,e);var r=this.scrollOption.currentOffset;var n=assign({},this.opts,{_scrollDistance_:r,animation:false});if(this.opts.type==="pie"||this.opts.type==="ring"||this.opts.type==="rose"||this.opts.type==="funnel"){var o=t.index==undefined?this.getCurrentDataIndex(e):t.index;if(o>-1){var n=assign({},this.opts,{animation:false});var s=assign({},n._series_[o]);var l=[{text:t.formatter?t.formatter(s,undefined,o,n):s.name+": "+s.data,color:s.color,legendShape:this.opts.extra.tooltip.legendShape=="auto"?s.legendShape:this.opts.extra.tooltip.legendShape}];var h={x:a.x,y:a.y};n.tooltip={textList:t.textList?t.textList:l,offset:t.offset!==undefined?t.offset:h,option:t,index:o}}drawCharts.call(this,n.type,n,this.config,this.context)}};export default uCharts;