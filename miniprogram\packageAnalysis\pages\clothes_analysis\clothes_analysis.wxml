<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-content">
      <view class="nav-back" bindtap="navigateBack">
        <text class="back-icon">←</text>
      </view>
      <view class="nav-title">
        <view class="title-container">
          <view class="section-title">衣橱分析</view>
        </view>
      </view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- 正文内容容器 -->
  <view class="page-content" style="margin-top: {{statusBarHeight + 90}}rpx;">
    <!-- 自定义日期选择器 -->
    <view class="date-picker-modal" wx:if="{{isDatePickerVisible}}">
      <view class="date-picker-content">
        <view class="date-picker-header">
          <text>自定义时间范围</text>
          <view class="date-picker-close" bindtap="cancelDatePicker">×</view>
        </view>
        <view class="date-range-container">
          <view class="date-range-item">
            <view class="date-range-label">开始日期</view>
            <picker mode="date" value="{{customDateRange.start || '2020-01-01'}}"
                    start="2000-01-01" end="2030-12-31" bindchange="onStartDateChange">
              <view class="date-picker-field">
                <text>{{customDateRange.start || '点击选择开始日期'}}</text>
              </view>
            </picker>
          </view>
          <view class="date-range-item">
            <view class="date-range-label">结束日期</view>
            <picker mode="date" value="{{customDateRange.end || '2023-01-01'}}"
                    start="2000-01-01" end="2030-12-31" bindchange="onEndDateChange">
              <view class="date-picker-field">
                <text>{{customDateRange.end || '点击选择结束日期'}}</text>
              </view>
            </picker>
          </view>
        </view>
        <view class="date-picker-actions">
          <view class="date-picker-action" bindtap="cancelDatePicker">取消</view>
          <view class="date-picker-action primary" bindtap="confirmDateRange">确认</view>
        </view>
      </view>
    </view>

    <!-- 上部分卡片：图表选择器和图表容器 -->
    <view class="top-section card-shadow">
      <!-- 改进的图表选择器 -->
      <scroll-view scroll-x="true" class="chart-selector-container" enable-flex="true" enhanced="true" show-scrollbar="false">
        <view class="chart-selector">
          <view class="selector-item {{currentChartType === 'category' ? 'active' : ''}}" bindtap="switchChart" data-type="category">
            <text class="selector-icon">🗂️</text>
            <text class="selector-text">分类统计</text>
          </view>
          <view class="selector-item {{currentChartType === 'color' ? 'active' : ''}}" bindtap="switchChart" data-type="color">
            <text class="selector-icon">🎨</text>
            <text class="selector-text">颜色统计</text>
          </view>
          <view class="selector-item {{currentChartType === 'season' ? 'active' : ''}}" bindtap="switchChart" data-type="season">
            <text class="selector-icon">🍃</text>
            <text class="selector-text">季节统计</text>
          </view>
          <view class="selector-item {{currentChartType === 'brand' ? 'active' : ''}}" bindtap="switchChart" data-type="brand">
            <text class="selector-icon">🏷️</text>
            <text class="selector-text">品牌统计</text>
          </view>
          <view class="selector-item {{currentChartType === 'style' ? 'active' : ''}}" bindtap="switchChart" data-type="style">
            <text class="selector-icon">👚</text>
            <text class="selector-text">风格统计</text>
          </view>
        </view>
      </scroll-view>

      <!-- 图表信息区域 -->
      <view class="chart-info">
        <!-- 如果是细分类视图，显示返回按钮 -->
        <view class="subcategory-header" wx:if="{{isShowingSubcategory}}">
          <view class="back-button" hover-class="button-hover" bindtap="backToMainCategory">
            <text class="back-icon">←</text> 返回分类
          </view>
          <view class="subcategory-title">{{selectedCategory}}的细分类统计</view>
        </view>

        <view class="chart-title">
          <block wx:if="{{!isShowingSubcategory}}">
            {{currentChartType === 'category' ? '分类统计' :
               currentChartType === 'color' ? '颜色统计' :
               currentChartType === 'season' ? '季节统计' :
               currentChartType === 'brand' ? '品牌统计' : '风格统计'}}
            <text class="wardrobe-badge" wx:if="{{currentWardrobe !== 'all'}}">{{wardrobeNames[currentWardrobe] || '当前衣柜'}}</text>
          </block>
          <block wx:else>
            {{selectedCategory}}细分类统计
            <text class="wardrobe-badge" wx:if="{{currentWardrobe !== 'all'}}">{{wardrobeNames[currentWardrobe] || '当前衣柜'}}</text>
          </block>
        </view>

        <view class="chart-subtitle">
          <text class="count-badge">
            <block wx:if="{{!isShowingSubcategory}}">
              共{{statistics.totalClothes || 0}}件衣物
            </block>
            <block wx:else>
              共{{selectedCategoryItems.length || 0}}件{{selectedCategory}}
            </block>
          </text>
        </view>

        <!-- 图例展示区 -->
        <view class="chart-legend" wx:if="{{chartData && chartData.categories}}">
          <view class="legend-row" wx:for="{{chartData.categories}}" wx:if="{{index % 2 === 0}}" wx:key="index">
            <view class="legend-item" hover-class="legend-hover"
                  bindtap="{{currentChartType === 'category' && !isShowingSubcategory ? 'handleLegendTap' : ''}}"
                  data-index="{{index}}">
              <view class="legend-color" style="background-color: {{chartColors[index % chartColors.length]}};"></view>
              <view class="legend-text">{{item}}</view>
              <view class="legend-count">{{chartData.series[0].data[index]}}件</view>
              <view class="legend-percent">({{chartPercentages[index]}}%)</view>
            </view>

            <view class="legend-item" hover-class="legend-hover"
                  bindtap="{{currentChartType === 'category' && !isShowingSubcategory ? 'handleLegendTap' : ''}}"
                  data-index="{{index + 1}}"
                  wx:if="{{index + 1 < chartData.categories.length}}">
              <view class="legend-color" style="background-color: {{chartColors[(index + 1) % chartColors.length]}};"></view>
              <view class="legend-text">{{chartData.categories[index + 1]}}</view>
              <view class="legend-count">{{chartData.series[0].data[index + 1]}}件</view>
              <view class="legend-percent">({{chartPercentages[index + 1]}}%)</view>
            </view>
          </view>
        </view>
      </view>

      <view class="chart-container {{showChart ? 'show' : 'hide'}}" bindtap="touchEndHandler">
        <canvas canvas-id="analysisChart" id="analysisChart" class="analysis-chart"
                bindtouchstart="touchStartHandler"
                bindtouchmove="touchMoveHandler"
                bindtouchend="touchEndHandler"
                bindtap="touchEndHandler"
                disable-scroll="true"></canvas>
      </view>
    </view>

    <!-- 下部分卡片：衣橱总览 -->
    <view class="summary-container card-shadow">
      <view class="summary-header">
        <text class="summary-title">衣橱总览</text>
        <text class="summary-subtitle">个人衣橱数据一目了然</text>
        <text class="wardrobe-subtitle" wx:if="{{currentWardrobe !== 'all'}}">当前衣柜: {{wardrobeNames[currentWardrobe] || '未命名衣柜'}}</text>
      </view>

      <view class="summary-flex-container">
        <!-- 一行两个卡片的弹性布局 -->
        <view class="summary-row">
          <view class="summary-item" hover-class="summary-item-hover">
            <view class="summary-icon clothes-icon">👕</view>
            <view class="summary-value">{{statistics.totalClothes || 0}}</view>
            <view class="summary-label">衣物总数</view>
          </view>

          <view class="summary-item" hover-class="summary-item-hover">
            <view class="summary-icon price-icon">¥</view>
            <view class="summary-value">{{statistics.totalPrice || 0}}<text class="unit">元</text></view>
            <view class="summary-label">衣物总价</view>
          </view>
        </view>

        <view class="summary-row">
          <view class="summary-item" hover-class="summary-item-hover">
            <view class="summary-icon avg-icon">¥</view>
            <view class="summary-value">{{statistics.avgPrice || 0}}<text class="unit">元</text></view>
            <view class="summary-label">平均单价</view>
          </view>

          <view class="summary-item" hover-class="summary-item-hover">
            <view class="summary-icon brand-icon">🛍️</view>
            <view class="summary-value">{{statistics.favoriteBrand || '-'}}</view>
            <view class="summary-label">最爱品牌</view>
          </view>
        </view>

        <view class="summary-row">
          <view class="summary-item" hover-class="summary-item-hover">
            <view class="summary-icon color-icon">🌈</view>
            <view class="summary-value">{{statistics.favoriteColor || '-'}}</view>
            <view class="summary-label">最爱颜色</view>
          </view>

          <view class="summary-item" hover-class="summary-item-hover">
            <view class="summary-icon date-icon">📆</view>
            <view class="summary-value">{{statistics.mostPurchaseDate || '-'}}</view>
            <view class="summary-label">购买高峰</view>
          </view>
        </view>

        <view class="summary-row">
          <view class="summary-item" hover-class="summary-item-hover">
            <view class="summary-icon most-icon">✨</view>
            <view class="summary-value">{{statistics.mostWornItem || '-'}}</view>
            <view class="summary-label">最常穿搭</view>
          </view>

          <view class="summary-item" hover-class="summary-item-hover">
            <view class="summary-icon least-icon">📉</view>
            <view class="summary-value">{{statistics.leastWornItem || '-'}}</view>
            <view class="summary-label">最少使用</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- DS分析按钮 -->
  <view class="ai-analysis-btn" bindtap="startAIClothesAnalysis" wx:if="{{!isAILoading}}">
    <view class="ai-text">DS衣服分析</view>
  </view>

  <!-- AI分析加载状态 -->
  <view class="ai-analysis-btn loading" wx:if="{{isAILoading}}">
    <view class="ai-loading-icon"></view>
    <view class="ai-text">分析中...</view>
  </view>

  <!-- 筛选按钮 -->
  <view class="filter-btn" bindtap="toggleFilterPanel">
    <view class="filter-btn-icon">🔍</view>
  </view>

  <!-- 筛选面板背景遮罩 -->
  <view class="filter-overlay {{showFilterPanel ? 'show' : ''}}" bindtap="toggleFilterPanel"></view>

  <!-- 筛选面板 -->
  <view class="filter-panel {{showFilterPanel ? 'show' : ''}}">
    <view class="filter-panel-header">
      <text>筛选选项</text>
      <view class="filter-panel-close" bindtap="toggleFilterPanel">×</view>
    </view>

    <view class="filter-panel-content">
      <!-- 衣柜选择器 -->
      <view class="wardrobe-picker-container">
        <view class="wardrobe-picker-label">
          <view class="wardrobe-icon">👕</view>
          <text>选择衣柜</text>
        </view>
        <picker bindchange="onWardrobeChange" value="{{wardrobePickerIndex}}" range="{{wardrobePickerArray}}" bindtap="togglePickerState">
          <view class="wardrobe-picker-selected {{isPickerOpen ? 'picker-open' : ''}}">
            <text>{{wardrobePickerArray[wardrobePickerIndex] || '全部衣柜'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <!-- 日期筛选器 -->
      <view class="date-filter-container">
        <view class="date-filter-label">
          <view class="date-filter-icon">📅</view>
          <text>购买日期</text>
        </view>
        <view class="date-filter-selected {{showDateFilterPanel ? 'filter-open' : ''}}" bindtap="toggleDateFilterPanel">
          <text>{{displayDateFilter || selectedDateFilter}}</text>
          <text class="filter-arrow">▼</text>
        </view>
      </view>

      <!-- 日期筛选面板 -->
      <view class="date-filter-panel" wx:if="{{showDateFilterPanel}}">
        <view class="date-filter-options">
          <block wx:for="{{dateFilterOptions}}" wx:key="*this">
            <view class="date-filter-option {{selectedDateFilter === item ? 'active' : ''}}"
                  bindtap="selectDateFilter" data-filter="{{item}}">
              <text>{{item}}</text>
              <icon wx:if="{{selectedDateFilter === item}}" type="success" size="16" color="#2a8c70"></icon>
            </view>
          </block>
        </view>
        <view class="date-filter-actions" wx:if="{{selectedDateFilter !== '全部时间'}}">
          <view class="date-filter-clear" bindtap="clearDateFilter">清除筛选</view>
        </view>
      </view>
    </view>
  </view>
</view>