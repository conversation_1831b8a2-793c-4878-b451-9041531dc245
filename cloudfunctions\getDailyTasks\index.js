// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 获取用户openid，优先使用传入的，否则使用上下文中的
  const userOpenId = event.userOpenId || wxContext.OPENID
  
  // 获取日期参数，格式为YYYY-MM-DD，如果没有则使用当前日期
  const requestDate = event.date || formatDate(new Date())
  
  if (!userOpenId) {
    return {
      success: false,
      error: '无法获取用户ID'
    }
  }
  
  try {
    // 查询用户当日任务
    const dailyTasksCollection = db.collection('dailyTasks')
    let userDailyTasks = await dailyTasksCollection.where({
      userOpenId: userOpenId,
      date: requestDate
    }).get()
    
    // 如果用户没有当日任务记录，创建一个新的
    if (!userDailyTasks.data || userDailyTasks.data.length === 0) {
      // 默认每日任务
      const defaultTasks = [
        { id: 'task1', name: '添加一件衣物', completed: false },
        { id: 'task2', name: '使用AI创建一个穿搭', completed: false },
        { id: 'task3', name: '分享给一个朋友', completed: false }
      ]
      
      const newDailyTaskData = {
        userOpenId: userOpenId,
        date: requestDate,
        tasks: defaultTasks,
        completedCount: 0,
        totalCount: defaultTasks.length,
        canDrawPrize: false,
        createdAt: db.serverDate(),
        updatedAt: db.serverDate()
      }
      
      // 创建新记录
      await dailyTasksCollection.add({
        data: newDailyTaskData
      })
      
      return {
        success: true,
        data: newDailyTaskData
      }
    }
    
    // 返回用户当日任务
    const userDailyTask = userDailyTasks.data[0]
    
    // 计算已完成任务数量（防止数据不一致）
    const completedCount = (userDailyTask.tasks || []).filter(task => task.completed).length
    const totalCount = (userDailyTask.tasks || []).length
    const canDrawPrize = completedCount >= totalCount && totalCount > 0
    
    // 如果计算的完成数量与存储的不一致，更新数据
    if (completedCount !== userDailyTask.completedCount || canDrawPrize !== userDailyTask.canDrawPrize) {
      await dailyTasksCollection.doc(userDailyTask._id).update({
        data: {
          completedCount: completedCount,
          canDrawPrize: canDrawPrize,
          updatedAt: db.serverDate()
        }
      })
      
      // 更新返回数据
      userDailyTask.completedCount = completedCount
      userDailyTask.canDrawPrize = canDrawPrize
    }
    
    return {
      success: true,
      data: userDailyTask
    }
  } catch (error) {
    console.error('获取用户每日任务失败:', error)
    
    return {
      success: false,
      error: error.message || '获取每日任务失败'
    }
  }
}

// 辅助函数：格式化日期为YYYY-MM-DD格式
function formatDate(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
} 