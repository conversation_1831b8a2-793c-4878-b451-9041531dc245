// 本地图片缓存模块
// 负责outfit_detail页面中图片的本地文件系统缓存

// 定义默认图片路径常量
const DEFAULT_PREVIEW_IMAGE = '/image/outfit-icon.png';
const DEFAULT_ITEM_IMAGE = '/image/short-dress.png';

// 缓存相关常量
const IMAGE_CACHE_FOLDER = 'outfit_detail_images/'; // 本地文件系统中的缓存文件夹
const URL_CACHE_KEY = 'outfit_detail_image_urls'; // 用于存储图片URL的本地缓存键
const MAX_BATCH_SIZE = 50; // 每批处理的最大图片数量

// 其他模块的缓存相关常量
const OOTD_CACHE_FOLDER = 'outfits_images/'; // OOTD模块中的缓存文件夹
const OOTD_URL_CACHE_KEY = 'ootd_image_urls'; // OOTD模块中的缓存键

// 衣柜图片缓存相关常量
const CLOTHES_CACHE_FOLDER = 'clothes_images/'; // 衣柜模块中的缓存文件夹
const CLOTHES_URL_CACHE_KEY = 'clothes_image_urls'; // 衣柜模块中的缓存键

/**
 * 获取默认预览图URL
 * @returns {string} 默认预览图URL
 */
function getDefaultPreviewImageUrl() {
  return DEFAULT_PREVIEW_IMAGE;
}

/**
 * 获取默认衣物图片URL
 * @returns {string} 默认衣物图片URL
 */
function getDefaultItemImageUrl() {
  return DEFAULT_ITEM_IMAGE;
}

/**
 * 确保缓存目录存在
 * @returns {string} 缓存目录路径
 */
function ensureCacheDir() {
  const fs = wx.getFileSystemManager();
  const cacheDir = `${wx.env.USER_DATA_PATH}/${IMAGE_CACHE_FOLDER}`;
  
  try {
    fs.accessSync(cacheDir);
  } catch (err) {
    try {
      fs.mkdirSync(cacheDir, true);
      console.log('创建outfit_detail图片缓存目录:', cacheDir);
    } catch (mkdirErr) {
      console.error('创建outfit_detail图片缓存目录失败:', mkdirErr);
    }
  }
  
  return cacheDir;
}

/**
 * 从本地缓存获取图片路径
 * @param {string} fileID - 文件ID
 * @returns {string|null} 本地缓存路径，如果不存在则返回null
 */
function getLocalCachedImage(fileID) {
  if (!fileID) return null;
  
  try {
    // 首先尝试从衣柜(closet)模块的缓存获取图片
    const clothesLocalPath = getClothesLocalCachedImage(fileID);
    if (clothesLocalPath) {
      console.log(`使用衣柜已缓存图片: ${fileID}`);
      return clothesLocalPath;
    }
    
    // 然后尝试从OOTD模块的缓存获取图片
    const ootdLocalPath = getOOTDLocalCachedImage(fileID);
    if (ootdLocalPath) {
      console.log(`使用OOTD已缓存图片: ${fileID}`);
      return ootdLocalPath;
    }
    
    // 如果其他模块没有缓存，再从outfit_detail模块的缓存获取
    const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};
    const cacheInfo = cachedURLInfo[fileID];
    
    if (!cacheInfo || !cacheInfo.localPath) {
      return null;
    }
    
    // 检查本地文件是否存在
    const fs = wx.getFileSystemManager();
    const localFilePath = `${wx.env.USER_DATA_PATH}/${cacheInfo.localPath}`;
    
    try {
      fs.accessSync(localFilePath);
      console.log(`使用outfit_detail本地缓存图片: ${fileID}`);
      return localFilePath;
    } catch (err) {
      console.warn(`outfit_detail本地缓存图片不存在: ${fileID}`, err);
      return null;
    }
  } catch (err) {
    console.error('获取本地缓存图片出错:', err);
    return null;
  }
}

/**
 * 从衣柜(closet)模块的缓存中获取图片路径
 * @param {string} fileID - 文件ID
 * @returns {string|null} 本地缓存路径，如果不存在则返回null
 */
function getClothesLocalCachedImage(fileID) {
  if (!fileID) return null;
  
  try {
    // 从衣柜模块的本地存储获取缓存信息
    const clothesCachedURLInfo = wx.getStorageSync(CLOTHES_URL_CACHE_KEY) || {};
    const cacheInfo = clothesCachedURLInfo[fileID];
    
    if (!cacheInfo || !cacheInfo.localPath) {
      return null;
    }
    
    // 检查本地文件是否存在
    const fs = wx.getFileSystemManager();
    const localFilePath = `${wx.env.USER_DATA_PATH}/${cacheInfo.localPath}`;
    
    try {
      fs.accessSync(localFilePath);
      console.log(`使用衣柜模块缓存的图片: ${fileID}`);
      return localFilePath;
    } catch (err) {
      console.warn(`衣柜模块缓存的图片不存在: ${fileID}`, err);
      return null;
    }
  } catch (err) {
    console.error('获取衣柜模块缓存图片出错:', err);
    return null;
  }
}

/**
 * 从OOTD模块的缓存中获取图片路径
 * @param {string} fileID - 文件ID
 * @returns {string|null} 本地缓存路径，如果不存在则返回null
 */
function getOOTDLocalCachedImage(fileID) {
  if (!fileID) return null;
  
  try {
    // 从OOTD模块的本地存储获取缓存信息
    const ootdCachedURLInfo = wx.getStorageSync(OOTD_URL_CACHE_KEY) || {};
    const cacheInfo = ootdCachedURLInfo[fileID];
    
    if (!cacheInfo || !cacheInfo.localPath) {
      return null;
    }
    
    // 检查本地文件是否存在
    const fs = wx.getFileSystemManager();
    const localFilePath = `${wx.env.USER_DATA_PATH}/${cacheInfo.localPath}`;
    
    try {
      fs.accessSync(localFilePath);
      console.log(`使用OOTD模块缓存的图片: ${fileID}`);
      return localFilePath;
    } catch (err) {
      console.warn(`OOTD模块缓存的图片不存在: ${fileID}`, err);
      return null;
    }
  } catch (err) {
    console.error('获取OOTD模块缓存图片出错:', err);
    return null;
  }
}

/**
 * 计算fileID的哈希文件名
 * @param {string} fileID - 文件ID
 * @returns {string} 哈希文件名
 */
function getHashFilename(fileID) {
  // 简单的哈希算法，生成一个相对唯一的文件名
  let hash = 0;
  for (let i = 0; i < fileID.length; i++) {
    hash = ((hash << 5) - hash) + fileID.charCodeAt(i);
    hash |= 0; // 转换为32位整数
  }
  
  // 确保是正数
  hash = Math.abs(hash);
  // 使用固定的后缀，同一个fileID始终生成相同的文件名
  return `outfit_detail_${hash.toString(36)}.jpg`;
}

/**
 * 下载图片到本地缓存
 * @param {string} fileID - 文件ID
 * @param {string} tempFileURL - 临时文件URL
 * @returns {Promise<string>} 本地文件路径
 */
function downloadImageToCache(fileID, tempFileURL) {
  return new Promise((resolve, reject) => {
    if (!fileID || !tempFileURL) {
      reject(new Error('fileID或tempFileURL为空'));
      return;
    }
    
    // 首先检查衣柜模块是否已经缓存了这个图片
    const clothesLocalPath = getClothesLocalCachedImage(fileID);
    if (clothesLocalPath) {
      console.log(`使用衣柜已缓存图片，无需下载: ${fileID}`);
      resolve(clothesLocalPath);
      return;
    }
    
    // 然后检查OOTD模块是否已经缓存了这个图片
    const ootdLocalPath = getOOTDLocalCachedImage(fileID);
    if (ootdLocalPath) {
      console.log(`使用OOTD已缓存图片，无需下载: ${fileID}`);
      resolve(ootdLocalPath);
      return;
    }
    
    // 使用fileID的哈希作为文件名，确保唯一性
    const fileName = getHashFilename(fileID);
    const localPath = `${IMAGE_CACHE_FOLDER}${fileName}`;
    const fullPath = `${wx.env.USER_DATA_PATH}/${localPath}`;
    
    // 确保缓存目录存在
    ensureCacheDir();
    
    // 下载图片到本地
    wx.downloadFile({
      url: tempFileURL,
      success: res => {
        if (res.statusCode === 200) {
          // 将临时文件保存到本地缓存目录
          const fs = wx.getFileSystemManager();
          fs.saveFile({
            tempFilePath: res.tempFilePath,
            filePath: fullPath,
            success: () => {
              console.log(`图片已缓存到本地: ${fileID}`);
              
              // 更新缓存信息
              const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};
              cachedURLInfo[fileID] = {
                localPath: localPath,
                timestamp: Date.now(),
                cloudUrl: tempFileURL
              };
              
              // 保存缓存信息
              wx.setStorage({
                key: URL_CACHE_KEY,
                data: cachedURLInfo,
                fail: err => console.error('保存缓存信息失败:', err)
              });
              
              resolve(fullPath);
            },
            fail: err => {
              console.error('保存图片到本地失败:', err);
              reject(err);
            }
          });
        } else {
          console.error(`下载图片失败，状态码: ${res.statusCode}`);
          reject(new Error(`下载图片失败，状态码: ${res.statusCode}`));
        }
      },
      fail: err => {
        console.error('下载图片失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 批量处理图片缓存
 * @param {Array} fileIDs - 文件ID数组
 * @returns {Promise<Object>} 文件ID到本地路径的映射
 */
function batchProcessImages(fileIDs) {
  return new Promise(async (resolve, reject) => {
    if (!fileIDs || !Array.isArray(fileIDs) || fileIDs.length === 0) {
      resolve({});
      return;
    }
    
    // 过滤掉无效的fileID
    const validFileIDs = fileIDs.filter(fileID => fileID && typeof fileID === 'string' && fileID.includes('cloud://'));
    
    if (validFileIDs.length === 0) {
      console.warn('没有有效的云存储fileID');
      resolve({});
      return;
    }
    
    // 获取缓存信息
    const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};
    const fileIDsToProcess = [];
    const result = {};
    
    // 首先检查本地缓存
    for (const fileID of validFileIDs) {
      const localPath = getLocalCachedImage(fileID);
      
      if (localPath) {
        // 已有缓存，直接使用
        result[fileID] = localPath;
      } else {
        // 需要处理的fileID
        fileIDsToProcess.push(fileID);
      }
    }
    
    if (fileIDsToProcess.length === 0) {
      console.log('所有图片都有缓存，无需下载');
      resolve(result);
      return;
    }
    
    console.log(`需要处理的图片数量: ${fileIDsToProcess.length}`);
    
    try {
      // 批量获取临时URL (每次最多50个，避免超出微信API限制)
      for (let i = 0; i < fileIDsToProcess.length; i += MAX_BATCH_SIZE) {
        const batchFileIDs = fileIDsToProcess.slice(i, i + MAX_BATCH_SIZE);
        
        // 获取临时URL
        const tempURLRes = await wx.cloud.getTempFileURL({
          fileList: batchFileIDs
        });
        
        // 处理每个文件
        const downloadPromises = [];
        
        tempURLRes.fileList.forEach(file => {
          if (file.fileID && file.tempFileURL) {
            downloadPromises.push(
              downloadImageToCache(file.fileID, file.tempFileURL)
                .then(localPath => {
                  result[file.fileID] = localPath;
                })
                .catch(err => {
                  console.error(`下载图片到缓存失败 ${file.fileID}:`, err);
                })
            );
          }
        });
        
        // 等待当前批次的所有下载完成
        await Promise.all(downloadPromises);
      }
      
      console.log('所有图片缓存处理完成');
      resolve(result);
    } catch (err) {
      console.error('批量处理图片出错:', err);
      // 即使有错误，也返回已处理好的图片
      resolve(result);
    }
  });
}

/**
 * 清除过期缓存
 * @param {number} expireDays - 过期天数，默认7天
 * @returns {Promise} 清除操作的Promise
 */
function clearExpiredCache(expireDays = 7) {
  return new Promise((resolve, reject) => {
    try {
      // 计算过期时间
      const expireTime = Date.now() - (expireDays * 24 * 60 * 60 * 1000);
      
      // 获取缓存信息
      const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};
      const fs = wx.getFileSystemManager();
      const expiredFileIDs = [];
      
      // 检查每个缓存项
      Object.keys(cachedURLInfo).forEach(fileID => {
        const cacheInfo = cachedURLInfo[fileID];
        if (cacheInfo.timestamp && cacheInfo.timestamp < expireTime) {
          // 过期文件
          expiredFileIDs.push(fileID);
          
          // 尝试删除本地文件
          try {
            const fullPath = `${wx.env.USER_DATA_PATH}/${cacheInfo.localPath}`;
            fs.accessSync(fullPath);
            fs.unlinkSync(fullPath);
            console.log(`已删除过期缓存文件: ${fullPath}`);
          } catch (err) {
            console.warn(`删除过期缓存文件失败: ${err.message}`);
            // 即使文件删除失败，也继续处理其他文件
          }
        }
      });
      
      // 从缓存信息中移除过期项
      expiredFileIDs.forEach(fileID => {
        delete cachedURLInfo[fileID];
      });
      
      // 更新缓存信息
      wx.setStorage({
        key: URL_CACHE_KEY,
        data: cachedURLInfo,
        success: () => {
          console.log(`已清除 ${expiredFileIDs.length} 个过期缓存项`);
          resolve();
        },
        fail: err => {
          console.error('更新缓存信息失败:', err);
          reject(err);
        }
      });
    } catch (err) {
      console.error('清除过期缓存出错:', err);
      reject(err);
    }
  });
}

// 导出模块
module.exports = {
  getDefaultPreviewImageUrl,
  getDefaultItemImageUrl,
  ensureCacheDir,
  getLocalCachedImage,
  downloadImageToCache,
  batchProcessImages,
  clearExpiredCache
}; 