/**
 * 排序管理模块 - 负责衣物排序逻辑
 */

// 根据价格排序
function sortByPrice(clothes, ascending = true) {
  return [...clothes].sort((a, b) => {
    const priceA = parseFloat(a.price) || 0;
    const priceB = parseFloat(b.price) || 0;
    return ascending ? priceA - priceB : priceB - priceA;
  });
}

// 根据购买时间排序
function sortByPurchaseDate(clothes, ascending = true) {
  return [...clothes].sort((a, b) => {
    // 如果没有日期，则放到最后
    if (!a.purchaseDate) return ascending ? 1 : -1;
    if (!b.purchaseDate) return ascending ? -1 : 1;

    const dateA = new Date(a.purchaseDate.replace(/-/g, '/'));
    const dateB = new Date(b.purchaseDate.replace(/-/g, '/'));

    return ascending ? dateA - dateB : dateB - dateA;
  });
}

// 根据上次穿着时间排序
function sortByLastWornDate(clothes, ascending = true) {
  return [...clothes].sort((a, b) => {
    // 如果没有日期，则放到最后
    if (!a.lastWornDate) return ascending ? 1 : -1;
    if (!b.lastWornDate) return ascending ? -1 : 1;

    const dateA = new Date(a.lastWornDate.replace(/-/g, '/'));
    const dateB = new Date(b.lastWornDate.replace(/-/g, '/'));

    return ascending ? dateA - dateB : dateB - dateA;
  });
}

// 根据穿着次数排序
function sortByWornCount(clothes, ascending = true) {
  return [...clothes].sort((a, b) => {
    const countA = parseInt(a.wornCount) || 0;
    const countB = parseInt(b.wornCount) || 0;
    return ascending ? countA - countB : countB - countA;
  });
}

// 根据颜色排序
function sortByColor(clothes, ascending = true) {
  return [...clothes].sort((a, b) => {
    if (!a.color) return ascending ? 1 : -1;
    if (!b.color) return ascending ? -1 : 1;

    return ascending ?
      a.color.localeCompare(b.color, 'zh') :
      b.color.localeCompare(a.color, 'zh');
  });
}

// 根据每次成本排序
function sortByCostPerWear(clothes, ascending = true) {
  return [...clothes].sort((a, b) => {
    const costA = a.price && a.wornCount && a.wornCount > 0 ? parseFloat(a.price) / parseInt(a.wornCount) : Infinity;
    const costB = b.price && b.wornCount && b.wornCount > 0 ? parseFloat(b.price) / parseInt(b.wornCount) : Infinity;
    return ascending ? costA - costB : costB - costA;
  });
}

// 根据创建时间排序
function sortByCreateTime(clothes, ascending = true) {
  return [...clothes].sort((a, b) => {
    // 如果没有创建时间，则放到最后
    if (!a.createTime) return ascending ? 1 : -1;
    if (!b.createTime) return ascending ? -1 : 1;

    const timeA = new Date(a.createTime).getTime();
    const timeB = new Date(b.createTime).getTime();

    return ascending ? timeA - timeB : timeB - timeA;
  });
}

// 根据排序类型执行相应的排序
function sortClothes(clothes, sortType, ascending = true) {
  switch (sortType) {
    case 'price':
      return sortByPrice(clothes, ascending);
    case 'purchaseDate':
      return sortByPurchaseDate(clothes, ascending);
    case 'lastWornDate':
      return sortByLastWornDate(clothes, ascending);
    case 'wornCount':
      return sortByWornCount(clothes, ascending);
    case 'color':
      return sortByColor(clothes, ascending);
    case 'costPerWear':
      return sortByCostPerWear(clothes, ascending);
    case 'createTime':
      return sortByCreateTime(clothes, ascending);
    default:
      // 如果没有指定排序类型，默认按创建时间降序排序
      return sortByCreateTime(clothes, false);
  }
}

// 获取可用的排序类型选项
function getSortOptions() {
  return [
    { value: 'createTime', label: '添加时间' },
    { value: 'price', label: '价格' },
    { value: 'purchaseDate', label: '购买时间' },
    { value: 'lastWornDate', label: '上次穿着' },
    { value: 'wornCount', label: '穿着次数' },
    { value: 'color', label: '颜色' },
    { value: 'costPerWear', label: '每次成本' }
  ];
}

module.exports = {
  sortClothes,
  getSortOptions
};