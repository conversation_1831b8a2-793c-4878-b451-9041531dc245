// 云函数入口文件
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;
const $ = _.aggregate;

// 云函数入口函数
exports.main = async (event, context) => {
  const { partialOpenid } = event;
  console.log('收到模糊查询请求，关键词:', partialOpenid);
  
  if (!partialOpenid || partialOpenid.length < 5) {
    return {
      code: -1,
      message: '搜索关键词太短，至少需要5个字符'
    };
  }

  try {
    // 先尝试精确匹配
    const exactMatch = await db.collection('users')
      .where({
        _openid: partialOpenid
      })
      .get();
    
    // 如果找到精确匹配，直接返回
    if (exactMatch.data && exactMatch.data.length > 0) {
      console.log('找到精确匹配结果:', exactMatch.data);
      return exactMatch.data;
    }
    
    // 使用正则表达式进行模糊匹配
    const regexResult = await db.collection('users')
      .where({
        _openid: db.RegExp({
          regexp: partialOpenid,
          options: 'i', // 不区分大小写
        })
      })
      .limit(10) // 限制返回10条记录
      .get();
    
    console.log('模糊匹配结果:', regexResult.data);
    
    // 如果正则没找到，尝试更模糊的查询
    if (regexResult.data.length === 0 && partialOpenid.length >= 6) {
      // 创建多个模糊匹配条件
      const searchPatterns = [];
      
      // 使用前缀
      searchPatterns.push({
        _openid: db.RegExp({
          regexp: `^${partialOpenid.substring(0, 6)}`,
          options: 'i',
        })
      });
      
      // 使用中间部分
      if (partialOpenid.length >= 10) {
        searchPatterns.push({
          _openid: db.RegExp({
            regexp: partialOpenid.substring(3, 8),
            options: 'i',
          })
        });
      }
      
      // 使用后缀
      searchPatterns.push({
        _openid: db.RegExp({
          regexp: `${partialOpenid.substring(partialOpenid.length - 6)}$`,
          options: 'i',
        })
      });
      
      const fuzzyResult = await db.collection('users')
        .where(_.or(searchPatterns))
        .limit(10)
        .get();
      
      console.log('更模糊的匹配结果:', fuzzyResult.data);
      return fuzzyResult.data;
    }
    
    return regexResult.data;
  } catch (error) {
    console.error('模糊查询用户失败:', error);
    return {
      code: -1,
      message: '查询失败: ' + error.message
    };
  }
}; 