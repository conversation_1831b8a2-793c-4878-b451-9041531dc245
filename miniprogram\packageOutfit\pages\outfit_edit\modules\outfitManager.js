// 搭配管理模块
// 负责处理搭配相关的操作，包括生成搭配图片、保存搭配到数据库等

// 导入用户管理模块
const userManager = require('./userManager');
// 导入图片管理模块
const imageManager = require('./imageManager');

// 生成搭配图片
function generateOutfitImage(canvasId, canvasItems, canvasWidth, canvasHeight) {
  return new Promise((resolve, reject) => {
    if (!canvasItems || canvasItems.length === 0) {
      reject(new Error('画布为空，无法生成图片'));
      return;
    }

    // 在WXS环境中使用相对单位px
    const canvasNodeQuery = wx.createSelectorQuery();
    canvasNodeQuery.select(`#${canvasId}`)
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res || !res[0] || !res[0].node) {
          reject(new Error('获取Canvas节点失败'));
          return;
        }

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        // 设置canvas大小
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;

        // 清空画布
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // 设置白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // 绘制边框
        ctx.strokeStyle = '#e0e0e0';
        ctx.lineWidth = 2;
        ctx.strokeRect(2, 2, canvasWidth - 4, canvasHeight - 4);

        // 按图层顺序排序画布项
        const sortedItems = [...canvasItems].sort((a, b) => a.layer - b.layer);

        // 创建一个新数组来存储调整后的画布项
        const adjustedItems = [];

        // 使用Promise队列绘制所有图像
        const drawImagePromises = sortedItems.map(item => {
          return new Promise((drawResolve, drawReject) => {
            const img = canvas.createImage();

            img.onload = () => {
              // 保存当前绘图状态
              ctx.save();

              // 移动到图像中心点
              const centerX = item.x + item.width / 2;
              const centerY = item.y + item.height / 2;
              ctx.translate(centerX, centerY);

              // 应用旋转
              if (item.rotation) {
                ctx.rotate((item.rotation * Math.PI) / 180);
              }

              // 获取图片的原始宽高比
              const imgRatio = img.width / img.height;
              const itemRatio = item.width / item.height;

              // 检查宽高比是否需要调整
              let drawWidth = item.width;
              let drawHeight = item.height;
              let needsAdjustment = false;

              // 如果比例差异超过阈值，调整绘制尺寸
              const ratioDifference = Math.abs(itemRatio - imgRatio);
              if (ratioDifference > 0.05) {
                console.log(`项目 ${item.id} 宽高比不匹配，进行调整 - 当前: ${itemRatio.toFixed(2)}, 原始: ${imgRatio.toFixed(2)}`);
                drawHeight = drawWidth / imgRatio;
                needsAdjustment = true;
              }

              // 绘制图像（注意坐标需要回退半个宽度和高度）
              ctx.drawImage(img, -drawWidth / 2, -drawHeight / 2, drawWidth, drawHeight);

              // 恢复绘图状态
              ctx.restore();

              // 将调整后的项目添加到新数组
              if (needsAdjustment) {
                // 创建一个新对象，保持中心点不变
                const adjustedItem = {...item};
                adjustedItem.height = drawHeight;
                // 重新计算y坐标以保持中心点不变
                adjustedItem.y = centerY - drawHeight / 2;
                // 保存原始宽高比
                adjustedItem.aspectRatio = imgRatio;
                // 标记这是一个新添加的项目，用于后续处理
                adjustedItem.isNewlyAdded = !item.hasOwnProperty('aspectRatio');

                adjustedItems.push({
                  index: sortedItems.indexOf(item),
                  item: adjustedItem
                });

                console.log(`项目 ${item.id} 尺寸已调整: ${item.width}x${item.height} -> ${drawWidth}x${drawHeight}, 是否新添加: ${adjustedItem.isNewlyAdded}`);
              }

              drawResolve();
            };

            img.onerror = () => {
              console.error('加载图片失败:', item.imageUrl);
              drawResolve(); // 即使图片加载失败，也继续执行，防止整个过程中断
            };

            img.src = item.imageUrl;
          });
        });

        // 等待所有图像绘制完成
        Promise.all(drawImagePromises)
          .then(() => {
            console.log('所有图像绘制完成，准备转换为高质量图片');

            // 更新原始canvasItems数组中的项目
            if (adjustedItems.length > 0) {
              console.log(`需要调整 ${adjustedItems.length} 个项目的尺寸`);

              // 筛选出不是新添加的项目
              const existingAdjustedItems = adjustedItems.filter(({item}) => !item.isNewlyAdded);
              const newlyAddedItems = adjustedItems.filter(({item}) => item.isNewlyAdded);

              console.log(`其中 ${existingAdjustedItems.length} 个是已有项目，${newlyAddedItems.length} 个是新添加的项目`);

              // 只更新已有项目的尺寸，保持新添加项目的原始尺寸
              existingAdjustedItems.forEach(({index, item}) => {
                // 移除标记字段
                delete item.isNewlyAdded;
                canvasItems[index] = item;
              });

              console.log('已更新已有项目的尺寸，保持新添加项目的原始尺寸');
            }

            // 将画布内容转为高质量临时文件
            return wx.canvasToTempFilePath({
              canvas: canvas,
              destWidth: canvasWidth,
              destHeight: canvasHeight,
              fileType: 'png', // 使用PNG格式避免压缩损失
              quality: 1.0     // 最高质量
            });
          })
          .then(res => {
            const tempFilePath = res.tempFilePath;
            console.log('Canvas转换为高质量临时文件成功:', tempFilePath);

            // 上传临时文件到云存储
            return wx.cloud.uploadFile({
              cloudPath: `outfits/${Date.now()}-${Math.random().toString(36).substr(2, 8)}.png`,
              filePath: tempFilePath
            });
          })
          .then(res => {
            const fileID = res.fileID;
            console.log('图片上传到云存储成功:', fileID);
            resolve(fileID);
          })
          .catch(err => {
            console.error('生成搭配图片过程出错:', err);
            reject(err);
          });
      });
  });
}

// 更新搭配到数据库
function updateOutfitInDatabase(outfitId, name, imageFileID, items, category) {
  return new Promise((resolve, reject) => {
    if (!outfitId) {
      reject(new Error('搭配ID不能为空'));
      return;
    }

    // 获取当前用户OpenID
    userManager.getUserOpenId()
      .then(openid => {
        console.log('开始更新搭配数据到数据库，用户ID:', openid);

        const db = wx.cloud.database();
        const _ = db.command;

        // 准备要更新的搭配数据
        const outfitData = {
          name: name,
          imageFileID: imageFileID,
          previewImage: imageFileID, // 兼容性字段
          items: items.map(item => ({
            ...item,
            updateTime: Date.now() // 添加更新时间
          })),
          category: category,
          updateTime: db.serverDate(),
          _updateTime: Date.now() // 用于本地排序
        };

        console.log('准备更新的搭配数据:', outfitData);

        // 更新数据库
        return db.collection('outfits')
          .doc(outfitId)
          .update({
            data: outfitData
          });
      })
      .then(res => {
        console.log('搭配数据更新成功, 影响行数:', res.stats.updated);

        resolve({
          success: true,
          outfitId: outfitId
        });
      })
      .catch(err => {
        console.error('更新搭配数据失败:', err);
        reject(new Error('保存失败，请重试'));
      });
  });
}

// 加载搭配详情
function getOutfitDetail(outfitId) {
  return new Promise((resolve, reject) => {
    if (!outfitId) {
      reject(new Error('搭配ID不能为空'));
      return;
    }

    console.log('开始加载搭配详情，ID:', outfitId);

    const db = wx.cloud.database();

    db.collection('outfits')
      .doc(outfitId)
      .get()
      .then(res => {
        const outfitData = res.data;
        console.log('成功获取搭配详情:', outfitData);

        // 格式化日期
        if (outfitData.createTime) {
          outfitData.createTimeFormatted = formatDate(outfitData.createTime);
        } else if (outfitData._createTime) {
          outfitData.createTimeFormatted = formatDate(outfitData._createTime);
        }

        resolve(outfitData);
      })
      .catch(err => {
        console.error('获取搭配详情失败:', err);
        reject(err);
      });
  });
}

// 格式化日期
function formatDate(timestamp) {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  return `${year}.${month}.${day}`;
}

module.exports = {
  generateOutfitImage,
  updateOutfitInDatabase,
  getOutfitDetail,
  formatDate
};