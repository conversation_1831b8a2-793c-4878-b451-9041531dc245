.page {
  min-height: 100vh;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
}

.page-header {
  margin-bottom: 30rpx;
  text-align: center;
}

.page-title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

/* 选项卡样式 */
.tab-container {
  display: flex;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 32rpx;
  transition: all 0.3s;
}

.tab.active {
  font-weight: bold;
}

/* 功能区域通用样式 */
.function-area {
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 输入区域样式 */
.input-section {
  margin-bottom: 30rpx;
  position: relative;
}

.input {
  width: 100%;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.btn {
  width: 100%;
  height: 80rpx;
  border-radius: 8rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 20rpx;
}

/* 模糊匹配结果列表 */
.match-list {
  position: absolute;
  width: 100%;
  background-color: white;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
  max-height: 300rpx;
  overflow-y: auto;
}

.match-item {
  padding: 20rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.match-item:last-child {
  border-bottom: none;
}

.match-info {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}

/* 错误和成功消息 */
.error-msg {
  color: #e64340;
  font-size: 28rpx;
  padding: 20rpx;
  background-color: rgba(230, 67, 64, 0.1);
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.success-msg {
  color: #09bb07;
  font-size: 28rpx;
  padding: 20rpx;
  background-color: rgba(9, 187, 7, 0.1);
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

/* 查询结果样式 */
.result-section {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 10rpx;
  padding: 20rpx;
  margin-top: 30rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.result-item {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 28rpx;
}

.label {
  width: 200rpx;
  color: #666;
}

.value {
  flex: 1;
  font-weight: bold;
}

/* 进度条样式 */
.progress-section {
  margin-top: 30rpx;
}

.progress-item {
  margin-bottom: 20rpx;
}

.progress-label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.progress-text {
  display: block;
  text-align: right;
  font-size: 24rpx;
  margin-top: 6rpx;
}

/* 当前选中用户信息 */
.selected-user {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 10rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  margin-bottom: 30rpx;
}

.selected-user-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-align: center;
}

.selected-user-info {
  font-size: 28rpx;
}

.info-item {
  display: flex;
  margin-bottom: 12rpx;
}

/* 修改区域样式 */
.modify-section {
  margin-top: 30rpx;
}

.modify-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  flex-wrap: wrap;
}

.modify-label {
  width: 200rpx;
  font-size: 28rpx;
  color: #666;
}

.modify-input {
  width: 120rpx;
  height: 70rpx;
  background-color: rgba(255, 255, 255, 0.8);
  text-align: center;
  border-radius: 6rpx;
  margin-right: 10rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.modify-unit {
  margin-right: 20rpx;
  font-size: 28rpx;
}

.modify-btn {
  width: 160rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  text-align: center;
  border-radius: 6rpx;
  padding: 0;
  margin: 0;
}

/* 功能链接区域样式 */
.function-links {
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

.function-link {
  display: flex;
  align-items: center;
  padding: 30rpx;
  position: relative;
}

.link-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  color: white;
  font-size: 32rpx;
}

.link-text {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
}

.link-arrow {
  font-size: 36rpx;
  font-weight: bold;
}

/* 勋章管理区域样式 */
.insert-section {
  margin-bottom: 40rpx;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 10rpx;
  padding: 20rpx;
}

.section-subtitle {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.description {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.info-section {
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 10rpx;
  padding: 20rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.info-content {
  font-size: 28rpx;
  line-height: 1.6;
}

.info-item {
  margin-bottom: 10rpx;
}