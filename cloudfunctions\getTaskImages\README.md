# 任务图片云函数 - getTaskImages

本云函数用于从数据库中获取任务图片的信息，包括图片的fileID、步骤序号、标题和描述等。支持多个任务的图片管理，通过taskId区分不同任务的图片。

## 数据库设置

在使用此云函数前，需要在云数据库中创建`taskImages`集合，集合结构如下：

```javascript
{
  "_id": "自动生成的ID",
  "taskId": "task1", // 任务ID，用于区分不同任务
  "fileID": "云存储中图片的fileID",
  "step": 1, // 步骤序号(1-6)
  "title": "步骤标题",
  "description": "步骤描述",
  "active": true, // 是否激活
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

## 上传图片到云存储

在使用此功能之前，需要先将任务图片上传到云存储中。可以通过以下步骤进行：

1. 在微信开发者工具中，选择"云开发"
2. 点击左侧菜单的"存储"
3. 为每个任务创建独立的文件夹，如`task_images/task1`、`task_images/task2`等
4. 上传6张任务步骤图片，建议命名为`step1.png`、`step2.png`等
5. 上传完成后，点击每个文件，复制右侧的fileID

## 创建任务图片数据库记录

获取fileID后，可以通过以下两种方式创建数据库记录：

### 方式一：通过数据库控制台手动创建

1. 在微信开发者工具中，选择"云开发"
2. 点击左侧菜单的"数据库"
3. 创建名为`taskImages`的集合
4. 点击"添加记录"，按照上面的数据结构添加记录
   - 为每个任务添加6条记录（对应6个步骤）
   - 确保每条记录都包含正确的`taskId`字段

### 方式二：自动创建默认记录

本云函数包含一个`createDefaultTaskImages`函数，当数据库中没有找到指定任务ID的图片记录时，会自动创建默认记录。

**注意：** 需要修改`index.js`中的`defaultFileIDs`数组，将其替换为实际上传到云存储的fileID。

```javascript
// 默认图片文件ID
const defaultFileIDs = [
  'cloud://你的环境ID.7072-你的环境ID-1318127641/task_images/task1/step1.png',
  'cloud://你的环境ID.7072-你的环境ID-1318127641/task_images/task1/step2.png',
  'cloud://你的环境ID.7072-你的环境ID-1318127641/task_images/task1/step3.png',
  'cloud://你的环境ID.7072-你的环境ID-1318127641/task_images/task1/step4.png',
  'cloud://你的环境ID.7072-你的环境ID-1318127641/task_images/task1/step5.png',
  'cloud://你的环境ID.7072-你的环境ID-1318127641/task_images/task1/step6.png'
]
```

## 部署云函数

1. 修改好上述配置后，在微信开发者工具中右键点击`getTaskImages`云函数
2. 选择"上传并部署：云端安装依赖（不上传node_modules）"
3. 等待部署完成

## 使用说明

### 调用参数

调用云函数时可以传入以下参数：

```javascript
{
  "taskId": "task1" // 要获取图片的任务ID，如果不传则使用默认值"default"
}
```

### 返回格式

云函数返回格式如下：

```javascript
{
  "success": true,
  "data": [
    {
      "_id": "记录ID",
      "taskId": "task1",
      "fileID": "云存储中图片的fileID",
      "step": 1,
      "title": "步骤1",
      "description": "步骤1的描述",
      "active": true,
      "createTime": "创建时间",
      "updateTime": "更新时间"
    },
    // 更多步骤...
  ]
}
```

## 图片下载流程

小程序端获取fileID后，需要先获取临时下载URL，然后再下载图片。完整的处理流程如下：

1. 调用 `getTaskImages` 云函数获取任务图片记录（包含fileID）
2. 调用 `getTempFileURL` 云函数获取这些fileID对应的临时下载URL
3. 使用 `wx.downloadFile` API下载图片
4. 将图片保存到本地缓存系统
5. 使用本地缓存的图片显示任务进度

### 为什么使用 getTempFileURL 云函数

直接使用云开发提供的 `wx.cloud.getTempFileURL` API获取临时URL有以下缺点：

1. 在前端直接调用此API存在一定的安全隐患
2. 没有批量处理和错误重试机制
3. 无法在前端进行复杂的权限控制

使用云函数 `getTempFileURL` 获取临时下载URL有以下优势：

1. 增强安全性：所有文件访问都通过云函数，可以进行权限验证
2. 性能优化：云函数内置了分批处理逻辑，可以处理大量文件请求
3. 错误处理：统一的错误处理和返回格式，简化前端逻辑
4. 可扩展性：可以在云函数中加入更多逻辑，如权限检查、访问日志等

由于支持多任务，小程序端会为每个任务单独缓存图片，避免混淆。缓存键会包含taskId信息，确保不同任务的图片不会相互干扰。 