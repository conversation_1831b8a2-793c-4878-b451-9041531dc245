const colors = require('../../../util/colors');
const closetUtils = require('../closet/modules/closetUtils');
// 引入limitManager
const limitManager = require('../common/limitManager');
// 引入排序管理模块
const sortManager = require('./modules/sortManager');
// 引入视图管理模块
const viewManager = require('./modules/viewManager');
// 引入图片处理模块
const imageProcessor = require('../closet/modules/imageProcessor');

// 用于存储图片URL的本地缓存键
const URL_CACHE_KEY = 'clothes_image_urls';

Page({
  data: {
    // 使用全局颜色配置
    needRefreshCloset: false,
    hidePrices: false, // 控制价格显示/隐藏
    colors: {
      darkBrown: colors.darkBrown,
      darkOlive: colors.deepOlive,
      lightTaupe: colors.lightTaupe,
      mediumBrown: colors.mediumBrown,
      darkCoffee: colors.darkCoffee,
    },
    pinkBlueColors: {
      pinkLight: '#f9d5e5',
      pinkMedium: '#f7bad8',
      pinkDark: '#e75480',
      blueLight: '#cce4f7',
      blueMedium: '#a7d1f4',
      blueDark: '#5da9e9',
      blueExtraDark: '#3d7ab3'
    },
    blackWhiteColors: {
      black: '#000000',
      darkGray: '#333333',
      mediumGray: '#666666',
      lightGray: '#999999',
      veryLightGray: '#f5f5f5',
      white: '#e8e8e8'
    },
    // 导航栏相关数据
    navBackground: '#E8D1A7', // 默认秋季主题导航栏背景色
    navFrontColor: '#000000', // 默认导航栏前景色
    navTitle: '',            // 导航栏标题文本,
    category: '',           // 当前查看的类别
    clothes: [],            // 当前类别的衣物
    isLoading: true,        // 加载状态
    themeStyle: 'autumn',   // 主题风格，与衣柜页面保持一致

    // 筛选相关
    subcategories: [],      // 该类别下的所有细分类
    currentSubcategory: '', // 当前选中的细分类，空字符串表示显示全部
    filteredClothes: [],    // 筛选后的衣物列表

    // 季节相关
    currentSeason: '',      // 当前季节
    showingCurrentSeason: false, // 是否正在显示当前季节的衣物

    // 无限滚动相关
    displayedClothes: [],    // 当前显示的衣物
    pageSize: 10,            // 每次加载的数量
    currentLoadIndex: 0,     // 当前加载索引
    isLoadingMore: false,    // 是否正在加载更多
    hasMoreClothes: true,    // 是否有更多衣物可加载

    // 用户ID
    userOpenId: '',

    // 详情弹窗
    clothesDetail: null,

    // 编辑相关
    showEditModal: false,
    editingClothing: {},
    editingId: '',
    validCategories: ['上衣', '裤子', '裙子', '外套', '鞋子', '配饰'],
    originalClothing: null,

    // 待办事项相关
    showTodoModal: false,           // 是否显示待办事项弹窗
    currentTodo: {                  // 当前编辑的待办事项
      title: '',                    // 待办事项标题
      dueDate: '',                  // 截止日期
      completed: false              // 是否已完成
    },
    editingTodoIndex: -1,           // 当前编辑的待办事项索引，-1表示新增

    // 类别选择器
    showCategoryPicker: false,
    tempSelectedCategory: '',
    userCategories: [], // 新增：用户所有衣物的类别列表
    customCategoryValue: '', // 新增：自定义类别的值

    // 季节选择器
    showSeasonPicker: false,
    tempSelectedSeason: [],

    // 日期选择器
    showDatePicker: false,
    datePickerType: '',
    datePickerTitle: '',
    datePickerValue: '',

    // 奖励弹窗相关
    showRewardModal: false,
    rewardImage: '',
    rewardMessage: '',
    rewardType: '',
    rewardItemId: '',

    showingSuggestions: false,
    currentSuggestionField: '',
    suggestions: {
      type_detail: [],
      color: [],
      style: [],
      size: [],
      brand: [],
      purchaseChannel: [],
      storageLocation: [],
      material: []
    },

    // 添加季节选中状态对象
    seasonIsSelected: {
      '春季': false,
      '夏季': false,
      '秋季': false,
      '冬季': false
    },

    // 容器高度已使用flex布局替代

    // 首次加载标志
    firstLoad: true,

    // 滑动相关变量
    touchStartX: 0,
    touchEndX: 0,
    touchStartY: 0,  // 添加Y轴坐标记录
    touchEndY: 0,    // 添加Y轴坐标记录
    touchStartTime: 0, // 添加触摸开始时间
    minSwipeDistance: 80,  // 增加最小滑动距离，从50改为80
    maxSwipeTime: 300,   // 添加最大滑动时间限制（毫秒）
    maxVerticalDistance: 40, // 添加最大垂直滑动距离，超过则不触发横向滑动
    categoryBodyAnimation: '', // 滑动动画控制
    showSwipeHint: false,  // 是否显示滑动提示
    isScrolling: false,    // 是否正在滚动
    scrollLockTime: 500,   // 滚动锁定时间（毫秒）
    // 衣柜选择相关
    showWardrobePicker: false,
    tempSelectedWardrobe: null,
    wardrobeIsSelected: {},
    wardrobes: [], // 用户的衣柜列表

    // 排序相关
    showSortPicker: false,       // 是否显示排序选择器
    currentSortType: 'createTime',  // 当前排序类型，默认为创建时间
    sortAscending: false,        // 是否升序排序，默认为降序
    sortOptions: [],             // 可用的排序选项

    // 视图类型
    viewType: viewManager.VIEW_TYPES.GRID, // 默认视图类型

    // 批量编辑相关
    isSelectionMode: false,      // 是否处于选择模式
    selectedClothes: [],        // 已选中的衣物ID列表
    clothesIsSelected: {},      // 记录每件衣物的选中状态
    selectAll: false,           // 是否全选

    // 批量编辑弹窗
    showBatchEditModal: false,  // 是否显示批量编辑弹窗
    batchEditData: {},         // 批量编辑的数据

    // 图片编辑器相关
    showImageEditor: false,
    editingImageUrl: '',
  },

  // 初始化导航栏数据
  navBarData: {
    autumn: {
      background: '#E8D1A7',
      color: '#000000',
      title: ''
    },
    pinkBlue: {
      background: '#F9C9D6',
      color: '#000000',
      title: ''
    },
    blackWhite: {
      background: '#FFFFFF',
      color: '#000000',
      title: ''
    }
  },

  onLoad: function(options) {
    // 初始化首次加载标志
    this.firstLoad = true;

    // 获取传递过来的类别
    let category = decodeURIComponent(options.category || '');

    // 检查是否是JSON字符串形式的数组
    try {
      const parsedCategory = JSON.parse(category);
      if (Array.isArray(parsedCategory)) {
        category = parsedCategory;
        console.log('解析到多个类别:', category);
      }
    } catch (e) {
      // 不是JSON格式，保持原样
      console.log('单一类别:', category);
    }

    // 设置页面标题文本
    const pageTitle = Array.isArray(category) ? '衣柜内部' : category + '列表';

    // 更新导航栏标题
    this.navBarData.autumn.title = pageTitle;
    this.navBarData.pinkBlue.title = pageTitle;
    this.navBarData.blackWhite.title = pageTitle;

    // 使用flex布局替代固定高度计算

    // 从全局数据获取该类别的衣物
    const app = getApp();
    const tempClothes = app.globalData.tempClothes || [];

    // 从本地存储获取主题风格
    try {
      const themeStyle = wx.getStorageSync('themeStyle') || 'autumn';
      this.setData({
        themeStyle: themeStyle
      });
      // 初始化导航栏样式
      this.updateNavBarStyle(themeStyle);
    } catch (e) {
      console.error('读取主题风格失败:', e);
    }

    // 从本地存储获取价格显示设置
    try {
      const hidePrices = wx.getStorageSync('hidePrices') || false;
      this.setData({
        hidePrices: hidePrices
      });
      console.log('价格显示设置:', hidePrices ? '隐藏' : '显示');
    } catch (e) {
      console.error('读取价格显示设置失败:', e);
    }

    // 获取用户OpenID
    this.getUserOpenId();

    // 提取该类别下的所有细分类
    const subcategories = this.extractSubcategories(tempClothes);

    // 获取当前季节
    const currentSeason = this.getCurrentSeason();

    // 读取缓存中的季节筛选状态
    let showingCurrentSeason = false;
    try {
      // 读取全局的季节筛选状态，跨所有类别页面保持一致
      showingCurrentSeason = wx.getStorageSync('globalSeasonFilter') || false;
      console.log('读取全局季节筛选状态:', showingCurrentSeason);
    } catch (e) {
      console.error('读取季节筛选状态失败:', e);
    }

    // 检查是否是首次访问类别页面，如果是则显示滑动提示
    let hasShownSwipeHint = false;
    try {
      hasShownSwipeHint = wx.getStorageSync('hasShownCategorySwipeHint') || false;
    } catch (e) {
      console.error('读取滑动提示状态失败:', e);
    }

    // 获取用户的衣柜列表
    this.loadWardrobes();

    // 获取排序选项
    const sortOptions = sortManager.getSortOptions();

    // 初始化视图管理器
    this.viewManager = viewManager.init(this);

    // 加载用户保存的视图类型
    this.viewManager.loadSavedViewType();

    // 对衣物按创建时间降序排序
    const sortedClothes = sortManager.sortClothes(tempClothes, 'createTime', false);

    // 更新数据
    this.setData({
      category: category,
      clothes: sortedClothes,
      filteredClothes: sortedClothes, // 初始状态下，筛选后的衣物列表等于所有衣物，已按创建时间排序
      subcategories: subcategories,
      isLoading: false,
      // 使用flex布局替代固定高度
      currentSeason: currentSeason,
      showingCurrentSeason: showingCurrentSeason,
      showSwipeHint: !hasShownSwipeHint && subcategories.length > 1, // 有多个细分类时才显示提示
      sortOptions: sortOptions,
    }, () => {
      // 初始化加载衣物数据 - 仅在不需要季节筛选时直接加载
      if (!this.data.showingCurrentSeason) {
        this.initLoadClothes();
      } else {
        // 如果需要显示当前季节的衣物，使用initSeasonFilter而不是filterCurrentSeason
        // 这样不会改变筛选状态
        console.log('使用初始化方法应用季节筛选');
        this.initSeasonFilter();

        // 提示用户已筛选当前季节的衣服
        wx.showToast({
          title: `已筛选${currentSeason}季衣服`,
          icon: 'none',
          duration: 2000
        });
      }

      // 设置已显示滑动提示标记
      if (this.data.showSwipeHint) {
        wx.setStorageSync('hasShownCategorySwipeHint', true);

        // 3秒后自动隐藏滑动提示
        setTimeout(() => {
          this.setData({
            showSwipeHint: false
          });
        }, 3000);
      }
    });
  },

  // 切换视图类型
  toggleViewType: function() {
    const newViewType = this.viewManager.toggleViewType();
    console.log('视图类型已切换为:', newViewType);
  },

  // 提取所有不重复的细分类
  extractSubcategories: function(clothes) {
    if (!clothes || !Array.isArray(clothes)) {
      return [];
    }

    // 使用Set来确保不重复
    const subcategories = new Set();

    // 遍历衣物，提取细分类
    clothes.forEach(item => {
      if (item.type_detail && item.type_detail.trim() !== '') {
        subcategories.add(item.type_detail.trim());
      }
    });

    // 转换为数组并排序
    return Array.from(subcategories).sort();
  },

  // 初始化加载衣物
  initLoadClothes: function() {
    this.setData({
      displayedClothes: [],
      currentLoadIndex: 0,
      hasMoreClothes: true
    }, () => {
      this.loadMoreClothes();
    });
  },

  // 加载更多衣物
  loadMoreClothes: function() {
    if (!this.data.hasMoreClothes || this.data.isLoadingMore) {
      return;
    }

    this.setData({
      isLoadingMore: true
    });

    const { filteredClothes, displayedClothes, currentLoadIndex, pageSize } = this.data;
    const start = currentLoadIndex;
    const end = Math.min(start + pageSize, filteredClothes.length);

    if (start >= filteredClothes.length) {
      this.setData({
        hasMoreClothes: false,
        isLoadingMore: false
      });
      return;
    }

    // 模拟网络延迟
    setTimeout(() => {
      const newClothes = filteredClothes.slice(start, end);

      this.setData({
        displayedClothes: [...displayedClothes, ...newClothes],
        currentLoadIndex: end,
        hasMoreClothes: end < filteredClothes.length,
        isLoadingMore: false
      });
    }, 300);
  },

  // 根据细分类筛选衣物
  filterBySubcategory: function(e) {
    const subcategory = e.currentTarget.dataset.subcategory;
    console.log('筛选细分类:', subcategory);

    // 更新当前选中的细分类
    this.setData({
      currentSubcategory: subcategory,
    });

    // 先根据细分类筛选衣物
    let filteredClothes = [];

    // 如果选择的是"全部"，则显示所有衣物
    if (!subcategory) {
      filteredClothes = [...this.data.clothes];
    } else {
      // 根据细分类筛选衣物
      filteredClothes = this.data.clothes.filter(item =>
        item.type_detail === subcategory
      );
    }

    // 如果当前已启用季节筛选，则再应用季节筛选
    if (this.data.showingCurrentSeason) {
      const seasonKeyword = this.data.currentSeason + '季';
      filteredClothes = filteredClothes.filter(item => {
        if (!item.season) return false;
        return item.season.includes(seasonKeyword);
      });
    }

    // 始终应用排序，即使没有指定排序类型也使用默认的createTime排序
    const sortType = this.data.currentSortType || 'createTime';
    const ascending = sortType === 'createTime' ? false : this.data.sortAscending; // createTime默认降序
    filteredClothes = sortManager.sortClothes(
      filteredClothes,
      sortType,
      ascending
    );

    // 更新筛选后的衣物列表
    this.setData({
      filteredClothes: filteredClothes
    }, () => {
      this.initLoadClothes();
    });

    // 如果筛选后没有衣物，显示提示
    if (filteredClothes.length === 0) {
      let message = '没有';
      if (subcategory) message += subcategory + '细分类的';
      if (this.data.showingCurrentSeason) message += this.data.currentSeason + '季的';
      message += this.data.category + '数据';

      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 获取用户OpenID - 优化使用缓存
  getUserOpenId: function() {
    // 先尝试从本地缓存获取openid
    try {
      const cachedOpenId = wx.getStorageSync('userOpenId');
      if (cachedOpenId) {
        console.log('从缓存获取用户OpenID成功:', cachedOpenId);
        this.setData({
          userOpenId: cachedOpenId
        });
        return; // 如果从缓存获取成功，则直接返回
      }
    } catch (e) {
      console.error('从缓存获取OpenID失败:', e);
    }

    // 缓存中没有，调用云函数获取
    console.log('缓存中无OpenID，调用云函数获取');
    wx.cloud.callFunction({
      name: 'getOpenId'
    })
    .then(res => {
      const openid = res.result.openid;
      console.log('通过云函数获取用户OpenID成功:', openid);

      // 缓存openid
      try {
        wx.setStorageSync('userOpenId', openid);
        console.log('成功将OpenID保存到缓存');
      } catch (e) {
        console.error('缓存OpenID失败:', e);
      }

      this.setData({
        userOpenId: openid
      });
    })
    .catch(err => {
      console.error('获取用户OpenID失败:', err);
    });
  },

  // 查看衣物详情
  viewClothesDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('查看衣物详情:', id);

    // 查找当前衣物数据
    const clothing = this.data.clothes.find(item => item._id === id);
    if (!clothing) {
      console.error('未找到衣物数据:', id);
      closetUtils.showErrorToast('未找到衣物数据');
      return;
    }

    console.log('找到衣物数据:', clothing);

    // 检查衣物是否有特殊标记
    if (clothing.flag) {
      // 处理增加衣物上限的标记
      if (clothing.flag === 'clothespulsfive') {
        // 显示带图片的奖励弹窗
        this.setData({
          showRewardModal: true,
          rewardImage: clothing.tempImageUrl || clothing.fileID,
          rewardMessage: '恭喜你，获得了衣服加5件的奖励！',
          rewardType: 'clothespulsfive',
          rewardItemId: id
        });
        return;
      }
      // 处理增加搭配上限的标记
      else if (clothing.flag === 'outfitpulsone') {
        // 显示带图片的奖励弹窗
        this.setData({
          showRewardModal: true,
          rewardImage: clothing.tempImageUrl || clothing.fileID,
          rewardMessage: '恭喜你，获得了搭配加1套的奖励！',
          rewardType: 'outfitpulsone',
          rewardItemId: id
        });
        return;
      }
    }

    // 获取衣物所属衣柜的名称
    const wardrobeId = clothing.wardrobeId;
    let wardrobeName = '';

    if (wardrobeId) {
      // 从本地缓存获取用户的衣柜数据
      const userWardrobesKey = 'user_wardrobes_' + this.data.userOpenId;
      const userWardrobes = wx.getStorageSync(userWardrobesKey) || [];

      // 查找匹配的衣柜
      const wardrobe = userWardrobes.find(w => w._id === wardrobeId);
      if (wardrobe) {
        wardrobeName = wardrobe.name || '默认衣柜';
      }
    }

    // 创建一个包含衣柜名称的衣物对象
    const clothingWithWardrobeName = {
      ...clothing,
      wardrobeName: wardrobeName
    };

    // 普通衣物，显示详情弹窗
    this.setData({
      clothesDetail: clothingWithWardrobeName
    });
  },

  // 关闭衣物详情
  closeClothesDetail: function() {
    this.setData({
      clothesDetail: null
    });
  },

  // 切换价格显示/隐藏
  togglePriceVisibility: function() {
    const newHidePrices = !this.data.hidePrices;

    // 更新页面数据
    this.setData({
      hidePrices: newHidePrices
    });

    // 保存设置到本地缓存
    try {
      wx.setStorageSync('hidePrices', newHidePrices);
      console.log('价格显示设置已更新:', newHidePrices ? '隐藏' : '显示');

      // 显示提示
      wx.showToast({
        title: newHidePrices ? '价格已隐藏' : '价格已显示',
        icon: 'none',
        duration: 1500
      });
    } catch (e) {
      console.error('保存价格显示设置失败:', e);
    }
  },

  // 存入箱子（将衣物设置为隐藏）
  storeClothing: function(e) {
    const clothingId = e.currentTarget.dataset.id;
    console.log('准备存入箱子:', clothingId);

    // 如果没有ID，直接返回
    if (!clothingId) {
      console.error('缺少衣物ID');
      closetUtils.showErrorToast('操作失败');
      return;
    }

    // 首先检查用户是否为VIP会员
    this.checkIsVipMember().then(isVip => {
      if (!isVip) {
        // 非VIP会员，显示提示
        wx.showModal({
          title: 'VIP专属功能',
          content: '存入换季箱是VIP会员专属功能，开通VIP会员即可使用此功能。',
          confirmText: '了解详情',
          cancelText: '取消',
          confirmColor: '#D4AF37',
          success: (res) => {
            if (res.confirm) {
              // 跳转到设置页面查看会员权益
              wx.navigateTo({
                url: '/page/settings/settings'
              });
            }
          }
        });
        return;
      }

      // VIP会员，显示确认对话框
      wx.showModal({
        title: '确认存入换季箱？',
        content: '确定要将该衣物存入换季箱吗？这个功能可以帮助你将当前不常用的衣服隐藏起来，存入后将不在衣柜页面显示，但可以在筛选和换季箱中找到。',
        success: (res) => {
          if (res.confirm) {
            // 显示加载提示
            closetUtils.showLoading('存入中...');

            // 准备更新数据
            const updateData = {
              clothingId: clothingId,
              hiden: true  // 设置隐藏属性为true
            };

            // 调用云函数更新衣物信息
            wx.cloud.callFunction({
              name: 'updateClothing',
              data: updateData
            })
            .then(res => {
              console.log('云函数调用结果:', res);
              if (res.result && res.result.success) {
                closetUtils.hideLoading();
                closetUtils.showSuccessToast('已存入箱子');

                // 更新本地数据
                this.updateLocalClothing(clothingId, { hiden: true });

                // 关闭详情弹窗
                this.setData({
                  clothesDetail: null
                });

                // 从当前页面的数组中删除该衣物
                const updatedClothes = this.data.clothes.filter(item => item._id !== clothingId);
                this.setData({
                  clothes: updatedClothes
                });

                // 更新父页面的数据
                this.updateClosetPage();
                this.data.needRefreshCloset = true;
              } else {
                throw new Error(res.result?.message || '存入失败');
              }
            })
            .catch(err => {
              console.error('存入箱子失败:', err);
              closetUtils.hideLoading();
              closetUtils.showErrorToast('存入失败: ' + (err.message || '未知错误'));
            });
          }
        }
      });
    }).catch(err => {
      console.error('检查VIP状态失败:', err);
      closetUtils.showErrorToast('系统错误，请稍后再试');
    });
  },

  // 从箱子取出（将衣物设置为非隐藏）
  retrieveClothing: function(e) {
    const clothingId = e.currentTarget.dataset.id;
    console.log('准备从箱子取出:', clothingId);

    // 如果没有ID，直接返回
    if (!clothingId) {
      console.error('缺少衣物ID');
      closetUtils.showErrorToast('操作失败');
      return;
    }

    // 显示确认对话框
    wx.showModal({
      title: '确认取出箱子',
      content: '确定要将该衣物从箱子中取出吗？取出后将在主页面显示。',
      success: (res) => {
        if (res.confirm) {
          // 显示加载提示
          closetUtils.showLoading('取出中...');

          // 准备更新数据
          const updateData = {
            clothingId: clothingId,
            hiden: false  // 设置隐藏属性为false
          };

          // 调用云函数更新衣物信息
          wx.cloud.callFunction({
            name: 'updateClothing',
            data: updateData
          })
          .then(res => {
            console.log('云函数调用结果:', res);
            if (res.result && res.result.success) {
              closetUtils.hideLoading();
              closetUtils.showSuccessToast('已取出箱子');

              // 更新本地数据
              this.updateLocalClothing(clothingId, { hiden: false });

              // 更新详情弹窗中的数据
              if (this.data.clothesDetail && this.data.clothesDetail._id === clothingId) {
                const updatedDetail = { ...this.data.clothesDetail, hiden: false };
                this.setData({
                  clothesDetail: updatedDetail
                });
              }

              // 更新父页面的数据
              this.updateClosetPage();
              wx.setStorageSync('fromFilterPage', true);
            } else {
              throw new Error(res.result?.message || '取出失败');
            }
          })
          .catch(err => {
            console.error('从箱子取出失败:', err);
            closetUtils.hideLoading();
            closetUtils.showErrorToast('取出失败: ' + (err.message || '未知错误'));
          });
        }
      }
    });
  },

  // 处理奖励弹窗的确认按钮点击
  confirmReward: function() {
    const { rewardType, rewardItemId } = this.data;

    if (rewardType === 'clothespulsfive') {
      // 为用户增加5件衣服上限
      limitManager.increaseClothesLimit(this.data.userOpenId, 5)
        .then(() => {
          // 增加上限成功后删除该衣物
          this.removeLocalClothing(rewardItemId);
          this.deleteClothing(rewardItemId);
          // 显示成功提示
          closetUtils.showSuccessToast('衣服上限已增加5件');
        })
        .catch(err => {
          console.error('增加衣服上限失败:', err);
          closetUtils.showErrorToast('增加衣服上限失败');
        });
    } else if (rewardType === 'outfitpulsone') {
      // 为用户增加1个搭配上限
      limitManager.increaseOutfitsLimit(this.data.userOpenId, 1)
        .then(() => {
          // 增加上限成功后删除该衣物
          this.removeLocalClothing(rewardItemId);
          this.deleteClothing(rewardItemId);
          // 显示成功提示
          closetUtils.showSuccessToast('搭配上限已增加1套');
        })
        .catch(err => {
          console.error('增加搭配上限失败:', err);
          closetUtils.showErrorToast('增加搭配上限失败');
        });
    }

    // 关闭奖励弹窗
    this.setData({
      showRewardModal: false,
      rewardImage: '',
      rewardMessage: '',
      rewardType: '',
      rewardItemId: ''
    });
  },

  // 关闭奖励弹窗
  closeRewardModal: function() {
    this.setData({
      showRewardModal: false,
      rewardImage: '',
      rewardMessage: '',
      rewardType: '',
      rewardItemId: ''
    });
  },

  // 从详情页面删除衣物
  deleteClothesFromDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('从详情页面删除衣物:', id);

    if (!id) {
      console.error('无效的衣物ID');
      closetUtils.showErrorToast('无法删除：无效的衣物数据');
      return;
    }

    // 获取衣物详情
    const clothing = this.data.clothesDetail;
    if (!clothing) {
      console.error('找不到衣物详情');
      return;
    }

    // 获取衣物名称，用于确认对话框显示
    const clothingName = clothing.name ||
                         ((clothing.color || '') + ' ' + (clothing.style || '') + (clothing.category || ''));

    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${clothingName}"吗？此操作不可撤销。`,
      confirmColor: '#e74c3c',
      success: res => {
        if (res.confirm) {
          // 先本地删除，提高响应速度
          this.removeLocalClothing(id);

          // 关闭详情弹窗
          this.setData({
            clothesDetail: null
          });

          // 然后在云端删除
          this.deleteClothing(id);

          // 同时更新上一页的衣柜页面数据（如果有）
          this.updateClosetPage();
        }
      }
    });
  },

  // 同步更新上一页衣柜页面数据
  updateClosetPage: function() {
    try {
      const pages = getCurrentPages();
      const closetPage = pages[pages.length - 2]; // 获取上一页面(可能是衣柜页面)
      if (closetPage && closetPage.route.includes('closet') && typeof closetPage.refreshClothes === 'function') {
        // 调用衣柜页面的刷新方法
        closetPage.refreshClothes();
      }
    } catch (err) {
      console.error('同步更新衣柜页面出错:', err);
    }
  },

  // 从本地数据中移除衣物
  removeLocalClothing: function(id) {
    if (!id) {
      console.error('无效的衣物ID');
      return;
    }

    // 从clothes中移除
    const clothes = this.data.clothes.filter(item => item._id !== id);

    // 从filteredClothes中也移除
    const filteredClothes = this.data.filteredClothes.filter(item => item._id !== id);

    // 更新页面数据
    this.setData({
      clothes: clothes,
      filteredClothes: filteredClothes
    });

    // 如果删除后列表为空，显示提示
    if (clothes.length === 0) {
      closetUtils.showInfoToast(`暂无${this.data.category}衣物`);
    } else if (filteredClothes.length === 0 && this.data.currentSubcategory) {
      // 如果当前有筛选，且筛选后的列表为空了，则提示用户
      closetUtils.showInfoToast(`暂无${this.data.currentSubcategory}细分类的${this.data.category}衣物`);
      // 自动清除筛选，显示所有
      this.setData({
        currentSubcategory: '',
        filteredClothes: clothes
      });
    }

    // 清除全局缓存，确保衣柜页面重新加载时能获取最新数据
    try {
      // 清除衣物缓存
      const cacheKey = `user_clothes_cache_${this.data.userOpenId}`;
      wx.removeStorageSync(cacheKey);

      // 设置刷新标记
      wx.setStorageSync('needRefreshClothes', true);

      console.log('已清除衣物缓存:', cacheKey);
    } catch (err) {
      console.error('清除缓存失败:', err);
    }

    console.log('已从本地移除衣物:', id);
  },

  // 删除衣物
  deleteClothing: function(id) {
    if (!id) {
      console.error('无效的衣物ID');
      return;
    }

    closetUtils.showLoading('删除中...');

    // 直接调用云函数执行删除操作
    wx.cloud.callFunction({
      name: 'deleteClothing',
      data: {
        clothingId: id
      }
    })
    .then(res => {
      console.log('删除衣物成功:', res);


      // 更新用户衣物计数（减1）
      return limitManager.updateClothesCount(this.data.userOpenId, -1)
        .then(() => res);
    })
    .then(res => {
      closetUtils.hideLoading();
      closetUtils.showSuccessToast('删除成功');


      // 设置刷新标记，通知首页更新衣柜概览数据
      wx.setStorageSync('needRefreshWardrobeSummary', true);
      // 设置刷新标记，通知衣柜页面刷新数据
      wx.setStorageSync('needRefreshClothes', true);

      // 更新应用内全局数据
      const app = getApp();
      app.globalData.limitDataNeedRefresh = true;
      if (app.globalData && Array.isArray(app.globalData.clothes)) {
        app.globalData.clothes = app.globalData.clothes.filter(item => item._id !== id);
        console.log('已更新全局衣物数据');
      }

      // 关闭详情视图（如果正在显示）
      if (this.data.clothesDetail && this.data.clothesDetail._id === id) {
        this.setData({
          clothesDetail: null
        });
      }
    })
    .catch(err => {
      console.error('删除衣物失败:', err);
      closetUtils.hideLoading();
      closetUtils.showErrorToast('删除失败: ' + (err.message || '未知错误'));
    });
  },

  // 点击编辑按钮
  editClothes: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('编辑衣物:', id);

    // 查找当前衣物数据
    const clothing = this.data.clothes.find(item => item._id === id);
    if (!clothing) {
      console.error('未找到衣物数据:', id);
      closetUtils.showErrorToast('未找到衣物数据');
      return;
    }

    // 保存详情信息，用于编辑后恢复查看
    const previousClothesDetail = this.data.clothesDetail;

    // 先关闭详情弹窗
    this.setData({
      clothesDetail: null,
      // 记录当前是从详情页面进入编辑界面的
      fromDetailView: true,
      // 保存编辑前的衣物ID，用于编辑后重新显示详情
      editingFromDetailId: id
    });

    // 显示编辑弹窗
    this.showEditClothingModal(clothing);
  },

  // 显示编辑衣物弹窗
  showEditClothingModal: function(clothing) {
    console.log('显示编辑弹窗，衣物数据:', clothing);
    // 创建深拷贝，避免直接修改原始数据
    const editingClothing = JSON.parse(JSON.stringify(clothing));
    const originalClothing = JSON.parse(JSON.stringify(clothing));

    // 确保价格字段为字符串类型
    if (editingClothing.price !== undefined && editingClothing.price !== null) {
      editingClothing.price = String(editingClothing.price);
    }

    // 确保todos字段存在
    if (!editingClothing.todos) {
      editingClothing.todos = [];
    }

    // 确保wardrobeName字段存在
    if (editingClothing.wardrobeId && !editingClothing.wardrobeName) {
      // 从本地缓存获取用户的衣柜数据
      const userWardrobesKey = 'user_wardrobes_' + this.data.userOpenId;
      try {
        const userWardrobes = wx.getStorageSync(userWardrobesKey) || [];
        // 查找匹配的衣柜
        const wardrobe = userWardrobes.find(w => w._id === editingClothing.wardrobeId);
        if (wardrobe) {
          editingClothing.wardrobeName = wardrobe.name || '默认衣柜';
          console.log('找到衣柜名称:', editingClothing.wardrobeName);
        }
      } catch (e) {
        console.error('获取衣柜名称失败:', e);
      }
    }

    // 初始化所有输入框的焦点状态为false
    editingClothing.nameFocus = false;
    editingClothing.type_detailFocus = false;
    editingClothing.colorFocus = false;
    editingClothing.styleFocus = false;
    editingClothing.sizeFocus = false;
    editingClothing.brandFocus = false;
    editingClothing.purchaseChannelFocus = false;
    editingClothing.storageLocationFocus = false;
    editingClothing.materialFocus = false;
    editingClothing.priceFocus = false;
    editingClothing.wornCountFocus = false;
    editingClothing.remarkFocus = false;
    editingClothing.todoTitleFocus = false;

    // 初始化suggestions结构，但不加载数据，等到字段获得焦点时再加载
    this.setData({
      editingClothing: editingClothing,
      originalClothing: originalClothing,
      showEditModal: true,
      suggestions: {
        type_detail: [],
        color: [],
        style: [],
        size: [],
        brand: [],
        purchaseChannel: [],
        storageLocation: [],
        material: []
      }
    });

    // 检测是否是iOS设备，如果是则使用特殊处理
    if (this.checkIsIOS()) {
      console.log('检测到iOS设备，使用特殊输入框处理');
      // 在iOS上需要等弹窗完全显示后再自动聚焦第一个输入框
      setTimeout(() => {
        this.setData({
          'editingClothing.nameFocus': true
        });
      }, 300);
    }
  },

  // 显示历史值建议
  showSuggestions: function(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({
      showingSuggestions: true,
      currentSuggestionField: field
    });

    // 在设置当前字段后立即加载该字段的建议
    this.loadSuggestions();
  },

  // 选择历史值建议
  selectSuggestion: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.currentTarget.dataset.value;

    // 根据字段类型调用相应的onChange函数
    switch(field) {
      case 'type_detail':
        this.setData({
          'editingClothing.type_detail': value,
          showingSuggestions: false
        });
        break;
      case 'color':
        this.setData({
          'editingClothing.color': value,
          showingSuggestions: false
        });
        break;
      case 'style':
        this.setData({
          'editingClothing.style': value,
          showingSuggestions: false
        });
        break;
      case 'size':
        this.setData({
          'editingClothing.size': value,
          showingSuggestions: false
        });
        break;
      case 'brand':
        this.setData({
          'editingClothing.brand': value,
          showingSuggestions: false
        });
        break;
      case 'purchaseChannel':
        this.setData({
          'editingClothing.purchaseChannel': value,
          showingSuggestions: false
        });
        break;
      case 'storageLocation':
        this.setData({
          'editingClothing.storageLocation': value,
          showingSuggestions: false
        });
        break;
      case 'material':
        this.setData({
          'editingClothing.material': value,
          showingSuggestions: false
        });
        break;
    }
  },

  // 加载历史值建议
  loadSuggestions: function() {
    // 获取所有衣物数据
    const allClothes = this.data.clothes || [];

    // 检查当前正在编辑的字段
    const currentField = this.data.currentSuggestionField;

    // 如果没有当前编辑字段，则不加载建议
    if (!currentField) {
      console.log('没有正在编辑的字段，不加载建议');
      return;
    }

    // 获取当前编辑衣物的类别
    const currentCategory = this.data.editingClothing?.category;
    console.log('为字段加载建议:', currentField, '当前类别:', currentCategory);

    // 初始化建议数组结构，但只填充当前编辑的字段
    const suggestions = {
      type_detail: [],
      color: [],
      style: [],
      size: [],
      brand: [],
      purchaseChannel: [],
      storageLocation: [],
      material: []
    };

    // 遍历所有衣物，仅收集当前编辑字段的唯一值
    // 且只收集与当前衣物同类别的历史值
    allClothes.forEach(item => {
      // 如果有类别且与当前编辑衣物类别不同，则跳过
      if (currentCategory && item.category !== currentCategory) {
        return;
      }

      const value = item[currentField];
      if (value && !suggestions[currentField].includes(value)) {
        suggestions[currentField].push(value);
      }
    });

    // 设置到data中
    this.setData({
      suggestions: suggestions
    });
  },

  // 隐藏编辑衣物弹窗时同时隐藏建议
  hideEditClothingModal: function() {
    this.setData({
      showEditModal: false,
      editingClothing: {},
      editingId: '',
      showingSuggestions: false,
      currentSuggestionField: ''
    });
  },

  // 点击其他地方时隐藏建议
  preventBubble: function(e) {
    // 只在显示建议时隐藏建议，避免阻止其他触摸事件
    if (this.data.showingSuggestions) {
      this.setData({
        showingSuggestions: false
      });
    }
    // 不阻止事件继续传播
  },

  // 检测设备是iOS还是Android
  checkIsIOS: function() {
    const systemInfo = wx.getSystemInfoSync();
    return systemInfo.platform === 'ios' || systemInfo.system.toLowerCase().includes('ios');
  },

  // iOS设备特殊处理焦点
  handleIOSFocus: function(e) {
    if (!this.checkIsIOS()) return;

    // 获取当前输入框
    const field = e.currentTarget.dataset.field;

    // iOS设备上，先让输入框失去焦点再重新聚焦，解决输入延迟问题
    setTimeout(() => {
      this.setData({
        [`editingClothing.${field}Focus`]: true
      });
    }, 50);
  },

  // 处理名称变化
  onNameChange: function(e) {
    this.setData({
      'editingClothing.name': e.detail.value
    });
  },

  // 处理类别变化 - 显示类别选择器
  showCategoryPicker: function() {
    // 获取最新的用户衣物类别
    const categories = this.getUserClothesCategories();

    this.setData({
      userCategories: categories,
      tempSelectedCategory: this.data.editingClothing.category || '',
      showCategoryPicker: true
    });
  },

  // 选择类别
  selectCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      tempSelectedCategory: category
    });
  },

  // 自定义类别输入处理
  onCustomCategoryInput: function(e) {
    console.log('自定义类别输入:', e.detail.value);
    this.setData({
      customCategoryValue: e.detail.value
    });
  },

  // 确认类别选择
  confirmCategoryPicker: function() {
    console.log('确认类别选择:', this.data.tempSelectedCategory);

    // 检查是否是批量编辑模式
    if (this.data.isBatchEdit) {
      let newCategory = this.data.tempSelectedCategory;

      // 如果选择了自定义类别，且输入了值，则使用自定义值
      if (newCategory === '自定义' && this.data.customCategoryValue.trim()) {
        newCategory = this.data.customCategoryValue.trim();
        console.log('使用自定义类别:', newCategory);

        // 将新类别添加到有效类别列表中
        if (!this.data.validCategories.includes(newCategory)) {
          const updatedValidCategories = [...this.data.validCategories, newCategory];
          this.setData({
            validCategories: updatedValidCategories,
            userCategories: updatedValidCategories
          });
          console.log('更新有效类别列表:', updatedValidCategories);
        }
      }

      // 更新批量编辑数据
      this.setData({
        'batchEditData.category': newCategory,
        showCategoryPicker: false,
        isBatchEdit: false,
        customCategoryValue: ''
      });
      return;
    }

    // 单个衣物编辑模式
    // 记录原始类别，用于判断是否发生变更
    const originalCategory = this.data.editingClothing.category;
    let newCategory = this.data.tempSelectedCategory;

    // 如果选择了自定义类别，且输入了值，则使用自定义值
    if (newCategory === '自定义' && this.data.customCategoryValue.trim()) {
      newCategory = this.data.customCategoryValue.trim();
      console.log('使用自定义类别:', newCategory);

      // 将新类别添加到有效类别列表中
      if (!this.data.validCategories.includes(newCategory)) {
        const updatedValidCategories = [...this.data.validCategories, newCategory];
        this.setData({
          validCategories: updatedValidCategories,
          userCategories: updatedValidCategories
        });
        console.log('更新有效类别列表:', updatedValidCategories);
      }
    }

    this.setData({
      'editingClothing.category': newCategory,
      showCategoryPicker: false,
      customCategoryValue: '' // 重置自定义类别输入
    });

    // 如果类别发生变更，在控制台输出提示
    if (originalCategory !== newCategory) {
      console.log('类别已变更:', originalCategory, '=>', newCategory);
    }
  },

  // 隐藏类别选择器
  hideCategoryPicker: function() {
    this.setData({
      showCategoryPicker: false
    });
  },

  // 处理细分类变化
  onTypeDetailChange: function(e) {
    this.setData({
      'editingClothing.type_detail': e.detail.value
    });
  },

  // 处理颜色变化
  onColorChange: function(e) {
    this.setData({
      'editingClothing.color': e.detail.value
    });
  },

  // 处理风格变化
  onStyleChange: function(e) {
    this.setData({
      'editingClothing.style': e.detail.value
    });
  },

  // 处理尺码变化
  onSizeChange: function(e) {
    this.setData({
      'editingClothing.size': e.detail.value
    });
  },

  // 处理品牌变化
  onBrandChange: function(e) {
    this.setData({
      'editingClothing.brand': e.detail.value
    });
  },

  // 处理购买渠道变化
  onPurchaseChannelChange: function(e) {
    this.setData({
      'editingClothing.purchaseChannel': e.detail.value
    });
  },

  // 处理存储位置变化
  onStorageLocationChange: function(e) {
    this.setData({
      'editingClothing.storageLocation': e.detail.value
    });
  },

  // 处理材质变化
  onMaterialChange: function(e) {
    this.setData({
      'editingClothing.material': e.detail.value
    });
  },

  // 处理备注变化
  onRemarkChange: function(e) {
    this.setData({
      'editingClothing.remark': e.detail.value
    });
  },

  // 处理价格变化
  onPriceChange: function(e) {
    this.setData({
      'editingClothing.price': e.detail.value
    });
  },

  // 处理穿着次数变化
  onWornCountChange: function(e) {
    this.setData({
      'editingClothing.wornCount': parseInt(e.detail.value) || 0
    });
  },

  // 显示最后穿着日期选择器
  showLastWornDatePicker: function() {
    this.setData({
      datePickerType: 'lastWornDate',
      datePickerTitle: '选择最后穿着日期',
      datePickerValue: this.data.editingClothing.lastWornDate || '',
      showDatePicker: true
    });
  },

  // 显示购买日期选择器
  showPurchaseDatePicker: function() {
    this.setData({
      datePickerType: 'purchaseDate',
      datePickerTitle: '选择购买日期',
      datePickerValue: this.data.editingClothing.purchaseDate || '',
      showDatePicker: true
    });
  },

  // 日期选择器变化
  onDatePickerChange: function(e) {
    this.setData({
      datePickerValue: e.detail.value
    });
  },

  // 确认日期选择
  confirmDatePicker: function() {
    const { datePickerType, datePickerValue } = this.data;

    if (datePickerType === 'lastWornDate') {
      this.setData({
        'editingClothing.lastWornDate': datePickerValue
      });
    } else if (datePickerType === 'purchaseDate') {
      this.setData({
        'editingClothing.purchaseDate': datePickerValue
      });
    }

    this.setData({
      showDatePicker: false
    });
  },

  // 隐藏日期选择器
  hideDatePicker: function() {
    this.setData({
      showDatePicker: false
    });
  },

  // 显示季节选择器
  showSeasonPicker: function() {
    let currentSeasons = [];

    if (this.data.editingClothing && this.data.editingClothing.season) {
      if (typeof this.data.editingClothing.season === 'string') {
        currentSeasons = this.data.editingClothing.season.split('/').filter(s => s);
      } else if (Array.isArray(this.data.editingClothing.season)) {
        currentSeasons = [...this.data.editingClothing.season];
      }
    }

    // 初始化季节选中状态对象
    const seasonIsSelected = {};
    const seasonOptions = ['春季', '夏季', '秋季', '冬季'];
    seasonOptions.forEach(season => {
      seasonIsSelected[season] = currentSeasons.includes(season);
    });

    this.setData({
      tempSelectedSeason: currentSeasons,
      seasonIsSelected: seasonIsSelected,
      showSeasonPicker: true
    });
  },

  // 选择季节
  selectSeason: function(e) {
    const season = e.currentTarget.dataset.season;
    const tempSelectedSeason = [...this.data.tempSelectedSeason];
    const index = tempSelectedSeason.indexOf(season);

    if (index === -1) {
      // 未选中，添加到选中列表
      tempSelectedSeason.push(season);
    } else {
      // 已选中，从选中列表移除
      tempSelectedSeason.splice(index, 1);
    }

    // 更新季节选中状态对象
    const seasonIsSelected = {...this.data.seasonIsSelected};
    seasonIsSelected[season] = !seasonIsSelected[season];

    this.setData({
      tempSelectedSeason: tempSelectedSeason,
      seasonIsSelected: seasonIsSelected
    });
  },

  // 确认季节选择
  confirmSeasonPicker: function() {
    // 检查是否是批量编辑模式
    if (this.data.isBatchEdit) {
      const seasonValue = this.data.tempSelectedSeason.join('/');
      this.setData({
        'batchEditData.season': seasonValue,
        showSeasonPicker: false,
        isBatchEdit: false
      });
      return;
    }

    // 单个衣物编辑模式
    this.setData({
      'editingClothing.season': this.data.tempSelectedSeason.join('/'),
      showSeasonPicker: false
    });
  },

  // 隐藏季节选择器
  hideSeasonPicker: function() {
    this.setData({
      showSeasonPicker: false
    });
  },

  // 清除季节选择
  clearSeasonSelection: function() {
    // 清除所有季节选中状态
    const seasonIsSelected = {};
    const seasonOptions = ['春季', '夏季', '秋季', '冬季'];
    seasonOptions.forEach(season => {
      seasonIsSelected[season] = false;
    });

    this.setData({
      tempSelectedSeason: [],
      seasonIsSelected: seasonIsSelected
    });
  },

  // 保存衣物编辑
  saveClothingEdit: function() {
    const editingClothing = this.data.editingClothing;
    const originalClothing = this.data.originalClothing;

    if (!editingClothing._id) {
      console.error('缺少衣物ID，无法保存');
      closetUtils.showErrorToast('保存失败：缺少必要数据');
      return;
    }

    // 检查是否有实际修改
    if (JSON.stringify(editingClothing) === JSON.stringify(originalClothing)) {
      console.log('未修改任何数据，无需保存');
      closetUtils.showInfoToast('未修改任何内容');
      this.hideEditClothingModal();
      return;
    }

    // 显示加载提示
    closetUtils.showLoading('保存中...');

    // 准备更新数据
    const updateData = {
      clothingId: editingClothing._id,
      name: editingClothing.name,
      category: editingClothing.category,
      type_detail: editingClothing.type_detail,
      color: editingClothing.color,
      style: editingClothing.style,
      season: editingClothing.season,
      price: editingClothing.price ? parseFloat(editingClothing.price) : undefined,
      wornCount: editingClothing.wornCount || 0,
      lastWornDate: editingClothing.lastWornDate,
      purchaseDate: editingClothing.purchaseDate,
      // 添加新字段
      size: editingClothing.size,
      brand: editingClothing.brand,
      purchaseChannel: editingClothing.purchaseChannel,
      storageLocation: editingClothing.storageLocation,
      material: editingClothing.material,
      remark: editingClothing.remark,
      // 添加衣柜ID字段
      wardrobeId: editingClothing.wardrobeId,
      // 添加待办事项
      todos: editingClothing.todos || []
    };

    // 调用云函数更新衣物数据
    wx.cloud.callFunction({
      name: 'updateClothing',
      data: updateData
    })
    .then(res => {
      if (res.result && res.result.success) {
        console.log('更新衣物成功:', res.result);
        closetUtils.hideLoading();
        closetUtils.showSuccessToast('更新成功');

        // 更新本地数据
        this.updateLocalClothing(editingClothing._id, updateData);

        // 如果是从详情页进入的编辑，更新后重新查看详情
        if (this.data.fromDetailView) {
          const updatedClothing = this.data.clothes.find(item => item._id === editingClothing._id);
          if (updatedClothing) {
            this.setData({
              clothesDetail: updatedClothing,
              fromDetailView: false,
              editingFromDetailId: ''
            });
          }
        }

        // 设置标记，提示首页需要刷新数据
        wx.setStorageSync('needRefreshWardrobeSummary', true);
        this.data.needRefreshCloset = true;

        // 关闭编辑弹窗
        this.hideEditClothingModal();

        // 同步更新上一页衣柜页面数据（如果有）
        this.updateClosetPage();
      } else {
        throw new Error(res.result?.message || '更新失败');
      }
    })
    .catch(err => {
      console.error('更新衣物失败:', err);
      closetUtils.hideLoading();
      closetUtils.showErrorToast('更新失败: ' + (err.message || '未知错误'));
    });
  },

  // 更新本地衣物数据
  updateLocalClothing: function(id, updateData) {
    if (!id || !updateData) {
      console.error('参数无效，无法更新本地衣物');
      return;
    }

    // 更新clothes数组中的数据
    const clothes = [...this.data.clothes];
    const index = clothes.findIndex(item => item._id === id);

    if (index !== -1) {
      // 更新找到的衣物
      clothes[index] = {
        ...clothes[index],
        ...updateData
      };

      // 检查是否需要更新筛选后的数据
      const currentSubcategory = this.data.currentSubcategory;
      let filteredClothes = [...this.data.filteredClothes];

      // 如果更新的衣物在筛选后的列表中
      const filteredIndex = filteredClothes.findIndex(item => item._id === id);

      if (filteredIndex !== -1) {
        // 如果细分类变了，且不匹配当前筛选条件，则从筛选结果中移除
        if (currentSubcategory && updateData.type_detail && updateData.type_detail !== currentSubcategory) {
          filteredClothes.splice(filteredIndex, 1);
        } else {
          // 否则更新筛选后的数据
          filteredClothes[filteredIndex] = {
            ...filteredClothes[filteredIndex],
            ...updateData
          };
        }
      } else if (currentSubcategory && updateData.type_detail === currentSubcategory) {
        // 如果更新后的衣物应该被包含在筛选结果中，则添加
        filteredClothes.push({
          ...clothes[index]
        });
      }

      // 更新页面数据
      this.setData({
        clothes: clothes,
        filteredClothes: filteredClothes
      });

      // 如果筛选后的列表为空了，给出提示
      if (filteredClothes.length === 0 && currentSubcategory) {
        closetUtils.showInfoToast(`暂无${currentSubcategory}细分类的${this.data.category}衣物`);
        // 自动清除筛选，显示所有
        this.setData({
          currentSubcategory: '',
          filteredClothes: clothes
        });
      }

      // 清除全局缓存，确保衣柜页面重新加载时能获取最新数据
      try {
        // 清除衣物缓存
        //const cacheKey = `user_clothes_cache_${this.data.userOpenId}`;
        //wx.removeStorageSync(cacheKey);

        // 设置刷新标记
        wx.setStorageSync('needRefreshClothes', true);

        // 更新应用内全局数据
        const app = getApp();
        if (app.globalData && Array.isArray(app.globalData.clothes)) {
          const globalIndex = app.globalData.clothes.findIndex(item => item._id === id);
          if (globalIndex !== -1) {
            app.globalData.clothes[globalIndex] = {
              ...app.globalData.clothes[globalIndex],
              ...updateData
            };
            console.log('已更新全局衣物数据');
          }
        }

        //console.log('已清除衣物缓存:', cacheKey);
      } catch (err) {
        console.error('清除缓存失败:', err);
      }

      console.log('本地衣物数据已更新');
    } else {
      console.error('未找到要更新的本地衣物:', id);
    }
  },

  // 删除当前编辑的衣物
  deleteCurrentClothing: function() {
    const clothing = this.data.editingClothing;
    if (!clothing || !clothing._id) {
      console.error('无效的衣物数据，无法删除');
      closetUtils.showErrorToast('无法删除：无效的衣物数据');
      return;
    }

    const clothingName = clothing.name ||
                        ((clothing.color || '') + ' ' + (clothing.style || '') + (clothing.category || ''));

    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${clothingName}"吗？此操作不可撤销。`,
      confirmColor: '#e74c3c',
      success: res => {
        if (res.confirm) {
          // 关闭编辑弹窗
          this.hideEditClothingModal();

          // 本地删除
          this.removeLocalClothing(clothing._id);

          // 云端删除
          this.deleteClothing(clothing._id);

          // 同步更新上一页衣柜页面数据（如果有）
          this.updateClosetPage();
          this.data.needRefreshCloset = true;

        }
      }
    });
  },

  // 图片加载失败处理
  handleImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const id = e.currentTarget.dataset.id;
    console.log('图片加载失败:', id, index);

    // 设置占位图
    if (id) {
      if (index !== undefined) {
        // 处理网格视图中的图片
        const key = `clothes[${index}].tempImageUrl`;
        this.setData({
          [key]: '/images/clothes_placeholder.png'
        });
      } else if (this.data.clothesDetail && this.data.clothesDetail._id === id) {
        // 处理详情视图中的图片
        this.setData({
          'clothesDetail.tempImageUrl': '/images/clothes_placeholder.png'
        });
      } else if (this.data.showRewardModal) {
        // 处理奖励弹窗中的图片
        this.setData({
          rewardImage: '/images/clothes_placeholder.png'
        });
      }
    } else if (this.data.showRewardModal) {
      // 处理奖励弹窗中没有ID的情况
      this.setData({
        rewardImage: '/images/clothes_placeholder.png'
      });
    }
  },

  // 长按衣物图片处理函数
  onClothesLongPress: function(e) {
    // 如果已经在选择模式，则不处理长按事件
    if (this.data.isSelectionMode) {
      return;
    }

    const clothingId = e.currentTarget.dataset.id;
    const clothingName = e.currentTarget.dataset.name;
    const wornCount = e.currentTarget.dataset.wornCount || 0;

    // 显示操作菜单
    wx.showActionSheet({
      itemList: ['进入批量选择模式', '标记断舍离 (VIP)', '增加穿着次数', '减少穿着次数'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 进入批量选择模式
          this.enterSelectionMode();
          // 选中当前长按的衣物
          this.toggleSelectClothing(clothingId);
        } else if (res.tapIndex === 1) {
          // 首先检查用户是否为VIP会员
          this.checkIsVipMember().then(isVip => {
            if (!isVip) {
              // 非VIP会员，显示提示
              wx.showModal({
                title: 'VIP专属功能',
                content: '断舍离标记是VIP会员专属功能，开通VIP会员即可使用此功能。',
                confirmText: '了解详情',
                cancelText: '取消',
                confirmColor: '#D4AF37',
                success: (res) => {
                  if (res.confirm) {
                    // 跳转到设置页面查看会员权益
                    wx.navigateTo({
                      url: '/page/settings/settings'
                    });
                  }
                }
              });
              return;
            }

            // VIP会员，允许标记断舍离
            const currentWantToDiscard = e.currentTarget.dataset.wantToDiscard || false;

            // 显示确认对话框
            wx.showModal({
              title: '断舍离标记',
              content: currentWantToDiscard ?
                `已将"${clothingName}"置于断舍离区域，是否要取消？` :
                `是否将"${clothingName}"置于断舍离区域？此操作有助于整理出不常穿的衣物。`,
              confirmColor: currentWantToDiscard ? '#07C160' : '#e74c3c',
              success: res => {
                if (res.confirm) {
                  // 更新标记状态
                  this.updateDiscardStatus(clothingId, !currentWantToDiscard);
                }
              }
            });
          }).catch(err => {
            console.error('检查VIP状态失败:', err);
            closetUtils.showErrorToast('系统错误，请稍后再试');
          });
        } else if (res.tapIndex === 2) {
          // 增加穿着次数
          this.updateWornCount(clothingId, clothingName, wornCount, 1);
        } else if (res.tapIndex === 3) {
          // 减少穿着次数
          this.updateWornCount(clothingId, clothingName, wornCount, -1);
        }
      }
    });
  },

  // 更新衣物穿着次数
  updateWornCount: function(clothingId, clothingName, currentCount, increment) {
    if (!clothingId) {
      console.error('无效的衣物ID');
      return;
    }

    // 计算新的穿着次数，确保不小于0
    const newCount = Math.max(0, parseInt(currentCount) + increment);
    const action = increment > 0 ? '增加' : '减少';

    // 如果是减少且当前已经是0，则不执行操作
    if (increment < 0 && currentCount <= 0) {
      wx.showToast({
        title: '穿着次数已经为0',
        icon: 'none'
      });
      return;
    }

    console.log(`${action}衣物穿着次数:`, clothingId, clothingName, '当前:', currentCount, '新值:', newCount);

    // 显示加载提示
    wx.showLoading({
      title: '更新中...',
      mask: true
    });

    // 准备更新数据
    const updateData = {
      clothingId: clothingId,
      wornCount: newCount
    };

    // 如果是增加穿着次数，同时更新最后穿着日期为今天
    if (increment > 0) {
      // 获取今天的日期，格式为YYYY-MM-DD
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      const todayString = `${year}-${month}-${day}`;

      updateData.lastWornDate = todayString;
      console.log('更新最后穿着日期为:', todayString);
    }

    // 调用云函数更新衣物穿着次数和最后穿着日期
    wx.cloud.callFunction({
      name: 'updateClothing',
      data: updateData
    })
    .then(res => {
      console.log('更新穿着次数结果:', res);
      if (res.result && res.result.success) {
        wx.hideLoading();
        wx.showToast({
          title: `穿着次数${action}成功`,
          icon: 'success'
        });

        // 更新本地数据
        this.updateLocalWornCount(clothingId, newCount, increment > 0 ? updateData.lastWornDate : null);
      } else {
        throw new Error(res.result?.message || '更新失败');
      }
    })
    .catch(err => {
      console.error('更新穿着次数失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      });
    });
  },

  // 更新本地衣物穿着次数数据
  updateLocalWornCount: function(clothingId, newCount, lastWornDate) {
    // 更新filteredClothes中的数据
    const filteredClothes = [...this.data.filteredClothes];
    const filteredIndex = filteredClothes.findIndex(item => item._id === clothingId);
    if (filteredIndex >= 0) {
      filteredClothes[filteredIndex].wornCount = newCount;
      // 如果提供了最后穿着日期，也更新它
      if (lastWornDate) {
        filteredClothes[filteredIndex].lastWornDate = lastWornDate;
      }
    }

    // 更新displayedClothes中的数据
    const displayedClothes = [...this.data.displayedClothes];
    const displayedIndex = displayedClothes.findIndex(item => item._id === clothingId);
    if (displayedIndex >= 0) {
      displayedClothes[displayedIndex].wornCount = newCount;
      // 如果提供了最后穿着日期，也更新它
      if (lastWornDate) {
        displayedClothes[displayedIndex].lastWornDate = lastWornDate;
      }
    }

    // 更新clothesDetail中的数据（如果正在查看详情）
    let clothesDetail = this.data.clothesDetail;
    if (clothesDetail && clothesDetail._id === clothingId) {
      clothesDetail = {
        ...clothesDetail,
        wornCount: newCount
      };
      // 如果提供了最后穿着日期，也更新它
      if (lastWornDate) {
        clothesDetail.lastWornDate = lastWornDate;
      }
    }

    // 更新UI
    this.setData({
      filteredClothes,
      displayedClothes,
      clothesDetail
    });

    // 设置刷新标记
    wx.setStorageSync('needRefreshClothes', true);

    // 更新应用内全局数据
    const app = getApp();
    if (app.globalData && Array.isArray(app.globalData.clothes)) {
      const globalIndex = app.globalData.clothes.findIndex(item => item._id === clothingId);
      if (globalIndex !== -1) {
        app.globalData.clothes[globalIndex].wornCount = newCount;
        // 如果提供了最后穿着日期，也更新它
        if (lastWornDate) {
          app.globalData.clothes[globalIndex].lastWornDate = lastWornDate;
        }
        console.log('已更新全局衣物数据');
      }
    }
  },

  // 进入选择模式
  enterSelectionMode: function() {
    // 初始化选中状态对象
    const clothesIsSelected = {};
    if (this.data.filteredClothes && this.data.filteredClothes.length > 0) {
      this.data.filteredClothes.forEach(item => {
        clothesIsSelected[item._id] = false;
      });
    }

    this.setData({
      isSelectionMode: true,
      selectedClothes: [],
      clothesIsSelected: clothesIsSelected,
      selectAll: false
    });
  },

  // 退出选择模式
  exitSelectionMode: function() {
    this.setData({
      isSelectionMode: false,
      selectedClothes: [],
      clothesIsSelected: {},
      selectAll: false
    });
  },

  // 切换选中状态
  toggleSelectClothing: function(clothingId) {
    const selectedClothes = [...this.data.selectedClothes];
    const clothesIsSelected = {...this.data.clothesIsSelected};
    const index = selectedClothes.indexOf(clothingId);

    if (index === -1) {
      // 添加到选中列表
      selectedClothes.push(clothingId);
      clothesIsSelected[clothingId] = true;
    } else {
      // 从选中列表移除
      selectedClothes.splice(index, 1);
      clothesIsSelected[clothingId] = false;
    }

    // 检查是否全选
    const selectAll = selectedClothes.length === this.data.filteredClothes.length;

    this.setData({
      selectedClothes: selectedClothes,
      clothesIsSelected: clothesIsSelected,
      selectAll: selectAll
    });
  },

  // 选择/取消选择单个衣物
  selectClothing: function(e) {
    const clothingId = e.currentTarget.dataset.id;
    this.toggleSelectClothing(clothingId);
  },

  // 全选/取消全选
  toggleSelectAll: function() {
    const currentSelectAll = this.data.selectAll;
    const newSelectAll = !currentSelectAll;

    // 创建新的选中状态对象
    const clothesIsSelected = {};

    if (newSelectAll) {
      // 全选 - 获取所有可见衣物的ID
      const allClothesIds = this.data.filteredClothes.map(item => item._id);

      // 设置所有衣物为选中状态
      this.data.filteredClothes.forEach(item => {
        clothesIsSelected[item._id] = true;
      });

      this.setData({
        selectedClothes: allClothesIds,
        clothesIsSelected: clothesIsSelected,
        selectAll: true
      });
    } else {
      // 取消全选 - 设置所有衣物为非选中状态
      this.data.filteredClothes.forEach(item => {
        clothesIsSelected[item._id] = false;
      });

      this.setData({
        selectedClothes: [],
        clothesIsSelected: clothesIsSelected,
        selectAll: false
      });
    }
  },

  // 显示批量编辑弹窗
  showBatchEditModal: function() {
    if (this.data.selectedClothes.length === 0) {
      wx.showToast({
        title: '请先选择衣物',
        icon: 'none'
      });
      return;
    }

    // 初始化批量编辑数据
    this.setData({
      batchEditData: {
        category: '',
        type_detail: '',
        color: '',
        style: '',
        season: '',
        wardrobeId: '',
        wardrobeName: '',
        brand: '',
        material: '',
        size: '',
        storageLocation: '',
        purchaseChannel: ''
      },
      showBatchEditModal: true
    });
  },

  // 显示类别选择器（批量编辑）
  showBatchCategoryPicker: function() {
    console.log('批量编辑：显示类别选择器');
    // 获取最新的用户衣物类别
    const categories = this.getUserClothesCategories();

    // 使用现有的类别选择器，但标记为批量编辑模式
    this.setData({
      userCategories: categories,
      tempSelectedCategory: this.data.batchEditData.category || '',
      showCategoryPicker: true,
      isBatchEdit: true,
      pickerTitle: '选择类别'
    });
  },

  // 显示季节选择器（批量编辑）
  showBatchSeasonPicker: function() {
    console.log('批量编辑：显示季节选择器');
    let currentSeasons = [];

    // 如果有已选的季节，则解析它
    if (this.data.batchEditData.season) {
      if (typeof this.data.batchEditData.season === 'string') {
        currentSeasons = this.data.batchEditData.season.split('/').filter(s => s);
      } else if (Array.isArray(this.data.batchEditData.season)) {
        currentSeasons = [...this.data.batchEditData.season];
      }
    }

    // 初始化季节选中状态对象
    const seasonIsSelected = {};
    const seasonOptions = ['春季', '夏季', '秋季', '冬季'];
    seasonOptions.forEach(season => {
      seasonIsSelected[season] = currentSeasons.includes(season);
    });

    // 使用现有的季节选择器，但标记为批量编辑模式
    this.setData({
      tempSelectedSeason: currentSeasons,
      seasonIsSelected: seasonIsSelected,
      showSeasonPicker: true,
      isBatchEdit: true
    });
  },

  // 显示衣柜选择器（批量编辑）
  showBatchWardrobePicker: function() {
    console.log('调用批量编辑衣柜选择器');
    // 显示加载提示
    wx.showLoading({
      title: '加载衣柜...',
      mask: true
    });

    // 确保衣柜数据已加载
    this.loadWardrobes()
      .then(wardrobes => {
        console.log('批量编辑模式下加载衣柜成功:', wardrobes.length, '个衣柜');

        let currentWardrobe = this.data.batchEditData.wardrobeId || null;

        const wardrobeIsSelected = {};
        if (wardrobes && wardrobes.length > 0) {
          wardrobes.forEach(wardrobe => {
            wardrobeIsSelected[wardrobe._id] = (currentWardrobe === wardrobe._id);
          });
        }

        // 使用现有的衣柜选择器，但标记为批量编辑模式
        this.setData({
          tempSelectedWardrobe: currentWardrobe,
          wardrobeIsSelected: wardrobeIsSelected,
          showWardrobePicker: true,
          isBatchEdit: true
        });

        // 隐藏加载提示
        wx.hideLoading();
      })
      .catch(err => {
        console.error('加载衣柜失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '加载衣柜失败',
          icon: 'none'
        });
      });
  },

  // 隐藏批量编辑弹窗
  hideBatchEditModal: function() {
    this.setData({
      showBatchEditModal: false,
      batchEditData: {}
    });
  },

  // 批量编辑字段变化处理
  onBatchEditChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    // 更新对应字段
    const batchEditData = {...this.data.batchEditData};
    batchEditData[field] = value;

    this.setData({
      batchEditData: batchEditData
    });
  },

  // 批量更新衣物
  saveBatchEdit: function() {
    const batchEditData = this.data.batchEditData;
    const selectedClothes = this.data.selectedClothes;

    if (selectedClothes.length === 0) {
      wx.showToast({
        title: '请先选择衣物',
        icon: 'none'
      });
      return;
    }

    // 检查是否有字段被修改
    let hasChanges = false;
    for (const key in batchEditData) {
      if (batchEditData[key] !== '') {
        hasChanges = true;
        break;
      }
    }

    if (!hasChanges) {
      wx.showToast({
        title: '请至少修改一个字段',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    closetUtils.showLoading('批量更新中...');

    // 准备更新数据 - 只包含有值的字段
    const updateData = {};
    for (const key in batchEditData) {
      if (batchEditData[key] !== '') {
        updateData[key] = batchEditData[key];
      }
    }

    // 添加衣物ID列表
    updateData.clothingIds = selectedClothes;

    // 调用云函数批量更新
    wx.cloud.callFunction({
      name: 'batchUpdateClothes',
      data: updateData
    })
    .then(res => {
      if (res.result && res.result.success) {
        closetUtils.hideLoading();
        closetUtils.showSuccessToast(`成功更新${res.result.updated}件衣物`);

        // 更新本地数据
        this.updateLocalClothesAfterBatchEdit(selectedClothes, updateData);

        // 关闭批量编辑弹窗
        this.hideBatchEditModal();

        // 退出选择模式
        this.exitSelectionMode();

        // 设置标记，提示首页需要刷新数据
        wx.setStorageSync('needRefreshWardrobeSummary', true);
        this.data.needRefreshCloset = true;

        // 同步更新上一页衣柜页面数据（如果有）
        this.updateClosetPage();
      } else {
        throw new Error(res.result?.message || '批量更新失败');
      }
    })
    .catch(err => {
      console.error('批量更新衣物失败:', err);
      closetUtils.hideLoading();
      closetUtils.showErrorToast('批量更新失败: ' + (err.message || '未知错误'));
    });
  },

  // 批量更新本地衣物数据
  updateLocalClothesAfterBatchEdit: function(clothingIds, updateData) {
    if (!clothingIds || !Array.isArray(clothingIds) || clothingIds.length === 0) {
      return;
    }

    // 创建更新数据对象 - 移除clothingIds字段
    const dataToUpdate = {...updateData};
    delete dataToUpdate.clothingIds;

    // 更新clothes数组中的数据
    const clothes = [...this.data.clothes];
    let hasUpdates = false;

    clothes.forEach(item => {
      if (clothingIds.includes(item._id)) {
        // 更新找到的衣物
        for (const key in dataToUpdate) {
          if (dataToUpdate[key] !== undefined) {
            item[key] = dataToUpdate[key];
            hasUpdates = true;
          }
        }
      }
    });

    if (hasUpdates) {
      // 更新页面数据
      this.setData({
        clothes: clothes
      });

      // 重新应用筛选
      this.filterBySubcategory({
        currentTarget: {
          dataset: {
            subcategory: this.data.currentSubcategory
          }
        }
      });

      // 清除全局缓存，确保衣柜页面重新加载时能获取最新数据
      try {
        // 设置刷新标记
        wx.setStorageSync('needRefreshClothes', true);

        // 更新应用内全局数据
        const app = getApp();
        if (app.globalData && Array.isArray(app.globalData.clothes)) {
          clothingIds.forEach(id => {
            const globalIndex = app.globalData.clothes.findIndex(item => item._id === id);
            if (globalIndex !== -1) {
              for (const key in dataToUpdate) {
                if (dataToUpdate[key] !== undefined) {
                  app.globalData.clothes[globalIndex][key] = dataToUpdate[key];
                }
              }
            }
          });
          console.log('已更新全局衣物数据');
        }
      } catch (err) {
        console.error('清除缓存失败:', err);
      }
    }
  },

  // 显示批量删除确认对话框
  showBatchDeleteConfirm: function() {
    if (this.data.selectedClothes.length === 0) {
      wx.showToast({
        title: '请先选择衣物',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认批量删除',
      content: `确定要删除选中的${this.data.selectedClothes.length}件衣物吗？此操作不可撤销。`,
      confirmColor: '#e74c3c',
      success: res => {
        if (res.confirm) {
          this.batchDeleteClothes();
        }
      }
    });
  },

  // 批量删除衣物
  batchDeleteClothes: function() {
    const selectedClothes = this.data.selectedClothes;

    if (selectedClothes.length === 0) {
      wx.showToast({
        title: '请先选择衣物',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    closetUtils.showLoading('批量删除中...');

    // 调用云函数批量删除
    wx.cloud.callFunction({
      name: 'batchDeleteClothes',
      data: {
        clothingIds: selectedClothes
      }
    })
    .then(res => {
      if (res.result && res.result.success) {
        closetUtils.hideLoading();
        closetUtils.showSuccessToast(`成功删除${res.result.deleted}件衣物`);

        // 从本地数据中移除已删除的衣物
        this.removeLocalClothesAfterBatchDelete(selectedClothes);

        // 退出选择模式
        this.exitSelectionMode();

        // 设置标记，提示首页需要刷新数据
        wx.setStorageSync('needRefreshWardrobeSummary', true);
        wx.setStorageSync('needRefreshClothes', true);
        this.data.needRefreshCloset = true;

        // 更新用户衣物计数
        limitManager.updateClothesCount(this.data.userOpenId, -selectedClothes.length);

        // 同步更新上一页衣柜页面数据（如果有）
        this.updateClosetPage();
      } else {
        throw new Error(res.result?.message || '批量删除失败');
      }
    })
    .catch(err => {
      console.error('批量删除衣物失败:', err);
      closetUtils.hideLoading();
      closetUtils.showErrorToast('批量删除失败: ' + (err.message || '未知错误'));
    });
  },

  // 批量删除后从本地移除衣物
  removeLocalClothesAfterBatchDelete: function(clothingIds) {
    if (!clothingIds || !Array.isArray(clothingIds) || clothingIds.length === 0) {
      return;
    }

    // 从clothes中移除
    const clothes = this.data.clothes.filter(item => !clothingIds.includes(item._id));

    // 从filteredClothes中也移除
    const filteredClothes = this.data.filteredClothes.filter(item => !clothingIds.includes(item._id));

    // 更新页面数据
    this.setData({
      clothes: clothes,
      filteredClothes: filteredClothes
    });

    // 如果删除后列表为空，显示提示
    if (clothes.length === 0) {
      closetUtils.showInfoToast(`暂无${this.data.category}衣物`);
    } else if (filteredClothes.length === 0 && this.data.currentSubcategory) {
      // 如果当前有筛选，且筛选后的列表为空了，则提示用户
      closetUtils.showInfoToast(`暂无${this.data.currentSubcategory}细分类的${this.data.category}衣物`);
      // 自动清除筛选，显示所有
      this.setData({
        currentSubcategory: '',
        filteredClothes: clothes
      });
    }

    // 清除全局缓存，确保衣柜页面重新加载时能获取最新数据
    try {
      // 清除衣物缓存
      const cacheKey = `user_clothes_cache_${this.data.userOpenId}`;
      wx.removeStorageSync(cacheKey);

      // 设置刷新标记
      wx.setStorageSync('needRefreshClothes', true);

      // 更新应用内全局数据
      const app = getApp();
      app.globalData.limitDataNeedRefresh = true;
      if (app.globalData && Array.isArray(app.globalData.clothes)) {
        app.globalData.clothes = app.globalData.clothes.filter(item => !clothingIds.includes(item._id));
        console.log('已更新全局衣物数据');
      }

      console.log('已清除衣物缓存:', cacheKey);
    } catch (err) {
      console.error('清除缓存失败:', err);
    }

    console.log('已从本地移除衣物:', clothingIds);
  },

  // 更新衣物的丢弃状态
  updateDiscardStatus: function(clothingId, wantToDiscard) {
    if (!clothingId) {
      console.error('无效的衣物ID');
      return;
    }

    console.log('更新衣物丢弃状态:', clothingId, wantToDiscard);

    // 首先检查用户是否为VIP会员
    this.checkIsVipMember().then(isVip => {
      if (!isVip) {
        // 非VIP会员，显示提示
        wx.showModal({
          title: 'VIP专属功能',
          content: '断舍离标记是VIP会员专属功能，开通VIP会员即可使用此功能。',
          confirmText: '了解详情',
          cancelText: '取消',
          confirmColor: '#D4AF37',
          success: (res) => {
            if (res.confirm) {
              // 跳转到设置页面查看会员权益
              wx.navigateTo({
                url: '/page/settings/settings'
              });
            }
          }
        });
        return;
      }

      // VIP会员，允许标记断舍离
      closetUtils.showLoading('更新中...');

      // 调用云函数更新衣物状态
      wx.cloud.callFunction({
        name: 'updateClothing',
        data: {
          clothingId: clothingId,
          wantToDiscard: wantToDiscard
        }
      })
      .then(res => {
        console.log('更新状态结果:', res);
        if (res.result && res.result.success) {
          closetUtils.hideLoading();
          closetUtils.showSuccessToast(wantToDiscard ? '已加入断舍离区域' : '已取消断舍离标记');

          // 更新本地数据
          this.updateLocalDiscardStatus(clothingId, wantToDiscard);

          // 同时更新上一页的衣柜页面数据
          const pages = getCurrentPages();
          const closetPage = pages[pages.length - 2]; // 获取上一页面(衣柜页面)
          if (closetPage && closetPage.route.includes('closet') && typeof closetPage.updateLocalDiscardStatus === 'function') {
            closetPage.updateLocalDiscardStatus(clothingId, wantToDiscard);
          }
        } else {
          throw new Error(res.result?.message || '更新失败');
        }
      })
      .catch(err => {
        console.error('更新衣物状态失败:', err);
        closetUtils.hideLoading();
        closetUtils.showErrorToast('更新失败: ' + (err.message || '未知错误'));
      });
    }).catch(err => {
      console.error('检查VIP状态失败:', err);
      closetUtils.showErrorToast('系统错误，请稍后再试');
    });
  },

  // 检查用户是否为VIP会员
  checkIsVipMember: function() {
    return new Promise((resolve, reject) => {
      const db = wx.cloud.database();

      db.collection('users')
        .where({
          _openid: this.data.userOpenId
        })
        .get()
        .then(res => {
          if (res.data && res.data.length > 0) {
            const userInfo = res.data[0];

            // 检查会员类型和过期时间
            const isVip = userInfo.memberType === 'VIP' &&
                        userInfo.memberExpireDate &&
                        new Date(userInfo.memberExpireDate) > new Date();

            resolve(isVip);
          } else {
            resolve(false);
          }
        })
        .catch(err => {
          console.error('获取用户会员信息失败:', err);
          resolve(false); // 出错时默认为非VIP
        });
    });
  },

  // 更新本地衣物的丢弃状态
  updateLocalDiscardStatus: function(clothingId, wantToDiscard) {
    if (!clothingId) {
      console.error('无效的衣物ID：clothingId为空');
      return;
    }

    console.log('类别页面更新本地衣物断舍离状态:', clothingId, wantToDiscard);

    // 更新clothes数组中的数据
    const clothes = Array.isArray(this.data.clothes) ? [...this.data.clothes] : [];
    const index = clothes.findIndex(item => item && item._id === clothingId);

    if (index !== -1) {
      // 更新找到的衣物数据
      clothes[index].wantToDiscard = wantToDiscard;

      // 同步更新filteredClothes中的数据
      const filteredClothes = Array.isArray(this.data.filteredClothes) ? [...this.data.filteredClothes] : [];
      const filteredIndex = filteredClothes.findIndex(item => item && item._id === clothingId);

      if (filteredIndex !== -1) {
        filteredClothes[filteredIndex].wantToDiscard = wantToDiscard;
      }

      // 如果当前是断舍离类别页面且取消了断舍离标记，则从列表中移除该衣物
      if (this.data.category === '断舍离' && !wantToDiscard) {
        console.log('从断舍离列表中移除衣物:', clothingId);
        clothes.splice(index, 1);

        // 同时从filteredClothes移除
        if (filteredIndex !== -1) {
          filteredClothes.splice(filteredIndex, 1);
        }

        // 如果移除后列表为空，显示提示并返回上一页
        if (clothes.length === 0) {
          console.log('断舍离列表现在为空，准备返回上一页');
          wx.showToast({
            title: '暂无断舍离衣物',
            icon: 'none',
            duration: 2000
          });

          // 短暂延迟后返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      }

      // 更新页面数据
      this.setData({
        clothes: clothes,
        filteredClothes: filteredClothes
      });

      // 清除全局缓存，确保衣柜页面重新加载时能获取最新数据
      try {
        // 清除衣物缓存
        const cacheKey = `user_clothes_cache_${this.data.userOpenId}`;
        wx.removeStorageSync(cacheKey);

        // 设置刷新标记
        wx.setStorageSync('needRefreshClothes', true);

        // 更新应用内全局数据
        const app = getApp();
        if (app.globalData && Array.isArray(app.globalData.clothes)) {
          const globalIndex = app.globalData.clothes.findIndex(item => item._id === clothingId);
          if (globalIndex !== -1) {
            app.globalData.clothes[globalIndex].wantToDiscard = wantToDiscard;
            console.log('已更新全局衣物数据');
          }
        }

        console.log('已清除衣物缓存:', cacheKey);
      } catch (err) {
        console.error('清除缓存失败:', err);
      }

      console.log('类别页面本地衣物断舍离状态已更新');

      // 同时更新上一页的衣柜页面数据（确保调用成功）
      try {
        const pages = getCurrentPages();
        const closetPage = pages[pages.length - 2]; // 获取上一页面(衣柜页面)
        if (closetPage && closetPage.route.includes('closet') && typeof closetPage.updateLocalDiscardStatus === 'function') {
          console.log('同步更新衣柜页面的断舍离状态');
          closetPage.updateLocalDiscardStatus(clothingId, wantToDiscard);
        } else {
          console.warn('无法更新衣柜页面:', closetPage ? closetPage.route : '页面不存在');
        }
      } catch (err) {
        console.error('同步更新衣柜页面出错:', err);
      }
    } else {
      console.error('在类别页面中未找到衣物:', clothingId);
    }
  },

  // 获取当前季节
  getCurrentSeason: function() {
    const date = new Date();
    const month = date.getMonth() + 1; // getMonth()返回0-11

    if (month >= 3 && month <= 5) {
      return '春';
    } else if (month >= 6 && month <= 8) {
      return '夏';
    } else if (month >= 9 && month <= 11) {
      return '秋';
    } else {
      return '冬';
    }
  },

  // 筛选当前季节的衣物
  filterCurrentSeason: function() {
    // 切换季节筛选状态
    const newStatus = !this.data.showingCurrentSeason;

    this.setData({
      showingCurrentSeason: newStatus
    });

    // 保存季节筛选状态到本地缓存 - 使用全局键以便跨页面共享状态
    try {
      wx.setStorageSync('globalSeasonFilter', newStatus);
      wx.setStorageSync('currentSeasonFilter', this.data.currentSeason + '季');
      console.log('已将季节筛选状态保存到全局:', newStatus);
    } catch (e) {
      console.error('保存季节筛选状态失败:', e);
    }

    // 如果取消季节筛选，则仅应用细分类筛选
    if (!newStatus) {
      // 应用当前的细分类筛选
      this.filterBySubcategory({
        currentTarget: {
          dataset: {
            subcategory: this.data.currentSubcategory
          }
        }
      });
      return;
    }

    // 获取当前季节的关键字
    const seasonKeyword = this.data.currentSeason + '季';

    // 先获取所有衣物或根据细分类筛选的衣物
    let baseClothes = this.data.clothes;

    // 如果有细分类筛选，先应用细分类筛选
    if (this.data.currentSubcategory) {
      baseClothes = baseClothes.filter(item =>
        item.type_detail === this.data.currentSubcategory
      );
    }

    // 再根据当前季节筛选衣物
    let seasonFiltered = baseClothes.filter(item => {
      if (!item.season) return false;
      return item.season.includes(seasonKeyword);
    });

    // 始终应用排序，即使没有指定排序类型也使用默认的createTime排序
    const sortType = this.data.currentSortType || 'createTime';
    const ascending = sortType === 'createTime' ? false : this.data.sortAscending; // createTime默认降序
    seasonFiltered = sortManager.sortClothes(
      seasonFiltered,
      sortType,
      ascending
    );

    // 更新筛选后的衣物列表
    this.setData({
      filteredClothes: seasonFiltered
    }, () => {
      this.initLoadClothes();
    });

    // 如果筛选后没有衣物，显示提示
    if (seasonFiltered.length === 0) {
      let message = '没有';
      if (this.data.currentSubcategory) message += this.data.currentSubcategory + '细分类的';
      message += this.data.currentSeason + '季的' + this.data.category + '数据';

      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 新增：初始化季节筛选（不改变状态）
  initSeasonFilter: function() {
    // 获取当前季节关键字
    const seasonKeyword = this.data.currentSeason + '季';
    console.log('初始化季节筛选，当前季节:', seasonKeyword);

    // 根据当前筛选条件过滤
    let filteredClothes = this.data.clothes;

    // 如果有细分类筛选，先应用细分类筛选
    if (this.data.currentSubcategory) {
      filteredClothes = filteredClothes.filter(item =>
        item.type_detail === this.data.currentSubcategory
      );
    }

    // 应用季节筛选
    filteredClothes = filteredClothes.filter(item => {
      if (!item.season) return false;
      return item.season.includes(seasonKeyword);
    });

    // 始终应用排序，即使没有指定排序类型也使用默认的createTime排序
    const sortType = this.data.currentSortType || 'createTime';
    const ascending = sortType === 'createTime' ? false : this.data.sortAscending; // createTime默认降序
    filteredClothes = sortManager.sortClothes(
      filteredClothes,
      sortType,
      ascending
    );

    // 更新筛选后的衣物列表
    this.setData({
      filteredClothes: filteredClothes
    }, () => {
      this.initLoadClothes();
    });

    // 如果筛选后没有衣物，显示提示
    if (filteredClothes.length === 0) {
      let message = '没有';
      if (this.data.currentSubcategory) message += this.data.currentSubcategory + '细分类的';
      message += this.data.currentSeason + '季的' + this.data.category + '数据';

      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 滚动事件处理
  onScroll: function(e) {
    // 标记正在滚动
    this.setData({
      isScrolling: true
    });

    // 滚动结束后一段时间恢复滑动功能
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }

    this.scrollTimer = setTimeout(() => {
      this.setData({
        isScrolling: false
      });
    }, this.data.scrollLockTime);
  },

  // 触摸开始事件处理
  touchStart: function(e) {
    // 如果正在滚动，不记录触摸起始位置，防止误触发滑动
    if (this.data.isScrolling) {
      return;
    }

    this.setData({
      touchStartX: e.changedTouches[0].clientX,
      touchStartY: e.changedTouches[0].clientY,  // 记录Y轴起始位置
      touchStartTime: Date.now()  // 记录触摸开始时间
    });
  },

  // 触摸结束事件处理
  touchEnd: function(e) {
    // 如果正在滚动，不处理滑动，防止误触发
    if (this.data.isScrolling || !this.data.touchStartX) {
      return;
    }

    const touchEndX = e.changedTouches[0].clientX;
    const touchEndY = e.changedTouches[0].clientY;
    const touchEndTime = Date.now();

    const {
      touchStartX,
      touchStartY,
      touchStartTime,
      minSwipeDistance,
      maxSwipeTime,
      maxVerticalDistance,
      subcategories,
      currentSubcategory
    } = this.data;

    // 计算水平和垂直滑动距离
    const swipeDistance = touchEndX - touchStartX;
    const verticalDistance = Math.abs(touchEndY - touchStartY);
    const swipeTime = touchEndTime - touchStartTime;

    // 判断是否满足有效滑动条件:
    // 1. 水平滑动距离必须大于最小阈值
    // 2. 滑动时间必须小于最大滑动时间
    // 3. 垂直滑动距离必须小于最大垂直距离
    // 4. 必须有多个细分类可供切换
    if (Math.abs(swipeDistance) >= minSwipeDistance &&
        swipeTime <= maxSwipeTime &&
        verticalDistance <= maxVerticalDistance &&
        subcategories.length > 1) {

      // 查找当前细分类的索引
      const currentIndex = currentSubcategory
        ? subcategories.indexOf(currentSubcategory)
        : -1;

      // 根据滑动方向切换细分类
      if (swipeDistance > 0) {
        // 向右滑动，显示上一个类别
        this.setData({
          categoryBodyAnimation: 'swipe-right-animation'
        });

        let targetIndex;
        if (currentIndex === -1 || currentIndex === 0) {
          // 当前是"全部"或第一个类别，切换到最后一个类别
          targetIndex = subcategories.length - 1;
        } else {
          // 切换到前一个类别
          targetIndex = currentIndex - 1;
        }

        // 更新类别
        setTimeout(() => {
          this.filterBySubcategory({
            currentTarget: {
              dataset: {
                subcategory: subcategories[targetIndex]
              }
            }
          });
        }, 200);
      } else {
        // 向左滑动，显示下一个类别
        this.setData({
          categoryBodyAnimation: 'swipe-left-animation'
        });

        // 如果当前是"全部"，切换到第一个类别
        // 如果当前是最后一个类别，切换到"全部"
        // 否则切换到下一个类别
        let nextSubcategory = '';

        if (currentIndex === -1) {
          // 当前是"全部"，切换到第一个类别
          nextSubcategory = subcategories[0];
        } else if (currentIndex === subcategories.length - 1) {
          // 当前是最后一个类别，切换到"全部"
          nextSubcategory = '';
        } else {
          // 切换到下一个类别
          nextSubcategory = subcategories[currentIndex + 1];
        }

        // 更新类别
        setTimeout(() => {
          this.filterBySubcategory({
            currentTarget: {
              dataset: {
                subcategory: nextSubcategory
              }
            }
          });
        }, 200);
      }
    }

    // 重置动画并更新结束位置
    setTimeout(() => {
      this.setData({
        categoryBodyAnimation: '',
        touchEndX: touchEndX,
        touchEndY: touchEndY
      });
    }, 400);
  },

  // 获取用户所有衣物的类别
  getUserClothesCategories: function() {
    console.log('获取用户所有衣物的类别');

    // 默认类别
    const categoriesSet = new Set(['上衣', '裤子', '裙子', '外套', '鞋子', '配饰']);

    // 从当前页面的衣物数据中提取类别
    if (this.data.clothes && Array.isArray(this.data.clothes)) {
      this.data.clothes.forEach(item => {
        if (item.category && item.category.trim()) {
          categoriesSet.add(item.category.trim());
        }
      });
    }

    // 尝试从缓存中获取更多类别
    try {
      const cacheKey = `user_clothes_cache_${this.data.userOpenId}`;
      const clothesCache = wx.getStorageSync(cacheKey);

      if (clothesCache) {
        // 处理不同的缓存结构
        const clothesArray = clothesCache.clothes || clothesCache;

        if (Array.isArray(clothesArray)) {
          clothesArray.forEach(item => {
            if (item.category && item.category.trim()) {
              categoriesSet.add(item.category.trim());
            }
          });
        }
      }
    } catch (err) {
      console.error('从缓存获取类别失败:', err);
    }

    // 将Set转换为数组
    const categoriesArray = Array.from(categoriesSet);
    console.log('用户衣物类别列表:', categoriesArray);

    // 更新有效类别列表
    this.setData({
      validCategories: categoriesArray
    });

    return categoriesArray;
  },

  // 加载衣柜列表
  loadWardrobes: function() {
    return new Promise((resolve, reject) => {
      const userOpenId = this.data.userOpenId;
      if (!userOpenId) {
        console.error('无法获取用户OpenID，无法加载衣柜列表');
        reject(new Error('无法获取用户OpenID'));
        return;
      }

      const WARDROBES_CACHE_KEY = 'user_wardrobes_';
      const cacheKey = WARDROBES_CACHE_KEY + userOpenId;

      // 尝试从缓存获取
      try {
        const cachedWardrobes = wx.getStorageSync(cacheKey);
        if (cachedWardrobes && cachedWardrobes.length > 0) {
          this.setData({
            wardrobes: cachedWardrobes
          });
          console.log('从缓存加载衣柜列表成功:', cachedWardrobes.length, '个衣柜');
          resolve(cachedWardrobes);
          return;
        }
      } catch (e) {
        console.error('读取衣柜缓存失败:', e);
      }

      // 如果缓存不存在，从数据库获取
      console.log('从数据库加载衣柜列表...');
      const db = wx.cloud.database();
      db.collection('wardrobes')
        .where({
          _openid: userOpenId
        })
        .get()
        .then(res => {
          if (res.data && res.data.length > 0) {
            this.setData({
              wardrobes: res.data
            });

            // 保存到缓存
            try {
              wx.setStorageSync(cacheKey, res.data);
            } catch (e) {
              console.error('缓存衣柜列表失败:', e);
            }

            console.log('从数据库加载衣柜列表成功:', res.data.length, '个衣柜');
            resolve(res.data);
          } else {
            console.log('没有找到衣柜数据');
            this.setData({
              wardrobes: []
            });
            resolve([]);
          }
        })
        .catch(err => {
          console.error('获取衣柜列表失败:', err);
          reject(err);
        });
    });
  },

  // 显示衣柜选择器
  showWardrobePicker: function() {
    // 显示加载提示
    wx.showLoading({
      title: '加载衣柜...',
      mask: true
    });

    // 确保衣柜数据已加载
    this.loadWardrobes()
      .then(wardrobes => {
        console.log('单个衣物编辑模式下加载衣柜成功:', wardrobes.length, '个衣柜');

        let currentWardrobe = null;

        if (this.data.editingClothing && this.data.editingClothing.wardrobeId) {
          currentWardrobe = this.data.editingClothing.wardrobeId;
        }

        const wardrobeIsSelected = {};
        if (wardrobes && wardrobes.length > 0) {
          wardrobes.forEach(wardrobe => {
            wardrobeIsSelected[wardrobe._id] = (currentWardrobe === wardrobe._id);
          });
        }

        this.setData({
          tempSelectedWardrobe: currentWardrobe,
          wardrobeIsSelected: wardrobeIsSelected,
          showWardrobePicker: true,
          isBatchEdit: false
        });

        // 隐藏加载提示
        wx.hideLoading();
      })
      .catch(err => {
        console.error('加载衣柜失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '加载衣柜失败',
          icon: 'none'
        });
      });
  },

  // 隐藏衣柜选择器
  hideWardrobePicker: function() {
    this.setData({
      showWardrobePicker: false
    });
  },

  // 选择衣柜
  selectWardrobe: function(e) {
    const wardrobeId = e.currentTarget.dataset.wardrobeid;
    console.log('选择衣柜:', wardrobeId, ', isBatchEdit =', this.data.isBatchEdit);

    // 只允许选择一个衣柜，更新所有选择状态
    const wardrobeIsSelected = {};
    this.data.wardrobes.forEach(wardrobe => {
      wardrobeIsSelected[wardrobe._id] = (wardrobe._id === wardrobeId);
      if (wardrobe._id === wardrobeId) {
        console.log('选中衣柜:', wardrobe.name);
      }
    });

    this.setData({
      tempSelectedWardrobe: wardrobeId,
      wardrobeIsSelected: wardrobeIsSelected
    });
  },

  // 确认衣柜选择
  confirmWardrobePicker: function() {
    console.log('确认衣柜选择, isBatchEdit =', this.data.isBatchEdit);

    // 找到选中的衣柜名称以显示
    let wardrobeName = '';
    let wardrobeId = this.data.tempSelectedWardrobe;

    if (wardrobeId) {
      const selectedWardrobe = this.data.wardrobes.find(w => w._id === wardrobeId);
      if (selectedWardrobe) {
        wardrobeName = selectedWardrobe.name;
        console.log('选中衣柜:', wardrobeName, '(ID:', wardrobeId, ')');
      }
    }

    // 检查是否是批量编辑模式
    if (this.data.isBatchEdit) {
      console.log('批量编辑模式: 更新衣柜为', wardrobeName);
      this.setData({
        'batchEditData.wardrobeId': wardrobeId,
        'batchEditData.wardrobeName': wardrobeName,
        showWardrobePicker: false,
        isBatchEdit: false
      });
      return;
    }

    // 单个衣物编辑模式
    console.log('单个衣物编辑模式: 更新衣柜为', wardrobeName);
    this.setData({
      'editingClothing.wardrobeId': wardrobeId,
      'editingClothing.wardrobeName': wardrobeName,
      showWardrobePicker: false
    });
  },

  // 显示排序选择器
  showSortPicker: function() {
    this.setData({
      showSortPicker: true
    });
  },

  // 隐藏排序选择器
  hideSortPicker: function() {
    this.setData({
      showSortPicker: false
    });
  },

  // 选择排序类型
  selectSortType: function(e) {
    const sortType = e.currentTarget.dataset.sortType;
    console.log('选择排序类型:', sortType);

    // 如果选择的是当前已选类型，则不做任何操作
    if (sortType === this.data.currentSortType) {
      return;
    }

    // 更新排序类型
    this.setData({
      currentSortType: sortType,
      // 重置排序方向（新选择的类型默认升序排列）
      sortAscending: true
    }, () => {
      // 应用排序
      this.applySorting();

      // 隐藏选择器
      this.hideSortPicker();
    });
  },

  // 切换排序方向
  toggleSortDirection: function(e) {
    const direction = e.currentTarget.dataset.direction;
    const ascending = direction === 'asc';

    // 如果当前已经是指定方向，则不做改变
    if (this.data.sortAscending === ascending) {
      return;
    }

    // 更新排序方向
    this.setData({
      sortAscending: ascending
    }, () => {
      // 应用排序
      this.applySorting();
    });

    // 阻止冒泡，防止触发selectSortType
    // e.stopPropagation(); // 移除这行代码，catchtap已经会阻止冒泡
  },

  // 应用排序
  applySorting: function() {
    const { filteredClothes, currentSortType, sortAscending } = this.data;

    // 始终使用排序，即使没有指定排序类型也使用默认的createTime排序
    const sortType = currentSortType || 'createTime';
    const ascending = sortType === 'createTime' ? false : sortAscending; // createTime默认降序

    console.log('应用排序:', sortType, ascending ? '升序' : '降序');

    // 使用sortManager进行排序
    const sortedClothes = sortManager.sortClothes(filteredClothes, sortType, ascending);

    // 更新排序后的衣物列表
    this.setData({
      filteredClothes: sortedClothes
    }, () => {
      // 重新初始化加载，以显示排序后的数据
      this.initLoadClothes();
    });
  },

  // 显示待办事项弹窗
  showTodoModal: function(e) {
    let index = -1;
    if (e && e.currentTarget) {
      index = e.currentTarget.dataset.index;
    }

    // 根据index设置当前待办事项
    if (index >= 0 && index < this.data.editingClothing.todos.length) {
      // 编辑已有待办事项
      const todo = this.data.editingClothing.todos[index];
      this.setData({
        currentTodo: {
          title: todo.title || '',
          dueDate: todo.dueDate || '',
          completed: todo.completed || false
        },
        editingTodoIndex: index,
        showTodoModal: true
      });
    } else {
      // 新增待办事项
      this.setData({
        currentTodo: {
          title: '',
          dueDate: '',
          completed: false
        },
        editingTodoIndex: -1,
        showTodoModal: true
      });
    }
  },

  // 页面卸载时清除缓存
  onUnload: function() {
    console.log('类别页面卸载，清除衣物缓存');

    // 清除衣物缓存
    if(this.data.needRefreshCloset){
      const cacheKey = `user_clothes_cache_${this.data.userOpenId}`;
      wx.removeStorageSync(cacheKey);

      // 设置刷新标记
      wx.setStorageSync('needRefreshClothes', true);
      console.log('已清除衣物缓存:', cacheKey);
    }

    // 通知前一个页面（衣柜页面）类别页面已关闭
    try {
      const pages = getCurrentPages();
      if (pages.length >= 2) {
        const prevPage = pages[pages.length - 2]; // 获取上一个页面
        const eventChannel = this.getOpenerEventChannel();
        if (eventChannel) {
          console.log('发送类别页面关闭事件');
          eventChannel.emit('categoryPageClosed', {});
        }
      }
    } catch (error) {
      console.error('发送页面关闭事件失败:', error);
    }
  },


  // 隐藏待办事项弹窗
  hideTodoModal: function() {
    this.setData({
      showTodoModal: false,
      currentTodo: {
        title: '',
        dueDate: '',
        completed: false
      },
      editingTodoIndex: -1
    });
  },

  // 保存待办事项
  saveTodo: function() {
    const { currentTodo, editingTodoIndex } = this.data;

    // 验证标题不能为空
    if (!currentTodo.title.trim()) {
      wx.showToast({
        title: '请输入待办事项',
        icon: 'none'
      });
      return;
    }

    // 获取当前的todos数组
    const todos = this.data.editingClothing.todos || [];

    if (editingTodoIndex >= 0) {
      // 更新现有待办事项
      todos[editingTodoIndex] = { ...currentTodo };
    } else {
      // 添加新的待办事项
      todos.push({ ...currentTodo });
    }

    // 更新数据
    this.setData({
      'editingClothing.todos': todos,
      showTodoModal: false
    });
    wx.setStorageSync('todoClothesneedRefresh', true);
    wx.showToast({
      title: editingTodoIndex >= 0 ? '待办事项已更新' : '待办事项已添加',
      icon: 'success'
    });
  },

  // 删除待办事项
  deleteTodo: function(e) {
    const index = e.currentTarget.dataset.index;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个待办事项吗？',
      success: res => {
        if (res.confirm) {
          // 获取当前的todos数组
          const todos = this.data.editingClothing.todos || [];

          // 移除指定索引的待办事项
          todos.splice(index, 1);

          // 更新数据
          this.setData({
            'editingClothing.todos': todos
          });
          wx.setStorageSync('todoClothesneedRefresh', true);
          wx.showToast({
            title: '待办事项已删除',
            icon: 'success'
          });
        }
      }
    });
  },

  // 切换待办事项的完成状态
  toggleTodoComplete: function(e) {
    const index = e.currentTarget.dataset.index;

    // 获取当前的todos数组
    const todos = this.data.editingClothing.todos || [];

    if (index >= 0 && index < todos.length) {
      // 切换完成状态
      todos[index].completed = !todos[index].completed;

      // 更新数据
      this.setData({
        'editingClothing.todos': todos
      });
      wx.setStorageSync('todoClothesneedRefresh', true);
    }
  },

  // 编辑待办事项的日期
  onTodoDateChange: function(e) {
    this.setData({
      'currentTodo.dueDate': e.detail.value
    });
  },

  // 编辑待办事项的标题
  onTodoTitleInput: function(e) {
    this.setData({
      'currentTodo.title': e.detail.value
    });
  },

  // 处理图片卡片点击，用于预览图片
  handleImageCardClick: function() {
    if (!this.data.clothesDetail) return;

    const imageUrl = this.data.clothesDetail.tempImageUrl || this.data.clothesDetail.imageFileID;
    if (!imageUrl) {
      wx.showToast({
        title: '无法预览图片',
        icon: 'none'
      });
      return;
    }

    // 预览图片
    wx.previewImage({
      urls: [imageUrl],
      current: imageUrl
    });
  },

  // 处理图片卡片长按，用于更新衣物图片
  handleImageCardLongPress: function() {
    if (!this.data.clothesDetail) return;

    const that = this;
    const clothingId = this.data.clothesDetail._id;

    wx.showActionSheet({
      itemList: ['编辑图片', '重新上传照片', '重新上传照片并抠图'],
      success: function(res) {
        if (res.tapIndex === 0) {
          // 编辑图片
          that.openImageEditor();
        } else if (res.tapIndex === 1 || res.tapIndex === 2) {
          // 是否需要抠图
          const needKoutu = res.tapIndex === 2;

          // 选择图片
          wx.chooseMedia({
            count: 1,
            mediaType: ['image'],
            sourceType: ['album', 'camera'],
            sizeType: ['original'],
            success: function(res) {
              const tempFilePath = res.tempFiles[0].tempFilePath;
              that.setData({
                needRefreshCloset: true
              });
              wx.showLoading({
                title: needKoutu ? '上传并抠图中...' : '上传中...',
                mask: true
              });

              // 上传图片到云存储
              imageProcessor.uploadImageToCloud(tempFilePath)
                .then(fileID => {
                  console.log('上传成功，fileID:', fileID);

                  // 获取临时URL
                  return imageProcessor.getTempFileURL(fileID)
                    .then(tempFileURL => {
                      // 如果需要抠图，先进行抠图处理
                      if (needKoutu) {
                        console.log('开始进行抠图处理');
                        return imageProcessor.processImageWithCozeAPI(tempFileURL)
                          .then(processedImageUrl => {
                            console.log('抠图处理成功，URL:', processedImageUrl);
                            // 保存抠图后的图片到云存储
                            return imageProcessor.saveProcessedImageToCloud(processedImageUrl)
                              .then(processedImageData => {
                                console.log('抠图后图片保存成功:', processedImageData);
                                // 更新数据库中的文件ID为抠图后的图片
                                return wx.cloud.callFunction({
                                  name: 'updateClothesImage',
                                  data: {
                                    clothingId: clothingId,
                                    imageFileID: processedImageData.fileID
                                  }
                                }).then(res => {
                                  console.log('更新数据库成功:', res);
                                  return {
                                    fileID: processedImageData.fileID,
                                    tempFileURL: processedImageData.tempImageUrl
                                  };
                                });
                              });
                          })
                          .catch(err => {
                            console.error('抠图处理失败:', err);
                            wx.hideLoading();
                            wx.showToast({
                              title: '抠图失败，使用原图',
                              icon: 'none',
                              duration: 2000
                            });

                            // 抠图失败时，使用原图
                            return wx.cloud.callFunction({
                              name: 'updateClothesImage',
                              data: {
                                clothingId: clothingId,
                                imageFileID: fileID
                              }
                            }).then(res => {
                              console.log('更新数据库成功(使用原图):', res);
                              return {fileID, tempFileURL};
                            });
                          });
                      } else {
                        // 不需要抠图，直接更新数据库
                        return wx.cloud.callFunction({
                          name: 'updateClothesImage',
                          data: {
                            clothingId: clothingId,
                            imageFileID: fileID
                          }
                        }).then(res => {
                          console.log('更新数据库成功:', res);
                          return {fileID, tempFileURL};
                        });
                      }
                    });
                })
                .then(({fileID, tempFileURL}) => {
                  // 创建本地文件名
                  const localFileName = `img_tmp${Math.floor(Math.random() * 1000)}.jpg`;
                  const localPath = `clothes_images/${localFileName}`;

                  // 确保clothes_images目录存在
                  const fs = wx.getFileSystemManager();

                  try {
                    fs.accessSync(`${wx.env.USER_DATA_PATH}/clothes_images`);
                  } catch (e) {
                    fs.mkdirSync(`${wx.env.USER_DATA_PATH}/clothes_images`, true);
                  }

                  // 下载图片并保存到本地
                  wx.downloadFile({
                    url: tempFileURL,
                    success: function(res) {
                      if (res.statusCode === 200) {
                        const tempFilePath = res.tempFilePath;

                        fs.saveFile({
                          tempFilePath: tempFilePath,
                          filePath: `${wx.env.USER_DATA_PATH}/${localPath}`,
                          success: function(saveRes) {
                            console.log('图片保存成功:', saveRes);

                            // 更新本地缓存
                            let cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};
                            cachedURLInfo[fileID] = {
                              localPath: localPath,
                              timestamp: Date.now()
                            };
                            wx.setStorageSync(URL_CACHE_KEY, cachedURLInfo);

                            // 设置图片更新标记，通知其他页面需要刷新图片
                            wx.setStorageSync('needRefreshCloset', true);
                            // 记录当前修改的衣物ID，便于精确更新
                            let updatedImageIds = wx.getStorageSync('updatedImageIds') || [];
                            if (!updatedImageIds.includes(clothingId)) {
                              updatedImageIds.push(clothingId);
                              wx.setStorageSync('updatedImageIds', updatedImageIds);
                            }

                            // 确保在返回页面时使用本地缓存的文件
                            const localFilePath = `${wx.env.USER_DATA_PATH}/${localPath}`;

                            // 清除云端图片缓存，保证下次请求时获取最新图片
                            wx.cloud.callFunction({
                              name: 'getTempFileURL',
                              data: {
                                fileIdList: [fileID],
                                clearCache: true
                              }
                            }).catch(err => console.error('清除云端缓存失败:', err));

                            // 更新界面显示
                            that.setData({
                              'clothesDetail.imageFileID': fileID,
                              'clothesDetail.tempImageUrl': localFilePath
                            });

                            // 同时更新clothes数组中的对应项
                            const clothes = that.data.clothes;
                            const index = clothes.findIndex(item => item._id === clothingId);
                            if (index !== -1) {
                              const key = `clothes[${index}].imageFileID`;
                              const tempUrlKey = `clothes[${index}].tempImageUrl`;
                              const updateData = {};
                              updateData[key] = fileID;
                              updateData[tempUrlKey] = localFilePath;
                              that.setData(updateData);

                              // 重新筛选和显示
                              that.filterBySubcategory({
                                currentTarget: {
                                  dataset: {
                                    subcategory: that.data.currentSubcategory || ''
                                  }
                                }
                              });
                            }

                            wx.hideLoading();
                            wx.showToast({
                              title: '图片更新成功',
                              icon: 'success'
                            });

                          },
                          fail: function(err) {
                            console.error('保存图片失败:', err);
                            wx.hideLoading();
                            wx.showToast({
                              title: '保存图片失败',
                              icon: 'error'
                            });
                          }
                        });
                      }
                    },
                    fail: function(err) {
                      console.error('下载图片失败:', err);
                      wx.hideLoading();
                      wx.showToast({
                        title: '下载图片失败',
                        icon: 'error'
                      });
                    }
                  });
                })
                .catch(err => {
                  console.error('上传或更新失败:', err);
                  wx.hideLoading();
                  wx.showToast({
                    title: '上传失败',
                    icon: 'error'
                  });
                });
            }
          });
        }
      }
    });
  },

  // 更新导航栏样式
  updateNavBarStyle: function(themeName) {
    // 获取当前主题的导航栏数据
    const navData = this.navBarData[themeName];
    if (!navData) {
      console.error('未找到主题对应的导航栏数据:', themeName);
      return;
    }

    // 更新导航栏数据
    this.setData({
      navBackground: navData.background,
      navFrontColor: navData.color,
      navTitle: navData.title
    });
  },

  // 切换主题
  switchTheme: function(e) {
    const themeName = e.currentTarget.dataset.theme;
    console.log('切换主题为:', themeName);

    if (themeName === this.data.themeStyle) {
      console.log('当前已经是', themeName, '主题，无需切换');
      return;
    }

    // 更新主题样式
    this.setData({
      themeStyle: themeName
    });

    // 更新导航栏样式
    this.updateNavBarStyle(themeName);

    // 保存主题设置到本地存储
    wx.setStorageSync('themeStyle', themeName);

    // 应用主题样式到TabBar
    if (themeName === 'autumn') {
      // 设置秋季主题TabBar
      wx.setTabBarStyle({
        backgroundColor: '#E8D1A7',
        borderStyle: 'black',
        color: '#442D1C',
        selectedColor: '#74301C'
      });
    } else if (themeName === 'pinkBlue') {
      // 设置粉蓝主题TabBar
      wx.setTabBarStyle({
        backgroundColor: '#F9C9D6',
        borderStyle: 'black',
        color: '#5EA0D0',
        selectedColor: '#D47C99'
      });
    } else if (themeName === 'blackWhite') {
      // 设置黑白主题TabBar
      wx.setTabBarStyle({
        backgroundColor: '#FFFFFF',
        borderStyle: 'black',
        color: '#666666',
        selectedColor: '#000000'
      });
    }

    // 显示切换成功提示
    wx.showToast({
      title: '主题切换成功',
      icon: 'success',
      duration: 1500
    });
  },

  // 打开图片编辑器
  openImageEditor: function() {
    if (!this.data.clothesDetail) return;

    const imageUrl = this.data.clothesDetail.tempImageUrl || this.data.clothesDetail.imageFileID;
    if (!imageUrl) {
      wx.showToast({
        title: '无法获取图片',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showImageEditor: true,
      editingImageUrl: imageUrl
    });
  },

  // 关闭图片编辑器
  closeImageEditor: function() {
    this.setData({
      showImageEditor: false,
      editingImageUrl: ''
    });
  },

  // 图片编辑完成
  onImageEditConfirm: function(e) {
    const { tempFilePath } = e.detail;
    if (!tempFilePath) return;

    const that = this;
    const clothingId = this.data.clothesDetail._id;

    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 上传编辑后的图片到云存储
    imageProcessor.uploadImageToCloud(tempFilePath)
      .then(fileID => {
        console.log('编辑后图片上传成功，fileID:', fileID);

        // 获取临时URL
        return imageProcessor.getTempFileURL(fileID)
          .then(tempFileURL => {
            // 更新数据库
            return wx.cloud.callFunction({
              name: 'updateClothesImage',
              data: {
                clothingId: clothingId,
                imageFileID: fileID
              }
            }).then(res => {
              console.log('更新数据库成功:', res);
              return { fileID, tempFileURL };
            });
          });
      })
      .then(({ fileID, tempFileURL }) => {
        // 创建本地文件名
        const localFileName = `img_edited_${Math.floor(Math.random() * 1000)}.jpg`;
        const localPath = `clothes_images/${localFileName}`;

        // 确保clothes_images目录存在
        const fs = wx.getFileSystemManager();
        try {
          fs.accessSync(`${wx.env.USER_DATA_PATH}/clothes_images`);
        } catch (e) {
          fs.mkdirSync(`${wx.env.USER_DATA_PATH}/clothes_images`, true);
        }

        // 下载图片并保存到本地
        wx.downloadFile({
          url: tempFileURL,
          success: function(res) {
            if (res.statusCode === 200) {
              const tempFilePath = res.tempFilePath;

              fs.saveFile({
                tempFilePath: tempFilePath,
                filePath: `${wx.env.USER_DATA_PATH}/${localPath}`,
                success: function(saveRes) {
                  console.log('编辑后图片保存成功:', saveRes);

                  // 更新本地缓存
                  let cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};
                  cachedURLInfo[fileID] = {
                    localPath: localPath,
                    timestamp: Date.now()
                  };
                  wx.setStorageSync(URL_CACHE_KEY, cachedURLInfo);

                  // 设置图片更新标记
                  wx.setStorageSync('needRefreshCloset', true);
                  let updatedImageIds = wx.getStorageSync('updatedImageIds') || [];
                  if (!updatedImageIds.includes(clothingId)) {
                    updatedImageIds.push(clothingId);
                    wx.setStorageSync('updatedImageIds', updatedImageIds);
                  }

                  const localFilePath = `${wx.env.USER_DATA_PATH}/${localPath}`;

                  // 更新界面显示
                  that.setData({
                    'clothesDetail.imageFileID': fileID,
                    'clothesDetail.tempImageUrl': localFilePath,
                    showImageEditor: false,
                    editingImageUrl: ''
                  });

                  // 同时更新clothes数组中的对应项
                  const clothes = that.data.clothes;
                  const index = clothes.findIndex(item => item._id === clothingId);
                  if (index !== -1) {
                    const key = `clothes[${index}].imageFileID`;
                    const tempUrlKey = `clothes[${index}].tempImageUrl`;
                    const updateData = {};
                    updateData[key] = fileID;
                    updateData[tempUrlKey] = localFilePath;
                    that.setData(updateData);

                    // 重新筛选和显示
                    that.filterBySubcategory({
                      currentTarget: {
                        dataset: {
                          subcategory: that.data.currentSubcategory || ''
                        }
                      }
                    });
                  }

                  wx.hideLoading();
                  wx.showToast({
                    title: '图片编辑成功',
                    icon: 'success'
                  });
                },
                fail: function(err) {
                  console.error('保存编辑后图片失败:', err);
                  wx.hideLoading();
                  wx.showToast({
                    title: '保存失败',
                    icon: 'error'
                  });
                }
              });
            }
          },
          fail: function(err) {
            console.error('下载编辑后图片失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '下载失败',
              icon: 'error'
            });
          }
        });
      })
      .catch(err => {
        console.error('编辑图片保存失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        });
      });
  },

  // 图片编辑取消
  onImageEditCancel: function() {
    this.closeImageEditor();
  },
});