<view class="container" style="background-color: {{themeStyle === 'autumn' ? colors.goldenBatter : pinkBlueColors.pinkLight}};">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner" style="border-top-color: {{colors.darkBrown}}; border-color: {{colors.goldenBatter}};"></view>
    <view class="loading-text" style="color: {{colors.darkBrown}};">加载中...</view>
  </view>

  <!-- 主内容区域 -->
  <view class="main-content" wx:if="{{!isLoading}}">
    <!-- 顶部操作栏 -->
    <view class="toolbar" style="background-color: {{themeStyle === 'autumn' ? colors.toastedCaramel : pinkBlueColors.pinkMedium}};">
      <view class="back-button" bindtap="goBack">
        <view class="back-icon" style="border-right-color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};"></view>
        <text style="color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};">返回</text>
      </view>
      
      <input class="outfit-name-input" 
        value="{{outfitName}}" 
        bindinput="updateOutfitName" 
        maxlength="20" 
        placeholder="搭配名称" 
        style="color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}}; border-color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};"
      />
      
      <!-- 穿搭类型选择器 -->
      <view class="category-selector" bindtap="toggleCategoryPicker" style="color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}}; border-color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};">
        <view class="selected-category">
          <block wx:if="{{outfitCategory === 'daily'}}">
            <text class="category-icon">👕</text>
            <text class="category-name">日常穿搭</text>
          </block>
          <block wx:elif="{{outfitCategory === 'work'}}">
            <text class="category-icon">👔</text>
            <text class="category-name">职业穿搭</text>
          </block>
          <block wx:elif="{{outfitCategory === 'party'}}">
            <text class="category-icon">👗</text>
            <text class="category-name">派对穿搭</text>
          </block>
          <block wx:elif="{{outfitCategory === 'sport'}}">
            <text class="category-icon">🏃</text>
            <text class="category-name">运动穿搭</text>
          </block>
          <block wx:elif="{{outfitCategory === 'seasonal'}}">
            <text class="category-icon">🍂</text>
            <text class="category-name">季节穿搭</text>
          </block>
          <view class="dropdown-arrow" style="border-top-color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};"></view>
        </view>
      </view>
      
      <!-- 类型选择下拉菜单 -->
      <view class="category-dropdown" wx:if="{{showCategoryPicker}}" style="background-color: {{themeStyle === 'autumn' ? colors.toastedCaramel : pinkBlueColors.pinkMedium}};">
        <view 
          wx:for="{{outfitCategoryOptions}}" 
          wx:key="value" 
          class="category-option {{outfitCategory === item.value ? 'active' : ''}}" 
          bindtap="selectCategory" 
          data-category="{{item.value}}"
          style="color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}}; background-color: {{outfitCategory === item.value ? (themeStyle === 'autumn' ? colors.spicedWine : pinkBlueColors.pinkDark) : 'transparent'}};"
        >
          <text class="option-icon">{{item.icon}}</text>
          <text class="option-name">{{item.name}}</text>
        </view>
      </view>
      
      <view class="save-button" bindtap="saveOutfit" style="background-color: {{themeStyle === 'autumn' ? colors.darkBrown : pinkBlueColors.blueDark}};">
        <text style="color: {{themeStyle === 'autumn' ? colors.goldenBatter : 'white'}};">保存</text>
      </view>
    </view>
    
    <!-- 遮罩层 - 点击空白区域关闭类型选择器 -->
    <view class="mask" wx:if="{{showCategoryPicker}}" bindtap="toggleCategoryPicker"></view>
    
    <!-- 中间内容区域：画布和衣柜 -->
    <view class="content-section">
      <!-- 左侧衣柜 -->
      <view class="closet-panel" style="background-color: {{themeStyle === 'autumn' ? colors.oliveHarvest : pinkBlueColors.blueDark}};">
        <!-- 类别筛选 -->
        <view class="category-filter">
          <scroll-view scroll-x="true" class="categories-scroll" enhanced="true" show-scrollbar="false">
            <view 
              wx:for="{{categories}}" 
              wx:key="id" 
              class="category-item {{currentCategory === (item.category || null) ? 'active' : ''}}" 
              bindtap="filterByCategory" 
              data-category="{{item.category}}"
              style="background-color: {{currentCategory === (item.category || null) ? (themeStyle === 'autumn' ? colors.spicedWine : pinkBlueColors.pinkDark) : 'transparent'}}; color: {{colors.goldenBatter}};"
            >
              <view class="category-icon">{{item.icon}}</view>
              <view class="category-text">
                <text>{{item.name}}</text>
                <text class="category-count">{{item.count}}</text>
              </view>
            </view>
          </scroll-view>
        </view>
        
        <!-- 衣物列表 -->
        <scroll-view scroll-y="true" class="clothes-scroll" enhanced="true">
          <view class="clothes-grid">
            <block wx:for="{{filteredClothes}}" wx:key="_id">
              <view 
                class="clothes-item" 
                bindtap="addToCanvas" 
                data-id="{{item._id}}"
                hover-class="clothes-item-hover"
                style="background-color: {{themeStyle === 'autumn' ? 'rgba(232, 209, 167, 0.2)' : 'rgba(255, 255, 255, 0.2)'}};"
              >
                <image 
                  src="{{item.tempImageUrl || item.processedImageUrl}}" 
                  mode="aspectFit" 
                  class="clothes-image"
                  lazy-load="true"
                />
                <view class="clothes-name" style="color: {{colors.goldenBatter}};">{{item.name || item.type || item.category}}</view>
              </view>
            </block>
            
            <view class="empty-hint" wx:if="{{filteredClothes.length === 0}}">
              <text style="color: {{colors.goldenBatter}};">没有相关衣物</text>
              <text style="color: {{colors.goldenBatter}}; font-size: 24rpx; margin-top: 20rpx;">请尝试选择其他类别</text>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 右侧画布 -->
      <view class="canvas-panel" bindtap="canvasTap">
        <view class="canvas-container" style="width: {{canvasWidth}}px; height: {{canvasHeight}}px; background-color: white;">
          <!-- 衣物画布项 -->
          <block wx:for="{{canvasItems}}" wx:key="id">
            <!-- 画布项目本体 -->
            <view 
              class="canvas-item {{item.active ? 'active' : ''}}" 
              style="left: {{item.x}}px; top: {{item.y}}px; width: {{item.width}}px; height: {{item.height}}px; z-index: {{item.layer || 0}}; transform: rotate({{item.rotation || 0}}deg);" 
              data-id="{{item.id}}"
              bindtap="itemTap"
              bindtouchstart="itemTouchStart"
              bindtouchmove="itemTouchMove"
              bindtouchend="itemTouchEnd"
            >
              <image src="{{item.imageUrl}}" mode="aspectFit" class="item-image" />
              
              <!-- 图层信息，仅在项目被选中时显示 -->
              <view class="layer-info" wx:if="{{item.active}}">
                <text>图层: {{item.layer || 0}}</text>
              </view>
            </view>
            
            <!-- 控制按钮，单独放置，不作为画布项目的子元素 -->
            <block wx:if="{{item.active}}">
              <!-- 删除按钮 -->
              <view 
                class="control-btn delete-btn" 
                style="left: {{item.x + item.width - 10}}px; top: {{item.y - 30}}px;" 
                data-id="{{item.id}}" 
                data-action="delete"
                catchtap="handleControlBtnTap"
              >×</view>
              
              <!-- 放大按钮 -->
              <view 
                class="control-btn size-increase-btn" 
                style="left: {{item.x + item.width - 10}}px; top: {{item.y + item.height - 10}}px;" 
                data-id="{{item.id}}" 
                data-action="increase"
                catchtap="handleControlBtnTap"
              >+</view>
              
              <!-- 缩小按钮 -->
              <view 
                class="control-btn size-decrease-btn" 
                style="left: {{item.x + item.width - 70}}px; top: {{item.y + item.height - 10}}px;" 
                data-id="{{item.id}}" 
                data-action="decrease"
                catchtap="handleControlBtnTap"
              >-</view>
              
              <!-- 顺时针旋转按钮 -->
              <view 
                class="control-btn rotate-cw-btn" 
                style="left: {{item.x - 30}}px; top: {{item.y - 30}}px;" 
                data-id="{{item.id}}" 
                data-action="rotateCW"
                catchtap="handleControlBtnTap"
              >↻</view>
              
              <!-- 逆时针旋转按钮 -->
              <view 
                class="control-btn rotate-ccw-btn" 
                style="left: {{item.x + 40}}px; top: {{item.y - 30}}px;" 
                data-id="{{item.id}}" 
                data-action="rotateCCW"
                catchtap="handleControlBtnTap"
              >↺</view>
              
              <!-- 图层上移按钮 -->
              <view 
                class="control-btn layer-up-btn" 
                style="left: {{item.x - 30}}px; top: {{item.y + item.height - 10}}px;" 
                data-id="{{item.id}}" 
                data-action="layerUp"
                catchtap="handleControlBtnTap"
              >↑</view>
              
              <!-- 图层下移按钮 -->
              <view 
                class="control-btn layer-down-btn" 
                style="left: {{item.x + 40}}px; top: {{item.y + item.height - 10}}px;" 
                data-id="{{item.id}}" 
                data-action="layerDown"
                catchtap="handleControlBtnTap"
              >↓</view>
            </block>
          </block>
          
          <!-- 提示文字，当画布为空时显示 -->
          <view class="canvas-hint" wx:if="{{canvasItems.length === 0}}">
            <text>从左侧选择衣物添加到画布</text>
            <text>可以拖动、旋转和调整大小</text>
          </view>
        </view>
        
        <!-- 底部按钮 -->
        <view class="canvas-action-button" catchtap="clearCanvas" style="background-color: {{themeStyle === 'autumn' ? colors.spicedWine : pinkBlueColors.pinkDark}};">
          <text style="color: {{colors.goldenBatter}};">清空画布</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 用于生成图片的画布，不可见 -->
  <canvas canvas-id="outfitCanvas" style="width: {{canvasWidth}}px; height: {{canvasHeight}}px; position: absolute; left: -9999px;"></canvas>
</view> 