# 穿搭创建功能

## 功能概述

穿搭创建功能允许用户从已上传的衣物中选择物品，并在画布上进行自由拖拽、旋转和大小调整，创建个性化的搭配组合。用户可以将创建的搭配保存到数据库中，以便日后查看和使用。

## 页面结构

1. **顶部工具栏**：包含返回按钮、搭配名称输入框和保存按钮
2. **左侧衣柜区域**：显示用户的衣物库，支持按类别筛选
3. **右侧画布区域**：用于拖放和摆放衣物的画布
4. **底部操作按钮**：清空画布按钮

## 主要功能

### 1. 衣物选择与分类筛选

- 用户可以从左侧衣柜中选择衣物
- 支持按类别筛选（上衣、裤子、裙子、外套、鞋子、配饰）
- 点击衣物项将其添加到画布中

### 2. 搭配画布操作

- **拖拽**：触摸并移动衣物进行位置调整
- **旋转**：使用旋转按钮调整衣物角度
- **调整大小**：使用大小调整按钮改变衣物尺寸
- **删除**：移除不需要的衣物
- **选中**：点击衣物将其设为活跃状态，显示控制按钮

### 3. 搭配保存功能

- 输入搭配名称
- 保存按钮将当前画布状态转换为图片
- 搭配信息（包括所有衣物的位置、大小、旋转角度等）被保存到数据库

## 技术实现

### 画布实现

使用微信小程序的视图层组件模拟画布功能，通过绝对定位和transform实现拖拽、旋转等操作。

### 拖拽实现

利用touchstart、touchmove和touchend事件处理衣物拖拽功能：
1. touchstart：记录起始触摸坐标和项目位置
2. touchmove：计算移动距离并更新项目位置
3. touchend：完成拖拽操作

### 旋转和调整大小

- 旋转通过改变CSS的transform: rotate()属性实现
- 调整大小通过改变width和height属性实现，保持纵横比

### 生成图片保存

1. 使用canvas绘制画布上所有衣物
2. 将canvas导出为图片（canvasToTempFilePath）
3. 上传图片到云存储
4. 将图片路径和搭配数据保存到数据库

## 数据结构

### 画布项目数据
```javascript
{
  id: Number,           // 画布项唯一ID
  clothingId: String,   // 对应衣物的ID
  imageUrl: String,     // 图片URL
  x: Number,            // X坐标位置
  y: Number,            // Y坐标位置
  width: Number,        // 宽度
  height: Number,       // 高度
  rotation: Number,     // 旋转角度
  zIndex: Number,       // 层叠顺序
  active: Boolean       // 是否处于选中状态
}
```

### 保存到数据库的搭配数据
```javascript
{
  name: String,         // 搭配名称
  imageFileID: String,  // 搭配效果图的云存储文件ID
  items: Array,         // 包含所有衣物项的位置、尺寸、角度信息
  createTime: Date      // 创建时间
}
```

## 使用说明

1. 从主页面点击"创建搭配"按钮进入搭配创建页面
2. 选择左侧类别筛选所需衣物
3. 点击衣物将其添加到画布
4. 在画布中拖拽、旋转、调整衣物大小
5. 输入搭配名称
6. 点击"保存"按钮完成创建

## 后续优化方向

1. **多层级管理**：添加图层管理功能，方便调整衣物的前后关系
2. **批量编辑**：支持同时选择多件衣物进行操作
3. **智能推荐**：根据已选衣物智能推荐搭配建议
4. **模板功能**：提供常用搭配模板，方便快速创建
5. **历史记录**：添加编辑历史记录，支持撤销和重做操作
6. **背景选择**：提供多种背景可选，增强视觉效果
