<view class="container theme-{{themeStyle}}">
  <!-- Loading state -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner" style="border-top-color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}}; border-color: {{themeStyle === 'blackWhite' ? blackWhiteColors.lightGray : colors.golden_batter}};"></view>
    <view class="loading-text" style="color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}};">加载中...</view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{!isLoading}}">
    <!-- 顶部区域 -->
    <view class="top-section">
      <view class="back-button" bindtap="goBack">
        <view class="back-icon" style="border-right-color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}};"></view>
        <text style="color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}};">返回</text>
      </view>

      <view class="category-title" style="color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : colors.cowhide_cocoa}};">{{categoryName}}</view>

      <!-- 创建搭配按钮 -->
      <view class="create-outfit-button" bindtap="goToCreateOutfit" style="background-color: {{themeStyle === 'blackWhite' ? blackWhiteColors.black : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : colors.spiced_wine)}}; color: {{themeStyle === 'blackWhite' ? blackWhiteColors.white : colors.golden_batter}};">
        <view class="plus-icon">+</view>
        <text>创建</text>
      </view>
    </view>

    <!-- 搭配列表 -->
    <view class="outfit-list" wx:if="{{outfits.length > 0}}">
      <scroll-view scroll-y="true" class="outfits-scroll">
        <block wx:for="{{outfits}}" wx:key="_id">
          <view class="outfit-card" bindtap="viewOutfitDetail" data-id="{{item._id || item.id}}" style="background-color: {{themeStyle === 'autumn' ? (index % 2 === 0 ? colors.toasted_caramel : colors.olive_harvest) : (themeStyle === 'pinkBlue' ? (index % 2 === 0 ? pinkBlueColors.pinkMedium : pinkBlueColors.blueDark) : (index % 2 === 0 ? blackWhiteColors.white : blackWhiteColors.lightGray))}}; border: {{themeStyle === 'blackWhite' ? '1px solid ' + blackWhiteColors.darkGray : 'none'}};">
            <!-- 搭配信息 -->
            <view class="outfit-info">
              <view class="outfit-name-container">
                <view class="outfit-name" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : (themeStyle === 'blackWhite' ? blackWhiteColors.black : 'white')}};">{{item.name || '未命名搭配'}}</view>
                <!-- 内联AI评分 -->
                <view class="ai-score-inline" wx:if="{{item.aiScore}}"
                      style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.blueDark)}};">
                  AI评分：{{item.aiScore}}
                </view>
              </view>
              <view class="outfit-date" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : (themeStyle === 'blackWhite' ? blackWhiteColors.darkGray : 'white')}};">{{item.createTime ? formatDate(item.createTime) : '未知日期'}}</view>

              <!-- 衣物列表 -->
              <view class="outfit-items" wx:if="{{item.items && item.items.length > 0}}">
                <block wx:for="{{item.items}}" wx:for-item="clothingItem" wx:for-index="itemIndex" wx:key="id">
                  <view class="outfit-item">
                    <image
                      src="{{clothingItem.imageUrl}}"
                      mode="aspectFit"
                      class="item-image"
                      style="border: 2rpx solid {{themeStyle === 'autumn' ? colors.golden_batter : (themeStyle === 'blackWhite' ? blackWhiteColors.darkGray : pinkBlueColors.blueMedium)}};"
                      binderror="handleImageError"
                      data-type="item"
                      data-index="{{index}}"
                      data-item-index="{{itemIndex}}"
                    ></image>
                    <view class="item-name" style="color: {{themeStyle === 'autumn' ? colors.golden_batter : (themeStyle === 'blackWhite' ? blackWhiteColors.black : 'white')}};">{{clothingItem.name || '未命名'}}</view>
                  </view>
                </block>
              </view>
              <view class="no-items" wx:else style="color: {{themeStyle === 'autumn' ? colors.golden_batter : (themeStyle === 'blackWhite' ? blackWhiteColors.black : 'white')}};">
                暂无衣物信息
              </view>
            </view>

            <!-- 搭配预览图 -->
            <view class="outfit-preview">
              <image
                src="{{item.previewImage}}"
                mode="aspectFill"
                class="preview-image"
                style="border: 2rpx solid {{themeStyle === 'autumn' ? colors.golden_batter : (themeStyle === 'blackWhite' ? blackWhiteColors.darkGray : pinkBlueColors.blueMedium)}};"
                binderror="handleImageError"
                data-type="preview"
                data-index="{{index}}"
              ></image>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>

    <!-- 空状态展示 -->
    <view class="empty-state" wx:if="{{!isLoading && outfits.length === 0}}">
      <view class="empty-icon">👕</view>
      <view class="empty-text" style="color: {{themeStyle === 'autumn' ? '#666' : (themeStyle === 'blackWhite' ? blackWhiteColors.darkGray : pinkBlueColors.pinkDark)}};">还没有{{categoryName}}，快来创建吧！</view>
      <view class="create-button" bindtap="goToCreateOutfit" style="background-color: {{themeStyle === 'autumn' ? '#84592B' : (themeStyle === 'blackWhite' ? blackWhiteColors.black : pinkBlueColors.pinkDark)}}; color: {{themeStyle === 'autumn' ? '#E8D1A7' : (themeStyle === 'blackWhite' ? blackWhiteColors.white : 'white')}};">创建{{categoryName}}</view>
    </view>
  </view>
</view>