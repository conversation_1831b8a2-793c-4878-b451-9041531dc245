// 图片管理模块
// 负责处理图片的获取、缓存、更新等操作

// 导入本地图片缓存模块（如果需要）
const localImageCache = require('../../modules/localImageCache');

// 默认图片URL
const DEFAULT_IMAGE_URL = 'https://mmbiz.qpic.cn/mmbiz_png/KyicVWur7UYpNH3GMqJDxDLsv9v2A2VrxI5RlBjSZK49AUB7oFb5OvJuNRLqicDXs3JCaUJW6tYoZQZQ1ibeTUmwA/0?wx_fmt=png';

// 缓存的临时URL信息，格式 {fileID: {url: '临时url', expireTime: 过期时间}}
let urlCache = {};

// 获取默认图片URL
function getDefaultImageUrl() {
  return DEFAULT_IMAGE_URL;
}

// 从云存储获取临时文件URL
function getTempFileURL(fileID) {
  return new Promise((resolve, reject) => {
    if (!fileID || !fileID.includes('cloud://')) {
      // 如果不是云存储文件ID，直接返回原始URL
      resolve(fileID);
      return;
    }

    // 检查缓存中是否有有效的临时URL
    const cached = urlCache[fileID];
    if (cached && cached.url && cached.expireTime > Date.now()) {
      console.log('使用缓存的临时URL:', fileID);
      resolve(cached.url);
      return;
    }

    // 本地没有缓存或已过期，从云存储获取新的临时URL
    console.log('从云存储获取临时URL:', fileID);
    wx.cloud.getTempFileURL({
      fileList: [fileID],
      success: res => {
        if (res.fileList && res.fileList[0] && res.fileList[0].tempFileURL) {
          const tempURL = res.fileList[0].tempFileURL;

          // 缓存临时URL，有效期2小时（保守估计）
          urlCache[fileID] = {
            url: tempURL,
            expireTime: Date.now() + 7200000 // 当前时间 + 2小时
          };

          resolve(tempURL);
        } else {
          console.error('获取临时URL失败:', res);
          reject(new Error('获取图片地址失败'));
        }
      },
      fail: err => {
        console.error('获取临时URL出错:', err);
        reject(err);
      }
    });
  });
}

// 刷新临时文件URL并回调
function refreshTempFileURL(fileID, callback) {
  if (!fileID || !fileID.includes('cloud://')) {
    // 不是云存储文件
    callback && callback(fileID);
    return;
  }

  getTempFileURL(fileID)
    .then(tempURL => {
      callback && callback(tempURL);
    })
    .catch(err => {
      console.error('刷新临时URL失败:', err);
      callback && callback(null);
    });
}

// 清除过期的URL缓存
function clearExpiredURLCache() {
  const now = Date.now();
  let count = 0;

  for (const fileID in urlCache) {
    if (urlCache[fileID].expireTime < now) {
      delete urlCache[fileID];
      count++;
    }
  }

  console.log(`已清除 ${count} 条过期的URL缓存`);
}

// 处理衣物图片，优先使用抠图后的图片
function processClothImage(clothItem) {
  return new Promise((resolve, reject) => {
    // 保存原始的isProcessed标志
    const wasProcessed = clothItem.isProcessed || false;

    // 收集所有可能的抠图文件ID
    let processedFileID = null;

    // 按优先级检查抠图后的图片
    if (clothItem.processedImageFileID && clothItem.processedImageFileID.includes('cloud://')) {
      processedFileID = clothItem.processedImageFileID;
      console.log(`使用processedImageFileID: ${processedFileID} 用于衣物 ${clothItem._id || 'unknown'}`);
    } else if (clothItem.processedImageUrl && clothItem.processedImageUrl.includes('cloud://')) {
      processedFileID = clothItem.processedImageUrl;
      console.log(`使用processedImageUrl: ${processedFileID} 用于衣物 ${clothItem._id || 'unknown'}`);
    } else if (clothItem.bgRemovedFileID && clothItem.bgRemovedFileID.includes('cloud://')) {
      processedFileID = clothItem.bgRemovedFileID;
      console.log(`使用bgRemovedFileID: ${processedFileID} 用于衣物 ${clothItem._id || 'unknown'}`);
    }

    // 收集原始图片文件ID
    const originalFileID = clothItem.fileID || clothItem.imageFileID || clothItem.originalImageFileID;

    // 首先检查是否有本地缓存
    if (processedFileID) {
      // 检查是否有本地缓存
      const localPath = localImageCache.getLocalCachedImage(processedFileID);
      if (localPath) {
        console.log(`使用本地缓存的抠图图片: ${clothItem._id || 'unknown'}`);
        clothItem.imageUrl = localPath;
        clothItem.isProcessed = true; // 标记为已抠图
        resolve(clothItem);
        return;
      }

      // 没有本地缓存，获取临时URL
      getTempFileURL(processedFileID)
        .then(tempURL => {
          // 标记为已处理（抠图）
          clothItem.imageUrl = tempURL;
          clothItem.isProcessed = true;

          // 缓存临时URL到本地文件系统
          localImageCache.downloadImageToCache(processedFileID, tempURL)
            .then(savedFilePath => {
              if (savedFilePath) {
                console.log(`已缓存抠图图片到本地: ${clothItem._id || 'unknown'}`);
                clothItem.imageUrl = savedFilePath;
              }
            })
            .catch(err => {
              console.warn(`缓存抠图图片失败: ${processedFileID}`, err);
            });

          resolve(clothItem);
        })
        .catch(err => {
          console.error(`获取抠图图片失败: ${err.message}，尝试使用原图`);
          // 失败后尝试使用原图
          if (originalFileID && originalFileID.includes('cloud://')) {
            return getTempFileURL(originalFileID);
          } else {
            // 没有原图，使用默认图片
            return Promise.resolve(DEFAULT_IMAGE_URL);
          }
        })
        .then(tempURL => {
          if (tempURL !== clothItem.imageUrl) {
            // 未使用抠图图片，使用原图或默认图
            clothItem.imageUrl = tempURL;
            clothItem.isProcessed = false;

            // 如果是原图，也尝试缓存
            if (originalFileID && originalFileID.includes('cloud://')) {
              localImageCache.downloadImageToCache(originalFileID, tempURL)
                .then(savedFilePath => {
                  if (savedFilePath) {
                    console.log(`已缓存原图到本地: ${clothItem._id || 'unknown'}`);
                    clothItem.imageUrl = savedFilePath;
                  }
                })
                .catch(err => {
                  console.warn(`缓存原图失败: ${originalFileID}`, err);
                });
            }
          }
          resolve(clothItem);
        })
        .catch(err => {
          console.error('处理图片完全失败:', err);
          // 使用默认图片
          clothItem.imageUrl = DEFAULT_IMAGE_URL;
          clothItem.isProcessed = false;
          resolve(clothItem);
        });
    } else if (originalFileID && originalFileID.includes('cloud://')) {
      // 检查原图是否有本地缓存
      const localPath = localImageCache.getLocalCachedImage(originalFileID);
      if (localPath) {
        console.log(`使用本地缓存的原图: ${clothItem._id || 'unknown'}`);
        clothItem.imageUrl = localPath;
        clothItem.isProcessed = false;
        resolve(clothItem);
        return;
      }

      // 没有抠图图片，使用原图
      getTempFileURL(originalFileID)
        .then(tempURL => {
          clothItem.imageUrl = tempURL;
          clothItem.isProcessed = false;

          // 缓存原图到本地
          localImageCache.downloadImageToCache(originalFileID, tempURL)
            .then(savedFilePath => {
              if (savedFilePath) {
                console.log(`已缓存原图到本地: ${clothItem._id || 'unknown'}`);
                clothItem.imageUrl = savedFilePath;
              }
            })
            .catch(err => {
              console.warn(`缓存原图失败: ${originalFileID}`, err);
            });

          resolve(clothItem);
        })
        .catch(err => {
          console.error('获取原图失败:', err);
          // 使用默认图片
          clothItem.imageUrl = DEFAULT_IMAGE_URL;
          clothItem.isProcessed = false;
          resolve(clothItem);
        });
    } else if (clothItem.imageUrl) {
      // 已有imageUrl，可能是本地图片或网络图片
      // 保留原始isProcessed标记
      if (wasProcessed) {
        clothItem.isProcessed = true;
      }
      resolve(clothItem);
    } else {
      // 没有任何图片信息，使用默认图片
      clothItem.imageUrl = DEFAULT_IMAGE_URL;
      clothItem.isProcessed = false;
      resolve(clothItem);
    }
  });
}

// 批量处理衣物图片
function batchProcessClothesImages(clothesList) {
  // 使用Promise.all处理所有衣物的图片
  const promises = clothesList.map(cloth => processClothImage(cloth));
  return Promise.all(promises);
}

/**
 * 从画布生成高质量图片
 * @param {string} canvasId - 画布ID
 * @returns {Promise<string>} 包含临时文件路径的Promise
 */
function generateImageFromCanvas(canvasId) {
  return new Promise((resolve, reject) => {
    wx.canvasToTempFilePath({
      canvasId: canvasId,
      fileType: 'png', // 使用PNG格式避免压缩损失
      quality: 1.0,    // 最高质量
      success: res => {
        console.log('画布生成高质量图片成功');
        resolve(res.tempFilePath);
      },
      fail: err => {
        console.error('画布生成图片失败:', err);
        reject(err);
      }
    });
  });
}

module.exports = {
  getDefaultImageUrl,
  getTempFileURL,
  refreshTempFileURL,
  clearExpiredURLCache,
  processClothImage,
  batchProcessClothesImages,
  generateImageFromCanvas
};