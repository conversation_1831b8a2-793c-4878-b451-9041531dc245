// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = event.openid || wxContext.OPENID
  
  if (!openid) {
    return {
      success: false,
      errMsg: '未能获取有效的用户标识'
    }
  }
  
  // 确保体力值在有效范围内（0-500）
  let catEnergy = parseInt(event.catEnergy) || 0
  catEnergy = Math.max(0, Math.min(catEnergy, 500))
  
  try {
    // 更新用户体力值
    const result = await db.collection('users').where({
      _openid: openid
    }).update({
      data: {
        catEnergy: catEnergy,
        updateTime: db.serverDate()
      }
    })
    
    return {
      success: true,
      updated: result.stats.updated,
      openid: openid,
      catEnergy: catEnergy
    }
  } catch (err) {
    console.error('更新体力值失败:', err)
    return {
      success: false,
      errMsg: err.message,
      openid: openid
    }
  }
}
