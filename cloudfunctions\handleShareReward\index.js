// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 处理分享奖励逻辑的云函数
 * 该函数实现两个功能：
 * 1. 记录分享信息，生成分享记录
 * 2. 处理分享奖励，当被分享者添加衣物后，给分享者奖励
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  // 如果没有提供action参数，默认为createShareRecord
  const action = event.action || 'createShareRecord'
  
  // 根据不同的action执行不同的操作
  switch (action) {
    // 创建分享记录
    case 'createShareRecord':
      return await createShareRecord(openid, event.localShareId)
    
    // 记录被分享者添加衣物
    case 'recordSharedUserAddClothes':
      if (!event.shareId) {
        return {
          success: false,
          message: '缺少必要参数shareId'
        }
      }
      
      // 使用前端传入的被分享用户openid，如果没有则使用当前用户的openid
      const sharedUserOpenid = event.sharedUserOpenid || openid
      
      // 调用函数并传入是否检查新用户的参数
      return await recordSharedUserAddClothes(
        event.shareId, 
        sharedUserOpenid, 
        event.checkNewUser === true
      )
    
    // 检查奖励状态
    case 'checkRewardStatus':
      return await checkRewardStatus(event.shareId, event.sharerId || openid)
    
    // 默认情况
    default:
      return {
        success: false,
        message: '不支持的操作: ' + action
      }
  }
}

/**
 * 创建分享记录
 * @param {string} sharerId - 分享者的OpenID
 * @param {string} localShareId - 前端生成的本地分享ID
 * @returns {Object} 创建结果
 */
async function createShareRecord(sharerId, localShareId) {
  try {
    // 创建一个分享记录
    const data = {
      sharerId: sharerId,         // 分享者ID
      createTime: db.serverDate(),// 创建时间
      status: 'pending',          // 状态: pending-待处理, completed-已完成, rewarded-已奖励
      sharedUsers: [],            // 被分享的用户列表
      rewardCount: 0              // 初始化奖励计数为0
    };
    
    // 如果前端传递了本地生成的分享ID，则使用该ID进行关联
    if (localShareId) {
      data.localShareId = localShareId;
    }
    
    const result = await db.collection('share_records').add({
      data: data
    });
    
    return {
      success: true,
      shareId: result._id,
      localShareId: localShareId,
      message: '创建分享记录成功'
    };
  } catch (error) {
    console.error('创建分享记录失败:', error);
    return {
      success: false,
      message: '创建分享记录失败: ' + error.message
    };
  }
}

/**
 * 记录被分享者添加衣物
 * @param {string} shareId - 分享记录ID
 * @param {string} sharedUserOpenId - 被分享者的OpenID
 * @param {boolean} checkNewUser - 是否检查新用户
 * @returns {Object} 操作结果
 */
async function recordSharedUserAddClothes(shareId, sharedUserOpenId, checkNewUser = false) {
  try {
    // 查询分享记录
    const shareRecord = await db.collection('share_records').where({
      localShareId: shareId
    }).get()
    
    if (!shareRecord.data || shareRecord.data.length === 0) {
      return {
        success: false,
        message: '分享记录不存在'
      }
    }
    
    // 如果该用户已经记录过，则不重复记录
    if (shareRecord.data[0].sharedUsers.includes(sharedUserOpenId)) {
      return {
        success: true,
        message: '该用户已添加过衣物',
        needReward: false
      }
    }
    
    // 检查是否为新用户（未在old_users表中出现过）
    let isNewUser = false;
    
    // 查询old_users表中是否存在该用户
    const userResult = await db.collection('old_users')
      .where({
        openid: sharedUserOpenId
      })
      .get();
    
    // 如果用户不存在于old_users表中，则视为新用户
    isNewUser = (userResult.data.length === 0);
    
    // 更新分享记录，添加被分享用户ID
    const sharedUsers = [...shareRecord.data[0].sharedUsers, sharedUserOpenId]
    
    // 查找分享记录和用户的组合是否已经奖励过
    const rewardRecord = await db.collection('share_reward_records')
      .where({
        shareId: shareId,
        sharedUserOpenId: sharedUserOpenId
      })
      .get();
    
    // 判断是否需要奖励：用户是新用户且没有对应的奖励记录
    const needReward = isNewUser && rewardRecord.data.length === 0;
    
    // 如果需要奖励，则给分享者增加衣物上限
    if (needReward) {
      await rewardSharer(shareRecord.data[0].sharerId)
      
      // 将用户添加到old_users表中，标记为已获得奖励过的用户
      await db.collection('old_users').add({
        data: {
          openid: sharedUserOpenId,
          createTime: db.serverDate(),
          from: 'share_reward'
        }
      })
      
      // 添加奖励记录，记录分享ID和被分享用户的组合
      await db.collection('share_reward_records').add({
        data: {
          shareId: shareId,
          sharerId: shareRecord.data[0].sharerId,
          sharedUserOpenId: sharedUserOpenId,
          createTime: db.serverDate()
        }
      })
    }
    
    // 更新分享记录状态
    await db.collection('share_records').where({
      localShareId: shareId
    }).update({
      data: {
        sharedUsers: sharedUsers,
        status: needReward ? 'rewarded' : 'completed',
        // 不再使用hasRewarded标记，而是通过查询share_reward_records表来判断
        rewardCount: _.inc(needReward ? 1 : 0)  // 增加奖励计数
      }
    })
    
    return {
      success: true,
      message: isNewUser ? '记录新用户添加衣物成功' : '记录老用户添加衣物成功',
      needReward: needReward,
      isNewUser: isNewUser
    }
  } catch (error) {
    console.error('记录被分享者添加衣物失败:', error)
    return {
      success: false,
      message: '记录被分享者添加衣物失败: ' + error.message
    }
  }
}

/**
 * 给分享者奖励衣物
 * @param {string} sharerId - 分享者OpenID
 * @returns {Object} 奖励结果
 */
async function rewardSharer(sharerId) {
  try {
    // 查询用户记录
    const userRecord = await db.collection('users')
      .where({
        _openid: sharerId
      })
      .get()
    
    if (!userRecord.data || userRecord.data.length === 0) {
      // 用户记录不存在，尝试创建一个
      await db.collection('users').add({
        data: {
          _openid: sharerId,
          clothesLimit: 35,     // 初始默认30+奖励5件
          outfitsLimit: 11,     // 初始默认10
          clothesCount: 0,
          outfitsCount: 0,
          createTime: db.serverDate()
        }
      })
    } else {
      // 用户记录存在，增加限制
      const currentUserData = userRecord.data[0];
      const currentClothesLimit = currentUserData.clothesLimit || 30;
      const currentOutfitsLimit = currentUserData.outfitsLimit || 10;
      
      await db.collection('users')
        .where({
          _openid: sharerId
        })
        .update({
          data: {
            clothesLimit: currentClothesLimit + 5,  // 增加5件衣物上限
            outfitsLimit: currentOutfitsLimit + 1
          }
        })
    }
    
    // 添加奖励记录
    await db.collection('reward_records').add({
      data: {
        userOpenId: sharerId,
        rewardType: 'share',
        rewardAmount: {
          clothesLimit: 5
        },
        createTime: db.serverDate(),
        remark: '分享小程序获得奖励'
      }
    })
    
    return {
      success: true,
      message: '奖励分享者成功'
    }
  } catch (error) {
    console.error('奖励分享者失败:', error)
    return {
      success: false,
      message: '奖励分享者失败: ' + error.message
    }
  }
}

/**
 * 检查分享奖励状态
 * @param {string} shareId - 分享记录ID
 * @param {string} sharerId - 分享者OpenID
 * @returns {Object} 检查结果
 */
async function checkRewardStatus(shareId, sharerId) {
  try {
    // 如果没有提供shareId，则查询该用户的所有分享记录
    if (!shareId) {
      // 获取用户的所有分享记录
      const shareRecords = await db.collection('share_records')
        .where({
          sharerId: sharerId
        })
        .orderBy('createTime', 'desc')
        .get()
      
      // 获取用户的所有奖励记录
      const rewardRecords = await db.collection('share_reward_records')
        .where({
          sharerId: sharerId
        })
        .get()
      
      // 统计已获得的奖励次数
      const rewardedCount = rewardRecords.data.length;
      
      // 为每个分享记录添加奖励信息
      const shareRecordsWithRewardInfo = shareRecords.data.map(record => {
        // 找出该分享记录对应的所有奖励
        const recordRewards = rewardRecords.data.filter(reward => 
          reward.shareId === record.localShareId || reward.shareId === record._id
        );
        
        return {
          ...record,
          rewardCount: recordRewards.length,
          rewardUsers: recordRewards.map(r => r.sharedUserOpenId)
        };
      });
      
      return {
        success: true,
        shareRecords: shareRecordsWithRewardInfo,
        rewardedCount: rewardedCount,
        totalRewardAmount: rewardedCount * 5 // 每次奖励5件衣物
      }
    }
    
    // 查询特定分享记录
    const shareRecord = await db.collection('share_records')
      .where({
        localShareId: shareId
      })
      .get()
    
    if (!shareRecord.data || shareRecord.data.length === 0) {
      // 尝试使用_id进行查询
      const shareRecordById = await db.collection('share_records')
        .where({
          _id: shareId
        })
        .get()
      
      if (!shareRecordById.data || shareRecordById.data.length === 0) {
        return {
          success: false,
          message: '分享记录不存在'
        }
      }
      
      // 找到记录，继续处理
      const record = shareRecordById.data[0];
      const recordId = record._id;
      
      // 获取该分享记录的所有奖励
      const rewardRecords = await db.collection('share_reward_records')
        .where({
          shareId: recordId
        })
        .get()
      
      return {
        success: true,
        shareRecord: {
          ...record,
          rewardCount: rewardRecords.data.length,
          rewardUsers: rewardRecords.data.map(r => r.sharedUserOpenId)
        }
      }
    }
    
    // 找到使用localShareId的记录
    const record = shareRecord.data[0];
    
    // 获取该分享记录的所有奖励
    const rewardRecords = await db.collection('share_reward_records')
      .where({
        shareId: shareId
      })
      .get()
    
    return {
      success: true,
      shareRecord: {
        ...record,
        rewardCount: rewardRecords.data.length,
        rewardUsers: rewardRecords.data.map(r => r.sharedUserOpenId)
      }
    }
  } catch (error) {
    console.error('检查奖励状态失败:', error)
    return {
      success: false,
      message: '检查奖励状态失败: ' + error.message
    }
  }
} 