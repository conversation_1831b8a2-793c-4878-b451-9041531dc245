// 本地图片缓存模块
// 负责outfit页面中图片的本地文件系统缓存

// 定义默认图片路径常量
const DEFAULT_PREVIEW_IMAGE = '/image/outfit-icon.png';
const DEFAULT_ITEM_IMAGE = '/image/short-dress.png';

// 缓存相关常量
const IMAGE_CACHE_FOLDER = 'outfit_images/'; // 本地文件系统中的缓存文件夹
const URL_CACHE_KEY = 'outfit_image_urls'; // 用于存储图片URL的本地缓存键
const MAX_BATCH_SIZE = 50; // 每批处理的最大图片数量

// 其他模块的缓存相关常量
const OOTD_CACHE_FOLDER = 'outfits_images/'; // OOTD模块中的缓存文件夹
const OOTD_URL_CACHE_KEY = 'ootd_image_urls'; // OOTD模块中的缓存键

// 衣柜图片缓存相关常量
const CLOTHES_CACHE_FOLDER = 'clothes_images/'; // 衣柜模块中的缓存文件夹
const CLOTHES_URL_CACHE_KEY = 'clothes_image_urls'; // 衣柜模块中的缓存键

/**
 * 获取默认预览图URL
 * @returns {string} 默认预览图URL
 */
function getDefaultPreviewImageUrl() {
  return DEFAULT_PREVIEW_IMAGE;
}

/**
 * 获取默认衣物图片URL
 * @returns {string} 默认衣物图片URL
 */
function getDefaultItemImageUrl() {
  return DEFAULT_ITEM_IMAGE;
}

/**
 * 确保缓存目录存在
 * @returns {string} 缓存目录路径
 */
function ensureCacheDir() {
  const fs = wx.getFileSystemManager();
  const cacheDir = `${wx.env.USER_DATA_PATH}/${IMAGE_CACHE_FOLDER}`;
  
  try {
    fs.accessSync(cacheDir);
  } catch (err) {
    try {
      fs.mkdirSync(cacheDir, true);
      console.log('创建Outfit图片缓存目录:', cacheDir);
    } catch (mkdirErr) {
      console.error('创建Outfit图片缓存目录失败:', mkdirErr);
    }
  }
  
  return cacheDir;
}

/**
 * 从本地缓存获取图片路径
 * @param {string} fileID - 文件ID
 * @returns {string|null} 本地缓存路径，如果不存在则返回null
 */
function getLocalCachedImage(fileID) {
  if (!fileID) return null;
  
  try {
    // 1. 首先尝试从OOTD模块的缓存获取图片
    const ootdLocalPath = getOotdLocalCachedImage(fileID);
    if (ootdLocalPath) {
      console.log(`使用OOTD已缓存图片: ${fileID}`);
      return ootdLocalPath;
    }
    
    // 2. 尝试从衣柜(closet)模块的缓存获取图片
    const clothesLocalPath = getClothesLocalCachedImage(fileID);
    if (clothesLocalPath) {
      console.log(`使用衣柜已缓存图片: ${fileID}`);
      return clothesLocalPath;
    }
    
    // 3. 如果其他模块没有缓存，再从Outfit模块的缓存获取
    const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};
    const cacheInfo = cachedURLInfo[fileID];
    
    if (!cacheInfo || !cacheInfo.localPath) {
      return null;
    }
    
    // 检查本地文件是否存在
    const fs = wx.getFileSystemManager();
    const localFilePath = `${wx.env.USER_DATA_PATH}/${cacheInfo.localPath}`;
    
    try {
      fs.accessSync(localFilePath);
      console.log(`使用Outfit本地缓存图片: ${fileID}`);
      return localFilePath;
    } catch (err) {
      console.warn(`Outfit本地缓存图片不存在: ${fileID}`, err);
      return null;
    }
  } catch (err) {
    console.error('获取本地缓存图片出错:', err);
    return null;
  }
}

/**
 * 从OOTD模块的缓存中获取图片路径
 * @param {string} fileID - 文件ID
 * @returns {string|null} 本地缓存路径，如果不存在则返回null
 */
function getOotdLocalCachedImage(fileID) {
  if (!fileID) return null;
  
  try {
    // 从OOTD模块的本地存储获取缓存信息
    const ootdCachedURLInfo = wx.getStorageSync(OOTD_URL_CACHE_KEY) || {};
    const cacheInfo = ootdCachedURLInfo[fileID];
    
    if (!cacheInfo || !cacheInfo.localPath) {
      return null;
    }
    
    // 检查本地文件是否存在
    const fs = wx.getFileSystemManager();
    const localFilePath = `${wx.env.USER_DATA_PATH}/${cacheInfo.localPath}`;
    
    try {
      fs.accessSync(localFilePath);
      console.log(`使用OOTD模块缓存的图片: ${fileID}`);
      return localFilePath;
    } catch (err) {
      console.warn(`OOTD模块缓存的图片不存在: ${fileID}`, err);
      return null;
    }
  } catch (err) {
    console.error('获取OOTD模块缓存图片出错:', err);
    return null;
  }
}

/**
 * 从衣柜(closet)模块的缓存中获取图片路径
 * @param {string} fileID - 文件ID
 * @returns {string|null} 本地缓存路径，如果不存在则返回null
 */
function getClothesLocalCachedImage(fileID) {
  if (!fileID) return null;
  
  try {
    // 从衣柜模块的本地存储获取缓存信息
    const clothesCachedURLInfo = wx.getStorageSync(CLOTHES_URL_CACHE_KEY) || {};
    const cacheInfo = clothesCachedURLInfo[fileID];
    
    if (!cacheInfo || !cacheInfo.localPath) {
      return null;
    }
    
    // 检查本地文件是否存在
    const fs = wx.getFileSystemManager();
    const localFilePath = `${wx.env.USER_DATA_PATH}/${cacheInfo.localPath}`;
    
    try {
      fs.accessSync(localFilePath);
      console.log(`使用衣柜模块缓存的图片: ${fileID}`);
      return localFilePath;
    } catch (err) {
      console.warn(`衣柜模块缓存的图片不存在: ${fileID}`, err);
      return null;
    }
  } catch (err) {
    console.error('获取衣柜模块缓存图片出错:', err);
    return null;
  }
}

/**
 * 计算fileID的哈希文件名
 * @param {string} fileID - 文件ID
 * @returns {string} 哈希文件名
 */
function getHashFilename(fileID) {
  // 简单的哈希算法，生成一个相对唯一的文件名
  let hash = 0;
  for (let i = 0; i < fileID.length; i++) {
    hash = ((hash << 5) - hash) + fileID.charCodeAt(i);
    hash |= 0; // 转换为32位整数
  }
  
  // 确保是正数
  hash = Math.abs(hash);
  // 使用固定的后缀，同一个fileID始终生成相同的文件名
  return `outfit_${hash.toString(36)}.jpg`;
}

/**
 * 下载图片到本地缓存
 * @param {string} fileID - 文件ID
 * @param {string} tempFileURL - 临时文件URL
 * @returns {Promise<string>} 本地文件路径
 */
function downloadImageToCache(fileID, tempFileURL) {
  return new Promise((resolve, reject) => {
    if (!fileID || !tempFileURL) {
      reject(new Error('fileID或tempFileURL为空'));
      return;
    }
    
    // 首先检查其他模块是否已经缓存了这个图片
    const ootdLocalPath = getOotdLocalCachedImage(fileID);
    if (ootdLocalPath) {
      console.log(`使用OOTD已缓存图片，无需下载: ${fileID}`);
      resolve(ootdLocalPath);
      return;
    }
    
    const clothesLocalPath = getClothesLocalCachedImage(fileID);
    if (clothesLocalPath) {
      console.log(`使用衣柜已缓存图片，无需下载: ${fileID}`);
      resolve(clothesLocalPath);
      return;
    }
    
    // 使用fileID的哈希作为文件名，确保唯一性
    const fileName = getHashFilename(fileID);
    const localPath = `${IMAGE_CACHE_FOLDER}${fileName}`;
    const fullPath = `${wx.env.USER_DATA_PATH}/${localPath}`;
    
    // 确保缓存目录存在
    ensureCacheDir();
    
    // 下载图片到本地
    wx.downloadFile({
      url: tempFileURL,
      success: res => {
        if (res.statusCode === 200) {
          // 将临时文件保存到本地缓存目录
          const fs = wx.getFileSystemManager();
          fs.saveFile({
            tempFilePath: res.tempFilePath,
            filePath: fullPath,
            success: () => {
              console.log(`图片已缓存到本地: ${fileID}`);
              
              // 更新缓存信息
              const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};
              cachedURLInfo[fileID] = {
                localPath: localPath,
                timestamp: Date.now(),
                originalURL: tempFileURL
              };
              
              wx.setStorageSync(URL_CACHE_KEY, cachedURLInfo);
              
              // 返回本地文件路径
              resolve(fullPath);
            },
            fail: err => {
              console.error('保存文件到本地失败:', err);
              // 如果保存失败，返回临时文件路径
              resolve(res.tempFilePath);
            }
          });
        } else {
          console.error('下载图片失败:', res);
          reject(new Error(`下载图片失败，状态码: ${res.statusCode}`));
        }
      },
      fail: err => {
        console.error('下载图片请求失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 批量处理图片
 * @param {Array} fileIDs - 文件ID数组
 * @returns {Promise<Object>} 文件ID到本地路径的映射
 */
function batchProcessImages(fileIDs) {
  return new Promise((resolve, reject) => {
    if (!fileIDs || !Array.isArray(fileIDs) || fileIDs.length === 0) {
      resolve({});
      return;
    }
    
    // 结果映射
    const fileIDToPath = {};
    
    // 检查本地缓存
    const findCachedPath = (fileID) => {
      // 首先检查其他模块的缓存
      const ootdPath = getOotdLocalCachedImage(fileID);
      if (ootdPath) {
        return ootdPath;
      }
      
      const clothesPath = getClothesLocalCachedImage(fileID);
      if (clothesPath) {
        return clothesPath;
      }
      
      // 再检查本模块的缓存
      const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};
      const cacheInfo = cachedURLInfo[fileID];
      
      if (cacheInfo && cacheInfo.localPath) {
        try {
          const fs = wx.getFileSystemManager();
          const localFilePath = `${wx.env.USER_DATA_PATH}/${cacheInfo.localPath}`;
          fs.accessSync(localFilePath);
          return localFilePath;
        } catch (err) {
          // 如果本地文件不存在，则返回null
          return null;
        }
      }
      
      return null;
    };
    
    // 处理所有文件ID，找出已缓存和未缓存的
    const uncachedFileIDs = [];
    fileIDs.forEach(fileID => {
      const localPath = findCachedPath(fileID);
      if (localPath) {
        fileIDToPath[fileID] = localPath;
      } else {
        uncachedFileIDs.push(fileID);
      }
    });
    
    console.log(`共有 ${fileIDs.length} 个图片，其中 ${fileIDs.length - uncachedFileIDs.length} 个已缓存，${uncachedFileIDs.length} 个需要下载`);
    
    // 如果所有文件都已缓存，提前返回
    if (uncachedFileIDs.length === 0) {
      resolve(fileIDToPath);
      return;
    }
    
    // 获取未缓存的fileID的临时URL
    wx.cloud.getTempFileURL({
      fileList: uncachedFileIDs,
      success: res => {
        if (!res.fileList || res.fileList.length === 0) {
          console.error('获取临时URL列表为空');
          resolve(fileIDToPath);
          return;
        }
        
        console.log(`获取了 ${res.fileList.length} 个临时URL`);
        
        // 分批下载图片，避免同时发起太多请求
        const downloadPromises = [];
        const fileIDToTempURL = {};
        
        // 保存fileID和临时URL的映射
        res.fileList.forEach(file => {
          if (file.fileID && file.tempFileURL) {
            fileIDToTempURL[file.fileID] = file.tempFileURL;
          }
        });
        
        // 分批下载
        for (let i = 0; i < uncachedFileIDs.length; i += MAX_BATCH_SIZE) {
          const batchFileIDs = uncachedFileIDs.slice(i, i + MAX_BATCH_SIZE);
          
          // 创建一个批次的Promise
          const batchPromise = new Promise(batchResolve => {
            // 处理每个文件
            const downloadResults = {};
            let completedCount = 0;
            
            batchFileIDs.forEach(fileID => {
              const tempURL = fileIDToTempURL[fileID];
              if (!tempURL) {
                completedCount++;
                if (completedCount === batchFileIDs.length) {
                  batchResolve(downloadResults);
                }
                return;
              }
              
              // 下载图片到本地
              downloadImageToCache(fileID, tempURL)
                .then(localPath => {
                  downloadResults[fileID] = localPath;
                })
                .catch(() => {
                  // 如果下载失败，使用临时URL
                  downloadResults[fileID] = tempURL;
                })
                .finally(() => {
                  completedCount++;
                  if (completedCount === batchFileIDs.length) {
                    batchResolve(downloadResults);
                  }
                });
            });
          });
          
          downloadPromises.push(batchPromise);
        }
        
        // 等待所有批次完成
        Promise.all(downloadPromises)
          .then(batchResults => {
            // 合并所有批次的结果
            batchResults.forEach(batchResult => {
              Object.assign(fileIDToPath, batchResult);
            });
            
            // 返回最终结果
            resolve(fileIDToPath);
          })
          .catch(err => {
            console.error('批量下载图片失败:', err);
            resolve(fileIDToPath); // 即使失败，仍然返回已处理的结果
          });
      },
      fail: err => {
        console.error('获取临时URL失败:', err);
        resolve(fileIDToPath); // 即使失败，仍然返回已处理的结果
      }
    });
  });
}

/**
 * 清除过期的图片缓存
 */
function clearExpiredCache() {
  console.log('开始清除过期的图片缓存');
  
  try {
    // 缓存过期时间：7天
    const CACHE_EXPIRATION = 7 * 24 * 60 * 60 * 1000; // 7天（毫秒）
    const now = Date.now();
    
    // 获取缓存信息
    const cachedURLInfo = wx.getStorageSync(URL_CACHE_KEY) || {};
    let hasExpired = false;
    
    // 找出过期的文件ID
    const expiredFileIDs = Object.keys(cachedURLInfo).filter(fileID => {
      const cacheInfo = cachedURLInfo[fileID];
      return (now - cacheInfo.timestamp > CACHE_EXPIRATION);
    });
    
    // 删除过期的缓存文件
    if (expiredFileIDs.length > 0) {
      const fs = wx.getFileSystemManager();
      
      expiredFileIDs.forEach(fileID => {
        const cacheInfo = cachedURLInfo[fileID];
        if (cacheInfo && cacheInfo.localPath) {
          try {
            const localFilePath = `${wx.env.USER_DATA_PATH}/${cacheInfo.localPath}`;
            fs.unlinkSync(localFilePath);
            delete cachedURLInfo[fileID];
            hasExpired = true;
          } catch (err) {
            // 如果文件不存在，直接从缓存信息中删除
            delete cachedURLInfo[fileID];
            hasExpired = true;
          }
        }
      });
      
      // 更新缓存信息
      if (hasExpired) {
        wx.setStorageSync(URL_CACHE_KEY, cachedURLInfo);
      }
      
      console.log(`清除了 ${expiredFileIDs.length} 个过期的图片缓存`);
    } else {
      console.log('没有过期的图片缓存需要清除');
    }
  } catch (err) {
    console.error('清除过期图片缓存出错:', err);
  }
}

// 导出模块接口
module.exports = {
  getDefaultPreviewImageUrl,
  getDefaultItemImageUrl,
  ensureCacheDir,
  getLocalCachedImage,
  downloadImageToCache,
  batchProcessImages,
  clearExpiredCache
}; 