// page/wardrobe/outfit/outfit_category_magazine/outfit_category_magazine.js

// 引入图片管理模块
const imageManager = require('../outfit_category/modules/imageManager');
// 引入搭配管理模块
const outfitManager = require('../outfit_category/modules/outfitManager');
// 引入本地图片缓存模块
const localImageCache = require('../outfit_category/modules/localImageCache');

// 定义颜色主题
const THEMES = {
  AUTUMN: 'autumn',
  PINK_BLUE: 'pinkBlue',
  BLACK_WHITE: 'blackWhite'
};

Page({
  data: {
    // 定义颜色常量 - 秋季色彩方案
    colors: {
      cowhide_cocoa: '#442D1C',   // 深棕色
      spiced_wine: '#74301C',     // 红棕色
      toasted_caramel: '#84592B', // 焦糖色
      olive_harvest: '#9D9167',   // 橄榄色
      golden_batter: '#E8D1A7',   // 金黄色
    },
    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',      // 深粉色
      pinkMedium: '#EEA0B2',    // 中粉色
      pinkLight: '#F9C9D6',     // 浅粉色
      blueLight: '#CBE0F9',     // 浅蓝色
      blueMedium: '#97C8E5',    // 中蓝色
      blueDark: '#5EA0D0',      // 深蓝色
    },
    // 黑白色系配色
    blackWhiteColors: {
      black: '#000000',         // 纯黑
      darkGray: '#333333',      // 深灰
      mediumGray: '#666666',    // 中灰
      lightGray: '#CCCCCC',     // 浅灰
      white: '#FFFFFF',         // 纯白
      offWhite: '#F5F5F5',      // 灰白
    },

    // 主题风格
    themeStyle: THEMES.AUTUMN,  // 默认秋季风格

    // 导航栏样式
    navBackground: '#E8D1A7',   // 默认导航栏背景色
    navFrontColor: '#000000',   // 默认导航栏前景色
    navTitle: '时尚穿搭杂志',   // 导航栏标题

    isLoading: true,            // 加载状态
    userOpenId: '',             // 用户OpenID

    // 类别信息
    category: '',               // 类别ID
    categoryName: '',           // 类别名称
    outfits: [],                // 所有搭配列表

    // 杂志翻页相关
    currentIndex: 0,            // 当前显示的搭配索引
    currentOutfit: null,        // 当前显示的搭配
    totalOutfits: 0,            // 搭配总数

    // 翻页动画
    animation: null,            // 动画实例
    animating: false,           // 是否正在动画中
    pageTurnClass: '',          // 翻页效果类名

    // 触摸事件相关
    touchStartX: 0,             // 触摸开始X坐标
    touchEndX: 0,               // 触摸结束X坐标

    // 页面交互相关
    showPageIndicator: true,    // 是否显示页码指示器
    showEmptyState: false,      // 是否显示空状态

    // 图片缓存控制
    imageRefreshNeeded: false,  // 标记图片是否需要刷新

    // 新增翻页状态控制
    isPageTurning: false,        // 是否正在翻页
    touchStartTime: 0,          // 触摸开始时间

    // 封面模式控制
    showCover: true,            // 默认显示封面

    // 页码拖动相关
    isDraggingPage: false,      // 是否正在拖动页码
    dragPageIndex: 0,           // 拖动时的页码索引
    sliderPosition: 0,          // 滑块位置百分比
  },

  // URL检查定时器
  urlCheckTimer: null,

  // 页面加载
  onLoad: function(options) {
    // 初始化云环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-3gi97kso9ab01185',
        traceUser: true,
      });
    }

    // 初始化动画实例
    this.initAnimation();

    // 清除过期的URL缓存
    imageManager.clearExpiredURLCache();

    // 确保本地图片缓存目录存在
    localImageCache.ensureCacheDir();

    // 清除过期的本地图片缓存
    localImageCache.clearExpiredCache();

    // 获取保存的主题设置
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme) {
      this.setData({
        themeStyle: savedTheme
      });
      // 应用主题样式
      this.applyThemeStyle(savedTheme);
    }

    // 获取类别参数
    if (options && options.category) {
      const category = options.category;

      // 设置类别名称
      const categoryNames = {
        'daily': '日常穿搭',
        'work': '职业穿搭',
        'party': '派对穿搭',
        'sport': '运动穿搭',
        'seasonal': '季节穿搭',
        'custom': '自定义穿搭'
      };

      const categoryName = categoryNames[category] || '穿搭杂志';
      const navTitle = `${categoryName}杂志`;

      this.setData({
        category: category,
        categoryName: categoryName,
        navTitle: navTitle
      });

      // 获取用户OpenID
      this.getUserOpenId();
    } else {
      wx.showToast({
        title: '缺少类别参数',
        icon: 'none'
      });

      // 延迟返回
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }

    // 初始化页面数据
    this.setData({
      isPageTurning: false,
      pageTurnClass: '',
      // ... 其他现有的初始化数据
    });

    // 加载数据
    this.loadOutfits();

    // 首次进入页面，提示长按上传封面功能
    wx.showToast({
      title: '长按封面可上传图片',
      icon: 'none',
      duration: 2000
    });
  },

  // 页面显示
  onShow: function() {
    console.log('页面显示，尝试获取最新数据');

    // 如果已经有类别信息，尝试获取最新数据
    if (this.data.category && this.data.userOpenId) {
      // 启动定时器，定期检查图片是否需要刷新（每5分钟检查一次）
      this.startURLCheckTimer();
    }
  },

  // 页面隐藏
  onHide: function() {
    // 页面隐藏时清除定时器
    this.clearURLCheckTimer();
  },

  // 页面卸载
  onUnload: function() {
    // 页面卸载时清除定时器
    this.clearURLCheckTimer();
  },

  // 初始化动画实例
  initAnimation: function() {
    this.animation = wx.createAnimation({
      duration: 300,
      timingFunction: 'ease',
      delay: 0
    });
  },

  // 应用主题样式
  applyThemeStyle: function(themeStyle) {
    let navBackground, navFrontColor;

    switch (themeStyle) {
      case THEMES.AUTUMN:
        navBackground = this.data.colors.golden_batter;
        navFrontColor = '#000000';
        break;
      case THEMES.PINK_BLUE:
        navBackground = this.data.pinkBlueColors.pinkLight;
        navFrontColor = '#000000';
        break;
      case THEMES.BLACK_WHITE:
        navBackground = this.data.blackWhiteColors.white;
        navFrontColor = '#000000';
        break;
      default:
        navBackground = this.data.colors.golden_batter;
        navFrontColor = '#000000';
    }

    this.setData({
      navBackground: navBackground,
      navFrontColor: navFrontColor
    });
  },

  // 启动URL检查定时器
  startURLCheckTimer: function() {
    // 清除可能存在的旧定时器
    this.clearURLCheckTimer();

    // 创建新定时器，每5分钟检查一次
    this.urlCheckTimer = setInterval(() => {
      this.refreshImages();
    }, 300000); // 5分钟

    console.log('已启动图片检查定时器');
  },

  // 清除URL检查定时器
  clearURLCheckTimer: function() {
    if (this.urlCheckTimer) {
      clearInterval(this.urlCheckTimer);
      this.urlCheckTimer = null;
      console.log('已清除图片检查定时器');
    }
  },

  // 刷新所有图片
  refreshImages: function() {
    console.log('开始刷新图片');

    if (!this.data.outfits || this.data.outfits.length === 0) {
      console.log('没有搭配数据，不需要刷新图片');
      return;
    }

    this.setData({
      imageRefreshNeeded: true
    }, () => {
      // 使用imageManager处理所有搭配的图片
      imageManager.processOutfitsImages(this.data.outfits)
        .then(processedOutfits => {
          console.log('图片刷新完成，已处理', processedOutfits.length, '个搭配');

          // 更新数据
          this.setData({
            outfits: processedOutfits,
            imageRefreshNeeded: false
          });

          // 更新当前显示的搭配
          if (processedOutfits.length > 0) {
            this.updateCurrentOutfit();
          }
        })
        .catch(err => {
          console.error('刷新图片出错:', err);
          this.setData({
            imageRefreshNeeded: false
          });
        });
    });
  },

  // 获取用户OpenID
  getUserOpenId: function() {
    // 尝试从缓存获取
    try {
      const userOpenId = wx.getStorageSync('userOpenId');
      if (userOpenId) {
        this.setData({
          userOpenId: userOpenId
        }, () => {
          // 加载搭配数据
          this.loadOutfits();
        });
        return;
      }
    } catch (e) {
      console.error('从缓存获取OpenID失败:', e);
    }

    // 如果缓存中没有，则从云函数获取
    wx.cloud.callFunction({
      name: 'login',
      data: {},
      success: res => {
        console.log('获取用户OpenID成功:', res.result.openid);
        const userOpenId = res.result.openid;

        // 保存到本地缓存
        wx.setStorageSync('userOpenId', userOpenId);

        // 更新数据并加载搭配
        this.setData({
          userOpenId: userOpenId
        }, () => {
          // 加载搭配数据
          this.loadOutfits();
        });
      },
      fail: err => {
        console.error('获取用户OpenID失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载搭配数据
  loadOutfits: function() {
    this.setData({
      isLoading: true
    });

    // 查询数据库获取该类别的搭配
    const db = wx.cloud.database();
    const _ = db.command;
    const outfitsCollection = db.collection('outfits');

    // 构建查询条件
    const query = {
      category: this.data.category
    };

    // 如果有用户OpenID，添加到查询条件
    if (this.data.userOpenId) {
      query._openid = this.data.userOpenId;
    }

    console.log('从云数据库查询搭配，查询条件:', query);

    outfitsCollection.where(query)
      .orderBy('createTime', 'desc')
      .get()
      .then(res => {
        console.log('获取搭配成功:', res.data);

        // 处理搭配数据
        let outfits = res.data || [];

        // 如果没有搭配数据，显示空状态
        if (outfits.length === 0) {
          console.log('当前类别没有搭配数据');

          this.setData({
            outfits: [],
            totalOutfits: 0,
            isLoading: false,
            showEmptyState: true
          });

          // 隐藏加载提示
          wx.hideLoading();
          return;
        }

        console.log('获取到', outfits.length, '条搭配数据');

        // 格式化搭配数据，确保有必要的字段
        outfits = outfits.map(outfit => {
          // 确保outfit有id字段
          if (!outfit.id && outfit._id) {
            outfit.id = outfit._id;
          }

          // 确保outfit有类型字段
          if (!outfit.type && outfit.category) {
            outfit.type = outfit.category;
          } else if (!outfit.type && !outfit.category) {
            // 默认类型为日常
            outfit.type = 'daily';
            outfit.category = 'daily';
          }

          return outfit;
        });

        // 检查是否需要获取完整的衣物数据
        const needFetchClothes = outfits.some(outfit =>
          outfit.items && Array.isArray(outfit.items) &&
          outfit.items.some(item => item.clothingId && (!item.name || !item.imageUrl || !item.imageFileID))
        );

        if (needFetchClothes) {
          console.log('需要获取衣物详细数据');

          // 收集所有需要获取的衣物ID
          const allClothingIds = [];
          outfits.forEach(outfit => {
            if (outfit.items && Array.isArray(outfit.items)) {
              outfit.items.forEach(item => {
                if (item && item.clothingId && (!item.name || !item.imageUrl || !item.imageFileID)) {
                  allClothingIds.push(item.clothingId);
                }
              });
            }
          });

          if (allClothingIds.length > 0) {
            console.log('准备获取', allClothingIds.length, '个衣物的详细数据');

            // 获取衣物数据
            this.getClothesData(allClothingIds)
              .then(clothesData => {
                console.log('获取到', clothesData.length, '个衣物详细数据');

                // 更新每个搭配的衣物项数据
                outfits.forEach(outfit => {
                  if (outfit.items && Array.isArray(outfit.items)) {
                    outfit.items = outfit.items.map(item => {
                      if (item && item.clothingId) {
                        const clothingData = clothesData.find(c => c && c._id === item.clothingId);
                        if (clothingData) {
                          // 优先使用抠图后的图片 (processedImageFileID)
                          const imageFileID = clothingData.processedImageFileID || clothingData.imageFileID || clothingData.imageUrl || null;

                          // 合并衣物数据
                          return {
                            ...item,
                            name: item.name || clothingData.name,
                            type: item.type || clothingData.type || clothingData.category,
                            category: item.category || clothingData.category,
                            // 优先使用抠图后的图片URL或文件ID
                            imageUrl: item.imageUrl || clothingData.processedImageUrl || clothingData.imageUrl || clothingData.processedImageFileID,
                            // 保存原始fileID和抠图后的fileID用于后续刷新
                            imageFileID: imageFileID,
                            processedImageFileID: clothingData.processedImageFileID || null,
                            originalImageFileID: clothingData.imageFileID || null,
                            originalClothing: clothingData
                          };
                        }
                      }
                      return item;
                    });
                  }
                });

                // 处理所有搭配的图片URL
                return imageManager.processOutfitsImages(outfits);
              })
              .then(processedOutfits => {
                console.log('图片处理完成，更新UI');

                // 确保每个搭配都有预览图
                processedOutfits = this.ensurePreviewImages(processedOutfits);

                this.setData({
                  outfits: processedOutfits,
                  totalOutfits: processedOutfits.length,
                  isLoading: false,
                  showEmptyState: false
                }, () => {
                  // 如果有搭配数据，更新当前显示的搭配
                  if (processedOutfits.length > 0) {
                    this.updateCurrentOutfit();
                  }
                });

                // 隐藏加载提示
                wx.hideLoading();
              })
              .catch(err => {
                console.error('处理搭配衣物详细数据出错:', err);

                // 即使出错，也尝试处理图片并显示搭配数据
                imageManager.processOutfitsImages(outfits)
                  .then(processedOutfits => {
                    // 确保每个搭配都有预览图
                    processedOutfits = this.ensurePreviewImages(processedOutfits);

                    this.setData({
                      outfits: processedOutfits,
                      totalOutfits: processedOutfits.length,
                      isLoading: false,
                      showEmptyState: false
                    }, () => {
                      // 如果有搭配数据，更新当前显示的搭配
                      if (processedOutfits.length > 0) {
                        this.updateCurrentOutfit();
                      }
                    });

                    // 隐藏加载提示
                    wx.hideLoading();
                  })
                  .catch(imgErr => {
                    console.error('处理搭配图片出错:', imgErr);

                    // 确保每个搭配都有预览图
                    outfits = this.ensurePreviewImages(outfits);

                    this.setData({
                      outfits: outfits,
                      totalOutfits: outfits.length,
                      isLoading: false,
                      showEmptyState: outfits.length === 0
                    }, () => {
                      // 如果有搭配数据，更新当前显示的搭配
                      if (outfits.length > 0) {
                        this.updateCurrentOutfit();
                      }
                    });

                    wx.hideLoading();
                  });
              });
          } else {
            // 即使没有需要获取的衣物ID，也需要处理图片
            imageManager.processOutfitsImages(outfits)
              .then(processedOutfits => {
                console.log('图片处理完成，更新UI');

                // 确保每个搭配都有预览图
                processedOutfits = this.ensurePreviewImages(processedOutfits);

                this.setData({
                  outfits: processedOutfits,
                  totalOutfits: processedOutfits.length,
                  isLoading: false,
                  showEmptyState: false
                }, () => {
                  // 如果有搭配数据，更新当前显示的搭配
                  if (processedOutfits.length > 0) {
                    this.updateCurrentOutfit();
                  }
                });

                // 隐藏加载提示
                wx.hideLoading();
              })
              .catch(err => {
                console.error('处理搭配图片出错:', err);

                // 确保每个搭配都有预览图
                outfits = this.ensurePreviewImages(outfits);

                this.setData({
                  outfits: outfits,
                  totalOutfits: outfits.length,
                  isLoading: false,
                  showEmptyState: outfits.length === 0
                }, () => {
                  // 如果有搭配数据，更新当前显示的搭配
                  if (outfits.length > 0) {
                    this.updateCurrentOutfit();
                  }
                });

                wx.hideLoading();
              });
          }
        } else {
          // 即使没有需要获取的衣物数据，也需要处理图片
          imageManager.processOutfitsImages(outfits)
            .then(processedOutfits => {
              console.log('图片处理完成，更新UI');

              // 确保每个搭配都有预览图
              processedOutfits = this.ensurePreviewImages(processedOutfits);

              this.setData({
                outfits: processedOutfits,
                totalOutfits: processedOutfits.length,
                isLoading: false,
                showEmptyState: false
              }, () => {
                // 如果有搭配数据，更新当前显示的搭配
                if (processedOutfits.length > 0) {
                  this.updateCurrentOutfit();
                }
              });

              // 隐藏加载提示
              wx.hideLoading();
            })
            .catch(err => {
              console.error('处理搭配图片出错:', err);

              // 确保每个搭配都有预览图
              outfits = this.ensurePreviewImages(outfits);

              this.setData({
                outfits: outfits,
                totalOutfits: outfits.length,
                isLoading: false,
                showEmptyState: outfits.length === 0
              }, () => {
                // 如果有搭配数据，更新当前显示的搭配
                if (outfits.length > 0) {
                  this.updateCurrentOutfit();
                }
              });

              wx.hideLoading();
            });
        }
      })
      .catch(err => {
        console.error('加载搭配数据失败:', err);
        this.setData({
          isLoading: false,
          showEmptyState: true
        });

        wx.showToast({
          title: '加载搭配失败',
          icon: 'none'
        });

        wx.hideLoading();
      });
  },

  // 获取衣物数据
  getClothesData: function(clothingIds) {
    return new Promise((resolve, reject) => {
      if (!clothingIds || !Array.isArray(clothingIds) || clothingIds.length === 0) {
        resolve([]);
        return;
      }

      console.log('获取衣物数据，ID数量:', clothingIds.length);

      const db = wx.cloud.database();
      const _ = db.command;

      // 查询条件：衣物ID在指定数组中
      db.collection('clothes')
        .where({
          _id: _.in(clothingIds)
        })
        .get()
        .then(res => {
          console.log('获取衣物数据成功:', res.data);
          resolve(res.data || []);
        })
        .catch(err => {
          console.error('获取衣物数据失败:', err);
          resolve([]);
        });
    });
  },

  // 确保每个搭配都有预览图
  ensurePreviewImages: function(outfits) {
    if (!outfits || !Array.isArray(outfits)) {
      return [];
    }

    return outfits.map(outfit => {
      // 如果有outfit_cover属性，优先使用它作为预览图
      if (outfit.outfit_cover) {
        // 确保同时设置previewImage以保持兼容性
        if (!outfit.previewImage) {
          outfit.previewImage = outfit.outfit_cover;
        }
        return outfit;
      }

      // 如果没有预览图，尝试使用第一个衣物的图片作为预览图
      if (!outfit.previewImage && outfit.items && outfit.items.length > 0 && outfit.items[0].imageUrl) {
        outfit.previewImage = outfit.items[0].imageUrl;
      }

      // 如果仍然没有预览图，使用默认图片
      if (!outfit.previewImage) {
        outfit.previewImage = imageManager.getDefaultPreviewImageUrl();
      }

      return outfit;
    });
  },

  // 更新当前显示的搭配
  updateCurrentOutfit: function() {
    if(this.data.outfits && this.data.outfits.length > 0) {
      const currentOutfit = this.data.outfits[this.data.currentIndex];
      this.setData({
        currentOutfit: currentOutfit
      });
    }
  },

  // 跳转到创建搭配页面
  goToCreateOutfit: function() {
    wx.navigateTo({
      url: `/page/wardrobe/outfit/outfit_create/outfit_create?category=${this.data.category}`
    });
  },

  // 查看搭配详情
  viewOutfitDetail: function(e) {
    const outfitId = e.currentTarget.dataset.id;

    wx.navigateTo({
      url: `/page/wardrobe/outfit/outfit_detail/outfit_detail?id=${outfitId}`
    });
  },

  // 处理图片加载错误
  handleImageError: function(e) {
    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;
    const itemIndex = e.currentTarget.dataset.itemIndex;

    console.log('图片加载失败:', type, index, itemIndex);

    if (type === 'preview') {
      // 预览图加载失败，设置默认图片
      const updatedOutfits = [...this.data.outfits];
      const defaultPreviewUrl = imageManager.getDefaultPreviewImageUrl();

      // 清除outfit_cover和previewImage属性
      updatedOutfits[index].outfit_cover = null;
      updatedOutfits[index].previewImage = defaultPreviewUrl;

      this.setData({
        outfits: updatedOutfits
      });

      if (index === this.data.currentIndex) {
        this.setData({
          'currentOutfit.outfit_cover': null,
          'currentOutfit.previewImage': defaultPreviewUrl
        });
      }
    } else if (type === 'item') {
      // 衣物图片加载失败，设置默认图片
      const updatedOutfits = [...this.data.outfits];
      if (updatedOutfits[index] && updatedOutfits[index].items && updatedOutfits[index].items[itemIndex]) {
        updatedOutfits[index].items[itemIndex].imageUrl = imageManager.getDefaultItemImageUrl();
      }

      this.setData({
        outfits: updatedOutfits
      });

      if (index === this.data.currentIndex) {
        this.setData({
          [`currentOutfit.items[${itemIndex}].imageUrl`]: imageManager.getDefaultItemImageUrl()
        });
      }
    }
  },

  // 切换封面模式和内容模式
  toggleCoverMode: function() {
    if (this.data.isAnimating) return;

    this.setData({
      isAnimating: true,
      pageTurnClass: 'page-turn-left'
    });

    // 延迟切换，让翻页动画有时间播放
    setTimeout(() => {
      this.setData({
        showCover: !this.data.showCover,
        pageTurnClass: '',
        isAnimating: false
      });

      // 播放翻页声音
      this.playPageTurnSound();
    }, 300);
  },

  // 跳转到搭配详情页
  goToOutfitDetail: function() {
    if (!this.data.currentOutfit) return;

    wx.navigateTo({
      url: `/page/wardrobe/outfit/outfit_detail/outfit_detail?id=${this.data.currentOutfit._id || this.data.currentOutfit.id}`
    });
  },

  // 处理翻页按钮点击
  onPageButtonTap: function(e) {
    const direction = e.currentTarget.dataset.direction;

    // 避免动画期间重复触发
    if (this.data.isAnimating) {
      return;
    }

    // 设置动画标志
    this.setData({
      isAnimating: true
    });

    if (direction === 'prev' && this.data.currentIndex > 0) {
      // 播放翻页声音
      this.playPageTurnSound();

      // 添加向右翻页的动画类 - 修正为right
      this.setData({
        pageTurnClass: 'page-turn-right'
      });

      // 延迟切换到前一个搭配
      setTimeout(() => {
        const newIndex = this.data.currentIndex - 1;
        this.setData({
          currentIndex: newIndex,
          currentOutfit: this.data.outfits[newIndex],
          pageTurnClass: '',
          isAnimating: false,
          showCover: true // 切换搭配时恢复为封面模式
        });
      }, 600); // 动画持续时间

    } else if (direction === 'next' && this.data.currentIndex < this.data.totalOutfits - 1) {
      // 播放翻页声音
      this.playPageTurnSound();

      // 添加向左翻页的动画类 - 修正为left
      this.setData({
        pageTurnClass: 'page-turn-left'
      });

      // 延迟切换到下一个搭配
      setTimeout(() => {
        const newIndex = this.data.currentIndex + 1;
        this.setData({
          currentIndex: newIndex,
          currentOutfit: this.data.outfits[newIndex],
          pageTurnClass: '',
          isAnimating: false,
          showCover: true // 切换搭配时恢复为封面模式
        });
      }, 600); // 动画持续时间
    }
  },

  // 播放翻页声音
  playPageTurnSound: function() {
    const app = getApp();
    // 检查是否有全局音效资源配置
    if (app && app.globalData && app.globalData.audioResources && app.globalData.audioResources.pageTurn) {
      // 使用微信的音频API播放翻页声音
      const audioContext = wx.createInnerAudioContext();
      audioContext.src = app.globalData.audioResources.pageTurn; // 使用全局配置的音效路径
      audioContext.play();
      console.log('播放翻页音效:', app.globalData.audioResources.pageTurn);
    } else {
      console.warn('未找到翻页音效资源');
    }
  },

  // 触摸开始时记录位置
  touchStart: function(e) {
    if (this.data.isAnimating) return;

    // 记录开始触摸的X坐标
    this.startX = e.touches[0].pageX;
    this.touchStartTime = Date.now();
  },

  // 触摸结束时判断是否需要翻页
  touchEnd: function(e) {
    if (this.data.isAnimating || !this.startX) return;

    const endX = e.changedTouches[0].pageX;
    const distanceX = endX - this.startX;
    const touchEndTime = Date.now();
    const touchDuration = touchEndTime - this.touchStartTime;

    // 计算滑动速度
    const velocity = Math.abs(distanceX) / touchDuration;

    // 滑动距离大于20px或速度足够快时触发翻页
    if (Math.abs(distanceX) > 20 || velocity > 0.25) {
      // 向左滑动，显示下一页
      if (distanceX < 0 && this.data.currentIndex < this.data.totalOutfits - 1) {
        this.onPageButtonTap({
          currentTarget: {
            dataset: { direction: 'next' }
          }
        });
      }
      // 向右滑动，显示上一页
      else if (distanceX > 0 && this.data.currentIndex > 0) {
        this.onPageButtonTap({
          currentTarget: {
            dataset: { direction: 'prev' }
          }
        });
      }
    }

    // 重置触摸位置
    this.startX = null;
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 格式化日期
  formatDate: function(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  /**
   * 处理长按事件，上传封面图
   */
  handleLongPress: function() {
    const that = this;
    wx.showActionSheet({
      itemList: ['上传封面图'],
      success: function(res) {
        if (res.tapIndex === 0) {
          // 上传封面图
          wx.chooseImage({
            count: 1,
            sizeType: ['compressed'],
            sourceType: ['album', 'camera'],
            success: function(res) {
              const tempFilePath = res.tempFilePaths[0];
              const currentOutfitId = that.data.currentOutfit._id;

              // 显示加载提示
              wx.showLoading({
                title: '正在上传...',
                mask: true
              });

              // 直接上传图片到服务器或云存储
              that.uploadCoverImage(tempFilePath, currentOutfitId);
            }
          });
        }
      }
    });
  },

  /**
   * 上传封面图到服务器或云存储
   */
  uploadCoverImage: function(tempFilePath, outfitId) {
    const that = this;
    // 判断是否使用云开发
    if (wx.cloud) {
      const cloudPath = `outfit_covers/${outfitId}_${new Date().getTime()}.jpg`;

      wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: tempFilePath,
        success: res => {
          // 获取图片的云存储地址
          const fileID = res.fileID;
          // 更新搭配的封面图
          that.updateOutfitCoverImage(outfitId, fileID);
        },
        fail: e => {
          wx.hideLoading();
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          });
          console.error('[上传封面] 失败：', e);
        }
      });
    } else {
      // 非云开发环境，使用普通上传API
      // 此处根据实际后端接口实现
      wx.uploadFile({
        url: 'YOUR_UPLOAD_API_URL', // 替换为实际的上传接口
        filePath: tempFilePath,
        name: 'file',
        formData: {
          'outfitId': outfitId
        },
        success: function(res) {
          const data = JSON.parse(res.data);
          if (data.code === 0) {
            // 上传成功，更新搭配的封面图
            that.updateOutfitCoverImage(outfitId, data.url);
          } else {
            wx.hideLoading();
            wx.showToast({
              title: data.msg || '上传失败',
              icon: 'none'
            });
          }
        },
        fail: function() {
          wx.hideLoading();
          wx.showToast({
            title: '网络异常，请重试',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 更新搭配的封面图
   */
  updateOutfitCoverImage: function(outfitId, imageUrl) {
    const that = this;
    // 判断是否使用云开发
    if (wx.cloud) {
      const db = wx.cloud.database();
      db.collection('outfits').doc(outfitId).update({
        data: {
          previewImage: imageUrl,  // 保留原有的previewImage更新，保持向后兼容
          outfit_cover: imageUrl,  // 新增outfit_cover属性
          updateTime: db.serverDate()
        },
        success: function() {
          wx.hideLoading();
          wx.showToast({
            title: '封面更新成功',
            icon: 'success'
          });

          // 更新本地数据
          const currentIndex = that.data.currentIndex;
          const outfits = that.data.outfits;
          outfits[currentIndex].previewImage = imageUrl;
          outfits[currentIndex].outfit_cover = imageUrl;  // 同时更新outfit_cover属性
          that.setData({
            outfits: outfits,
            currentOutfit: outfits[currentIndex]
          });
        },
        fail: function(err) {
          wx.hideLoading();
          wx.showToast({
            title: '封面更新失败',
            icon: 'none'
          });
          console.error('[更新封面] 失败：', err);
        }
      });
    } else {
      // 非云开发环境，使用普通API更新
      // 此处根据实际后端接口实现
      wx.request({
        url: 'YOUR_UPDATE_API_URL', // 替换为实际的更新接口
        method: 'POST',
        data: {
          outfitId: outfitId,
          previewImage: imageUrl,
          outfit_cover: imageUrl  // 同时更新outfit_cover属性
        },
        success: function(res) {
          const data = res.data;
          if (data.code === 0) {
            wx.hideLoading();
            wx.showToast({
              title: '封面更新成功',
              icon: 'success'
            });

            // 更新本地数据
            const currentIndex = that.data.currentIndex;
            const outfits = that.data.outfits;
            outfits[currentIndex].previewImage = imageUrl;
            outfits[currentIndex].outfit_cover = imageUrl;  // 同时更新outfit_cover属性
            that.setData({
              outfits: outfits,
              currentOutfit: outfits[currentIndex]
            });
          } else {
            wx.hideLoading();
            wx.showToast({
              title: data.msg || '封面更新失败',
              icon: 'none'
            });
          }
        },
        fail: function() {
          wx.hideLoading();
          wx.showToast({
            title: '网络异常，请重试',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 开始页码拖动
   */
  startPageDrag: function(e) {
    if (this.data.totalOutfits <= 1) return; // 只有一个搭配时不需要拖动功能

    // 显示拖动提示
    wx.showToast({
      title: '拖动可快速定位',
      icon: 'none',
      duration: 1000
    });

    // 初始化拖动状态
    this.setData({
      isDraggingPage: true,
      dragPageIndex: this.data.currentIndex,
      sliderPosition: (this.data.currentIndex / (this.data.totalOutfits - 1)) * 100
    });

    // 记录触摸起始位置
    this.dragStartX = e.touches[0].clientX;
    this.dragStartIndex = this.data.currentIndex;
  },

  /**
   * 页码拖动中
   */
  onPageDrag: function(e) {
    if (!this.data.isDraggingPage) return;

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();

    // 计算拖动距离
    const moveX = e.touches[0].clientX - this.dragStartX;

    // 计算滑动比例 (400rpx是滑块宽度)
    const sliderWidth = 400 * systemInfo.windowWidth / 750;
    const moveRatio = moveX / sliderWidth;

    // 计算新的索引位置 - 使用更精确的计算方法
    const totalSteps = this.data.totalOutfits - 1;
    let newIndex;

    if (totalSteps <= 0) {
      newIndex = 0;
    } else {
      // 计算拖动后的位置比例
      const positionRatio = this.dragStartIndex / totalSteps + moveRatio;
      // 将比例转换为索引
      newIndex = Math.round(positionRatio * totalSteps);
      // 限制索引范围
      newIndex = Math.max(0, Math.min(totalSteps, newIndex));
    }

    // 计算滑块位置百分比
    const sliderPosition = totalSteps <= 0 ? 50 : (newIndex / totalSteps) * 100;

    // 更新数据
    this.setData({
      dragPageIndex: newIndex,
      sliderPosition: sliderPosition
    });

    // 添加触觉反馈
    if (this.lastDragIndex !== newIndex) {
      wx.vibrateShort({
        type: 'light'
      });
      this.lastDragIndex = newIndex;
    }
  },

  /**
   * 结束页码拖动
   */
  endPageDrag: function() {
    if (!this.data.isDraggingPage) return;

    // 获取最终选择的索引
    const targetIndex = this.data.dragPageIndex;

    // 重置拖动索引记录
    this.lastDragIndex = null;

    // 如果索引发生变化，跳转到对应搭配
    if (targetIndex !== this.data.currentIndex) {
      // 设置动画标志
      this.setData({
        isAnimating: true,
        // 根据方向设置翻页动画
        pageTurnClass: targetIndex > this.data.currentIndex ? 'page-turn-left' : 'page-turn-right'
      });

      // 播放翻页声音
      this.playPageTurnSound();

      // 延迟切换到目标搭配
      setTimeout(() => {
        this.setData({
          currentIndex: targetIndex,
          currentOutfit: this.data.outfits[targetIndex],
          pageTurnClass: '',
          isAnimating: false,
          showCover: true, // 切换搭配时恢复为封面模式
          isDraggingPage: false // 结束拖动状态
        });

        // 显示跳转提示
        wx.showToast({
          title: `已跳转到第${targetIndex + 1}套搭配`,
          icon: 'none',
          duration: 1500
        });
      }, 600); // 动画持续时间
    } else {
      // 索引未变化，直接结束拖动状态
      this.setData({
        isDraggingPage: false
      });
    }
  },
});
