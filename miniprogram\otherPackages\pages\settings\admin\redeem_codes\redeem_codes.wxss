/* 兑换码管理页面样式 */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
  box-sizing: border-box;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  margin-top: 200rpx;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #74301C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 28rpx;
}

/* 无权限提示样式 */
.no-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 200rpx;
}

.no-permission-text {
  color: #666;
  font-size: 32rpx;
  margin-top: 30rpx;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-buttons {
  display: flex;
  gap: 16rpx;
}

.add-btn {
  background-color: #74301C;
  color: white;
  padding: 15rpx 30rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.membership-btn {
  background-color: #D4AF37;
  color: white;
  padding: 15rpx 30rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 部分样式 */
.section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 15rpx;
}

.empty-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 50rpx 0;
}

/* 兑换码列表样式 */
.code-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.code-item {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.code-item-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.code-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.code-value {
  font-size: 28rpx;
  color: #74301C;
  font-family: monospace;
}

.code-item-actions {
  display: flex;
  justify-content: flex-end;
  margin: 15rpx 0;
}

.action-btn {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  margin-left: 15rpx;
}

.copy-btn {
  background-color: #e8f0fe;
  color: #4285f4;
}

.disable-btn {
  background-color: #fee8e8;
  color: #f44336;
}

.enable-btn {
  background-color: #e8feea;
  color: #4caf50;
}

.code-item-info {
  display: flex;
  flex-wrap: wrap;
  font-size: 24rpx;
  color: #666;
}

.code-tag {
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.active-tag {
  background-color: #e8feea;
  color: #4caf50;
}

.inactive-tag {
  background-color: #fee8e8;
  color: #f44336;
}

.onetime-tag {
  background-color: #fef8e8;
  color: #ff9800;
}

.multi-tag {
  background-color: #e8f0fe;
  color: #4285f4;
}

.code-time {
  flex: 1;
  text-align: right;
  padding: 6rpx 0;
}

/* 兑换历史样式 */
.history-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.history-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.history-info {
  flex: 1;
}

.history-code {
  font-size: 26rpx;
  color: #74301C;
  font-family: monospace;
}

.history-name {
  font-size: 28rpx;
  color: #333;
  margin-top: 5rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

.history-detail {
  display: flex;
  flex-direction: column;
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

.history-user {
  margin-bottom: 5rpx;
}

.history-reward {
  color: #74301C;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 85%;
  max-width: 650rpx;
  max-height: 90vh;
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #74301C;
  color: white;
}

.close-btn {
  font-size: 40rpx;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
  overflow-y: auto;
  max-height: 60vh;
}

.form-item {
  margin-bottom: 25rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-radio-group {
  display: flex;
  flex-wrap: wrap;
}

.radio-label {
  margin-right: 30rpx;
  margin-bottom: 15rpx;
  font-size: 28rpx;
}

.switch-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 15rpx;
}

.modal-footer {
  padding: 20rpx 30rpx 40rpx;
  display: flex;
  justify-content: flex-end;
}

.modal-btn {
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-left: 20rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #74301C;
  color: white;
}

.confirm-btn[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}

/* 详情样式 */
.detail-row {
  display: flex;
  margin-bottom: 20rpx;
}

.detail-label {
  width: 200rpx;
  color: #666;
  font-size: 28rpx;
}

.detail-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
}

.code-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copy-icon {
  color: #4285f4;
  font-size: 26rpx;
}

.status-tag {
  display: inline-block;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.active-status {
  background-color: #e8feea;
  color: #4caf50;
}

.inactive-status {
  background-color: #fee8e8;
  color: #f44336;
}

.form-hint {
  font-size: 24rpx;
  color: #888;
  margin-top: 6rpx;
  margin-left: 10rpx;
}

/* 批量生成成功后的结果显示 */
.batch-result {
  margin-top: 20rpx;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 20rpx;
  border-radius: 8rpx;
  max-height: 200rpx;
  overflow-y: auto;
}

.batch-result-item {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  padding: 8rpx 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.batch-result-item:last-child {
  border-bottom: none;
}

.result-code {
  font-family: monospace;
  color: #74301C;
}

.copy-all-btn {
  text-align: center;
  background-color: #f5f5f5;
  color: #333;
  padding: 10rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
  font-size: 24rpx;
}