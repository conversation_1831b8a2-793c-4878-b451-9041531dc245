// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  // 获取用户openid，优先使用传入的，否则使用上下文中的
  const userOpenId = event.userOpenId || wxContext.OPENID
  
  // 获取任务参数
  const { taskId, completed, date, rewardClaimed } = event
  
  // 获取日期参数，格式为YYYY-MM-DD，如果没有则使用当前日期
  const requestDate = date || formatDate(new Date())
  
  if (!userOpenId) {
    return {
      success: false,
      error: '无法获取用户ID'
    }
  }
  
  if (!taskId) {
    return {
      success: false,
      error: '缺少任务ID'
    }
  }
  
  try {
    // 查询用户当日任务
    const dailyTasksCollection = db.collection('dailyTasks')
    let userDailyTasks = await dailyTasksCollection.where({
      userOpenId: userOpenId,
      date: requestDate
    }).get()
    
    // 如果用户没有当日任务记录，先创建默认任务
    if (!userDailyTasks.data || userDailyTasks.data.length === 0) {
      // 调用getDailyTasks云函数获取默认任务
      const result = await cloud.callFunction({
        name: 'getDailyTasks',
        data: {
          userOpenId: userOpenId,
          date: requestDate
        }
      })
      
      if (!result.result.success) {
        return {
          success: false,
          error: '创建任务失败'
        }
      }
      
      userDailyTasks = {
        data: [result.result.data]
      }
    }
    
    const userDailyTask = userDailyTasks.data[0]
    
    // 查找并更新指定任务的状态
    const tasks = userDailyTask.tasks || []
    const taskIndex = tasks.findIndex(task => task.id === taskId)
    
    if (taskIndex === -1) {
      return {
        success: false,
        error: '指定的任务不存在'
      }
    }
    
    // 更新任务状态
    tasks[taskIndex].completed = completed !== undefined ? completed : !tasks[taskIndex].completed
    
    // 计算已完成任务数量
    const completedCount = tasks.filter(task => task.completed).length
    const totalCount = tasks.length
    const canDrawPrize = completedCount >= totalCount
    
    // 更新数据库
    const updateData = {
      tasks: tasks,
      completedCount: completedCount,
      canDrawPrize: canDrawPrize,
      updatedAt: db.serverDate()
    };
    
    // 如果提供了rewardClaimed参数，添加到更新数据中
    if (rewardClaimed !== undefined) {
      updateData.rewardClaimed = rewardClaimed;
      
      // 如果标记为已领取奖励，则不可再抽奖
      if (rewardClaimed === true) {
        updateData.canDrawPrize = false;
      }
    }
    
    await dailyTasksCollection.doc(userDailyTask._id).update({
      data: updateData
    })
    
    // 返回更新后的任务数据
    return {
      success: true,
      data: {
        ...userDailyTask,
        tasks: tasks,
        completedCount: completedCount,
        canDrawPrize: canDrawPrize,
        rewardClaimed: rewardClaimed !== undefined ? rewardClaimed : (userDailyTask.rewardClaimed || false)
      }
    }
  } catch (error) {
    console.error('更新每日任务状态失败:', error)
    
    return {
      success: false,
      error: error.message || '更新任务状态失败'
    }
  }
}

// 辅助函数：格式化日期为YYYY-MM-DD格式
function formatDate(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
} 